# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*.tsbuildinfo

# Untracked files/directories to be ignored
src/Cesium1.117/
src/js/cache/useCache.ts
static/jsdata/TEST/README.md
static/jsdata/TEST/example_model_config.json
test.xml
test_tile.png
