# PowerLine3D

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vitejs.dev/config/).

## Development Environment Setup

To ensure a consistent development environment, this project has the following requirements:

- **Node.js**: Version `v21.0.0` is required. We recommend using [nvm](https://github.com/nvm-sh/nvm) (Node Version Manager) to manage Node.js versions. To switch to the correct version, run the following command in the project root:
  ```sh
  nvm use
  ```
  This command reads the `.nvmrc` file and automatically switches to the specified Node.js version.

- **CesiumJS**: The project uses Cesium version `1.117.0`. The version is locked via the `package.json` and the `.npmrc` file (`save-exact=true`) to prevent accidental updates and ensure dependency consistency.

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```
