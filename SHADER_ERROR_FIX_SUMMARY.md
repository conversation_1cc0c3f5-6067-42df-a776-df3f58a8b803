# 机械化施工着色器编译错误修复方案

## 问题分析

机械化施工模型在运行一段时间后出现着色器编译错误：
```
RuntimeError: Fragment shader failed to compile. Compile log: ERROR: 0:544: 'v_texCoord_0' : undeclared identifier
```

### 根本原因

1. **增量纹理加载导致着色器重编译**：`incrementallyLoadTextures: true` 导致模型在运行时动态加载纹理，触发着色器重新编译
2. **纹理坐标变量声明不一致**：顶点着色器和片元着色器之间的varying变量声明不匹配
3. **纹理加载失败的累积效应**：纹理加载失败时的错误处理机制干扰了着色器编译过程
4. **着色器防护机制不完善**：现有的着色器防护在处理varying变量时存在问题

## 解决方案

### 1. 修复模型加载配置 (`src/js/construction/ConstructionModelManager.ts`)

**修改内容**：
- 关闭增量纹理加载：`incrementallyLoadTextures: false`
- 添加着色器缓存键：`cacheKey: 'construction_model_${modelId}'`

**原因**：避免运行时纹理加载导致的着色器重编译问题。

### 2. 优化纹理错误处理 (`src/js/construction/ModelResourceManager.ts`)

**修改内容**：
- 移除对 `console.error` 的重写
- 使用事件监听器代替直接重写控制台方法
- 添加清理函数确保监听器正确移除

**原因**：避免错误处理机制干扰着色器编译过程。

### 3. 增强着色器防护机制 (`src/main.ts`)

**修改内容**：
- 改进备用片元着色器，确保varying变量声明一致
- 添加纹理坐标值的范围检查：`clamp(coord, 0.0, 1.0)`
- 在渲染恢复中添加着色器缓存清理

**原因**：提供更稳定的着色器备用方案，防止编译错误。

### 4. 添加错误监听和恢复机制 (`src/views/PowerLineView.vue`)

**修改内容**：
- 添加着色器错误监听器：`setupShaderErrorListener()`
- 实现错误处理方法：`handleShaderError()` 和 `handleConstructionModelError()`
- 在组件销毁时清理监听器

**原因**：及时检测和处理着色器错误，防止应用崩溃。

## 技术细节

### 着色器变量一致性

确保顶点着色器和片元着色器中的varying变量声明完全一致：

```glsl
// 顶点着色器
varying vec2 v_texCoord_0;
varying vec2 v_st;
varying vec3 v_positionEC;
varying vec3 v_normalEC;

// 片元着色器
varying vec2 v_texCoord_0;  // 必须与顶点着色器完全匹配
varying vec2 v_st;
varying vec3 v_positionEC;
varying vec3 v_normalEC;
```

### 纹理坐标安全处理

在片元着色器中添加安全检查：

```glsl
void main() {
  vec2 coord = v_texCoord_0;
  coord = clamp(coord, 0.0, 1.0);  // 确保坐标在有效范围内
  gl_FragColor = vec4(0.5 + coord.x * 0.3, 0.4 + coord.y * 0.3, 0.8, 0.8);
}
```

### 错误恢复策略

1. **检测错误**：监听包含 `shader failed to compile`、`v_texCoord_0`、`undeclared identifier` 的错误
2. **清理资源**：移除有问题的模型和相关实体
3. **重置状态**：清理着色器缓存，强制重新渲染
4. **防止递归**：使用延迟处理避免错误处理过程中再次触发错误

## 预期效果

1. **消除着色器编译错误**：通过关闭增量纹理加载和改进着色器防护
2. **提高系统稳定性**：添加错误监听和恢复机制
3. **保持功能完整性**：确保机械化施工模型正常显示
4. **改善用户体验**：避免应用因着色器错误而停止渲染

## 测试建议

1. **长时间运行测试**：让机械化施工模型运行较长时间，观察是否还会出现着色器错误
2. **多类型模型测试**：测试不同类型的机械化施工模型（索道运输、架线施工等）
3. **错误恢复测试**：人为触发着色器错误，验证恢复机制是否正常工作
4. **性能影响测试**：确认修改不会显著影响渲染性能

## 注意事项

1. **关闭增量纹理加载**可能会增加初始加载时间，但能避免运行时的着色器重编译问题
2. **错误监听器**需要在组件销毁时正确清理，避免内存泄漏
3. **着色器缓存清理**是一个较重的操作，只在必要时执行
4. **备用着色器**提供基本的渲染效果，可能与原始模型外观有差异
