# 机械化施工着色器编译错误深度修复方案

## 问题分析

机械化施工模型在运行一段时间后出现着色器编译错误：
```
RuntimeError: Fragment shader failed to compile. Compile log: ERROR: 0:544: 'v_texCoord_0' : undeclared identifier
ERROR: 0:544: '=' : dimension mismatch
ERROR: 0:544: '=' : cannot convert from 'const highp float' to 'highp 2-component vector of float'
```

### 深层根本原因

1. **多处增量纹理加载配置不一致**：`ConstructionModelManager.ts` 和 `ModelResourceManager.ts` 中都有增量纹理加载设置，导致部分模型仍然触发运行时着色器重编译
2. **模型文件本身的着色器问题**：某些GLB模型文件内部的着色器代码存在纹理坐标变量声明不一致的问题
3. **Cesium着色器系统的动态编译机制**：Cesium在运行时会根据模型特性动态生成着色器变体，可能产生不兼容的代码
4. **WebGL上下文状态管理问题**：长时间运行可能导致WebGL上下文状态异常，影响着色器编译
5. **着色器缓存机制失效**：着色器缓存在某些情况下可能失效，导致重复编译有问题的着色器

## 深度解决方案

### 1. 全面关闭增量纹理加载

**修改文件**：
- `src/js/construction/ConstructionModelManager.ts`
- `src/js/construction/ModelResourceManager.ts`

**修改内容**：
- 在所有模型加载点设置 `incrementallyLoadTextures: false`
- 添加 `allowPicking: false` 减少着色器变体
- 设置 `shadows: Cesium.ShadowMode.DISABLED` 简化着色器
- 添加 `releaseGltfJson: true` 释放内存

**原因**：彻底避免运行时纹理加载导致的着色器重编译问题。

### 2. 深层着色器编译拦截 (`src/main.ts`)

**新增功能**：
- **WebGL上下文恢复机制**：监听WebGL上下文丢失和恢复事件
- **着色器编译拦截**：在WebGL层面拦截着色器编译，自动修复常见错误
- **智能着色器修复**：检测 `v_texCoord_0` 相关错误并自动修复
- **备用着色器注入**：编译失败时自动使用最简单的备用着色器

**技术细节**：
```javascript
// 拦截WebGL着色器编译
Context.prototype.createShader = function(source, type, logShaderCompilation) {
  try {
    return originalCreateShader.call(this, source, type, logShaderCompilation);
  } catch (error) {
    // 自动修复纹理坐标相关错误
    if (source.includes('v_texCoord_0')) {
      let fixedSource = source;
      // 修复varying声明不一致
      // 修复纹理坐标赋值错误
      return originalCreateShader.call(this, fixedSource, type, logShaderCompilation);
    }
  }
};
```

### 3. 模型着色器验证机制 (`src/js/construction/ConstructionModelManager.ts`)

**新增功能**：
- **模型就绪验证**：模型加载完成后验证着色器状态
- **着色器一致性检查**：检查顶点和片元着色器的varying变量一致性
- **问题模型标记**：标记存在着色器问题的模型
- **预防性移除**：可选择立即移除有问题的模型

### 4. 增强错误恢复机制 (`src/views/PowerLineView.vue`)

**新增功能**：
- **实时着色器错误监听**：监听包含着色器编译错误的事件
- **全面资源清理**：清理模型、实体、索道线路等所有相关资源
- **着色器缓存清理**：强制清理Cesium着色器缓存
- **功能临时禁用**：错误后临时禁用机械化施工功能避免重复触发

### 5. 优化纹理错误处理 (`src/js/construction/ModelResourceManager.ts`)

**修改内容**：
- 移除对 `console.error` 的危险重写
- 使用安全的事件监听器监听纹理错误
- 添加清理函数确保监听器正确移除
- 避免错误处理机制干扰着色器编译过程

## 技术细节

### 着色器变量一致性

确保顶点着色器和片元着色器中的varying变量声明完全一致：

```glsl
// 顶点着色器
varying vec2 v_texCoord_0;
varying vec2 v_st;
varying vec3 v_positionEC;
varying vec3 v_normalEC;

// 片元着色器
varying vec2 v_texCoord_0;  // 必须与顶点着色器完全匹配
varying vec2 v_st;
varying vec3 v_positionEC;
varying vec3 v_normalEC;
```

### 纹理坐标安全处理

在片元着色器中添加安全检查：

```glsl
void main() {
  vec2 coord = v_texCoord_0;
  coord = clamp(coord, 0.0, 1.0);  // 确保坐标在有效范围内
  gl_FragColor = vec4(0.5 + coord.x * 0.3, 0.4 + coord.y * 0.3, 0.8, 0.8);
}
```

### 错误恢复策略

1. **检测错误**：监听包含 `shader failed to compile`、`v_texCoord_0`、`undeclared identifier` 的错误
2. **清理资源**：移除有问题的模型和相关实体
3. **重置状态**：清理着色器缓存，强制重新渲染
4. **防止递归**：使用延迟处理避免错误处理过程中再次触发错误

## 预期效果

1. **消除着色器编译错误**：通过关闭增量纹理加载和改进着色器防护
2. **提高系统稳定性**：添加错误监听和恢复机制
3. **保持功能完整性**：确保机械化施工模型正常显示
4. **改善用户体验**：避免应用因着色器错误而停止渲染

## 测试建议

1. **长时间运行测试**：让机械化施工模型运行较长时间，观察是否还会出现着色器错误
2. **多类型模型测试**：测试不同类型的机械化施工模型（索道运输、架线施工等）
3. **错误恢复测试**：人为触发着色器错误，验证恢复机制是否正常工作
4. **性能影响测试**：确认修改不会显著影响渲染性能

## 注意事项

1. **关闭增量纹理加载**可能会增加初始加载时间，但能避免运行时的着色器重编译问题
2. **错误监听器**需要在组件销毁时正确清理，避免内存泄漏
3. **着色器缓存清理**是一个较重的操作，只在必要时执行
4. **备用着色器**提供基本的渲染效果，可能与原始模型外观有差异
