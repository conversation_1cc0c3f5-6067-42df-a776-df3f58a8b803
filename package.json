{"name": "powerline3d", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host --mode devserver", "dev-server": "vite --host --mode gsd", "build": "npm run type-check && npm run build-only", "preview": "vite preview", "build-only": "vite build", "build:gsd": "vite build --mode gsd", "build:owner": "vite build --mode owner", "build:prod-dm": "vite build --mode prod-dm", "type-check": "vue-tsc --build --force"}, "dependencies": {"@dvgis/cesium-map": "^3.1.0", "@turf/turf": "^7.0.0", "axios": "^1.7.4", "coordtransform": "^2.1.2", "dexie": "^4.0.8", "element-plus": "^2.9.3", "fs-extra": "^11.3.0", "jsencrypt": "^3.3.2", "leaflet": "^1.9.4", "pinia": "^2.2.2", "pinia-plugin-persist": "^1.0.0", "pinia-plugin-persistedstate": "^3.2.1", "proj4": "^2.12.0", "three": "^0.155.0", "tsparticles-slim": "^2.12.0", "vue": "^3.4.29", "vue-router": "^4.3.3"}, "devDependencies": {"@rollup/plugin-replace": "^6.0.2", "@tsconfig/node20": "^20.1.4", "@types/fs-extra": "^11.0.3", "@types/node": "^20.14.5", "@vitejs/plugin-vue": "^5.0.5", "@vue/tsconfig": "^0.5.1", "less": "^4.2.0", "npm-run-all2": "^6.2.0", "ts-node": "^10.9.2", "typescript": "^5.4.0", "vite": "^5.3.1", "vite-plugin-static-copy": "^3.1.0", "vue-tsc": "^2.0.21"}}