lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@dvgis/cesium-map':
        specifier: ^3.1.0
        version: 3.1.0
      '@turf/turf':
        specifier: ^7.0.0
        version: 7.1.0
      axios:
        specifier: ^1.7.4
        version: 1.7.4
      coordtransform:
        specifier: ^2.1.2
        version: 2.1.2
      dexie:
        specifier: ^4.0.8
        version: 4.0.8
      element-plus:
        specifier: ^2.9.3
        version: 2.9.10(vue@3.4.38(typescript@5.4.5))
      fs-extra:
        specifier: ^11.3.0
        version: 11.3.0
      jsencrypt:
        specifier: ^3.3.2
        version: 3.3.2
      leaflet:
        specifier: ^1.9.4
        version: 1.9.4
      pinia:
        specifier: ^2.2.2
        version: 2.2.2(typescript@5.4.5)(vue@3.4.38(typescript@5.4.5))
      pinia-plugin-persist:
        specifier: ^1.0.0
        version: 1.0.0(pinia@2.2.2(typescript@5.4.5)(vue@3.4.38(typescript@5.4.5)))(vue@3.4.38(typescript@5.4.5))
      proj4:
        specifier: ^2.12.0
        version: 2.12.0
      three:
        specifier: ^0.155.0
        version: 0.155.0
      tsparticles-slim:
        specifier: ^2.12.0
        version: 2.12.0
      vue:
        specifier: ^3.4.29
        version: 3.4.38(typescript@5.4.5)
      vue-router:
        specifier: ^4.3.3
        version: 4.4.3(vue@3.4.38(typescript@5.4.5))
    devDependencies:
      '@tsconfig/node20':
        specifier: ^20.1.4
        version: 20.1.4
      '@types/cesium':
        specifier: ^1.70.4
        version: 1.70.4
      '@types/fs-extra':
        specifier: ^11.0.3
        version: 11.0.4
      '@types/node':
        specifier: ^20.14.5
        version: 20.15.0
      '@vitejs/plugin-vue':
        specifier: ^5.0.5
        version: 5.1.2(vite@5.4.1(@types/node@20.15.0)(less@4.2.0))(vue@3.4.38(typescript@5.4.5))
      '@vue/tsconfig':
        specifier: ^0.5.1
        version: 0.5.1
      less:
        specifier: ^4.2.0
        version: 4.2.0
      npm-run-all2:
        specifier: ^6.2.0
        version: 6.2.2
      ts-node:
        specifier: ^10.9.2
        version: 10.9.2(@types/node@20.15.0)(typescript@5.4.5)
      typescript:
        specifier: ^5.4.0
        version: 5.4.5
      vite:
        specifier: ^5.3.1
        version: 5.4.1(@types/node@20.15.0)(less@4.2.0)
      vue-tsc:
        specifier: ^2.0.21
        version: 2.0.29(typescript@5.4.5)

packages:

  '@babel/helper-string-parser@7.24.8':
    resolution: {integrity: sha512-pO9KhhRcuUyGnJWwyEgnRJTSIZHiT+vMD0kPeD+so0l7mxkMT19g3pjY9GTnHySck/hDzq+dtW/4VgnMkippsQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.24.7':
    resolution: {integrity: sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.25.3':
    resolution: {integrity: sha512-iLTJKDbJ4hMvFPgQwwsVoxtHyWpKKPBrxkANrSYewDPaPpT5py5yeVkgPIJ7XYXhndxJpaA3PyALSXQ7u8e/Dw==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/types@7.25.2':
    resolution: {integrity: sha512-YTnYtra7W9e6/oAZEHj0bJehPRUlLH9/fbpT5LfB0NhQXyALCRkRs3zH9v07IYhkgpqX6Z78FnuccZr/l4Fs4Q==}
    engines: {node: '>=6.9.0'}

  '@cesium/engine@2.4.0':
    resolution: {integrity: sha512-U5/m0XSpoxr5uo7rf0+rBNw4AwTMdQRRB5FX5bcunfE6J2eyKWtMp0lRhcTKVDN0O7HZwJIIZt0rtiXt/aC0tQ==}
    engines: {node: '>=14.0.0'}

  '@cesium/widgets@2.3.0':
    resolution: {integrity: sha512-U+lHh1LoDezuKN/f4Hu5WROh87S5YECsO8Vb0gcXoEEG6hHyHz2zOO8GXmDWtKPSTjq88gxAp4APGKztvn7pxQ==}
    engines: {node: '>=14.0.0'}

  '@cspotcode/source-map-support@0.8.1':
    resolution: {integrity: sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==}
    engines: {node: '>=12'}

  '@ctrl/tinycolor@3.6.1':
    resolution: {integrity: sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==}
    engines: {node: '>=10'}

  '@dvgis/cesium-map@3.1.0':
    resolution: {integrity: sha512-wBcpyeTMgOtQzumZGAP1tzN7mNkjJnsWKJ5og2cuNu5iBGjmMRmsnWkTlcl29jqH3JJs9Ued5IPz9XA1XLFg6g==}
    deprecated: change the package to @cesium-china/cesium-map

  '@element-plus/icons-vue@2.3.1':
    resolution: {integrity: sha512-XxVUZv48RZAd87ucGS48jPf6pKu0yV5UCg9f4FFwtrYxXOwWuVJo6wOvSLKEoMQKjv8GsX/mhP6UsC1lRwbUWg==}
    peerDependencies:
      vue: ^3.2.0

  '@esbuild/aix-ppc64@0.21.5':
    resolution: {integrity: sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.21.5':
    resolution: {integrity: sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.21.5':
    resolution: {integrity: sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.21.5':
    resolution: {integrity: sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.21.5':
    resolution: {integrity: sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.21.5':
    resolution: {integrity: sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.21.5':
    resolution: {integrity: sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.21.5':
    resolution: {integrity: sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.21.5':
    resolution: {integrity: sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.21.5':
    resolution: {integrity: sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.21.5':
    resolution: {integrity: sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.21.5':
    resolution: {integrity: sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.21.5':
    resolution: {integrity: sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.21.5':
    resolution: {integrity: sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.21.5':
    resolution: {integrity: sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.21.5':
    resolution: {integrity: sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.21.5':
    resolution: {integrity: sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-x64@0.21.5':
    resolution: {integrity: sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-x64@0.21.5':
    resolution: {integrity: sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.21.5':
    resolution: {integrity: sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.21.5':
    resolution: {integrity: sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.21.5':
    resolution: {integrity: sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.21.5':
    resolution: {integrity: sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@floating-ui/core@1.6.7':
    resolution: {integrity: sha512-yDzVT/Lm101nQ5TCVeK65LtdN7Tj4Qpr9RTXJ2vPFLqtLxwOrpoxAHAJI8J3yYWUc40J0BDBheaitK5SJmno2g==}

  '@floating-ui/dom@1.6.10':
    resolution: {integrity: sha512-fskgCFv8J8OamCmyun8MfjB1Olfn+uZKjOKZ0vhYF3gRmEUXcGOjxWL8bBr7i4kIuPZ2KD2S3EUIOxnjC8kl2A==}

  '@floating-ui/utils@0.2.7':
    resolution: {integrity: sha512-X8R8Oj771YRl/w+c1HqAC1szL8zWQRwFvgDwT129k9ACdBoud/+/rX9V0qiMl6LWUdP9voC2nDVZYPMQQsb6eA==}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}

  '@jridgewell/trace-mapping@0.3.9':
    resolution: {integrity: sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==}

  '@protobufjs/aspromise@1.1.2':
    resolution: {integrity: sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==}

  '@protobufjs/base64@1.1.2':
    resolution: {integrity: sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==}

  '@protobufjs/codegen@2.0.4':
    resolution: {integrity: sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==}

  '@protobufjs/eventemitter@1.1.0':
    resolution: {integrity: sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==}

  '@protobufjs/fetch@1.1.0':
    resolution: {integrity: sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==}

  '@protobufjs/float@1.0.2':
    resolution: {integrity: sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==}

  '@protobufjs/inquire@1.1.0':
    resolution: {integrity: sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==}

  '@protobufjs/path@1.1.2':
    resolution: {integrity: sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==}

  '@protobufjs/pool@1.1.0':
    resolution: {integrity: sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==}

  '@protobufjs/utf8@1.1.0':
    resolution: {integrity: sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==}

  '@rollup/rollup-android-arm-eabi@4.20.0':
    resolution: {integrity: sha512-TSpWzflCc4VGAUJZlPpgAJE1+V60MePDQnBd7PPkpuEmOy8i87aL6tinFGKBFKuEDikYpig72QzdT3QPYIi+oA==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.20.0':
    resolution: {integrity: sha512-u00Ro/nok7oGzVuh/FMYfNoGqxU5CPWz1mxV85S2w9LxHR8OoMQBuSk+3BKVIDYgkpeOET5yXkx90OYFc+ytpQ==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.20.0':
    resolution: {integrity: sha512-uFVfvzvsdGtlSLuL0ZlvPJvl6ZmrH4CBwLGEFPe7hUmf7htGAN+aXo43R/V6LATyxlKVC/m6UsLb7jbG+LG39Q==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.20.0':
    resolution: {integrity: sha512-xbrMDdlev53vNXexEa6l0LffojxhqDTBeL+VUxuuIXys4x6xyvbKq5XqTXBCEUA8ty8iEJblHvFaWRJTk/icAQ==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-linux-arm-gnueabihf@4.20.0':
    resolution: {integrity: sha512-jMYvxZwGmoHFBTbr12Xc6wOdc2xA5tF5F2q6t7Rcfab68TT0n+r7dgawD4qhPEvasDsVpQi+MgDzj2faOLsZjA==}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm-musleabihf@4.20.0':
    resolution: {integrity: sha512-1asSTl4HKuIHIB1GcdFHNNZhxAYEdqML/MW4QmPS4G0ivbEcBr1JKlFLKsIRqjSwOBkdItn3/ZDlyvZ/N6KPlw==}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-arm64-gnu@4.20.0':
    resolution: {integrity: sha512-COBb8Bkx56KldOYJfMf6wKeYJrtJ9vEgBRAOkfw6Ens0tnmzPqvlpjZiLgkhg6cA3DGzCmLmmd319pmHvKWWlQ==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm64-musl@4.20.0':
    resolution: {integrity: sha512-+it+mBSyMslVQa8wSPvBx53fYuZK/oLTu5RJoXogjk6x7Q7sz1GNRsXWjn6SwyJm8E/oMjNVwPhmNdIjwP135Q==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-powerpc64le-gnu@4.20.0':
    resolution: {integrity: sha512-yAMvqhPfGKsAxHN8I4+jE0CpLWD8cv4z7CK7BMmhjDuz606Q2tFKkWRY8bHR9JQXYcoLfopo5TTqzxgPUjUMfw==}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-gnu@4.20.0':
    resolution: {integrity: sha512-qmuxFpfmi/2SUkAw95TtNq/w/I7Gpjurx609OOOV7U4vhvUhBcftcmXwl3rqAek+ADBwSjIC4IVNLiszoj3dPA==}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-s390x-gnu@4.20.0':
    resolution: {integrity: sha512-I0BtGXddHSHjV1mqTNkgUZLnS3WtsqebAXv11D5BZE/gfw5KoyXSAXVqyJximQXNvNzUo4GKlCK/dIwXlz+jlg==}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-gnu@4.20.0':
    resolution: {integrity: sha512-y+eoL2I3iphUg9tN9GB6ku1FA8kOfmF4oUEWhztDJ4KXJy1agk/9+pejOuZkNFhRwHAOxMsBPLbXPd6mJiCwew==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-musl@4.20.0':
    resolution: {integrity: sha512-hM3nhW40kBNYUkZb/r9k2FKK+/MnKglX7UYd4ZUy5DJs8/sMsIbqWK2piZtVGE3kcXVNj3B2IrUYROJMMCikNg==}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-win32-arm64-msvc@4.20.0':
    resolution: {integrity: sha512-psegMvP+Ik/Bg7QRJbv8w8PAytPA7Uo8fpFjXyCRHWm6Nt42L+JtoqH8eDQ5hRP7/XW2UiIriy1Z46jf0Oa1kA==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.20.0':
    resolution: {integrity: sha512-GabekH3w4lgAJpVxkk7hUzUf2hICSQO0a/BLFA11/RMxQT92MabKAqyubzDZmMOC/hcJNlc+rrypzNzYl4Dx7A==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.20.0':
    resolution: {integrity: sha512-aJ1EJSuTdGnM6qbVC4B5DSmozPTqIag9fSzXRNNo+humQLG89XpPgdt16Ia56ORD7s+H8Pmyx44uczDQ0yDzpg==}
    cpu: [x64]
    os: [win32]

  '@sxzz/popperjs-es@2.11.7':
    resolution: {integrity: sha512-Ccy0NlLkzr0Ex2FKvh2X+OyERHXJ88XJ1MXtsI9y9fGexlaXaVTPzBCRBwIxFkORuOb+uBqeu+RqnpgYTEZRUQ==}

  '@tsconfig/node10@1.0.11':
    resolution: {integrity: sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==}

  '@tsconfig/node12@1.0.11':
    resolution: {integrity: sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==}

  '@tsconfig/node14@1.0.3':
    resolution: {integrity: sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==}

  '@tsconfig/node16@1.0.4':
    resolution: {integrity: sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==}

  '@tsconfig/node20@20.1.4':
    resolution: {integrity: sha512-sqgsT69YFeLWf5NtJ4Xq/xAF8p4ZQHlmGW74Nu2tD4+g5fAsposc4ZfaaPixVu4y01BEiDCWLRDCvDM5JOsRxg==}

  '@turf/along@7.1.0':
    resolution: {integrity: sha512-WLgBZJ/B6CcASF6WL7M+COtHlVP0hBrMbrtKyF7KBlicwRuijJZXDtEQA5oLgr+k1b2HqGN+UqH2A0/E719enQ==}

  '@turf/angle@7.1.0':
    resolution: {integrity: sha512-YMHEV/YrARsWgWoQuXEWrQMsvB8z67nTMw2eiLZ883V7jwkhWQGvCW6W+/mGgsWQdHppjCZNcKryryhD2GRWVA==}

  '@turf/area@7.1.0':
    resolution: {integrity: sha512-w91FEe02/mQfMPRX2pXua48scFuKJ2dSVMF2XmJ6+BJfFiCPxp95I3+Org8+ZsYv93CDNKbf0oLNEPnuQdgs2g==}

  '@turf/bbox-clip@7.1.0':
    resolution: {integrity: sha512-PhZubKCzF/afwStUzODqOJluiCbCw244lCtVhXA9F+Pgkhvk8KvbFdgpPquOZ45OwuktrchSB28BrBkSBiadHw==}

  '@turf/bbox-polygon@7.1.0':
    resolution: {integrity: sha512-fvZB09ErCZOVlWVDop836hmpKaGUmfXnR9naMhS73A/8nn4M3hELbQtMv2R8gXj7UakXCuxS/i9erdpDFZ2O+g==}

  '@turf/bbox@7.1.0':
    resolution: {integrity: sha512-PdWPz9tW86PD78vSZj2fiRaB8JhUHy6piSa/QXb83lucxPK+HTAdzlDQMTKj5okRCU8Ox/25IR2ep9T8NdopRA==}

  '@turf/bearing@7.1.0':
    resolution: {integrity: sha512-X5lackrZ6FW+YhgjWxwVFRgWD1j4xm4t5VvE6EE6v/1PVaHQ5OCjf6u1oaLx5LSG+gaHUhjTlAHrn9MYPFaeTA==}

  '@turf/bezier-spline@7.1.0':
    resolution: {integrity: sha512-bhBY70bcVYJEosuW7B/TFtnE5rmPTTpxmJvljhGC0eyM84oNVv7apDBuseb5KdlTOOBIvdD9nIE4qV8lmplp6w==}

  '@turf/boolean-clockwise@7.1.0':
    resolution: {integrity: sha512-H5DYno+gHwZx+VaiC8DUBZXZQlxYecdSvqCfCACWi1uMsKvlht/O+xy65hz2P57lk2smlcV+1ETFVxJlEZduYg==}

  '@turf/boolean-concave@7.1.0':
    resolution: {integrity: sha512-IFCN25DI+hvngxIsv4+MPuRJQRl/Lz/xnZgpH82leCn4Jqn5wW7KqKFMz7G4GoKK+93cK5/6ioAxY7hVWBXxJw==}

  '@turf/boolean-contains@7.1.0':
    resolution: {integrity: sha512-ldy4j1/RVChYTYjEb4wWaE/JyF1jA87WpsB4eVLic6OcAYJGs7POF1kfKbcdkJJiRBmhI3CXNA+u+m9y4Z/j3g==}

  '@turf/boolean-crosses@7.1.0':
    resolution: {integrity: sha512-LK8UM3AENycuGinLCDaL0QSznGMnD0XsjFDGnY4KehshiL5Zd8ZsPyKmHOPygUJT9DWeH69iLx459lOc+5Vj2w==}

  '@turf/boolean-disjoint@7.1.0':
    resolution: {integrity: sha512-JapOG03kOCoGeYMWgTQjEifhr1nUoK4Os2cX0iC5X9kvZF4qCHeruX8/rffBQDx7PDKQKusSTXq8B1ISFi0hOw==}

  '@turf/boolean-equal@7.1.0':
    resolution: {integrity: sha512-deghtFMApc7fNsdXtZdgYR4gsU+TVfowcv666nrvZbPPsXL6NTYGBhDFmYXsJ8gPTCGT9uT0WXppdgT8diWOxA==}

  '@turf/boolean-intersects@7.1.0':
    resolution: {integrity: sha512-gpksWbb0RT+Z3nfqRfoACY3KEFyv2BPaxJ3L76PH67DhHZviq3Nfg85KYbpuhS64FSm+9tXe4IaKn6EjbHo20g==}

  '@turf/boolean-overlap@7.1.0':
    resolution: {integrity: sha512-mJRN0X8JiPm8eDZk5sLvIrsP03A2GId6ijx4VgSE1AvHwV6qB561KlUbWxga2AScocIfv/y/qd2OCs+/TQSZcg==}

  '@turf/boolean-parallel@7.1.0':
    resolution: {integrity: sha512-tA84Oux0X91CxUc6c/lZph5W9wUZGNT4fxFOg5Gp1IMTSwtxSYL1LMvKsr/VmMnwdOUkNcqAgU06+t4wBLtDfg==}

  '@turf/boolean-point-in-polygon@7.1.0':
    resolution: {integrity: sha512-mprVsyIQ+ijWTZwbnO4Jhxu94ZW2M2CheqLiRTsGJy0Ooay9v6Av5/Nl3/Gst7ZVXxPqMeMaFYkSzcTc87AKew==}

  '@turf/boolean-point-on-line@7.1.0':
    resolution: {integrity: sha512-Kd83EjeTyY4kVMAhcW3Lb8aChwh24BUIhmpE9Or8M+ETNsFGzn9M7qtIySJHLRzKAL3letvWSKXKQPuK1AhAzg==}

  '@turf/boolean-touches@7.1.0':
    resolution: {integrity: sha512-qN4LCs3RfVtNAAdn5GpsUFBqoZyAaK9UzSnGSh67GP9sy5M8MEHwM/HAJ5zGWJqQADrczI3U6BRWGLcGfGSz3Q==}

  '@turf/boolean-valid@7.1.0':
    resolution: {integrity: sha512-zq1QCfQEyn+piHlvxxDifjmsJn2xl53i4mnKFYdMQI/i09XiX+Fi/MVM3i2hf3D5AsEPsud8Tk7C7rWNCm4nVw==}

  '@turf/boolean-within@7.1.0':
    resolution: {integrity: sha512-pgXgKCzYHssADQ1nClB1Q9aWI/dE1elm2jy3B5X59XdoFXKrKDZA+gCHYOYgp2NGO/txzVfl3UKvnxIj54Fa4w==}

  '@turf/buffer@7.1.0':
    resolution: {integrity: sha512-QM3JiCMYA19k5ouO8wJtvICX3Y8XntxVpDfHSKhFFidZcCkMTR2PWWOpwS6EoL3t75rSKw/FOLIPLZGtIu963w==}

  '@turf/center-mean@7.1.0':
    resolution: {integrity: sha512-NQZB1LUVsyAD+p0+D4huzX2XVnfVx1yEEI9EX602THmi+g+nkge4SK9OMV11ov/Tv8JJ6aVNVPo/cy1vm/LCIQ==}

  '@turf/center-median@7.1.0':
    resolution: {integrity: sha512-jx4/Ql5+v41Cd0J/gseNCUbLTzWUT2LUaiXn8eFWDrvmEgqHIx7KJcGcJd5HzV+9zJwng4AXxyh5NMvUR0NjwA==}

  '@turf/center-of-mass@7.1.0':
    resolution: {integrity: sha512-j38oBlj7LBoCjZbrIo8EoHVGhk7UQmMLQ1fe8ZPAF9pd05XEL1qxyHKZKdQ/deGISiaEhXCyfLNrKAHAuy25RA==}

  '@turf/center@7.1.0':
    resolution: {integrity: sha512-p9AvBMwNZmRg65kU27cGKHAUQnEcdz8Y7f/i5DvaMfm4e8zmawr+hzPKXaUpUfiTyLs8Xt2W9vlOmNGyH+6X3w==}

  '@turf/centroid@7.1.0':
    resolution: {integrity: sha512-1Y1b2l+ZB1CZ+ITjUCsGqC4/tSjwm/R4OUfDztVqyyCq/VvezkLmTNqvXTGXgfP0GXkpv68iCfxF5M7QdM5pJQ==}

  '@turf/circle@7.1.0':
    resolution: {integrity: sha512-6qhF1drjwH0Dg3ZB9om1JkWTJfAqBcbtIrAj5UPlrAeHP87hGoCO2ZEsFEAL9Q18vntpivT89Uho/nqQUjJhYw==}

  '@turf/clean-coords@7.1.0':
    resolution: {integrity: sha512-q1U8UbRVL5cRdwOlNjD8mad8pWjFGe0s4ihg1pSiVNq7i47WASJ3k20yZiUFvuAkyNjV0rZ/A7Jd7WzjcierFg==}

  '@turf/clone@7.1.0':
    resolution: {integrity: sha512-5R9qeWvL7FDdBIbEemd0eCzOStr09oburDvJ1hRiPCFX6rPgzcZBQ0gDmZzoF4AFcNLb5IwknbLZjVLaUGWtFA==}

  '@turf/clusters-dbscan@7.1.0':
    resolution: {integrity: sha512-BmrBTOEaKN5FIED6b3yb3V3ejfK0A2Q3pT9/ji3mcRLJiBaRGeiN5V6gtGXe7PeMYdoqhHykU5Ye2uUtREWRdQ==}

  '@turf/clusters-kmeans@7.1.0':
    resolution: {integrity: sha512-M8cCqR6iE1jDSUF/UU9QdPUFrobZS2fo59TfF1IRHZ2G1EjbcK4GzZcUfmQS6DZraGudYutpMYIuNdm1dPMqdQ==}

  '@turf/clusters@7.1.0':
    resolution: {integrity: sha512-7CY3Ai+5V6q2O9/IgqLpJQrmrTy7aUJjTW1iRan8Tz3WixvxyJHeS3iyRy8Oc0046chQIaHLtyTgKVt2QdsPSA==}

  '@turf/collect@7.1.0':
    resolution: {integrity: sha512-6indMWLiKeBh4AsioNeFeFnO0k9U5CBsWAFEje6tOEFI4c+P7LF9mNA9z91H8KkrhegR9XNO5Vm2rmdY63aYXw==}

  '@turf/combine@7.1.0':
    resolution: {integrity: sha512-Xl7bGKKjgzIq2T/IemS6qnIykyuxU6cMxKtz+qLeWJGoNww/BllwxXePSV+dWRPXZTFFj96KIhBXAW0aUjAQKQ==}

  '@turf/concave@7.1.0':
    resolution: {integrity: sha512-aSid53gYRee4Tjc4pfeI3KI+RoBUnL/hRMilxIPduagTgZZS+cvvk01OQWBKm5UTVfHRGuy0XIqnK8y9RFinDQ==}

  '@turf/convex@7.1.0':
    resolution: {integrity: sha512-w9fUMZYE36bLrEWEj7L7aVMCB7NBtr2o8G+avRvUIwF4DPqbtcjlcZE9EEBfq44uYdn+/Pke6Iq42T/zyD/cpg==}

  '@turf/destination@7.1.0':
    resolution: {integrity: sha512-97XuvB0iaAiMg86hrnZ529WwP44TQAA9mmI5PMlchACiA4LFrEtWjjDzvO6234coieoqhrw6dZYcJvd5O2PwrQ==}

  '@turf/difference@7.1.0':
    resolution: {integrity: sha512-+JVzdskICQ8ULKQ9CpWUM5kBvoXxN4CO78Ez/Ki3/7NXl7+HM/nb12B0OyM8hkJchpb8TsOi0YwyJiKMqEpTBA==}

  '@turf/dissolve@7.1.0':
    resolution: {integrity: sha512-fyOnCSYVUZ8SF9kt9ROnQYlkJTE0hpWSoWwbMZQCAR7oVZVPiuPq7eIbzTP+k5jzEAnofsqoGs5qVDTjHcWMiw==}

  '@turf/distance-weight@7.1.0':
    resolution: {integrity: sha512-8m6s4y8Yyt6r3itf44yAJjXC+62UkrkhOpskIfaE0lHcBcvZz9wjboHoBf3bS4l/42E4StcanbFZdjOpODAdZw==}

  '@turf/distance@7.1.0':
    resolution: {integrity: sha512-hhNHhxCHB3ddzAGCNY4BtE29OZh+DAJPvUapQz+wOjISnlwvMcwLKvslgHWSYF536QDVe/93FEU2q67+CsZTPA==}

  '@turf/ellipse@7.1.0':
    resolution: {integrity: sha512-AfOahUmStDExWGPg8ZWxxkgom+fdJs7Mn9DzZH+fV/uZ+je1bLQpbPCUu9/ev6u/HhbYGl4VAL/CeQzjOyy6LQ==}

  '@turf/envelope@7.1.0':
    resolution: {integrity: sha512-WeLQse9wuxsxhzSqrJA6Ha7rLWnLKgdKY9cfxmJKHSpgqcJyNk60m7+T3UpI/nkGwpfbpeyB3EGC1EWPbxiDUg==}

  '@turf/explode@7.1.0':
    resolution: {integrity: sha512-To+GUbU6HtcHZ8S0w/dw1EbdQIOCXALTr6Ug5/IFg8hIBMJelDpVr3Smwy8uqhDRFinY2eprBwQnDPcd10eCqA==}

  '@turf/flatten@7.1.0':
    resolution: {integrity: sha512-Kb23pqEarcLsdBqnQcK0qTrSMiWNTVb9tOFrNlZc66DIhDLAdpOKG4eqk00CMoUzWTixlnawDgJRqcStRrR4WA==}

  '@turf/flip@7.1.0':
    resolution: {integrity: sha512-vac73W8WblzzNFanzWYLBzWDIcqc5xczOrtEO07RDEiKEI3Heo0471Jed3v9W506uuOX6/HAiCjXbRjTLjiLfw==}

  '@turf/geojson-rbush@7.1.0':
    resolution: {integrity: sha512-j1C7Ohlxa1z644bNOpgibcFGaDLgLXGLOzwF1tfQaP5y7E4PJQUXL0DWIgNb3Ke7gZC05LPHM25a5TRReUfFBQ==}

  '@turf/great-circle@7.1.0':
    resolution: {integrity: sha512-92q5fqUp5oW+FYekUIrUVR5PZBWbOV6NHKHPIiNahiPvtkpZItbbjoO+tGn5+2i8mxZP9FGOthayJe4V0a1xkg==}

  '@turf/helpers@7.1.0':
    resolution: {integrity: sha512-dTeILEUVeNbaEeoZUOhxH5auv7WWlOShbx7QSd4s0T4Z0/iz90z9yaVCtZOLbU89umKotwKaJQltBNO9CzVgaQ==}

  '@turf/hex-grid@7.1.0':
    resolution: {integrity: sha512-I+Apx0smOPkMzaS5HHL44YOxSkSUvrz+wtSIETsDFWWLT2xKNkaaEcYU5MkgSoEfQsj082M7EkOIIpocXlA3kg==}

  '@turf/interpolate@7.1.0':
    resolution: {integrity: sha512-VWec1OW9gHZLPS3yYkUXAHKMGQuYO4aqh8WCltT7Ym4efrKqkSOE5T+mBqO68QgcL8nY4kiNa8lxwXd0SfXDSA==}

  '@turf/intersect@7.1.0':
    resolution: {integrity: sha512-T0VhI6yhptX9EoMsuuBETyqV+edyq31SUC8bfuM6kdJ5WwJ0EvUfQoC+3bhMtCOn60lHawrUuGBgW+vCO8KGMg==}

  '@turf/invariant@7.1.0':
    resolution: {integrity: sha512-OCLNqkItBYIP1nE9lJGuIUatWGtQ4rhBKAyTfFu0z8npVzGEYzvguEeof8/6LkKmTTEHW53tCjoEhSSzdRh08Q==}

  '@turf/isobands@7.1.0':
    resolution: {integrity: sha512-iMLTOP/K5C05AttF4N1WeV+KrY4O5VWW/abO0N86XCWh1OeqmIUgqIBKEmhDzttAqC0UK2YrUfj0lI1Ez1fYZQ==}

  '@turf/isolines@7.1.0':
    resolution: {integrity: sha512-V6QTHXBT5ZsL3s9ZVBJgHYtz3gCFKqNnQLysNE02LE0fVVqaSao3sFrcpghmdDxf0hBCDK8lZVvyRGO6o32LHQ==}

  '@turf/jsts@2.7.1':
    resolution: {integrity: sha512-+nwOKme/aUprsxnLSfr2LylV6eL6T1Tuln+4Hl92uwZ8FrmjDRCH5Bi1LJNVfWCiYgk8+5K+t2zDphWNTsIFDA==}

  '@turf/kinks@7.1.0':
    resolution: {integrity: sha512-KKLYUsyJPU17fODwA81mhHzFYGQYocdbk9NxDPCcdRHvxzM8t95lptkGx/2k/9rXBs1DK7NmyzI4m7zDO0DK7g==}

  '@turf/length@7.1.0':
    resolution: {integrity: sha512-wUJj9WLKEudG1ngNao2ZwD+Dt6UkvWIbubuJ6lR6FndFDL3iezFhNGy0IXS+0xH9kXi2apiTnM9Vk5+i8BTEvQ==}

  '@turf/line-arc@7.1.0':
    resolution: {integrity: sha512-9/bM34PozTyJ5FXXPAzl/j0RpcTImgMFJZ0WhH0pZZEZRum6P0rJnENt2E2qI441zeozQ9H6X5DCiJogDmRUEw==}

  '@turf/line-chunk@7.1.0':
    resolution: {integrity: sha512-1lIUfqAQvCWAuUNC2ip8UYmM5kDltXOidLPW45Ee1OAIKYGBeFNtjwnxc0mQ40tnfTXclTYLDdOOP9LShspT9w==}

  '@turf/line-intersect@7.1.0':
    resolution: {integrity: sha512-JI3dvOsAoCqd4vUJ134FIzgcC42QpC/tBs+b4OJoxWmwDek3REv4qGaZY6wCg9X4hFSlCKFcnhMIQQZ/n720Qg==}

  '@turf/line-offset@7.1.0':
    resolution: {integrity: sha512-pz6irzhiQlJurU7DoXada6k3ei7PzY+VpsE/Wotm0D2KEAnoxqum2WK0rqqrhKPHKn+xpUGsHN9W/6K+qtmaHg==}

  '@turf/line-overlap@7.1.0':
    resolution: {integrity: sha512-BdHuEoFAtqvVw3LkjCdivG035nfuwZuxji2ijst+mkmDnlv7uwSBudJqcDGjU6up2r8P1mXChS4im4xjUz+lwg==}

  '@turf/line-segment@7.1.0':
    resolution: {integrity: sha512-9rgIIH6ZzC3IiWxDQtKsq+j6eu8fRinMkJeusfI9HqOTm4vO02Ll4F/FigjOMOO/6X3TJ+Pqe3gS99TUaBINkw==}

  '@turf/line-slice-along@7.1.0':
    resolution: {integrity: sha512-UwfnFORZnu4xdnuRXiQM3ODa8f9Q0FBjQF/XHNsPEI/xxmnwgQj3MZiULbAeHUbtU/7psTC7gEjfE3Lf0tcKQw==}

  '@turf/line-slice@7.1.0':
    resolution: {integrity: sha512-44xcjgMQxTa7tTAZlSD3t1cFjHi5SCfAqjg1ONv45EYKsQSonPaxD7LGzCbU5pR2RJjx3R7QRJx2G88hnGcXjQ==}

  '@turf/line-split@7.1.0':
    resolution: {integrity: sha512-QqUAmtlrnEu75cpLOmpEuiYU63BeVwpSKOBllBbu5gkP+7H/WBM/9fh7J0VgHNFHzqZCKiu8v4158k+CZr0QAg==}

  '@turf/line-to-polygon@7.1.0':
    resolution: {integrity: sha512-n/IWBRbo+l4XDTz4sfQsQm5bU9xex8KrthK397jQasd7a9PiOKGon9Z1t/lddTJhND6ajVyJ3hl+eZMtpQaghQ==}

  '@turf/mask@7.1.0':
    resolution: {integrity: sha512-d+u3IIiRhe17TDfP/+UMn9qRlJYPJpK7sj6WorsssluGi0yIG/Z24uWpcLskWKSI8NNgkIbDrp+GIYkJi2t7SA==}

  '@turf/meta@7.1.0':
    resolution: {integrity: sha512-ZgGpWWiKz797Fe8lfRj7HKCkGR+nSJ/5aKXMyofCvLSc2PuYJs/qyyifDPWjASQQCzseJ7AlF2Pc/XQ/3XkkuA==}

  '@turf/midpoint@7.1.0':
    resolution: {integrity: sha512-uiUU9TwRZOCeiTUn8+7oE6MJUvclfq+n6KQ5VCMTZXiRUJjPu7nDLpBle1t2WSv7/w7O0kSQ4FfKXh0gHnkJOw==}

  '@turf/moran-index@7.1.0':
    resolution: {integrity: sha512-xsvAr3IRF/C6PlRMoN/ANrRx6c3QFUJgBCIVfI7re+Lkdprrzgw1HZA48ZjP4F91xbhgA1scnRgQdHFi2vO2SA==}

  '@turf/nearest-neighbor-analysis@7.1.0':
    resolution: {integrity: sha512-FAhT8/op3DuvqH0XFhv055JhYq/FC4aaIxEZ4hj8c7W6sYhUHAQgdRZ0tJ1RLe5/h+eXhCTbQ+DFfnfv3klu8g==}

  '@turf/nearest-point-on-line@7.1.0':
    resolution: {integrity: sha512-aTjAOm7ab0tl5JoxGYRx/J/IbRL1DY1ZCIYQDMEQjK5gOllhclgeBC0wDXDkEZFGaVftjw0W2RtE2I0jX7RG4A==}

  '@turf/nearest-point-to-line@7.1.0':
    resolution: {integrity: sha512-rY2F/iY4S6U8H0hIoOI25xMWYEiKywxeTvTvn5GP8KCu+2oemfZROWa7n2+hQDRwO2/uaegrGEpxO7zlFarvzg==}

  '@turf/nearest-point@7.1.0':
    resolution: {integrity: sha512-VyInmhqfVWp+jE7sCK95o46qc4tDjAgzbRfRjr+rTgfFS1Sndyy1PdwyNn6TjBFDxiM6e+mjMEeGPjb1smJlEg==}

  '@turf/planepoint@7.1.0':
    resolution: {integrity: sha512-hFORBkCd7Q0kNUzLqksT4XglLgTQF9tCjG+dbnZ1VehpZu+w+vlHdoW/mY7XCX3Kj1ObiyzVmXffmVYgwXwF6Q==}

  '@turf/point-grid@7.1.0':
    resolution: {integrity: sha512-ihuuUcWuCu4Z1+34UYCM5NGsU2DJaB4uE8cS3jDQoUqlc+8ii2ng8kcGEtTwVn0HdPsoKA7bgvSZcisJO0v6Ww==}

  '@turf/point-on-feature@7.1.0':
    resolution: {integrity: sha512-lOO5J9I0diuGbN+r6jViEKRH3qfymsBvv25b7U0MuP8g/YC19ncUXZ86dmKfJx1++Rb485DS9h0nFvPmJpaOdg==}

  '@turf/point-to-line-distance@7.1.0':
    resolution: {integrity: sha512-Ps9eTOCaiNgxDaSNQux0wAcSLcrI0y0zYFaD9HnVm+yCMRliQXneFti2XXotS+gR7TpgnLRAAzyx4VzJMSN2tw==}

  '@turf/points-within-polygon@7.1.0':
    resolution: {integrity: sha512-SzqeD9Gcp11rEya+rCVMy6IPuYMrphNEkCiQ39W6ec9hsaqKlruqmtudKhhckMGVLVUUBCQAu5f55yjcDfVW2w==}

  '@turf/polygon-smooth@7.1.0':
    resolution: {integrity: sha512-mTlmg4XUP5rKgCP/73N91owkAXIc3t1ZKLuwsJGQM1/Op48T3rJmDwVR/WZIMnVlxl5tFbssWCCB3blj4ivx9g==}

  '@turf/polygon-tangents@7.1.0':
    resolution: {integrity: sha512-ffBgHXtkrpgkNs8E6s9sVLSKG4lPGH3WBk294FNKBt9NS+rbhNCv8yTuOMeP0bOm/WizaCq/SUtVryJpUSoI/g==}

  '@turf/polygon-to-line@7.1.0':
    resolution: {integrity: sha512-FBlfyBWNQZCTVGqlJH7LR2VXmvj8AydxrA8zegqek/5oPGtQDeUgIppKmvmuNClqbglhv59QtCUVaDK4bOuCTA==}

  '@turf/polygonize@7.1.0':
    resolution: {integrity: sha512-FBjxnOzO29MbE7MWnMPHHYtOo93cQopT5pXhkuPyoKgcTUCntR1+iVFpl5YFbMkYup0j5Oexjo/pbY38lVSZGw==}

  '@turf/projection@7.1.0':
    resolution: {integrity: sha512-3wHluMoOvXnTe7dfi0kcluTyLNG5MwGsSsK5OA98vkkLH6a1xvItn8e9GcesuT07oB2km/bgefxYEIvjQG5JCA==}

  '@turf/quadrat-analysis@7.1.0':
    resolution: {integrity: sha512-4O5h9PyWgpqYXja9O+kzr+qk5MUz0IkJqPtt5oWWX5s4jRcLNqiEUf+zi/GDBQkVV8jH3S5klT5CLrF1fxK3hQ==}

  '@turf/random@7.1.0':
    resolution: {integrity: sha512-22mXv8ejDMUWkz8DSMMqdZb0s7a0ISJzXt6T9cHovfT//vsotzkVH+5PDxJQjvmigKMnpaUgobHmQss23tAwEQ==}

  '@turf/rectangle-grid@7.1.0':
    resolution: {integrity: sha512-4d2AuDj4LfMMJxNHbds5yX1oFR3mIVAB5D7mx6pFB0e+YkQW0mE2dUWhDTFGJZM+n45yqbNQ5hg19bmiXv94ug==}

  '@turf/rewind@7.1.0':
    resolution: {integrity: sha512-zX0KDZpeiH89m1vYLTEJdDL6mFyoAsCxcG0P94mXO7/JXWf0AaxzA9MkNnA/d2QYX0G4ioCMjZ5cD6nXb8SXzw==}

  '@turf/rhumb-bearing@7.1.0':
    resolution: {integrity: sha512-ESZt70eOljHVnQMFKIdiu8LIHuQlpZgzh2nqSfV40BrYjsjI/sBKeK+sp2cBWk88nsSDlriPuMTNh4f50Jqpkw==}

  '@turf/rhumb-destination@7.1.0':
    resolution: {integrity: sha512-WA2TeO3qrv5ZrzNihtTLLYu8X4kd12WEC6JKElm99XhgLao1/4ao2SJUi43l88HqwbrnNiq4TueGQ6tYpXGU7A==}

  '@turf/rhumb-distance@7.1.0':
    resolution: {integrity: sha512-fR1V+yC4E1tnbdThomosiLcv0PQOwbfLSPM8rSWuxbMcJtffsncWxyJ0+N1F5juuHbcdaYhlduX8ri5I0ZCejw==}

  '@turf/sample@7.1.0':
    resolution: {integrity: sha512-9Iq/Ankr4+sgBoh4FpuVVvoW+AA10eej3FS89Zu79SFdCqUIdT7T42Nn3MlSVj4jMyA1oXyT2HIAlNWkwgLw6Q==}

  '@turf/sector@7.1.0':
    resolution: {integrity: sha512-2FI2rg//eXpa/l+WJtFfvHaf1NJ7ie2MoJ+RH5dKANtrfoof1Ed+y9dXSyuhem2tp/Srq2GhrjaSofFN5/g5vA==}

  '@turf/shortest-path@7.1.0':
    resolution: {integrity: sha512-1UmFhS5zHNacLv5rszoFOXq02BGov1oJvjlDatXsSWAd+Z7tqxpDc8D+41edrXy0ZB0Yxsy6WPNagM6hG9PRaA==}

  '@turf/simplify@7.1.0':
    resolution: {integrity: sha512-JypymaoiSiFzGHwEoUkK0OPW1KQSnH3hEsEW3UIRS+apzltJ4HdFovYjsfqQgGZJZ+NJ9+dv7h8pgGLYuqcBUQ==}

  '@turf/square-grid@7.1.0':
    resolution: {integrity: sha512-JyhsALULVRlkh8htdTi9aXaXFSUv6wRNbeFbqyGJKKlA5eF+AYmyWdI/BlFGQN27xtbtMPeAuLmj+8jaB2omGw==}

  '@turf/square@7.1.0':
    resolution: {integrity: sha512-ANuA+WXZheGTLW6Veq0i+/B2S4KMhEHAixDv9gQEb9e6FTyqTJVwrqP4CHI3OzA3DZ/ytFf+NTKVofetO/BBQg==}

  '@turf/standard-deviational-ellipse@7.1.0':
    resolution: {integrity: sha512-JqvQFH/witHh+3XgPC1Qk4+3G8w8WQta2NTJjnGinOgFulH+7RD4DcxCT+XXtCHoeq8IvL9VPJRX3ciaW5nSCg==}

  '@turf/tag@7.1.0':
    resolution: {integrity: sha512-cD8TC++DnNmdI1B/apTf3nj2zRNY6SoLRliB8K76OB+70Kev8tOf4ZVgAqOd0u+Hpdg/T6l7dO7fyJ6UouE7jA==}

  '@turf/tesselate@7.1.0':
    resolution: {integrity: sha512-E/Z94Mx6kUjvQVbEcSuM9MbEo2dkOczRe4ZzjhFlLgJh1dCkfRgwYLH49mb2CcfG/me1arxoCgmtG+qgm7LrCg==}

  '@turf/tin@7.1.0':
    resolution: {integrity: sha512-h8Bdm0IYN6OpKHM8lBRWGxkJnZcxL0KYecf8U6pa6DCEYsEXuEExMTvYSD2OmqIsL5ml8P6RjwgyI+dZeE0O9A==}

  '@turf/transform-rotate@7.1.0':
    resolution: {integrity: sha512-Vp7VBZ6DqaPV8mkwSycksBFRLqSj3y16zg+uEPSCsXUjbFtw9DOLcyH2F5vMpnC2bOpS9NOB4hebhJRwBwAPWQ==}

  '@turf/transform-scale@7.1.0':
    resolution: {integrity: sha512-m5fLnh3JqrWSv0sAC8Aieet/fr5IZND8BFaE9LakMidtNaJqOIPOyVmUoklcrGn6eK6MX+66rRPn+5a1pahlLQ==}

  '@turf/transform-translate@7.1.0':
    resolution: {integrity: sha512-XA6Oh7VqUDrieY9m9/OF4XpBTd8qlfVGi3ObywojCqtHaHKLK3aXwTBZ276i0QKmZqOQA+2jFa9NhgF/TgBDrw==}

  '@turf/triangle-grid@7.1.0':
    resolution: {integrity: sha512-hrPyRAuX5PKu7txmc/11VPKrlJDR+JGzd+eijupKTspNLR4n2sqZUx8UXqSxZ/1nq06ScTyjIfGQJVzlRS8BTg==}

  '@turf/truncate@7.1.0':
    resolution: {integrity: sha512-rrF3AML9PGZw2i5wmt53ESI+Ln9cZyCXgJ7QrEvkT8NbE4OFgmw6p8/1xT8+VEWFSpD4gHz+hmM+5FaFxXvtNg==}

  '@turf/turf@7.1.0':
    resolution: {integrity: sha512-7NA6tAjbu9oIvIfpRO5AdPrZbFTlUFU02HVA7sLJM9jFeNIZovW09QuDo23uoS2z5l94SXV1GgKKxN5wo7prCw==}

  '@turf/union@7.1.0':
    resolution: {integrity: sha512-7VI8jONdBg9qmbfNlLQycPr93l5aU9HGMgWI9M6pb4ERuU2+p8KgffCgs2NyMtP2HxPrKSybzj31g7bnbEKofQ==}

  '@turf/unkink-polygon@7.1.0':
    resolution: {integrity: sha512-pqkirni2aLpRA1ELFIuJz+mkjYyJQX8Ar6BflSu1b0ajY/CTrcDxbIv1x8UfvbybLzdJc4Gxzg5mo4cEtSwtaQ==}

  '@turf/voronoi@7.1.0':
    resolution: {integrity: sha512-xUvzPDG6GaqEekgxd+pjeMKJXOYJ3eFIqUHbTe/ISKzzv3f2cFGiR2VH7ZGXri8d4ozzCQbUQ27ilHPPLf5+xw==}

  '@tweenjs/tween.js@18.6.4':
    resolution: {integrity: sha512-lB9lMjuqjtuJrx7/kOkqQBtllspPIN+96OvTCeJ2j5FEzinoAXTdAMFnDAQT1KVPRlnYfBrqxtqP66vDM40xxQ==}

  '@types/cesium@1.70.4':
    resolution: {integrity: sha512-dSBhF5qftTCyB8xYRpumjzeEdEiV0wjDfmLtXF+1cNYUHvn2xs2HcvxCKFCqH6hEcATPWDP0WW6aaCCqrUo9dQ==}
    deprecated: This is a stub types definition. cesium provides its own type definitions, so you do not need this installed.

  '@types/d3-voronoi@1.1.12':
    resolution: {integrity: sha512-DauBl25PKZZ0WVJr42a6CNvI6efsdzofl9sajqZr2Gf5Gu733WkDdUGiPkUHXiUvYGzNNlFQde2wdZdfQPG+yw==}

  '@types/estree@1.0.5':
    resolution: {integrity: sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==}

  '@types/fs-extra@11.0.4':
    resolution: {integrity: sha512-yTbItCNreRooED33qjunPthRcSjERP1r4MqCZc7wv0u2sUkzTFp45tgUfS5+r7FrZPdmCCNflLhVSP/o+SemsQ==}

  '@types/geojson@7946.0.15':
    resolution: {integrity: sha512-9oSxFzDCT2Rj6DfcHF8G++jxBKS7mBqXl5xrRW+Kbvjry6Uduya2iiwqHPhVXpasAVMBYKkEPGgKhd3+/HZ6xA==}

  '@types/jsonfile@6.1.4':
    resolution: {integrity: sha512-D5qGUYwjvnNNextdU59/+fI+spnwtTFmyQP0h+PfIOSkNfpU6AOICUOkm4i0OnSk+NyjdPJrxCDro0sJsWlRpQ==}

  '@types/lodash-es@4.17.12':
    resolution: {integrity: sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==}

  '@types/lodash@4.17.7':
    resolution: {integrity: sha512-8wTvZawATi/lsmNu10/j2hk1KEP0IvjubqPE3cu1Xz7xfXXt5oCq3SNUz4fMIP4XGF9Ky+Ue2tBA3hcS7LSBlA==}

  '@types/node@20.15.0':
    resolution: {integrity: sha512-eQf4OkH6gA9v1W0iEpht/neozCsZKMTK+C4cU6/fv7wtJCCL8LEQ4hie2Ln8ZP/0YYM2xGj7//f8xyqItkJ6QA==}

  '@types/web-bluetooth@0.0.16':
    resolution: {integrity: sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ==}

  '@vitejs/plugin-vue@5.1.2':
    resolution: {integrity: sha512-nY9IwH12qeiJqumTCLJLE7IiNx7HZ39cbHaysEUd+Myvbz9KAqd2yq+U01Kab1R/H1BmiyM2ShTYlNH32Fzo3A==}
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      vite: ^5.0.0
      vue: ^3.2.25

  '@volar/language-core@2.4.0-alpha.18':
    resolution: {integrity: sha512-JAYeJvYQQROmVRtSBIczaPjP3DX4QW1fOqW1Ebs0d3Y3EwSNRglz03dSv0Dm61dzd0Yx3WgTW3hndDnTQqgmyg==}

  '@volar/source-map@2.4.0-alpha.18':
    resolution: {integrity: sha512-MTeCV9MUwwsH0sNFiZwKtFrrVZUK6p8ioZs3xFzHc2cvDXHWlYN3bChdQtwKX+FY2HG6H3CfAu1pKijolzIQ8g==}

  '@volar/typescript@2.4.0-alpha.18':
    resolution: {integrity: sha512-sXh5Y8sqGUkgxpMWUGvRXggxYHAVxg0Pa1C42lQZuPDrW6vHJPR0VCK8Sr7WJsAW530HuNQT/ZIskmXtxjybMQ==}

  '@vue/compiler-core@3.4.38':
    resolution: {integrity: sha512-8IQOTCWnLFqfHzOGm9+P8OPSEDukgg3Huc92qSG49if/xI2SAwLHQO2qaPQbjCWPBcQoO1WYfXfTACUrWV3c5A==}

  '@vue/compiler-dom@3.4.38':
    resolution: {integrity: sha512-Osc/c7ABsHXTsETLgykcOwIxFktHfGSUDkb05V61rocEfsFDcjDLH/IHJSNJP+/Sv9KeN2Lx1V6McZzlSb9EhQ==}

  '@vue/compiler-sfc@3.4.38':
    resolution: {integrity: sha512-s5QfZ+9PzPh3T5H4hsQDJtI8x7zdJaew/dCGgqZ2630XdzaZ3AD8xGZfBqpT8oaD/p2eedd+pL8tD5vvt5ZYJQ==}

  '@vue/compiler-ssr@3.4.38':
    resolution: {integrity: sha512-YXznKFQ8dxYpAz9zLuVvfcXhc31FSPFDcqr0kyujbOwNhlmaNvL2QfIy+RZeJgSn5Fk54CWoEUeW+NVBAogGaw==}

  '@vue/compiler-vue2@2.7.16':
    resolution: {integrity: sha512-qYC3Psj9S/mfu9uVi5WvNZIzq+xnXMhOwbTFKKDD7b1lhpnn71jXSFdTQ+WsIEk0ONCd7VV2IMm7ONl6tbQ86A==}

  '@vue/devtools-api@6.6.3':
    resolution: {integrity: sha512-0MiMsFma/HqA6g3KLKn+AGpL1kgKhFWszC9U29NfpWK5LE7bjeXxySWJrOJ77hBz+TBrBQ7o4QJqbPbqbs8rJw==}

  '@vue/language-core@2.0.29':
    resolution: {integrity: sha512-o2qz9JPjhdoVj8D2+9bDXbaI4q2uZTHQA/dbyZT4Bj1FR9viZxDJnLcKVHfxdn6wsOzRgpqIzJEEmSSvgMvDTQ==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@vue/reactivity@3.4.38':
    resolution: {integrity: sha512-4vl4wMMVniLsSYYeldAKzbk72+D3hUnkw9z8lDeJacTxAkXeDAP1uE9xr2+aKIN0ipOL8EG2GPouVTH6yF7Gnw==}

  '@vue/runtime-core@3.4.38':
    resolution: {integrity: sha512-21z3wA99EABtuf+O3IhdxP0iHgkBs1vuoCAsCKLVJPEjpVqvblwBnTj42vzHRlWDCyxu9ptDm7sI2ZMcWrQqlA==}

  '@vue/runtime-dom@3.4.38':
    resolution: {integrity: sha512-afZzmUreU7vKwKsV17H1NDThEEmdYI+GCAK/KY1U957Ig2NATPVjCROv61R19fjZNzMmiU03n79OMnXyJVN0UA==}

  '@vue/server-renderer@3.4.38':
    resolution: {integrity: sha512-NggOTr82FbPEkkUvBm4fTGcwUY8UuTsnWC/L2YZBmvaQ4C4Jl/Ao4HHTB+l7WnFCt5M/dN3l0XLuyjzswGYVCA==}
    peerDependencies:
      vue: 3.4.38

  '@vue/shared@3.4.38':
    resolution: {integrity: sha512-q0xCiLkuWWQLzVrecPb0RMsNWyxICOjPrcrwxTUEHb1fsnvni4dcuyG7RT/Ie7VPTvnjzIaWzRMUBsrqNj/hhw==}

  '@vue/tsconfig@0.5.1':
    resolution: {integrity: sha512-VcZK7MvpjuTPx2w6blwnwZAu5/LgBUtejFOi3pPGQFXQN5Ela03FUtd2Qtg4yWGGissVL0dr6Ro1LfOFh+PCuQ==}

  '@vueuse/core@9.13.0':
    resolution: {integrity: sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==}

  '@vueuse/metadata@9.13.0':
    resolution: {integrity: sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==}

  '@vueuse/shared@9.13.0':
    resolution: {integrity: sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==}

  '@zip.js/zip.js@2.4.26':
    resolution: {integrity: sha512-I9HBO3BHIxEMQmltmHM3iqUW6IHqi3gsL9wTSXvHTRpOrA6q2OxtR58EDSaOGjHhDVJ+wIOAxZyKq2x00AVmqw==}

  acorn-walk@8.3.4:
    resolution: {integrity: sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==}
    engines: {node: '>=0.4.0'}

  acorn@8.14.1:
    resolution: {integrity: sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  arg@4.1.3:
    resolution: {integrity: sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==}

  async-validator@4.2.5:
    resolution: {integrity: sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  autolinker@4.0.0:
    resolution: {integrity: sha512-fl5Kh6BmEEZx+IWBfEirnRUU5+cOiV0OK7PEt0RBKvJMJ8GaRseIOeDU3FKf4j3CE5HVefcjHmhYPOcaVt0bZw==}

  axios@1.7.4:
    resolution: {integrity: sha512-DukmaFRnY6AzAALSH4J2M3k6PkaC+MfaAGdEERRWcC9q3/TWQwLpHR8ZRLKTdQ3aBDL64EdluRDjJqKw+BPZEw==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  bitmap-sdf@1.0.4:
    resolution: {integrity: sha512-1G3U4n5JE6RAiALMxu0p1XmeZkTeCwGKykzsLTCqVzfSDaN6S7fKnkIkfejogz+iwqBWc0UYAIKnKHNN7pSfDg==}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  cesium@1.105.2:
    resolution: {integrity: sha512-iX4YpOuF4qgce2iHrimMs3Yi0TY+qWiIWoeM7GZyy8fo0zubzqPd/sadr37bEtKhzOFn+JaP5nodxNUJ5exRWQ==}
    engines: {node: '>=14.0.0'}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  computeds@0.0.1:
    resolution: {integrity: sha512-7CEBgcMjVmitjYo5q8JTJVra6X5mQ20uTThdK+0kR7UEaDrAWEQcRiBtWJzga4eRpP6afNwwLsX2SET2JhVB1Q==}

  concaveman@1.2.1:
    resolution: {integrity: sha512-PwZYKaM/ckQSa8peP5JpVr7IMJ4Nn/MHIaWUjP4be+KoZ7Botgs8seAZGpmaOM+UZXawcdYRao/px9ycrCihHw==}

  coordtransform@2.1.2:
    resolution: {integrity: sha512-0xLJApBlrUP+clyLJWIaqg4GXE5JTbAJb5d/CDMqebIksAMMze8eAyO6YfHEIxWJ+c42mXoMHBzWTeUrG7RFhw==}

  copy-anything@2.0.6:
    resolution: {integrity: sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==}

  create-require@1.1.1:
    resolution: {integrity: sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==}

  cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  d3-array@1.2.4:
    resolution: {integrity: sha512-KHW6M86R+FUPYGb3R5XiYjXPq7VzwxZ22buHhAEVG5ztoEcZZMLov530mmccaqA1GghZArjQV46fuc8kUqhhHw==}

  d3-geo@1.7.1:
    resolution: {integrity: sha512-O4AempWAr+P5qbk2bC2FuN/sDW4z+dN2wDf9QV3bxQt4M5HfOEeXLgJ/UKQW0+o1Dj8BE+L5kiDbdWUMjsmQpw==}

  d3-voronoi@1.1.2:
    resolution: {integrity: sha512-RhGS1u2vavcO7ay7ZNAPo4xeDh/VYeGof3x5ZLJBQgYhLegxr3s5IykvWmJ94FTU6mcbtp4sloqZ54mP6R4Utw==}

  dayjs@1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==}

  de-indent@1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  dexie@4.0.8:
    resolution: {integrity: sha512-1G6cJevS17KMDK847V3OHvK2zei899GwpDiqfEXHP1ASvme6eWJmAp9AU4s1son2TeGkWmC0g3y8ezOBPnalgQ==}

  diff@4.0.2:
    resolution: {integrity: sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==}
    engines: {node: '>=0.3.1'}

  dompurify@3.1.6:
    resolution: {integrity: sha512-cTOAhc36AalkjtBpfG6O8JimdTMWNXjiePT2xQH/ppBGi/4uIpmj8eKyIkMJErXWARyINV/sB38yf8JCLF5pbQ==}

  earcut@2.2.4:
    resolution: {integrity: sha512-/pjZsA1b4RPHbeWZQn66SWS8nZZWLQQ23oE3Eam7aroEFGEvwKAsJfZ9ytiEMycfzXWpca4FA9QIOehf7PocBQ==}

  element-plus@2.9.10:
    resolution: {integrity: sha512-W2v9jWnm1kl/zm4bSvCh8aFCVlxvhG3fmqiDZwyd6WQiWGE595J/mpjcCggEr+49QDgIymhXrpPMOPPSARUbng==}
    peerDependencies:
      vue: ^3.2.0

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  errno@0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true

  esbuild@0.21.5:
    resolution: {integrity: sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==}
    engines: {node: '>=12'}
    hasBin: true

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  follow-redirects@1.15.6:
    resolution: {integrity: sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}

  fs-extra@11.3.0:
    resolution: {integrity: sha512-Z4XaCL6dUDHfP/jT25jJKMmtxvuwbkrD1vNSMFlo9lNLY2c5FHYSQgHPRZUjAB26TpDEoW9HCOgplrdbaPV/ew==}
    engines: {node: '>=14.14'}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  geojson-equality-ts@1.0.2:
    resolution: {integrity: sha512-h3Ryq+0mCSN/7yLs0eDgrZhvc9af23o/QuC4aTiuuzP/MRCtd6mf5rLsLRY44jX0RPUfM8c4GqERQmlUxPGPoQ==}

  geojson-polygon-self-intersections@1.2.1:
    resolution: {integrity: sha512-/QM1b5u2d172qQVO//9CGRa49jEmclKEsYOQmWP9ooEjj63tBM51m2805xsbxkzlEELQ2REgTf700gUhhlegxA==}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  grapheme-splitter@1.0.4:
    resolution: {integrity: sha512-bzh50DW9kTPM00T8y4o8vQg89Di9oLJVLW/KaOGIXJWP/iqCN6WKYkbNOF04vFLJhwcpYUh9ydh/+5vpOqV4YQ==}

  he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  image-size@0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  is-what@3.14.1:
    resolution: {integrity: sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  jsencrypt@3.3.2:
    resolution: {integrity: sha512-arQR1R1ESGdAxY7ZheWr12wCaF2yF47v5qpB76TtV64H1pyGudk9Hvw8Y9tb/FiTIaaTRUyaSnm5T/Y53Ghm/A==}

  jsep@1.3.9:
    resolution: {integrity: sha512-i1rBX5N7VPl0eYb6+mHNp52sEuaS2Wi8CDYx1X5sn9naevL78+265XJqy1qENEk7mRKwS06NHpUqiBwR7qeodw==}
    engines: {node: '>= 10.16.0'}

  json-parse-even-better-errors@3.0.2:
    resolution: {integrity: sha512-fi0NG4bPjCHunUJffmLd0gxssIgkNmArMvis4iNah6Owg1MCJjWhEcDLmsK6iGkJq3tHwbDkTlce70/tmXN4cQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}

  jsts@2.7.1:
    resolution: {integrity: sha512-x2wSZHEBK20CY+Wy+BPE7MrFQHW6sIsdaGUMEqmGAio+3gFzQaBYPwLRonUfQf9Ak8pBieqj9tUofX1+WtAEIg==}
    engines: {node: '>= 12'}

  kdbush@4.0.2:
    resolution: {integrity: sha512-WbCVYJ27Sz8zi9Q7Q0xHC+05iwkm3Znipc2XTlrnJbsHMYktW4hPhXUE8Ys1engBrvffoSCqbil1JQAa7clRpA==}

  ktx-parse@0.5.0:
    resolution: {integrity: sha512-5IZrv5s1byUeDTIee1jjJQBiD5LPDB0w9pJJ0oT9BCKKJf16Tuj123vm1Ps0GOHSHmeWPgKM0zuViCVuTRpqaA==}

  leaflet@1.9.4:
    resolution: {integrity: sha512-nxS1ynzJOmOlHp+iL3FyWqK89GtNL8U8rvlMOsQdTTssxZwCXh8N2NB3GDQOL+YR3XnWyZAxwQixURb+FA74PA==}

  lerc@2.0.0:
    resolution: {integrity: sha512-7qo1Mq8ZNmaR4USHHm615nEW2lPeeWJ3bTyoqFbd35DLx0LUH7C6ptt5FDCTAlbIzs3+WKrk5SkJvw8AFDE2hg==}

  less@4.2.0:
    resolution: {integrity: sha512-P3b3HJDBtSzsXUl0im2L7gTO5Ubg8mEN6G8qoTS77iXxXX4Hvu4Qj540PZDvQ8V6DmX6iXo98k7Md0Cm1PrLaA==}
    engines: {node: '>=6'}
    hasBin: true

  lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}

  lodash-unified@1.0.3:
    resolution: {integrity: sha512-WK9qSozxXOD7ZJQlpSqOT+om2ZfcT4yO+03FuzAHD0wF6S0l0090LRPDx3vhTTLZ8cFKpBn+IOcVXK6qOcIlfQ==}
    peerDependencies:
      '@types/lodash-es': '*'
      lodash: '*'
      lodash-es: '*'

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  long@5.2.3:
    resolution: {integrity: sha512-lcHwpNoggQTObv5apGNCTdJrO69eHOZMi4BNC+rTLER8iHAqGrUVeLh/irVIM7zTw2bOXA8T6uNPeujwOLg/2Q==}

  magic-string@0.30.11:
    resolution: {integrity: sha512-+Wri9p0QHMy+545hKww7YAu5NyzF8iomPL/RQazugQ9+Ez4Ic3mERMd8ZTX5rfK944j+560ZJi8iAwgak1Ac7A==}

  make-dir@2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}

  make-error@1.3.6:
    resolution: {integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==}

  marchingsquares@1.3.3:
    resolution: {integrity: sha512-gz6nNQoVK7Lkh2pZulrT4qd4347S/toG9RXH2pyzhLgkL5mLkBoqgv4EvAGXcV0ikDW72n/OQb3Xe8bGagQZCg==}

  memoize-one@6.0.0:
    resolution: {integrity: sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==}

  memorystream@0.3.1:
    resolution: {integrity: sha512-S3UwM3yj5mtUSEfP41UZmt/0SCoVYUcU1rkXv+BQ5Ig8ndL4sPoJNBUJERafdPb5jjHJGuMgytgKvKIf58XNBw==}
    engines: {node: '>= 0.10.0'}

  mersenne-twister@1.1.0:
    resolution: {integrity: sha512-mUYWsMKNrm4lfygPkL3OfGzOPTR2DBlTkBNHM//F6hGp8cLThY897crAlk3/Jo17LEOOjQUrNAx6DvgO77QJkA==}

  meshoptimizer@0.18.1:
    resolution: {integrity: sha512-ZhoIoL7TNV4s5B6+rx5mC//fw8/POGyNxS/DZyCJeiZ12ScLfVwRE/GfsxwiTkMYYD5DmK2/JXnEVXqL4rF+Sw==}

  mgrs@1.0.0:
    resolution: {integrity: sha512-awNbTOqCxK1DBGjalK3xqWIstBZgN6fxsMSiXLs9/spqWkF2pAhb2rrYCFSsr1/tT7PhcDGjZndG8SWYn0byYA==}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  muggle-string@0.4.1:
    resolution: {integrity: sha512-VNTrAak/KhO2i8dqqnqnAHOa3cYBwXEZe9h+D5h/1ZqFSTEFHdM65lR7RoIqq3tBBYavsOXV84NoHXZ0AkPyqQ==}

  nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  needle@3.3.1:
    resolution: {integrity: sha512-6k0YULvhpw+RoLNiQCRKOl09Rv1dPLr8hHnVjHqdolKwDrdNyk+Hmrthi4lIGPPz3r39dLx0hsF5s40sZ3Us4Q==}
    engines: {node: '>= 4.4.x'}
    hasBin: true

  normalize-wheel-es@1.2.0:
    resolution: {integrity: sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw==}

  nosleep.js@0.12.0:
    resolution: {integrity: sha512-9d1HbpKLh3sdWlhXMhU6MMH+wQzKkrgfRkYV0EBdvt99YJfj0ilCJrWRDYG2130Tm4GXbEoTCx5b34JSaP+HhA==}

  npm-normalize-package-bin@3.0.1:
    resolution: {integrity: sha512-dMxCf+zZ+3zeQZXKxmyuCKlIDPGuv8EF940xbkC4kQVDTtqoh6rJFO+JTKSA6/Rwi0getWmtuy4Itup0AMcaDQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-run-all2@6.2.2:
    resolution: {integrity: sha512-Q+alQAGIW7ZhKcxLt8GcSi3h3ryheD6xnmXahkMRVM5LYmajcUrSITm8h+OPC9RYWMV2GR0Q1ntTUCfxaNoOJw==}
    engines: {node: ^14.18.0 || ^16.13.0 || >=18.0.0, npm: '>= 8'}
    hasBin: true

  pako@2.1.0:
    resolution: {integrity: sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug==}

  parse-node-version@1.0.1:
    resolution: {integrity: sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==}
    engines: {node: '>= 0.10'}

  path-browserify@1.0.1:
    resolution: {integrity: sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  picocolors@1.0.1:
    resolution: {integrity: sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==}

  pidtree@0.6.0:
    resolution: {integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==}
    engines: {node: '>=0.10'}
    hasBin: true

  pify@4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}

  pinia-plugin-persist@1.0.0:
    resolution: {integrity: sha512-M4hBBd8fz/GgNmUPaaUsC29y1M09lqbXrMAHcusVoU8xlQi1TqgkWnnhvMikZwr7Le/hVyMx8KUcumGGrR6GVw==}
    peerDependencies:
      '@vue/composition-api': ^1.0.0
      pinia: ^2.0.0
      vue: ^2.0.0 || >=3.0.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  pinia@2.2.2:
    resolution: {integrity: sha512-ja2XqFWZC36mupU4z1ZzxeTApV7DOw44cV4dhQ9sGwun+N89v/XP7+j7q6TanS1u1tdbK4r+1BUx7heMaIdagA==}
    peerDependencies:
      '@vue/composition-api': ^1.4.0
      typescript: '>=4.4.4'
      vue: ^2.6.14 || ^3.3.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
      typescript:
        optional: true

  point-in-polygon-hao@1.2.3:
    resolution: {integrity: sha512-uZsWylGd8nthIYS8F7aSyM7Pot+4L/bgXheJcCNdRr4eLpsM/rMb3hIi5SqNxAVjUoDDao3QzCtdaVDzmeF9Cw==}

  point-in-polygon@1.1.0:
    resolution: {integrity: sha512-3ojrFwjnnw8Q9242TzgXuTD+eKiutbzyslcq1ydfu82Db2y+Ogbmyrkpv0Hgj31qwT3lbS9+QAAO/pIQM35XRw==}

  polygon-clipping@0.15.7:
    resolution: {integrity: sha512-nhfdr83ECBg6xtqOAJab1tbksbBAOMUltN60bU+llHVOL0e5Onm1WpAXXWXVB39L8AJFssoIhEVuy/S90MmotA==}

  postcss@8.4.41:
    resolution: {integrity: sha512-TesUflQ0WKZqAvg52PWL6kHgLKP6xB6heTOdoYM0Wt2UHyxNa4K25EZZMgKns3BH1RLVbZCREPpLY0rhnNoHVQ==}
    engines: {node: ^10 || ^12 || >=14}

  proj4@2.12.0:
    resolution: {integrity: sha512-cQJxcVX7+fmAhOxoazKgk76GkGYQ5HcLod4rdy2MizhPvLdrZQJThxsHoz/TjjdxUvTm/rbozMgE0q9mdXKWIw==}

  protobufjs@7.3.2:
    resolution: {integrity: sha512-RXyHaACeqXeqAKGLDl68rQKbmObRsTIn4TYVUUug1KfS47YWCo5MacGITEryugIgZqORCvJWEk4l449POg5Txg==}
    engines: {node: '>=12.0.0'}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  prr@1.0.1:
    resolution: {integrity: sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==}

  quickselect@1.1.1:
    resolution: {integrity: sha512-qN0Gqdw4c4KGPsBOQafj6yj/PA6c/L63f6CaZ/DCF/xF4Esu3jVmKLUDYxghFx8Kb/O7y9tI7x2RjTSXwdK1iQ==}

  quickselect@2.0.0:
    resolution: {integrity: sha512-RKJ22hX8mHe3Y6wH/N3wCM6BWtjaxIyyUIkpHOvfFnxdI4yD4tBXEBKSbriGujF6jnSVkJrffuo6vxACiSSxIw==}

  rbush@2.0.2:
    resolution: {integrity: sha512-XBOuALcTm+O/H8G90b6pzu6nX6v2zCKiFG4BJho8a+bY6AER6t8uQUZdi5bomQc0AprCWhEGa7ncAbbRap0bRA==}

  rbush@3.0.1:
    resolution: {integrity: sha512-XRaVO0YecOpEuIvbhbpTrZgoiI6xBlz6hnlr6EHhd+0x9ase6EmeN+hdwwUaJvLcsFFQ8iWVF1GAK1yB0BWi0w==}

  read-package-json-fast@3.0.2:
    resolution: {integrity: sha512-0J+Msgym3vrLOUB3hzQCuZHII0xkNGCtz/HJH9xZshwv9DbDwkw1KaE3gx/e2J5rpEY5rtOy6cyhKOPrkP7FZw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  robust-predicates@2.0.4:
    resolution: {integrity: sha512-l4NwboJM74Ilm4VKfbAtFeGq7aEjWL+5kVFcmgFA2MrdnQWx9iE/tUGvxY5HyMI7o/WpSIUFLbC5fbeaHgSCYg==}

  robust-predicates@3.0.2:
    resolution: {integrity: sha512-IXgzBWvWQwE6PrDI05OvmXUIruQTcoMDzRsOd5CDvHCVLcLHMTSYvOK5Cm46kWqlV3yAbuSpBZdJ5oP5OUoStg==}

  rollup@4.20.0:
    resolution: {integrity: sha512-6rbWBChcnSGzIlXeIdNIZTopKYad8ZG8ajhl78lGRLsI2rX8IkaotQhVas2Ma+GPxJav19wrSzvRvuiv0YKzWw==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  sax@1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==}

  semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true

  semver@7.6.3:
    resolution: {integrity: sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==}
    engines: {node: '>=10'}
    hasBin: true

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  shell-quote@1.8.1:
    resolution: {integrity: sha512-6j1W9l1iAs/4xYBI1SYOVZyFcCis9b4KCLQ8fgAGG07QvzaRLVVRQvAy85yNmmZSjYjg4MWh4gNvlPujU/5LpA==}

  skmeans@0.9.7:
    resolution: {integrity: sha512-hNj1/oZ7ygsfmPZ7ZfN5MUBRoGg1gtpnImuJBgLO0ljQ67DtJuiQaiYdS4lUA6s0KCwnPhGivtC/WRwIZLkHyg==}

  source-map-js@1.2.0:
    resolution: {integrity: sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==}
    engines: {node: '>=0.10.0'}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  splaytree@3.1.2:
    resolution: {integrity: sha512-4OM2BJgC5UzrhVnnJA4BkHKGtjXNzzUfpQjCO8I05xYPsfS/VuQDwjCGGMi8rYQilHEV4j8NBqTFbls/PZEE7A==}

  sweepline-intersections@1.5.0:
    resolution: {integrity: sha512-AoVmx72QHpKtItPu72TzFL+kcYjd67BPLDoR0LarIk+xyaRg+pDTMFXndIEvZf9xEKnJv6JdhgRMnocoG0D3AQ==}

  three@0.155.0:
    resolution: {integrity: sha512-sNgCYmDijnIqkD/bMfk+1pHg3YzsxW7V2ChpuP6HCQ8NiZr3RufsXQr8M3SSUMjW4hG+sUk7YbyuY0DncaDTJQ==}

  tinyqueue@2.0.3:
    resolution: {integrity: sha512-ppJZNDuKGgxzkHihX8v9v9G5f+18gzaTfrukGrq6ueg0lmH4nqVnA2IPG0AEH3jKEk2GRJCUhDoqpoiw3PHLBA==}

  to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}

  topojson-client@3.1.0:
    resolution: {integrity: sha512-605uxS6bcYxGXw9qi62XyrV6Q3xwbndjachmNxu8HWTtVPxZfEJN9fd/SZS1Q54Sn2y0TMyMxFj/cJINqGHrKw==}
    hasBin: true

  topojson-server@3.0.1:
    resolution: {integrity: sha512-/VS9j/ffKr2XAOjlZ9CgyyeLmgJ9dMwq6Y0YEON8O7p/tGGk+dCWnrE03zEdu7i4L7YsFZLEPZPzCvcB7lEEXw==}
    hasBin: true

  ts-node@10.9.2:
    resolution: {integrity: sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==}
    hasBin: true
    peerDependencies:
      '@swc/core': '>=1.2.50'
      '@swc/wasm': '>=1.2.50'
      '@types/node': '*'
      typescript: '>=2.7'
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      '@swc/wasm':
        optional: true

  tslib@2.6.3:
    resolution: {integrity: sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ==}

  tsparticles-basic@2.12.0:
    resolution: {integrity: sha512-pN6FBpL0UsIUXjYbiui5+IVsbIItbQGOlwyGV55g6IYJBgdTNXgFX0HRYZGE9ZZ9psEXqzqwLM37zvWnb5AG9g==}

  tsparticles-engine@2.12.0:
    resolution: {integrity: sha512-ZjDIYex6jBJ4iMc9+z0uPe7SgBnmb6l+EJm83MPIsOny9lPpetMsnw/8YJ3xdxn8hV+S3myTpTN1CkOVmFv0QQ==}
    deprecated: starting from tsparticles v3 the packages are now moved to @tsparticles/package-name instead of tsparticles-package-name

  tsparticles-interaction-external-attract@2.12.0:
    resolution: {integrity: sha512-0roC6D1QkFqMVomcMlTaBrNVjVOpyNzxIUsjMfshk2wUZDAvTNTuWQdUpmsLS4EeSTDN3rzlGNnIuuUQqyBU5w==}

  tsparticles-interaction-external-bounce@2.12.0:
    resolution: {integrity: sha512-MMcqKLnQMJ30hubORtdq+4QMldQ3+gJu0bBYsQr9BsThsh8/V0xHc1iokZobqHYVP5tV77mbFBD8Z7iSCf0TMQ==}

  tsparticles-interaction-external-bubble@2.12.0:
    resolution: {integrity: sha512-5kImCSCZlLNccXOHPIi2Yn+rQWTX3sEa/xCHwXW19uHxtILVJlnAweayc8+Zgmb7mo0DscBtWVFXHPxrVPFDUA==}

  tsparticles-interaction-external-connect@2.12.0:
    resolution: {integrity: sha512-ymzmFPXz6AaA1LAOL5Ihuy7YSQEW8MzuSJzbd0ES13U8XjiU3HlFqlH6WGT1KvXNw6WYoqrZt0T3fKxBW3/C3A==}

  tsparticles-interaction-external-grab@2.12.0:
    resolution: {integrity: sha512-iQF/A947hSfDNqAjr49PRjyQaeRkYgTYpfNmAf+EfME8RsbapeP/BSyF6mTy0UAFC0hK2A2Hwgw72eT78yhXeQ==}

  tsparticles-interaction-external-pause@2.12.0:
    resolution: {integrity: sha512-4SUikNpsFROHnRqniL+uX2E388YTtfRWqqqZxRhY0BrijH4z04Aii3YqaGhJxfrwDKkTQlIoM2GbFT552QZWjw==}

  tsparticles-interaction-external-push@2.12.0:
    resolution: {integrity: sha512-kqs3V0dgDKgMoeqbdg+cKH2F+DTrvfCMrPF1MCCUpBCqBiH+TRQpJNNC86EZYHfNUeeLuIM3ttWwIkk2hllR/Q==}

  tsparticles-interaction-external-remove@2.12.0:
    resolution: {integrity: sha512-2eNIrv4m1WB2VfSVj46V2L/J9hNEZnMgFc+A+qmy66C8KzDN1G8aJUAf1inW8JVc0lmo5+WKhzex4X0ZSMghBg==}

  tsparticles-interaction-external-repulse@2.12.0:
    resolution: {integrity: sha512-rSzdnmgljeBCj5FPp4AtGxOG9TmTsK3AjQW0vlyd1aG2O5kSqFjR+FuT7rfdSk9LEJGH5SjPFE6cwbuy51uEWA==}

  tsparticles-interaction-external-slow@2.12.0:
    resolution: {integrity: sha512-2IKdMC3om7DttqyroMtO//xNdF0NvJL/Lx7LDo08VpfTgJJozxU+JAUT8XVT7urxhaDzbxSSIROc79epESROtA==}

  tsparticles-interaction-particles-attract@2.12.0:
    resolution: {integrity: sha512-Hl8qwuwF9aLq3FOkAW+Zomu7Gb8IKs6Y3tFQUQScDmrrSCaeRt2EGklAiwgxwgntmqzL7hbMWNx06CHHcUQKdQ==}

  tsparticles-interaction-particles-collisions@2.12.0:
    resolution: {integrity: sha512-Se9nPWlyPxdsnHgR6ap4YUImAu3W5MeGKJaQMiQpm1vW8lSMOUejI1n1ioIaQth9weKGKnD9rvcNn76sFlzGBA==}

  tsparticles-interaction-particles-links@2.12.0:
    resolution: {integrity: sha512-e7I8gRs4rmKfcsHONXMkJnymRWpxHmeaJIo4g2NaDRjIgeb2AcJSWKWZvrsoLnm7zvaf/cMQlbN6vQwCixYq3A==}

  tsparticles-move-base@2.12.0:
    resolution: {integrity: sha512-oSogCDougIImq+iRtIFJD0YFArlorSi8IW3HD2gO3USkH+aNn3ZqZNTqp321uB08K34HpS263DTbhLHa/D6BWw==}

  tsparticles-move-parallax@2.12.0:
    resolution: {integrity: sha512-58CYXaX8Ih5rNtYhpnH0YwU4Ks7gVZMREGUJtmjhuYN+OFr9FVdF3oDIJ9N6gY5a5AnAKz8f5j5qpucoPRcYrQ==}

  tsparticles-particles.js@2.12.0:
    resolution: {integrity: sha512-LyOuvYdhbUScmA4iDgV3LxA0HzY1DnOwQUy3NrPYO393S2YwdDjdwMod6Btq7EBUjg9FVIh+sZRizgV5elV2dg==}

  tsparticles-plugin-easing-quad@2.12.0:
    resolution: {integrity: sha512-2mNqez5pydDewMIUWaUhY5cNQ80IUOYiujwG6qx9spTq1D6EEPLbRNAEL8/ecPdn2j1Um3iWSx6lo340rPkv4Q==}

  tsparticles-shape-circle@2.12.0:
    resolution: {integrity: sha512-L6OngbAlbadG7b783x16ns3+SZ7i0SSB66M8xGa5/k+YcY7zm8zG0uPt1Hd+xQDR2aNA3RngVM10O23/Lwk65Q==}

  tsparticles-shape-image@2.12.0:
    resolution: {integrity: sha512-iCkSdUVa40DxhkkYjYuYHr9MJGVw+QnQuN5UC+e/yBgJQY+1tQL8UH0+YU/h0GHTzh5Sm+y+g51gOFxHt1dj7Q==}

  tsparticles-shape-line@2.12.0:
    resolution: {integrity: sha512-RcpKmmpKlk+R8mM5wA2v64Lv1jvXtU4SrBDv3vbdRodKbKaWGGzymzav1Q0hYyDyUZgplEK/a5ZwrfrOwmgYGA==}

  tsparticles-shape-polygon@2.12.0:
    resolution: {integrity: sha512-5YEy7HVMt1Obxd/jnlsjajchAlYMr9eRZWN+lSjcFSH6Ibra7h59YuJVnwxOxAobpijGxsNiBX0PuGQnB47pmA==}

  tsparticles-shape-square@2.12.0:
    resolution: {integrity: sha512-33vfajHqmlODKaUzyPI/aVhnAOT09V7nfEPNl8DD0cfiNikEuPkbFqgJezJuE55ebtVo7BZPDA9o7GYbWxQNuw==}

  tsparticles-shape-star@2.12.0:
    resolution: {integrity: sha512-4sfG/BBqm2qBnPLASl2L5aBfCx86cmZLXeh49Un+TIR1F5Qh4XUFsahgVOG0vkZQa+rOsZPEH04xY5feWmj90g==}

  tsparticles-shape-text@2.12.0:
    resolution: {integrity: sha512-v2/FCA+hyTbDqp2ymFOe97h/NFb2eezECMrdirHWew3E3qlvj9S/xBibjbpZva2gnXcasBwxn0+LxKbgGdP0rA==}

  tsparticles-slim@2.12.0:
    resolution: {integrity: sha512-27w9aGAAAPKHvP4LHzWFpyqu7wKyulayyaZ/L6Tuuejy4KP4BBEB4rY5GG91yvAPsLtr6rwWAn3yS+uxnBDpkA==}
    deprecated: starting from tsparticles v3 the packages are now moved to @tsparticles/package-name instead of tsparticles-package-name

  tsparticles-updater-color@2.12.0:
    resolution: {integrity: sha512-KcG3a8zd0f8CTiOrylXGChBrjhKcchvDJjx9sp5qpwQK61JlNojNCU35xoaSk2eEHeOvFjh0o3CXWUmYPUcBTQ==}

  tsparticles-updater-life@2.12.0:
    resolution: {integrity: sha512-J7RWGHAZkowBHpcLpmjKsxwnZZJ94oGEL2w+wvW1/+ZLmAiFFF6UgU0rHMC5CbHJT4IPx9cbkYMEHsBkcRJ0Bw==}

  tsparticles-updater-opacity@2.12.0:
    resolution: {integrity: sha512-YUjMsgHdaYi4HN89LLogboYcCi1o9VGo21upoqxq19yRy0hRCtx2NhH22iHF/i5WrX6jqshN0iuiiNefC53CsA==}

  tsparticles-updater-out-modes@2.12.0:
    resolution: {integrity: sha512-owBp4Gk0JNlSrmp12XVEeBroDhLZU+Uq3szbWlHGSfcR88W4c/0bt0FiH5bHUqORIkw+m8O56hCjbqwj69kpOQ==}

  tsparticles-updater-rotate@2.12.0:
    resolution: {integrity: sha512-waOFlGFmEZOzsQg4C4VSejNVXGf4dMf3fsnQrEROASGf1FCd8B6WcZau7JtXSTFw0OUGuk8UGz36ETWN72DkCw==}

  tsparticles-updater-size@2.12.0:
    resolution: {integrity: sha512-B0yRdEDd/qZXCGDL/ussHfx5YJ9UhTqNvmS5X2rR2hiZhBAE2fmsXLeWkdtF2QusjPeEqFDxrkGiLOsh6poqRA==}

  tsparticles-updater-stroke-color@2.12.0:
    resolution: {integrity: sha512-MPou1ZDxsuVq6SN1fbX+aI5yrs6FyP2iPCqqttpNbWyL+R6fik1rL0ab/x02B57liDXqGKYomIbBQVP3zUTW1A==}

  typescript@5.4.5:
    resolution: {integrity: sha512-vcI4UpRgg81oIRUFwR0WSIHKt11nJ7SAVlYNIu+QpqeyXP+gpQJy/Z4+F0aGxSE4MqwjyXvW/TzgkLAx2AGHwQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  undici-types@6.13.0:
    resolution: {integrity: sha512-xtFJHudx8S2DSoujjMd1WeWvn7KKWFRESZTMeL1RptAYERu29D6jphMjjY+vn96jvN3kVPDNxU/E13VTaXj6jg==}

  universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}

  urijs@1.19.11:
    resolution: {integrity: sha512-HXgFDgDommxn5/bIv0cnQZsPhHDA90NPHD6+c/v21U5+Sx5hoP8+dP9IZXBU1gIfvdRfhG8cel9QNPeionfcCQ==}

  v8-compile-cache-lib@3.0.1:
    resolution: {integrity: sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==}

  vite@5.4.1:
    resolution: {integrity: sha512-1oE6yuNXssjrZdblI9AfBbHCC41nnyoVoEZxQnID6yvQZAFBzxxkqoFLtHUMkYunL8hwOLEjgTuxpkRxvba3kA==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || >=20.0.0
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true

  vscode-uri@3.0.8:
    resolution: {integrity: sha512-AyFQ0EVmsOZOlAnxoFOGOq1SQDWAB7C6aqMGS23svWAllfOaxbuFvcT8D1i8z3Gyn8fraVeZNNmN6e9bxxXkKw==}

  vue-demi@0.12.5:
    resolution: {integrity: sha512-BREuTgTYlUr0zw0EZn3hnhC3I6gPWv+Kwh4MCih6QcAeaTlaIX0DwOVN0wHej7hSvDPecz4jygy/idsgKfW58Q==}
    engines: {node: '>=12'}
    hasBin: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  vue-demi@0.14.10:
    resolution: {integrity: sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==}
    engines: {node: '>=12'}
    hasBin: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  vue-router@4.4.3:
    resolution: {integrity: sha512-sv6wmNKx2j3aqJQDMxLFzs/u/mjA9Z5LCgy6BE0f7yFWMjrPLnS/sPNn8ARY/FXw6byV18EFutn5lTO6+UsV5A==}
    peerDependencies:
      vue: ^3.2.0

  vue-tsc@2.0.29:
    resolution: {integrity: sha512-MHhsfyxO3mYShZCGYNziSbc63x7cQ5g9kvijV7dRe1TTXBRLxXyL0FnXWpUF1xII2mJ86mwYpYsUmMwkmerq7Q==}
    hasBin: true
    peerDependencies:
      typescript: '>=5.0.0'

  vue@3.4.38:
    resolution: {integrity: sha512-f0ZgN+mZ5KFgVv9wz0f4OgVKukoXtS3nwET4c2vLBGQR50aI8G0cqbFtLlX9Yiyg3LFGBitruPHt2PxwTduJEw==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  wkt-parser@1.3.3:
    resolution: {integrity: sha512-ZnV3yH8/k58ZPACOXeiHaMuXIiaTk1t0hSUVisbO0t4RjA5wPpUytcxeyiN2h+LZRrmuHIh/1UlrR9e7DHDvTw==}

  yn@3.1.1:
    resolution: {integrity: sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==}
    engines: {node: '>=6'}

snapshots:

  '@babel/helper-string-parser@7.24.8': {}

  '@babel/helper-validator-identifier@7.24.7': {}

  '@babel/parser@7.25.3':
    dependencies:
      '@babel/types': 7.25.2

  '@babel/types@7.25.2':
    dependencies:
      '@babel/helper-string-parser': 7.24.8
      '@babel/helper-validator-identifier': 7.24.7
      to-fast-properties: 2.0.0

  '@cesium/engine@2.4.0':
    dependencies:
      '@tweenjs/tween.js': 18.6.4
      '@zip.js/zip.js': 2.4.26
      autolinker: 4.0.0
      bitmap-sdf: 1.0.4
      dompurify: 3.1.6
      earcut: 2.2.4
      grapheme-splitter: 1.0.4
      jsep: 1.3.9
      kdbush: 4.0.2
      ktx-parse: 0.5.0
      lerc: 2.0.0
      mersenne-twister: 1.1.0
      meshoptimizer: 0.18.1
      pako: 2.1.0
      protobufjs: 7.3.2
      rbush: 3.0.1
      topojson-client: 3.1.0
      urijs: 1.19.11

  '@cesium/widgets@2.3.0':
    dependencies:
      '@cesium/engine': 2.4.0
      nosleep.js: 0.12.0

  '@cspotcode/source-map-support@0.8.1':
    dependencies:
      '@jridgewell/trace-mapping': 0.3.9

  '@ctrl/tinycolor@3.6.1': {}

  '@dvgis/cesium-map@3.1.0':
    dependencies:
      '@cesium/engine': 2.4.0

  '@element-plus/icons-vue@2.3.1(vue@3.4.38(typescript@5.4.5))':
    dependencies:
      vue: 3.4.38(typescript@5.4.5)

  '@esbuild/aix-ppc64@0.21.5':
    optional: true

  '@esbuild/android-arm64@0.21.5':
    optional: true

  '@esbuild/android-arm@0.21.5':
    optional: true

  '@esbuild/android-x64@0.21.5':
    optional: true

  '@esbuild/darwin-arm64@0.21.5':
    optional: true

  '@esbuild/darwin-x64@0.21.5':
    optional: true

  '@esbuild/freebsd-arm64@0.21.5':
    optional: true

  '@esbuild/freebsd-x64@0.21.5':
    optional: true

  '@esbuild/linux-arm64@0.21.5':
    optional: true

  '@esbuild/linux-arm@0.21.5':
    optional: true

  '@esbuild/linux-ia32@0.21.5':
    optional: true

  '@esbuild/linux-loong64@0.21.5':
    optional: true

  '@esbuild/linux-mips64el@0.21.5':
    optional: true

  '@esbuild/linux-ppc64@0.21.5':
    optional: true

  '@esbuild/linux-riscv64@0.21.5':
    optional: true

  '@esbuild/linux-s390x@0.21.5':
    optional: true

  '@esbuild/linux-x64@0.21.5':
    optional: true

  '@esbuild/netbsd-x64@0.21.5':
    optional: true

  '@esbuild/openbsd-x64@0.21.5':
    optional: true

  '@esbuild/sunos-x64@0.21.5':
    optional: true

  '@esbuild/win32-arm64@0.21.5':
    optional: true

  '@esbuild/win32-ia32@0.21.5':
    optional: true

  '@esbuild/win32-x64@0.21.5':
    optional: true

  '@floating-ui/core@1.6.7':
    dependencies:
      '@floating-ui/utils': 0.2.7

  '@floating-ui/dom@1.6.10':
    dependencies:
      '@floating-ui/core': 1.6.7
      '@floating-ui/utils': 0.2.7

  '@floating-ui/utils@0.2.7': {}

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.9':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@protobufjs/aspromise@1.1.2': {}

  '@protobufjs/base64@1.1.2': {}

  '@protobufjs/codegen@2.0.4': {}

  '@protobufjs/eventemitter@1.1.0': {}

  '@protobufjs/fetch@1.1.0':
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/inquire': 1.1.0

  '@protobufjs/float@1.0.2': {}

  '@protobufjs/inquire@1.1.0': {}

  '@protobufjs/path@1.1.2': {}

  '@protobufjs/pool@1.1.0': {}

  '@protobufjs/utf8@1.1.0': {}

  '@rollup/rollup-android-arm-eabi@4.20.0':
    optional: true

  '@rollup/rollup-android-arm64@4.20.0':
    optional: true

  '@rollup/rollup-darwin-arm64@4.20.0':
    optional: true

  '@rollup/rollup-darwin-x64@4.20.0':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.20.0':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.20.0':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.20.0':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.20.0':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.20.0':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.20.0':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.20.0':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.20.0':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.20.0':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.20.0':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.20.0':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.20.0':
    optional: true

  '@sxzz/popperjs-es@2.11.7': {}

  '@tsconfig/node10@1.0.11': {}

  '@tsconfig/node12@1.0.11': {}

  '@tsconfig/node14@1.0.3': {}

  '@tsconfig/node16@1.0.4': {}

  '@tsconfig/node20@20.1.4': {}

  '@turf/along@7.1.0':
    dependencies:
      '@turf/bearing': 7.1.0
      '@turf/destination': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/angle@7.1.0':
    dependencies:
      '@turf/bearing': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/rhumb-bearing': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/area@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/bbox-clip@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/bbox-polygon@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/bbox@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/bearing@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/bezier-spline@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/boolean-clockwise@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/boolean-concave@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/boolean-contains@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/boolean-point-on-line': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/boolean-crosses@7.1.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@turf/polygon-to-line': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/boolean-disjoint@7.1.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/polygon-to-line': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/boolean-equal@7.1.0':
    dependencies:
      '@turf/clean-coords': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      geojson-equality-ts: 1.0.2
      tslib: 2.6.3

  '@turf/boolean-intersects@7.1.0':
    dependencies:
      '@turf/boolean-disjoint': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/boolean-overlap@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@turf/line-overlap': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      geojson-equality-ts: 1.0.2
      tslib: 2.6.3

  '@turf/boolean-parallel@7.1.0':
    dependencies:
      '@turf/clean-coords': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/line-segment': 7.1.0
      '@turf/rhumb-bearing': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/boolean-point-in-polygon@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      point-in-polygon-hao: 1.2.3
      tslib: 2.6.3

  '@turf/boolean-point-on-line@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/boolean-touches@7.1.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/boolean-point-on-line': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/boolean-valid@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/boolean-crosses': 7.1.0
      '@turf/boolean-disjoint': 7.1.0
      '@turf/boolean-overlap': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/boolean-point-on-line': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@types/geojson': 7946.0.15
      geojson-polygon-self-intersections: 1.2.1
      tslib: 2.6.3

  '@turf/boolean-within@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/boolean-point-on-line': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/buffer@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/center': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/jsts': 2.7.1
      '@turf/meta': 7.1.0
      '@turf/projection': 7.1.0
      '@types/geojson': 7946.0.15
      d3-geo: 1.7.1

  '@turf/center-mean@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/center-median@7.1.0':
    dependencies:
      '@turf/center-mean': 7.1.0
      '@turf/centroid': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/center-of-mass@7.1.0':
    dependencies:
      '@turf/centroid': 7.1.0
      '@turf/convex': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/center@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/centroid@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/circle@7.1.0':
    dependencies:
      '@turf/destination': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/clean-coords@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/clone@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/clusters-dbscan@7.1.0':
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      rbush: 3.0.1
      tslib: 2.6.3

  '@turf/clusters-kmeans@7.1.0':
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      skmeans: 0.9.7
      tslib: 2.6.3

  '@turf/clusters@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/collect@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      rbush: 3.0.1
      tslib: 2.6.3

  '@turf/combine@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/concave@7.1.0':
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/tin': 7.1.0
      '@types/geojson': 7946.0.15
      topojson-client: 3.1.0
      topojson-server: 3.0.1
      tslib: 2.6.3

  '@turf/convex@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      concaveman: 1.2.1
      tslib: 2.6.3

  '@turf/destination@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/difference@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      polygon-clipping: 0.15.7
      tslib: 2.6.3

  '@turf/dissolve@7.1.0':
    dependencies:
      '@turf/flatten': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      polygon-clipping: 0.15.7
      tslib: 2.6.3

  '@turf/distance-weight@7.1.0':
    dependencies:
      '@turf/centroid': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/distance@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/ellipse@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/rhumb-destination': 7.1.0
      '@turf/transform-rotate': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/envelope@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/bbox-polygon': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/explode@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/flatten@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/flip@7.1.0':
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/geojson-rbush@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      rbush: 3.0.1

  '@turf/great-circle@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15

  '@turf/helpers@7.1.0':
    dependencies:
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/hex-grid@7.1.0':
    dependencies:
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/intersect': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/interpolate@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/centroid': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/hex-grid': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/point-grid': 7.1.0
      '@turf/square-grid': 7.1.0
      '@turf/triangle-grid': 7.1.0
      '@types/geojson': 7946.0.15

  '@turf/intersect@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      polygon-clipping: 0.15.7
      tslib: 2.6.3

  '@turf/invariant@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/isobands@7.1.0':
    dependencies:
      '@turf/area': 7.1.0
      '@turf/bbox': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/explode': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      marchingsquares: 1.3.3
      tslib: 2.6.3

  '@turf/isolines@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      marchingsquares: 1.3.3
      tslib: 2.6.3

  '@turf/jsts@2.7.1':
    dependencies:
      jsts: 2.7.1

  '@turf/kinks@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/length@7.1.0':
    dependencies:
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/line-arc@7.1.0':
    dependencies:
      '@turf/circle': 7.1.0
      '@turf/destination': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/line-chunk@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/length': 7.1.0
      '@turf/line-slice-along': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15

  '@turf/line-intersect@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      sweepline-intersections: 1.5.0
      tslib: 2.6.3

  '@turf/line-offset@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15

  '@turf/line-overlap@7.1.0':
    dependencies:
      '@turf/boolean-point-on-line': 7.1.0
      '@turf/geojson-rbush': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-segment': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/nearest-point-on-line': 7.1.0
      '@types/geojson': 7946.0.15
      fast-deep-equal: 3.1.3
      tslib: 2.6.3

  '@turf/line-segment@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/line-slice-along@7.1.0':
    dependencies:
      '@turf/bearing': 7.1.0
      '@turf/destination': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15

  '@turf/line-slice@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/nearest-point-on-line': 7.1.0
      '@types/geojson': 7946.0.15

  '@turf/line-split@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/geojson-rbush': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@turf/line-segment': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/nearest-point-on-line': 7.1.0
      '@turf/square': 7.1.0
      '@turf/truncate': 7.1.0
      '@types/geojson': 7946.0.15

  '@turf/line-to-polygon@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/mask@7.1.0':
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      polygon-clipping: 0.15.7
      tslib: 2.6.3

  '@turf/meta@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15

  '@turf/midpoint@7.1.0':
    dependencies:
      '@turf/bearing': 7.1.0
      '@turf/destination': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/moran-index@7.1.0':
    dependencies:
      '@turf/distance-weight': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/nearest-neighbor-analysis@7.1.0':
    dependencies:
      '@turf/area': 7.1.0
      '@turf/bbox': 7.1.0
      '@turf/bbox-polygon': 7.1.0
      '@turf/centroid': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/nearest-point': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/nearest-point-on-line@7.1.0':
    dependencies:
      '@turf/bearing': 7.1.0
      '@turf/destination': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/nearest-point-to-line@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/point-to-line-distance': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/nearest-point@7.1.0':
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/planepoint@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/point-grid@7.1.0':
    dependencies:
      '@turf/boolean-within': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/point-on-feature@7.1.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/center': 7.1.0
      '@turf/explode': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/nearest-point': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/point-to-line-distance@7.1.0':
    dependencies:
      '@turf/bearing': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/projection': 7.1.0
      '@turf/rhumb-bearing': 7.1.0
      '@turf/rhumb-distance': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/points-within-polygon@7.1.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/polygon-smooth@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/polygon-tangents@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/boolean-within': 7.1.0
      '@turf/explode': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/nearest-point': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/polygon-to-line@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/polygonize@7.1.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/envelope': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/projection@7.1.0':
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/quadrat-analysis@7.1.0':
    dependencies:
      '@turf/area': 7.1.0
      '@turf/bbox': 7.1.0
      '@turf/bbox-polygon': 7.1.0
      '@turf/centroid': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/point-grid': 7.1.0
      '@turf/random': 7.1.0
      '@turf/square-grid': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/random@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/rectangle-grid@7.1.0':
    dependencies:
      '@turf/boolean-intersects': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/rewind@7.1.0':
    dependencies:
      '@turf/boolean-clockwise': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/rhumb-bearing@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/rhumb-destination@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/rhumb-distance@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/sample@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/sector@7.1.0':
    dependencies:
      '@turf/circle': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/line-arc': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/shortest-path@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/bbox-polygon': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/clean-coords': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/transform-scale': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/simplify@7.1.0':
    dependencies:
      '@turf/clean-coords': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/square-grid@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/rectangle-grid': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/square@7.1.0':
    dependencies:
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/standard-deviational-ellipse@7.1.0':
    dependencies:
      '@turf/center-mean': 7.1.0
      '@turf/ellipse': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/points-within-polygon': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/tag@7.1.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/tesselate@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      earcut: 2.2.4
      tslib: 2.6.3

  '@turf/tin@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/transform-rotate@7.1.0':
    dependencies:
      '@turf/centroid': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/rhumb-bearing': 7.1.0
      '@turf/rhumb-destination': 7.1.0
      '@turf/rhumb-distance': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/transform-scale@7.1.0':
    dependencies:
      '@turf/bbox': 7.1.0
      '@turf/center': 7.1.0
      '@turf/centroid': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/rhumb-bearing': 7.1.0
      '@turf/rhumb-destination': 7.1.0
      '@turf/rhumb-distance': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/transform-translate@7.1.0':
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/rhumb-destination': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/triangle-grid@7.1.0':
    dependencies:
      '@turf/distance': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/intersect': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/truncate@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/turf@7.1.0':
    dependencies:
      '@turf/along': 7.1.0
      '@turf/angle': 7.1.0
      '@turf/area': 7.1.0
      '@turf/bbox': 7.1.0
      '@turf/bbox-clip': 7.1.0
      '@turf/bbox-polygon': 7.1.0
      '@turf/bearing': 7.1.0
      '@turf/bezier-spline': 7.1.0
      '@turf/boolean-clockwise': 7.1.0
      '@turf/boolean-concave': 7.1.0
      '@turf/boolean-contains': 7.1.0
      '@turf/boolean-crosses': 7.1.0
      '@turf/boolean-disjoint': 7.1.0
      '@turf/boolean-equal': 7.1.0
      '@turf/boolean-intersects': 7.1.0
      '@turf/boolean-overlap': 7.1.0
      '@turf/boolean-parallel': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/boolean-point-on-line': 7.1.0
      '@turf/boolean-touches': 7.1.0
      '@turf/boolean-valid': 7.1.0
      '@turf/boolean-within': 7.1.0
      '@turf/buffer': 7.1.0
      '@turf/center': 7.1.0
      '@turf/center-mean': 7.1.0
      '@turf/center-median': 7.1.0
      '@turf/center-of-mass': 7.1.0
      '@turf/centroid': 7.1.0
      '@turf/circle': 7.1.0
      '@turf/clean-coords': 7.1.0
      '@turf/clone': 7.1.0
      '@turf/clusters': 7.1.0
      '@turf/clusters-dbscan': 7.1.0
      '@turf/clusters-kmeans': 7.1.0
      '@turf/collect': 7.1.0
      '@turf/combine': 7.1.0
      '@turf/concave': 7.1.0
      '@turf/convex': 7.1.0
      '@turf/destination': 7.1.0
      '@turf/difference': 7.1.0
      '@turf/dissolve': 7.1.0
      '@turf/distance': 7.1.0
      '@turf/distance-weight': 7.1.0
      '@turf/ellipse': 7.1.0
      '@turf/envelope': 7.1.0
      '@turf/explode': 7.1.0
      '@turf/flatten': 7.1.0
      '@turf/flip': 7.1.0
      '@turf/geojson-rbush': 7.1.0
      '@turf/great-circle': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/hex-grid': 7.1.0
      '@turf/interpolate': 7.1.0
      '@turf/intersect': 7.1.0
      '@turf/invariant': 7.1.0
      '@turf/isobands': 7.1.0
      '@turf/isolines': 7.1.0
      '@turf/kinks': 7.1.0
      '@turf/length': 7.1.0
      '@turf/line-arc': 7.1.0
      '@turf/line-chunk': 7.1.0
      '@turf/line-intersect': 7.1.0
      '@turf/line-offset': 7.1.0
      '@turf/line-overlap': 7.1.0
      '@turf/line-segment': 7.1.0
      '@turf/line-slice': 7.1.0
      '@turf/line-slice-along': 7.1.0
      '@turf/line-split': 7.1.0
      '@turf/line-to-polygon': 7.1.0
      '@turf/mask': 7.1.0
      '@turf/meta': 7.1.0
      '@turf/midpoint': 7.1.0
      '@turf/moran-index': 7.1.0
      '@turf/nearest-neighbor-analysis': 7.1.0
      '@turf/nearest-point': 7.1.0
      '@turf/nearest-point-on-line': 7.1.0
      '@turf/nearest-point-to-line': 7.1.0
      '@turf/planepoint': 7.1.0
      '@turf/point-grid': 7.1.0
      '@turf/point-on-feature': 7.1.0
      '@turf/point-to-line-distance': 7.1.0
      '@turf/points-within-polygon': 7.1.0
      '@turf/polygon-smooth': 7.1.0
      '@turf/polygon-tangents': 7.1.0
      '@turf/polygon-to-line': 7.1.0
      '@turf/polygonize': 7.1.0
      '@turf/projection': 7.1.0
      '@turf/quadrat-analysis': 7.1.0
      '@turf/random': 7.1.0
      '@turf/rectangle-grid': 7.1.0
      '@turf/rewind': 7.1.0
      '@turf/rhumb-bearing': 7.1.0
      '@turf/rhumb-destination': 7.1.0
      '@turf/rhumb-distance': 7.1.0
      '@turf/sample': 7.1.0
      '@turf/sector': 7.1.0
      '@turf/shortest-path': 7.1.0
      '@turf/simplify': 7.1.0
      '@turf/square': 7.1.0
      '@turf/square-grid': 7.1.0
      '@turf/standard-deviational-ellipse': 7.1.0
      '@turf/tag': 7.1.0
      '@turf/tesselate': 7.1.0
      '@turf/tin': 7.1.0
      '@turf/transform-rotate': 7.1.0
      '@turf/transform-scale': 7.1.0
      '@turf/transform-translate': 7.1.0
      '@turf/triangle-grid': 7.1.0
      '@turf/truncate': 7.1.0
      '@turf/union': 7.1.0
      '@turf/unkink-polygon': 7.1.0
      '@turf/voronoi': 7.1.0
      '@types/geojson': 7946.0.15
      tslib: 2.6.3

  '@turf/union@7.1.0':
    dependencies:
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      polygon-clipping: 0.15.7
      tslib: 2.6.3

  '@turf/unkink-polygon@7.1.0':
    dependencies:
      '@turf/area': 7.1.0
      '@turf/boolean-point-in-polygon': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/meta': 7.1.0
      '@types/geojson': 7946.0.15
      rbush: 3.0.1
      tslib: 2.6.3

  '@turf/voronoi@7.1.0':
    dependencies:
      '@turf/clone': 7.1.0
      '@turf/helpers': 7.1.0
      '@turf/invariant': 7.1.0
      '@types/d3-voronoi': 1.1.12
      '@types/geojson': 7946.0.15
      d3-voronoi: 1.1.2
      tslib: 2.6.3

  '@tweenjs/tween.js@18.6.4': {}

  '@types/cesium@1.70.4':
    dependencies:
      cesium: 1.105.2

  '@types/d3-voronoi@1.1.12': {}

  '@types/estree@1.0.5': {}

  '@types/fs-extra@11.0.4':
    dependencies:
      '@types/jsonfile': 6.1.4
      '@types/node': 20.15.0

  '@types/geojson@7946.0.15': {}

  '@types/jsonfile@6.1.4':
    dependencies:
      '@types/node': 20.15.0

  '@types/lodash-es@4.17.12':
    dependencies:
      '@types/lodash': 4.17.7

  '@types/lodash@4.17.7': {}

  '@types/node@20.15.0':
    dependencies:
      undici-types: 6.13.0

  '@types/web-bluetooth@0.0.16': {}

  '@vitejs/plugin-vue@5.1.2(vite@5.4.1(@types/node@20.15.0)(less@4.2.0))(vue@3.4.38(typescript@5.4.5))':
    dependencies:
      vite: 5.4.1(@types/node@20.15.0)(less@4.2.0)
      vue: 3.4.38(typescript@5.4.5)

  '@volar/language-core@2.4.0-alpha.18':
    dependencies:
      '@volar/source-map': 2.4.0-alpha.18

  '@volar/source-map@2.4.0-alpha.18': {}

  '@volar/typescript@2.4.0-alpha.18':
    dependencies:
      '@volar/language-core': 2.4.0-alpha.18
      path-browserify: 1.0.1
      vscode-uri: 3.0.8

  '@vue/compiler-core@3.4.38':
    dependencies:
      '@babel/parser': 7.25.3
      '@vue/shared': 3.4.38
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.2.0

  '@vue/compiler-dom@3.4.38':
    dependencies:
      '@vue/compiler-core': 3.4.38
      '@vue/shared': 3.4.38

  '@vue/compiler-sfc@3.4.38':
    dependencies:
      '@babel/parser': 7.25.3
      '@vue/compiler-core': 3.4.38
      '@vue/compiler-dom': 3.4.38
      '@vue/compiler-ssr': 3.4.38
      '@vue/shared': 3.4.38
      estree-walker: 2.0.2
      magic-string: 0.30.11
      postcss: 8.4.41
      source-map-js: 1.2.0

  '@vue/compiler-ssr@3.4.38':
    dependencies:
      '@vue/compiler-dom': 3.4.38
      '@vue/shared': 3.4.38

  '@vue/compiler-vue2@2.7.16':
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0

  '@vue/devtools-api@6.6.3': {}

  '@vue/language-core@2.0.29(typescript@5.4.5)':
    dependencies:
      '@volar/language-core': 2.4.0-alpha.18
      '@vue/compiler-dom': 3.4.38
      '@vue/compiler-vue2': 2.7.16
      '@vue/shared': 3.4.38
      computeds: 0.0.1
      minimatch: 9.0.5
      muggle-string: 0.4.1
      path-browserify: 1.0.1
    optionalDependencies:
      typescript: 5.4.5

  '@vue/reactivity@3.4.38':
    dependencies:
      '@vue/shared': 3.4.38

  '@vue/runtime-core@3.4.38':
    dependencies:
      '@vue/reactivity': 3.4.38
      '@vue/shared': 3.4.38

  '@vue/runtime-dom@3.4.38':
    dependencies:
      '@vue/reactivity': 3.4.38
      '@vue/runtime-core': 3.4.38
      '@vue/shared': 3.4.38
      csstype: 3.1.3

  '@vue/server-renderer@3.4.38(vue@3.4.38(typescript@5.4.5))':
    dependencies:
      '@vue/compiler-ssr': 3.4.38
      '@vue/shared': 3.4.38
      vue: 3.4.38(typescript@5.4.5)

  '@vue/shared@3.4.38': {}

  '@vue/tsconfig@0.5.1': {}

  '@vueuse/core@9.13.0(vue@3.4.38(typescript@5.4.5))':
    dependencies:
      '@types/web-bluetooth': 0.0.16
      '@vueuse/metadata': 9.13.0
      '@vueuse/shared': 9.13.0(vue@3.4.38(typescript@5.4.5))
      vue-demi: 0.14.10(vue@3.4.38(typescript@5.4.5))
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@vueuse/metadata@9.13.0': {}

  '@vueuse/shared@9.13.0(vue@3.4.38(typescript@5.4.5))':
    dependencies:
      vue-demi: 0.14.10(vue@3.4.38(typescript@5.4.5))
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@zip.js/zip.js@2.4.26': {}

  acorn-walk@8.3.4:
    dependencies:
      acorn: 8.14.1

  acorn@8.14.1: {}

  ansi-styles@6.2.1: {}

  arg@4.1.3: {}

  async-validator@4.2.5: {}

  asynckit@0.4.0: {}

  autolinker@4.0.0:
    dependencies:
      tslib: 2.6.3

  axios@1.7.4:
    dependencies:
      follow-redirects: 1.15.6
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  balanced-match@1.0.2: {}

  bitmap-sdf@1.0.4: {}

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  cesium@1.105.2:
    dependencies:
      '@cesium/engine': 2.4.0
      '@cesium/widgets': 2.3.0

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@2.20.3: {}

  computeds@0.0.1: {}

  concaveman@1.2.1:
    dependencies:
      point-in-polygon: 1.1.0
      rbush: 3.0.1
      robust-predicates: 2.0.4
      tinyqueue: 2.0.3

  coordtransform@2.1.2: {}

  copy-anything@2.0.6:
    dependencies:
      is-what: 3.14.1

  create-require@1.1.1: {}

  cross-spawn@7.0.3:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  csstype@3.1.3: {}

  d3-array@1.2.4: {}

  d3-geo@1.7.1:
    dependencies:
      d3-array: 1.2.4

  d3-voronoi@1.1.2: {}

  dayjs@1.11.13: {}

  de-indent@1.0.2: {}

  delayed-stream@1.0.0: {}

  dexie@4.0.8: {}

  diff@4.0.2: {}

  dompurify@3.1.6: {}

  earcut@2.2.4: {}

  element-plus@2.9.10(vue@3.4.38(typescript@5.4.5)):
    dependencies:
      '@ctrl/tinycolor': 3.6.1
      '@element-plus/icons-vue': 2.3.1(vue@3.4.38(typescript@5.4.5))
      '@floating-ui/dom': 1.6.10
      '@popperjs/core': '@sxzz/popperjs-es@2.11.7'
      '@types/lodash': 4.17.7
      '@types/lodash-es': 4.17.12
      '@vueuse/core': 9.13.0(vue@3.4.38(typescript@5.4.5))
      async-validator: 4.2.5
      dayjs: 1.11.13
      escape-html: 1.0.3
      lodash: 4.17.21
      lodash-es: 4.17.21
      lodash-unified: 1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21)
      memoize-one: 6.0.0
      normalize-wheel-es: 1.2.0
      vue: 3.4.38(typescript@5.4.5)
    transitivePeerDependencies:
      - '@vue/composition-api'

  entities@4.5.0: {}

  errno@0.1.8:
    dependencies:
      prr: 1.0.1
    optional: true

  esbuild@0.21.5:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.21.5
      '@esbuild/android-arm': 0.21.5
      '@esbuild/android-arm64': 0.21.5
      '@esbuild/android-x64': 0.21.5
      '@esbuild/darwin-arm64': 0.21.5
      '@esbuild/darwin-x64': 0.21.5
      '@esbuild/freebsd-arm64': 0.21.5
      '@esbuild/freebsd-x64': 0.21.5
      '@esbuild/linux-arm': 0.21.5
      '@esbuild/linux-arm64': 0.21.5
      '@esbuild/linux-ia32': 0.21.5
      '@esbuild/linux-loong64': 0.21.5
      '@esbuild/linux-mips64el': 0.21.5
      '@esbuild/linux-ppc64': 0.21.5
      '@esbuild/linux-riscv64': 0.21.5
      '@esbuild/linux-s390x': 0.21.5
      '@esbuild/linux-x64': 0.21.5
      '@esbuild/netbsd-x64': 0.21.5
      '@esbuild/openbsd-x64': 0.21.5
      '@esbuild/sunos-x64': 0.21.5
      '@esbuild/win32-arm64': 0.21.5
      '@esbuild/win32-ia32': 0.21.5
      '@esbuild/win32-x64': 0.21.5

  escape-html@1.0.3: {}

  estree-walker@2.0.2: {}

  fast-deep-equal@3.1.3: {}

  follow-redirects@1.15.6: {}

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  fs-extra@11.3.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fsevents@2.3.3:
    optional: true

  geojson-equality-ts@1.0.2:
    dependencies:
      '@types/geojson': 7946.0.15

  geojson-polygon-self-intersections@1.2.1:
    dependencies:
      rbush: 2.0.2

  graceful-fs@4.2.11: {}

  grapheme-splitter@1.0.4: {}

  he@1.2.0: {}

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2
    optional: true

  image-size@0.5.5:
    optional: true

  is-what@3.14.1: {}

  isexe@2.0.0: {}

  jsencrypt@3.3.2: {}

  jsep@1.3.9: {}

  json-parse-even-better-errors@3.0.2: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsts@2.7.1: {}

  kdbush@4.0.2: {}

  ktx-parse@0.5.0: {}

  leaflet@1.9.4: {}

  lerc@2.0.0: {}

  less@4.2.0:
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.6.3
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      needle: 3.3.1
      source-map: 0.6.1

  lodash-es@4.17.21: {}

  lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21):
    dependencies:
      '@types/lodash-es': 4.17.12
      lodash: 4.17.21
      lodash-es: 4.17.21

  lodash@4.17.21: {}

  long@5.2.3: {}

  magic-string@0.30.11:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  make-dir@2.1.0:
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    optional: true

  make-error@1.3.6: {}

  marchingsquares@1.3.3: {}

  memoize-one@6.0.0: {}

  memorystream@0.3.1: {}

  mersenne-twister@1.1.0: {}

  meshoptimizer@0.18.1: {}

  mgrs@1.0.0: {}

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0:
    optional: true

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  muggle-string@0.4.1: {}

  nanoid@3.3.7: {}

  needle@3.3.1:
    dependencies:
      iconv-lite: 0.6.3
      sax: 1.4.1
    optional: true

  normalize-wheel-es@1.2.0: {}

  nosleep.js@0.12.0: {}

  npm-normalize-package-bin@3.0.1: {}

  npm-run-all2@6.2.2:
    dependencies:
      ansi-styles: 6.2.1
      cross-spawn: 7.0.3
      memorystream: 0.3.1
      minimatch: 9.0.5
      pidtree: 0.6.0
      read-package-json-fast: 3.0.2
      shell-quote: 1.8.1

  pako@2.1.0: {}

  parse-node-version@1.0.1: {}

  path-browserify@1.0.1: {}

  path-key@3.1.1: {}

  picocolors@1.0.1: {}

  pidtree@0.6.0: {}

  pify@4.0.1:
    optional: true

  pinia-plugin-persist@1.0.0(pinia@2.2.2(typescript@5.4.5)(vue@3.4.38(typescript@5.4.5)))(vue@3.4.38(typescript@5.4.5)):
    dependencies:
      pinia: 2.2.2(typescript@5.4.5)(vue@3.4.38(typescript@5.4.5))
      vue: 3.4.38(typescript@5.4.5)
      vue-demi: 0.12.5(vue@3.4.38(typescript@5.4.5))

  pinia@2.2.2(typescript@5.4.5)(vue@3.4.38(typescript@5.4.5)):
    dependencies:
      '@vue/devtools-api': 6.6.3
      vue: 3.4.38(typescript@5.4.5)
      vue-demi: 0.14.10(vue@3.4.38(typescript@5.4.5))
    optionalDependencies:
      typescript: 5.4.5

  point-in-polygon-hao@1.2.3:
    dependencies:
      robust-predicates: 3.0.2

  point-in-polygon@1.1.0: {}

  polygon-clipping@0.15.7:
    dependencies:
      robust-predicates: 3.0.2
      splaytree: 3.1.2

  postcss@8.4.41:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.1
      source-map-js: 1.2.0

  proj4@2.12.0:
    dependencies:
      mgrs: 1.0.0
      wkt-parser: 1.3.3

  protobufjs@7.3.2:
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/base64': 1.1.2
      '@protobufjs/codegen': 2.0.4
      '@protobufjs/eventemitter': 1.1.0
      '@protobufjs/fetch': 1.1.0
      '@protobufjs/float': 1.0.2
      '@protobufjs/inquire': 1.1.0
      '@protobufjs/path': 1.1.2
      '@protobufjs/pool': 1.1.0
      '@protobufjs/utf8': 1.1.0
      '@types/node': 20.15.0
      long: 5.2.3

  proxy-from-env@1.1.0: {}

  prr@1.0.1:
    optional: true

  quickselect@1.1.1: {}

  quickselect@2.0.0: {}

  rbush@2.0.2:
    dependencies:
      quickselect: 1.1.1

  rbush@3.0.1:
    dependencies:
      quickselect: 2.0.0

  read-package-json-fast@3.0.2:
    dependencies:
      json-parse-even-better-errors: 3.0.2
      npm-normalize-package-bin: 3.0.1

  robust-predicates@2.0.4: {}

  robust-predicates@3.0.2: {}

  rollup@4.20.0:
    dependencies:
      '@types/estree': 1.0.5
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.20.0
      '@rollup/rollup-android-arm64': 4.20.0
      '@rollup/rollup-darwin-arm64': 4.20.0
      '@rollup/rollup-darwin-x64': 4.20.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.20.0
      '@rollup/rollup-linux-arm-musleabihf': 4.20.0
      '@rollup/rollup-linux-arm64-gnu': 4.20.0
      '@rollup/rollup-linux-arm64-musl': 4.20.0
      '@rollup/rollup-linux-powerpc64le-gnu': 4.20.0
      '@rollup/rollup-linux-riscv64-gnu': 4.20.0
      '@rollup/rollup-linux-s390x-gnu': 4.20.0
      '@rollup/rollup-linux-x64-gnu': 4.20.0
      '@rollup/rollup-linux-x64-musl': 4.20.0
      '@rollup/rollup-win32-arm64-msvc': 4.20.0
      '@rollup/rollup-win32-ia32-msvc': 4.20.0
      '@rollup/rollup-win32-x64-msvc': 4.20.0
      fsevents: 2.3.3

  safer-buffer@2.1.2:
    optional: true

  sax@1.4.1:
    optional: true

  semver@5.7.2:
    optional: true

  semver@7.6.3: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shell-quote@1.8.1: {}

  skmeans@0.9.7: {}

  source-map-js@1.2.0: {}

  source-map@0.6.1:
    optional: true

  splaytree@3.1.2: {}

  sweepline-intersections@1.5.0:
    dependencies:
      tinyqueue: 2.0.3

  three@0.155.0: {}

  tinyqueue@2.0.3: {}

  to-fast-properties@2.0.0: {}

  topojson-client@3.1.0:
    dependencies:
      commander: 2.20.3

  topojson-server@3.0.1:
    dependencies:
      commander: 2.20.3

  ts-node@10.9.2(@types/node@20.15.0)(typescript@5.4.5):
    dependencies:
      '@cspotcode/source-map-support': 0.8.1
      '@tsconfig/node10': 1.0.11
      '@tsconfig/node12': 1.0.11
      '@tsconfig/node14': 1.0.3
      '@tsconfig/node16': 1.0.4
      '@types/node': 20.15.0
      acorn: 8.14.1
      acorn-walk: 8.3.4
      arg: 4.1.3
      create-require: 1.1.1
      diff: 4.0.2
      make-error: 1.3.6
      typescript: 5.4.5
      v8-compile-cache-lib: 3.0.1
      yn: 3.1.1

  tslib@2.6.3: {}

  tsparticles-basic@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0
      tsparticles-move-base: 2.12.0
      tsparticles-shape-circle: 2.12.0
      tsparticles-updater-color: 2.12.0
      tsparticles-updater-opacity: 2.12.0
      tsparticles-updater-out-modes: 2.12.0
      tsparticles-updater-size: 2.12.0

  tsparticles-engine@2.12.0: {}

  tsparticles-interaction-external-attract@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-interaction-external-bounce@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-interaction-external-bubble@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-interaction-external-connect@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-interaction-external-grab@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-interaction-external-pause@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-interaction-external-push@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-interaction-external-remove@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-interaction-external-repulse@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-interaction-external-slow@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-interaction-particles-attract@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-interaction-particles-collisions@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-interaction-particles-links@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-move-base@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-move-parallax@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-particles.js@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-plugin-easing-quad@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-shape-circle@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-shape-image@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-shape-line@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-shape-polygon@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-shape-square@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-shape-star@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-shape-text@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-slim@2.12.0:
    dependencies:
      tsparticles-basic: 2.12.0
      tsparticles-engine: 2.12.0
      tsparticles-interaction-external-attract: 2.12.0
      tsparticles-interaction-external-bounce: 2.12.0
      tsparticles-interaction-external-bubble: 2.12.0
      tsparticles-interaction-external-connect: 2.12.0
      tsparticles-interaction-external-grab: 2.12.0
      tsparticles-interaction-external-pause: 2.12.0
      tsparticles-interaction-external-push: 2.12.0
      tsparticles-interaction-external-remove: 2.12.0
      tsparticles-interaction-external-repulse: 2.12.0
      tsparticles-interaction-external-slow: 2.12.0
      tsparticles-interaction-particles-attract: 2.12.0
      tsparticles-interaction-particles-collisions: 2.12.0
      tsparticles-interaction-particles-links: 2.12.0
      tsparticles-move-base: 2.12.0
      tsparticles-move-parallax: 2.12.0
      tsparticles-particles.js: 2.12.0
      tsparticles-plugin-easing-quad: 2.12.0
      tsparticles-shape-circle: 2.12.0
      tsparticles-shape-image: 2.12.0
      tsparticles-shape-line: 2.12.0
      tsparticles-shape-polygon: 2.12.0
      tsparticles-shape-square: 2.12.0
      tsparticles-shape-star: 2.12.0
      tsparticles-shape-text: 2.12.0
      tsparticles-updater-color: 2.12.0
      tsparticles-updater-life: 2.12.0
      tsparticles-updater-opacity: 2.12.0
      tsparticles-updater-out-modes: 2.12.0
      tsparticles-updater-rotate: 2.12.0
      tsparticles-updater-size: 2.12.0
      tsparticles-updater-stroke-color: 2.12.0

  tsparticles-updater-color@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-updater-life@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-updater-opacity@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-updater-out-modes@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-updater-rotate@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-updater-size@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  tsparticles-updater-stroke-color@2.12.0:
    dependencies:
      tsparticles-engine: 2.12.0

  typescript@5.4.5: {}

  undici-types@6.13.0: {}

  universalify@2.0.1: {}

  urijs@1.19.11: {}

  v8-compile-cache-lib@3.0.1: {}

  vite@5.4.1(@types/node@20.15.0)(less@4.2.0):
    dependencies:
      esbuild: 0.21.5
      postcss: 8.4.41
      rollup: 4.20.0
    optionalDependencies:
      '@types/node': 20.15.0
      fsevents: 2.3.3
      less: 4.2.0

  vscode-uri@3.0.8: {}

  vue-demi@0.12.5(vue@3.4.38(typescript@5.4.5)):
    dependencies:
      vue: 3.4.38(typescript@5.4.5)

  vue-demi@0.14.10(vue@3.4.38(typescript@5.4.5)):
    dependencies:
      vue: 3.4.38(typescript@5.4.5)

  vue-router@4.4.3(vue@3.4.38(typescript@5.4.5)):
    dependencies:
      '@vue/devtools-api': 6.6.3
      vue: 3.4.38(typescript@5.4.5)

  vue-tsc@2.0.29(typescript@5.4.5):
    dependencies:
      '@volar/typescript': 2.4.0-alpha.18
      '@vue/language-core': 2.0.29(typescript@5.4.5)
      semver: 7.6.3
      typescript: 5.4.5

  vue@3.4.38(typescript@5.4.5):
    dependencies:
      '@vue/compiler-dom': 3.4.38
      '@vue/compiler-sfc': 3.4.38
      '@vue/runtime-dom': 3.4.38
      '@vue/server-renderer': 3.4.38(vue@3.4.38(typescript@5.4.5))
      '@vue/shared': 3.4.38
    optionalDependencies:
      typescript: 5.4.5

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wkt-parser@1.3.3: {}

  yn@3.1.1: {}
