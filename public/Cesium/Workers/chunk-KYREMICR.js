/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{b as Z,h as A}from"./chunk-VBRVI5XI.js";import{a as n,b as v,d as N,e as _}from"./chunk-7L2LUDC3.js";import{a as on}from"./chunk-77KFIUJG.js";import{a as en,b as h}from"./chunk-PX3QTMVS.js";import{e as x}from"./chunk-FE4HG5RY.js";function I(e){this._ellipsoid=e??N.default,this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(I.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}});I.prototype.project=function(e,t){let o=this._semimajorAxis,c=e.longitude*o,r=e.latitude*o,d=e.height;return x(t)?(t.x=c,t.y=r,t.z=d,t):new n(c,r,d)};I.prototype.unproject=function(e,t){if(!x(e))throw new en("cartesian is required");let o=this._oneOverSemimajorAxis,c=e.x*o,r=e.y*o,d=e.z;return x(t)?(t.longitude=c,t.latitude=r,t.height=d,t):new v(c,r,d)};var k=I;var sn={OUTSIDE:-1,INTERSECTING:0,INSIDE:1},U=Object.freeze(sn);function mn(e,t){this.start=e??0,this.stop=t??0}var cn=mn;function a(e,t){this.center=n.clone(e??n.ZERO),this.radius=t??0}var X=new n,F=new n,Y=new n,J=new n,K=new n,Q=new n,$=new n,b=new n,L=new n,H=new n,nn=new n,tn=new n,un=4/3*on.PI;a.fromPoints=function(e,t){if(x(t)||(t=new a),!x(e)||e.length===0)return t.center=n.clone(n.ZERO,t.center),t.radius=0,t;let o=n.clone(e[0],$),c=n.clone(o,X),r=n.clone(o,F),d=n.clone(o,Y),f=n.clone(o,J),s=n.clone(o,K),m=n.clone(o,Q),z=e.length,y;for(y=1;y<z;y++){n.clone(e[y],o);let C=o.x,M=o.y,w=o.z;C<c.x&&n.clone(o,c),C>f.x&&n.clone(o,f),M<r.y&&n.clone(o,r),M>s.y&&n.clone(o,s),w<d.z&&n.clone(o,d),w>m.z&&n.clone(o,m)}let u=n.magnitudeSquared(n.subtract(f,c,b)),i=n.magnitudeSquared(n.subtract(s,r,b)),B=n.magnitudeSquared(n.subtract(m,d,b)),T=c,q=f,O=u;i>O&&(O=i,T=r,q=s),B>O&&(O=B,T=d,q=m);let p=L;p.x=(T.x+q.x)*.5,p.y=(T.y+q.y)*.5,p.z=(T.z+q.z)*.5;let S=n.magnitudeSquared(n.subtract(q,p,b)),l=Math.sqrt(S),j=H;j.x=c.x,j.y=r.y,j.z=d.z;let P=nn;P.x=f.x,P.y=s.y,P.z=m.z;let E=n.midpoint(j,P,tn),D=0;for(y=0;y<z;y++){n.clone(e[y],o);let C=n.magnitude(n.subtract(o,E,b));C>D&&(D=C);let M=n.magnitudeSquared(n.subtract(o,p,b));if(M>S){let w=Math.sqrt(M);l=(l+w)*.5,S=l*l;let g=w-l;p.x=(l*p.x+g*o.x)/w,p.y=(l*p.y+g*o.y)/w,p.z=(l*p.z+g*o.z)/w}}return l<D?(n.clone(p,t.center),t.radius=l):(n.clone(E,t.center),t.radius=D),t};var rn=new k,xn=new n,yn=new n,G=new v,V=new v;a.fromRectangle2D=function(e,t,o){return a.fromRectangleWithHeights2D(e,t,0,0,o)};a.fromRectangleWithHeights2D=function(e,t,o,c,r){if(x(r)||(r=new a),!x(e))return r.center=n.clone(n.ZERO,r.center),r.radius=0,r;rn._ellipsoid=N.default,t=t??rn,A.southwest(e,G),G.height=o,A.northeast(e,V),V.height=c;let d=t.project(G,xn),f=t.project(V,yn),s=f.x-d.x,m=f.y-d.y,z=f.z-d.z;r.radius=Math.sqrt(s*s+m*m+z*z)*.5;let y=r.center;return y.x=d.x+s*.5,y.y=d.y+m*.5,y.z=d.z+z*.5,r};var ln=[];a.fromRectangle3D=function(e,t,o,c){if(t=t??N.default,o=o??0,x(c)||(c=new a),!x(e))return c.center=n.clone(n.ZERO,c.center),c.radius=0,c;let r=A.subsample(e,t,o,ln);return a.fromPoints(r,c)};a.fromVertices=function(e,t,o,c){if(x(c)||(c=new a),!x(e)||e.length===0)return c.center=n.clone(n.ZERO,c.center),c.radius=0,c;t=t??n.ZERO,o=o??3,h.typeOf.number.greaterThanOrEquals("stride",o,3);let r=$;r.x=e[0]+t.x,r.y=e[1]+t.y,r.z=e[2]+t.z;let d=n.clone(r,X),f=n.clone(r,F),s=n.clone(r,Y),m=n.clone(r,J),z=n.clone(r,K),y=n.clone(r,Q),u=e.length,i;for(i=0;i<u;i+=o){let w=e[i]+t.x,g=e[i+1]+t.y,R=e[i+2]+t.z;r.x=w,r.y=g,r.z=R,w<d.x&&n.clone(r,d),w>m.x&&n.clone(r,m),g<f.y&&n.clone(r,f),g>z.y&&n.clone(r,z),R<s.z&&n.clone(r,s),R>y.z&&n.clone(r,y)}let B=n.magnitudeSquared(n.subtract(m,d,b)),T=n.magnitudeSquared(n.subtract(z,f,b)),q=n.magnitudeSquared(n.subtract(y,s,b)),O=d,p=m,S=B;T>S&&(S=T,O=f,p=z),q>S&&(S=q,O=s,p=y);let l=L;l.x=(O.x+p.x)*.5,l.y=(O.y+p.y)*.5,l.z=(O.z+p.z)*.5;let j=n.magnitudeSquared(n.subtract(p,l,b)),P=Math.sqrt(j),E=H;E.x=d.x,E.y=f.y,E.z=s.z;let D=nn;D.x=m.x,D.y=z.y,D.z=y.z;let C=n.midpoint(E,D,tn),M=0;for(i=0;i<u;i+=o){r.x=e[i]+t.x,r.y=e[i+1]+t.y,r.z=e[i+2]+t.z;let w=n.magnitude(n.subtract(r,C,b));w>M&&(M=w);let g=n.magnitudeSquared(n.subtract(r,l,b));if(g>j){let R=Math.sqrt(g);P=(P+R)*.5,j=P*P;let W=R-P;l.x=(P*l.x+W*r.x)/R,l.y=(P*l.y+W*r.y)/R,l.z=(P*l.z+W*r.z)/R}}return P<M?(n.clone(l,c.center),c.radius=P):(n.clone(C,c.center),c.radius=M),c};a.fromEncodedCartesianVertices=function(e,t,o){if(x(o)||(o=new a),!x(e)||!x(t)||e.length!==t.length||e.length===0)return o.center=n.clone(n.ZERO,o.center),o.radius=0,o;let c=$;c.x=e[0]+t[0],c.y=e[1]+t[1],c.z=e[2]+t[2];let r=n.clone(c,X),d=n.clone(c,F),f=n.clone(c,Y),s=n.clone(c,J),m=n.clone(c,K),z=n.clone(c,Q),y=e.length,u;for(u=0;u<y;u+=3){let M=e[u]+t[u],w=e[u+1]+t[u+1],g=e[u+2]+t[u+2];c.x=M,c.y=w,c.z=g,M<r.x&&n.clone(c,r),M>s.x&&n.clone(c,s),w<d.y&&n.clone(c,d),w>m.y&&n.clone(c,m),g<f.z&&n.clone(c,f),g>z.z&&n.clone(c,z)}let i=n.magnitudeSquared(n.subtract(s,r,b)),B=n.magnitudeSquared(n.subtract(m,d,b)),T=n.magnitudeSquared(n.subtract(z,f,b)),q=r,O=s,p=i;B>p&&(p=B,q=d,O=m),T>p&&(p=T,q=f,O=z);let S=L;S.x=(q.x+O.x)*.5,S.y=(q.y+O.y)*.5,S.z=(q.z+O.z)*.5;let l=n.magnitudeSquared(n.subtract(O,S,b)),j=Math.sqrt(l),P=H;P.x=r.x,P.y=d.y,P.z=f.z;let E=nn;E.x=s.x,E.y=m.y,E.z=z.z;let D=n.midpoint(P,E,tn),C=0;for(u=0;u<y;u+=3){c.x=e[u]+t[u],c.y=e[u+1]+t[u+1],c.z=e[u+2]+t[u+2];let M=n.magnitude(n.subtract(c,D,b));M>C&&(C=M);let w=n.magnitudeSquared(n.subtract(c,S,b));if(w>l){let g=Math.sqrt(w);j=(j+g)*.5,l=j*j;let R=g-j;S.x=(j*S.x+R*c.x)/g,S.y=(j*S.y+R*c.y)/g,S.z=(j*S.z+R*c.z)/g}}return j<C?(n.clone(S,o.center),o.radius=j):(n.clone(D,o.center),o.radius=C),o};a.fromCornerPoints=function(e,t,o){h.typeOf.object("corner",e),h.typeOf.object("oppositeCorner",t),x(o)||(o=new a);let c=n.midpoint(e,t,o.center);return o.radius=n.distance(c,t),o};a.fromEllipsoid=function(e,t){return h.typeOf.object("ellipsoid",e),x(t)||(t=new a),n.clone(n.ZERO,t.center),t.radius=e.maximumRadius,t};var pn=new n;a.fromBoundingSpheres=function(e,t){if(x(t)||(t=new a),!x(e)||e.length===0)return t.center=n.clone(n.ZERO,t.center),t.radius=0,t;let o=e.length;if(o===1)return a.clone(e[0],t);if(o===2)return a.union(e[0],e[1],t);let c=[],r;for(r=0;r<o;r++)c.push(e[r].center);t=a.fromPoints(c,t);let d=t.center,f=t.radius;for(r=0;r<o;r++){let s=e[r];f=Math.max(f,n.distance(d,s.center,pn)+s.radius)}return t.radius=f,t};var hn=new n,zn=new n,Sn=new n;a.fromOrientedBoundingBox=function(e,t){h.defined("orientedBoundingBox",e),x(t)||(t=new a);let o=e.halfAxes,c=_.getColumn(o,0,hn),r=_.getColumn(o,1,zn),d=_.getColumn(o,2,Sn);return n.add(c,r,c),n.add(c,d,c),t.center=n.clone(e.center,t.center),t.radius=n.magnitude(c),t};var wn=new n,Pn=new n;a.fromTransformation=function(e,t){h.typeOf.object("transformation",e),x(t)||(t=new a);let o=Z.getTranslation(e,wn),c=Z.getScale(e,Pn),r=.5*n.magnitude(c);return t.center=n.clone(o,t.center),t.radius=r,t};a.clone=function(e,t){if(x(e))return x(t)?(t.center=n.clone(e.center,t.center),t.radius=e.radius,t):new a(e.center,e.radius)};a.packedLength=4;a.pack=function(e,t,o){h.typeOf.object("value",e),h.defined("array",t),o=o??0;let c=e.center;return t[o++]=c.x,t[o++]=c.y,t[o++]=c.z,t[o]=e.radius,t};a.unpack=function(e,t,o){h.defined("array",e),t=t??0,x(o)||(o=new a);let c=o.center;return c.x=e[t++],c.y=e[t++],c.z=e[t++],o.radius=e[t],o};var gn=new n,On=new n;a.union=function(e,t,o){h.typeOf.object("left",e),h.typeOf.object("right",t),x(o)||(o=new a);let c=e.center,r=e.radius,d=t.center,f=t.radius,s=n.subtract(d,c,gn),m=n.magnitude(s);if(r>=m+f)return e.clone(o),o;if(f>=m+r)return t.clone(o),o;let z=(r+m+f)*.5,y=n.multiplyByScalar(s,(-r+z)/m,On);return n.add(y,c,y),n.clone(y,o.center),o.radius=z,o};var jn=new n;a.expand=function(e,t,o){h.typeOf.object("sphere",e),h.typeOf.object("point",t),o=a.clone(e,o);let c=n.magnitude(n.subtract(t,o.center,jn));return c>o.radius&&(o.radius=c),o};a.intersectPlane=function(e,t){h.typeOf.object("sphere",e),h.typeOf.object("plane",t);let o=e.center,c=e.radius,r=t.normal,d=n.dot(r,o)+t.distance;return d<-c?U.OUTSIDE:d<c?U.INTERSECTING:U.INSIDE};a.transform=function(e,t,o){return h.typeOf.object("sphere",e),h.typeOf.object("transform",t),x(o)||(o=new a),o.center=Z.multiplyByPoint(t,e.center,o.center),o.radius=Z.getMaximumScale(t)*e.radius,o};var bn=new n;a.distanceSquaredTo=function(e,t){h.typeOf.object("sphere",e),h.typeOf.object("cartesian",t);let o=n.subtract(e.center,t,bn),c=n.magnitude(o)-e.radius;return c<=0?0:c*c};a.transformWithoutScale=function(e,t,o){return h.typeOf.object("sphere",e),h.typeOf.object("transform",t),x(o)||(o=new a),o.center=Z.multiplyByPoint(t,e.center,o.center),o.radius=e.radius,o};var qn=new n;a.computePlaneDistances=function(e,t,o,c){h.typeOf.object("sphere",e),h.typeOf.object("position",t),h.typeOf.object("direction",o),x(c)||(c=new cn);let r=n.subtract(e.center,t,qn),d=n.dot(o,r);return c.start=d-e.radius,c.stop=d+e.radius,c};var an=new n,Mn=new n,Tn=new n,Cn=new n,Rn=new n,En=new v,fn=new Array(8);for(let e=0;e<8;++e)fn[e]=new n;var dn=new k;a.projectTo2D=function(e,t,o){h.typeOf.object("sphere",e),dn._ellipsoid=N.default,t=t??dn;let c=t.ellipsoid,r=e.center,d=e.radius,f;n.equals(r,n.ZERO)?f=n.clone(n.UNIT_X,an):f=c.geodeticSurfaceNormal(r,an);let s=n.cross(n.UNIT_Z,f,Mn);n.normalize(s,s);let m=n.cross(f,s,Tn);n.normalize(m,m),n.multiplyByScalar(f,d,f),n.multiplyByScalar(m,d,m),n.multiplyByScalar(s,d,s);let z=n.negate(m,Rn),y=n.negate(s,Cn),u=fn,i=u[0];n.add(f,m,i),n.add(i,s,i),i=u[1],n.add(f,m,i),n.add(i,y,i),i=u[2],n.add(f,z,i),n.add(i,y,i),i=u[3],n.add(f,z,i),n.add(i,s,i),n.negate(f,f),i=u[4],n.add(f,m,i),n.add(i,s,i),i=u[5],n.add(f,m,i),n.add(i,y,i),i=u[6],n.add(f,z,i),n.add(i,y,i),i=u[7],n.add(f,z,i),n.add(i,s,i);let B=u.length;for(let p=0;p<B;++p){let S=u[p];n.add(r,S,S);let l=c.cartesianToCartographic(S,En);t.project(l,S)}o=a.fromPoints(u,o),r=o.center;let T=r.x,q=r.y,O=r.z;return r.x=O,r.y=T,r.z=q,o};a.isOccluded=function(e,t){return h.typeOf.object("sphere",e),h.typeOf.object("occluder",t),!t.isBoundingSphereVisible(e)};a.equals=function(e,t){return e===t||x(e)&&x(t)&&n.equals(e.center,t.center)&&e.radius===t.radius};a.prototype.intersectPlane=function(e){return a.intersectPlane(this,e)};a.prototype.distanceSquaredTo=function(e){return a.distanceSquaredTo(this,e)};a.prototype.computePlaneDistances=function(e,t,o){return a.computePlaneDistances(this,e,t,o)};a.prototype.isOccluded=function(e){return a.isOccluded(this,e)};a.prototype.equals=function(e){return a.equals(this,e)};a.prototype.clone=function(e){return a.clone(this,e)};a.prototype.volume=function(){let e=this.radius;return un*e*e*e};var Ln=a;export{k as a,U as b,cn as c,Ln as d};
