/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as N}from"./chunk-KA7ZX4BQ.js";import{a as Q}from"./chunk-AAOMPF7M.js";import{a as ae}from"./chunk-PSBTKXXJ.js";import{b as oe,c as re,d as J}from"./chunk-UJOKCDQH.js";import{b as E,d as ie}from"./chunk-KYREMICR.js";import{a as C,b as F,f as D}from"./chunk-VBRVI5XI.js";import{a as H}from"./chunk-5ZFOKSDK.js";import{a as r,e as G,f as A}from"./chunk-7L2LUDC3.js";import{a as O}from"./chunk-77KFIUJG.js";import{a as w,b}from"./chunk-PX3QTMVS.js";import{e as a}from"./chunk-FE4HG5RY.js";function R(e){this.planes=e??[]}var X=[new r,new r,new r];r.clone(r.UNIT_X,X[0]);r.clone(r.UNIT_Y,X[1]);r.clone(r.UNIT_Z,X[2]);var j=new r,de=new r,fe=new Q(new r(1,0,0),0);R.fromBoundingSphere=function(e,t){if(!a(e))throw new w("boundingSphere is required.");a(t)||(t=new R);let n=X.length,f=t.planes;f.length=2*n;let s=e.center,_=e.radius,p=0;for(let m=0;m<n;++m){let i=X[m],c=f[p],d=f[p+1];a(c)||(c=f[p]=new C),a(d)||(d=f[p+1]=new C),r.multiplyByScalar(i,-_,j),r.add(s,j,j),c.x=i.x,c.y=i.y,c.z=i.z,c.w=-r.dot(i,j),r.multiplyByScalar(i,_,j),r.add(s,j,j),d.x=-i.x,d.y=-i.y,d.z=-i.z,d.w=-r.dot(r.negate(i,de),j),p+=2}return t};R.prototype.computeVisibility=function(e){if(!a(e))throw new w("boundingVolume is required.");let t=this.planes,n=!1;for(let f=0,s=t.length;f<s;++f){let _=e.intersectPlane(Q.fromCartesian4(t[f],fe));if(_===E.OUTSIDE)return E.OUTSIDE;_===E.INTERSECTING&&(n=!0)}return n?E.INTERSECTING:E.INSIDE};R.prototype.computeVisibilityWithPlaneMask=function(e,t){if(!a(e))throw new w("boundingVolume is required.");if(!a(t))throw new w("parentPlaneMask is required.");if(t===R.MASK_OUTSIDE||t===R.MASK_INSIDE)return t;let n=R.MASK_INSIDE,f=this.planes;for(let s=0,_=f.length;s<_;++s){let p=s<31?1<<s:0;if(s<31&&(t&p)===0)continue;let m=e.intersectPlane(Q.fromCartesian4(f[s],fe));if(m===E.OUTSIDE)return R.MASK_OUTSIDE;m===E.INTERSECTING&&(n|=p)}return n};R.MASK_OUTSIDE=4294967295;R.MASK_INSIDE=0;R.MASK_INDETERMINATE=2147483647;var $=R;function M(e){e=e??A.EMPTY_OBJECT,this.left=e.left,this._left=void 0,this.right=e.right,this._right=void 0,this.top=e.top,this._top=void 0,this.bottom=e.bottom,this._bottom=void 0,this.near=e.near??1,this._near=this.near,this.far=e.far??5e8,this._far=this.far,this._cullingVolume=new $,this._orthographicMatrix=new F}function ce(e){if(!a(e.right)||!a(e.left)||!a(e.top)||!a(e.bottom)||!a(e.near)||!a(e.far))throw new w("right, left, top, bottom, near, or far parameters are not set.");if(e.top!==e._top||e.bottom!==e._bottom||e.left!==e._left||e.right!==e._right||e.near!==e._near||e.far!==e._far){if(e.left>e.right)throw new w("right must be greater than left.");if(e.bottom>e.top)throw new w("top must be greater than bottom.");if(e.near<=0||e.near>e.far)throw new w("near must be greater than zero and less than far.");e._left=e.left,e._right=e.right,e._top=e.top,e._bottom=e.bottom,e._near=e.near,e._far=e.far,e._orthographicMatrix=F.computeOrthographicOffCenter(e.left,e.right,e.bottom,e.top,e.near,e.far,e._orthographicMatrix)}}Object.defineProperties(M.prototype,{projectionMatrix:{get:function(){return ce(this),this._orthographicMatrix}}});var me=new r,ye=new r,ge=new r,ee=new r;M.prototype.computeCullingVolume=function(e,t,n){if(!a(e))throw new w("position is required.");if(!a(t))throw new w("direction is required.");if(!a(n))throw new w("up is required.");let f=this._cullingVolume.planes,s=this.top,_=this.bottom,p=this.right,m=this.left,i=this.near,c=this.far,d=r.cross(t,n,me);r.normalize(d,d);let y=ye;r.multiplyByScalar(t,i,y),r.add(e,y,y);let l=ge;r.multiplyByScalar(d,m,l),r.add(y,l,l);let o=f[0];return a(o)||(o=f[0]=new C),o.x=d.x,o.y=d.y,o.z=d.z,o.w=-r.dot(d,l),r.multiplyByScalar(d,p,l),r.add(y,l,l),o=f[1],a(o)||(o=f[1]=new C),o.x=-d.x,o.y=-d.y,o.z=-d.z,o.w=-r.dot(r.negate(d,ee),l),r.multiplyByScalar(n,_,l),r.add(y,l,l),o=f[2],a(o)||(o=f[2]=new C),o.x=n.x,o.y=n.y,o.z=n.z,o.w=-r.dot(n,l),r.multiplyByScalar(n,s,l),r.add(y,l,l),o=f[3],a(o)||(o=f[3]=new C),o.x=-n.x,o.y=-n.y,o.z=-n.z,o.w=-r.dot(r.negate(n,ee),l),o=f[4],a(o)||(o=f[4]=new C),o.x=t.x,o.y=t.y,o.z=t.z,o.w=-r.dot(t,y),r.multiplyByScalar(t,c,l),r.add(e,l,l),o=f[5],a(o)||(o=f[5]=new C),o.x=-t.x,o.y=-t.y,o.z=-t.z,o.w=-r.dot(r.negate(t,ee),l),this._cullingVolume};M.prototype.getPixelDimensions=function(e,t,n,f,s){if(ce(this),!a(e)||!a(t))throw new w("Both drawingBufferWidth and drawingBufferHeight are required.");if(e<=0)throw new w("drawingBufferWidth must be greater than zero.");if(t<=0)throw new w("drawingBufferHeight must be greater than zero.");if(!a(n))throw new w("distance is required.");if(!a(f))throw new w("pixelRatio is required.");if(f<=0)throw new w("pixelRatio must be greater than zero.");if(!a(s))throw new w("A result object is required.");let _=this.right-this.left,p=this.top-this.bottom,m=f*_/e,i=f*p/t;return s.x=m,s.y=i,s};M.prototype.clone=function(e){return a(e)||(e=new M),e.left=this.left,e.right=this.right,e.top=this.top,e.bottom=this.bottom,e.near=this.near,e.far=this.far,e._left=void 0,e._right=void 0,e._top=void 0,e._bottom=void 0,e._near=void 0,e._far=void 0,e};M.prototype.equals=function(e){return a(e)&&e instanceof M&&this.right===e.right&&this.left===e.left&&this.top===e.top&&this.bottom===e.bottom&&this.near===e.near&&this.far===e.far};M.prototype.equalsEpsilon=function(e,t,n){return e===this||a(e)&&e instanceof M&&O.equalsEpsilon(this.right,e.right,t,n)&&O.equalsEpsilon(this.left,e.left,t,n)&&O.equalsEpsilon(this.top,e.top,t,n)&&O.equalsEpsilon(this.bottom,e.bottom,t,n)&&O.equalsEpsilon(this.near,e.near,t,n)&&O.equalsEpsilon(this.far,e.far,t,n)};var he=M;function x(e){e=e??A.EMPTY_OBJECT,this._offCenterFrustum=new he,this.width=e.width,this._width=void 0,this.aspectRatio=e.aspectRatio,this._aspectRatio=void 0,this.near=e.near??1,this._near=this.near,this.far=e.far??5e8,this._far=this.far}x.packedLength=4;x.pack=function(e,t,n){return b.typeOf.object("value",e),b.defined("array",t),n=n??0,t[n++]=e.width,t[n++]=e.aspectRatio,t[n++]=e.near,t[n]=e.far,t};x.unpack=function(e,t,n){return b.defined("array",e),t=t??0,a(n)||(n=new x),n.width=e[t++],n.aspectRatio=e[t++],n.near=e[t++],n.far=e[t],n};function B(e){if(!a(e.width)||!a(e.aspectRatio)||!a(e.near)||!a(e.far))throw new w("width, aspectRatio, near, or far parameters are not set.");let t=e._offCenterFrustum;if(e.width!==e._width||e.aspectRatio!==e._aspectRatio||e.near!==e._near||e.far!==e._far){if(e.aspectRatio<0)throw new w("aspectRatio must be positive.");if(e.near<0||e.near>e.far)throw new w("near must be greater than zero and less than far.");e._aspectRatio=e.aspectRatio,e._width=e.width,e._near=e.near,e._far=e.far;let n=1/e.aspectRatio;t.right=e.width*.5,t.left=-t.right,t.top=n*t.right,t.bottom=-t.top,t.near=e.near,t.far=e.far}}Object.defineProperties(x.prototype,{projectionMatrix:{get:function(){return B(this),this._offCenterFrustum.projectionMatrix}},offCenterFrustum:{get:function(){return B(this),this._offCenterFrustum}}});x.prototype.computeCullingVolume=function(e,t,n){return B(this),this._offCenterFrustum.computeCullingVolume(e,t,n)};x.prototype.getPixelDimensions=function(e,t,n,f,s){return B(this),this._offCenterFrustum.getPixelDimensions(e,t,n,f,s)};x.prototype.clone=function(e){return a(e)||(e=new x),e.aspectRatio=this.aspectRatio,e.width=this.width,e.near=this.near,e.far=this.far,e._aspectRatio=void 0,e._width=void 0,e._near=void 0,e._far=void 0,this._offCenterFrustum.clone(e._offCenterFrustum),e};x.prototype.equals=function(e){return!a(e)||!(e instanceof x)?!1:(B(this),B(e),this.width===e.width&&this.aspectRatio===e.aspectRatio&&this._offCenterFrustum.equals(e._offCenterFrustum))};x.prototype.equalsEpsilon=function(e,t,n){return!a(e)||!(e instanceof x)?!1:(B(this),B(e),O.equalsEpsilon(this.width,e.width,t,n)&&O.equalsEpsilon(this.aspectRatio,e.aspectRatio,t,n)&&this._offCenterFrustum.equalsEpsilon(e._offCenterFrustum,t,n))};var V=x;function k(e){e=e??A.EMPTY_OBJECT,this.left=e.left,this._left=void 0,this.right=e.right,this._right=void 0,this.top=e.top,this._top=void 0,this.bottom=e.bottom,this._bottom=void 0,this.near=e.near??1,this._near=this.near,this.far=e.far??5e8,this._far=this.far,this._cullingVolume=new $,this._perspectiveMatrix=new F,this._infinitePerspective=new F}function te(e){if(!a(e.right)||!a(e.left)||!a(e.top)||!a(e.bottom)||!a(e.near)||!a(e.far))throw new w("right, left, top, bottom, near, or far parameters are not set.");let{top:t,bottom:n,right:f,left:s,near:_,far:p}=e;if(t!==e._top||n!==e._bottom||s!==e._left||f!==e._right||_!==e._near||p!==e._far){if(e.near<=0||e.near>e.far)throw new w("near must be greater than zero and less than far.");e._left=s,e._right=f,e._top=t,e._bottom=n,e._near=_,e._far=p,e._perspectiveMatrix=F.computePerspectiveOffCenter(s,f,n,t,_,p,e._perspectiveMatrix),e._infinitePerspective=F.computeInfinitePerspectiveOffCenter(s,f,n,t,_,e._infinitePerspective)}}Object.defineProperties(k.prototype,{projectionMatrix:{get:function(){return te(this),this._perspectiveMatrix}},infiniteProjectionMatrix:{get:function(){return te(this),this._infinitePerspective}}});var Ce=new r,be=new r,Oe=new r,Fe=new r;k.prototype.computeCullingVolume=function(e,t,n){if(!a(e))throw new w("position is required.");if(!a(t))throw new w("direction is required.");if(!a(n))throw new w("up is required.");let f=this._cullingVolume.planes,s=this.top,_=this.bottom,p=this.right,m=this.left,i=this.near,c=this.far,d=r.cross(t,n,Ce),y=be;r.multiplyByScalar(t,i,y),r.add(e,y,y);let l=Oe;r.multiplyByScalar(t,c,l),r.add(e,l,l);let o=Fe;r.multiplyByScalar(d,m,o),r.add(y,o,o),r.subtract(o,e,o),r.normalize(o,o),r.cross(o,n,o),r.normalize(o,o);let h=f[0];return a(h)||(h=f[0]=new C),h.x=o.x,h.y=o.y,h.z=o.z,h.w=-r.dot(o,e),r.multiplyByScalar(d,p,o),r.add(y,o,o),r.subtract(o,e,o),r.cross(n,o,o),r.normalize(o,o),h=f[1],a(h)||(h=f[1]=new C),h.x=o.x,h.y=o.y,h.z=o.z,h.w=-r.dot(o,e),r.multiplyByScalar(n,_,o),r.add(y,o,o),r.subtract(o,e,o),r.cross(d,o,o),r.normalize(o,o),h=f[2],a(h)||(h=f[2]=new C),h.x=o.x,h.y=o.y,h.z=o.z,h.w=-r.dot(o,e),r.multiplyByScalar(n,s,o),r.add(y,o,o),r.subtract(o,e,o),r.cross(o,d,o),r.normalize(o,o),h=f[3],a(h)||(h=f[3]=new C),h.x=o.x,h.y=o.y,h.z=o.z,h.w=-r.dot(o,e),h=f[4],a(h)||(h=f[4]=new C),h.x=t.x,h.y=t.y,h.z=t.z,h.w=-r.dot(t,y),r.negate(t,o),h=f[5],a(h)||(h=f[5]=new C),h.x=o.x,h.y=o.y,h.z=o.z,h.w=-r.dot(o,l),this._cullingVolume};k.prototype.getPixelDimensions=function(e,t,n,f,s){if(te(this),!a(e)||!a(t))throw new w("Both drawingBufferWidth and drawingBufferHeight are required.");if(e<=0)throw new w("drawingBufferWidth must be greater than zero.");if(t<=0)throw new w("drawingBufferHeight must be greater than zero.");if(!a(n))throw new w("distance is required.");if(!a(f))throw new w("pixelRatio is required");if(f<=0)throw new w("pixelRatio must be greater than zero.");if(!a(s))throw new w("A result object is required.");let _=1/this.near,p=this.top*_,m=2*f*n*p/t;p=this.right*_;let i=2*f*n*p/e;return s.x=i,s.y=m,s};k.prototype.clone=function(e){return a(e)||(e=new k),e.right=this.right,e.left=this.left,e.top=this.top,e.bottom=this.bottom,e.near=this.near,e.far=this.far,e._left=void 0,e._right=void 0,e._top=void 0,e._bottom=void 0,e._near=void 0,e._far=void 0,e};k.prototype.equals=function(e){return a(e)&&e instanceof k&&this.right===e.right&&this.left===e.left&&this.top===e.top&&this.bottom===e.bottom&&this.near===e.near&&this.far===e.far};k.prototype.equalsEpsilon=function(e,t,n){return e===this||a(e)&&e instanceof k&&O.equalsEpsilon(this.right,e.right,t,n)&&O.equalsEpsilon(this.left,e.left,t,n)&&O.equalsEpsilon(this.top,e.top,t,n)&&O.equalsEpsilon(this.bottom,e.bottom,t,n)&&O.equalsEpsilon(this.near,e.near,t,n)&&O.equalsEpsilon(this.far,e.far,t,n)};var se=k;function v(e){e=e??A.EMPTY_OBJECT,this._offCenterFrustum=new se,this.fov=e.fov,this._fov=void 0,this._fovy=void 0,this._sseDenominator=void 0,this.aspectRatio=e.aspectRatio,this._aspectRatio=void 0,this.near=e.near??1,this._near=this.near,this.far=e.far??5e8,this._far=this.far,this.xOffset=e.xOffset??0,this._xOffset=this.xOffset,this.yOffset=e.yOffset??0,this._yOffset=this.yOffset}v.packedLength=6;v.pack=function(e,t,n){return b.typeOf.object("value",e),b.defined("array",t),n=n??0,t[n++]=e.fov,t[n++]=e.aspectRatio,t[n++]=e.near,t[n++]=e.far,t[n++]=e.xOffset,t[n]=e.yOffset,t};v.unpack=function(e,t,n){return b.defined("array",e),t=t??0,a(n)||(n=new v),n.fov=e[t++],n.aspectRatio=e[t++],n.near=e[t++],n.far=e[t++],n.xOffset=e[t++],n.yOffset=e[t],n};function T(e){if(!a(e.fov)||!a(e.aspectRatio)||!a(e.near)||!a(e.far))throw new w("fov, aspectRatio, near, or far parameters are not set.");if(!(e.fov!==e._fov||e.aspectRatio!==e._aspectRatio||e.near!==e._near||e.far!==e._far||e.xOffset!==e._xOffset||e.yOffset!==e._yOffset))return;if(b.typeOf.number.greaterThanOrEquals("fov",e.fov,0),b.typeOf.number.lessThan("fov",e.fov,Math.PI),b.typeOf.number.greaterThanOrEquals("aspectRatio",e.aspectRatio,0),b.typeOf.number.greaterThanOrEquals("near",e.near,0),e.near>e.far)throw new w("near must be less than far.");e._aspectRatio=e.aspectRatio,e._fov=e.fov,e._fovy=e.aspectRatio<=1?e.fov:Math.atan(Math.tan(e.fov*.5)/e.aspectRatio)*2,e._near=e.near,e._far=e.far,e._sseDenominator=2*Math.tan(.5*e._fovy),e._xOffset=e.xOffset,e._yOffset=e.yOffset;let n=e._offCenterFrustum;n.top=e.near*Math.tan(.5*e._fovy),n.bottom=-n.top,n.right=e.aspectRatio*n.top,n.left=-n.right,n.near=e.near,n.far=e.far,n.right+=e.xOffset,n.left+=e.xOffset,n.top+=e.yOffset,n.bottom+=e.yOffset}Object.defineProperties(v.prototype,{projectionMatrix:{get:function(){return T(this),this._offCenterFrustum.projectionMatrix}},infiniteProjectionMatrix:{get:function(){return T(this),this._offCenterFrustum.infiniteProjectionMatrix}},fovy:{get:function(){return T(this),this._fovy}},sseDenominator:{get:function(){return T(this),this._sseDenominator}},offCenterFrustum:{get:function(){return T(this),this._offCenterFrustum}}});v.prototype.computeCullingVolume=function(e,t,n){return T(this),this._offCenterFrustum.computeCullingVolume(e,t,n)};v.prototype.getPixelDimensions=function(e,t,n,f,s){return T(this),this._offCenterFrustum.getPixelDimensions(e,t,n,f,s)};v.prototype.clone=function(e){return a(e)||(e=new v),e.aspectRatio=this.aspectRatio,e.fov=this.fov,e.near=this.near,e.far=this.far,e._aspectRatio=void 0,e._fov=void 0,e._near=void 0,e._far=void 0,this._offCenterFrustum.clone(e._offCenterFrustum),e};v.prototype.equals=function(e){return!a(e)||!(e instanceof v)?!1:(T(this),T(e),this.fov===e.fov&&this.aspectRatio===e.aspectRatio&&this._offCenterFrustum.equals(e._offCenterFrustum))};v.prototype.equalsEpsilon=function(e,t,n){return!a(e)||!(e instanceof v)?!1:(T(this),T(e),O.equalsEpsilon(this.fov,e.fov,t,n)&&O.equalsEpsilon(this.aspectRatio,e.aspectRatio,t,n)&&this._offCenterFrustum.equalsEpsilon(e._offCenterFrustum,t,n))};var L=v;var I=0,Pe=1;function u(e){b.typeOf.object("options",e),b.typeOf.object("options.frustum",e.frustum),b.typeOf.object("options.origin",e.origin),b.typeOf.object("options.orientation",e.orientation);let t=e.frustum,n=e.orientation,f=e.origin,s=e.vertexFormat??N.DEFAULT,_=e._drawNearPlane??!0,p,m;t instanceof L?(p=I,m=L.packedLength):t instanceof V&&(p=Pe,m=V.packedLength),this._frustumType=p,this._frustum=t.clone(),this._origin=r.clone(f),this._orientation=D.clone(n),this._drawNearPlane=_,this._vertexFormat=s,this._workerName="createFrustumGeometry",this.packedLength=2+m+r.packedLength+D.packedLength+N.packedLength}u.pack=function(e,t,n){b.typeOf.object("value",e),b.defined("array",t),n=n??0;let f=e._frustumType,s=e._frustum;return t[n++]=f,f===I?(L.pack(s,t,n),n+=L.packedLength):(V.pack(s,t,n),n+=V.packedLength),r.pack(e._origin,t,n),n+=r.packedLength,D.pack(e._orientation,t,n),n+=D.packedLength,N.pack(e._vertexFormat,t,n),n+=N.packedLength,t[n]=e._drawNearPlane?1:0,t};var ze=new L,xe=new V,ve=new D,Re=new r,Te=new N;u.unpack=function(e,t,n){b.defined("array",e),t=t??0;let f=e[t++],s;f===I?(s=L.unpack(e,t,ze),t+=L.packedLength):(s=V.unpack(e,t,xe),t+=V.packedLength);let _=r.unpack(e,t,Re);t+=r.packedLength;let p=D.unpack(e,t,ve);t+=D.packedLength;let m=N.unpack(e,t,Te);t+=N.packedLength;let i=e[t]===1;if(!a(n))return new u({frustum:s,origin:_,orientation:p,vertexFormat:m,_drawNearPlane:i});let c=f===n._frustumType?n._frustum:void 0;return n._frustum=s.clone(c),n._frustumType=f,n._origin=r.clone(_,n._origin),n._orientation=D.clone(p,n._orientation),n._vertexFormat=N.clone(m,n._vertexFormat),n._drawNearPlane=i,n};function W(e,t,n,f,s,_,p,m){let i=e/3*2;for(let c=0;c<4;++c)a(t)&&(t[e]=_.x,t[e+1]=_.y,t[e+2]=_.z),a(n)&&(n[e]=p.x,n[e+1]=p.y,n[e+2]=p.z),a(f)&&(f[e]=m.x,f[e+1]=m.y,f[e+2]=m.z),e+=3;s[i]=0,s[i+1]=0,s[i+2]=1,s[i+3]=0,s[i+4]=1,s[i+5]=1,s[i+6]=0,s[i+7]=1}var qe=new G,Me=new F,ne=new F,pe=new r,le=new r,we=new r,ke=new r,Se=new r,De=new r,U=new Array(3),Z=new Array(4);Z[0]=new C(-1,-1,1,1);Z[1]=new C(1,-1,1,1);Z[2]=new C(1,1,1,1);Z[3]=new C(-1,1,1,1);var _e=new Array(4);for(let e=0;e<4;++e)_e[e]=new C;u._computeNearFarPlanes=function(e,t,n,f,s,_,p,m){let i=G.fromQuaternion(t,qe),c=_??pe,d=p??le,y=m??we;c=G.getColumn(i,0,c),d=G.getColumn(i,1,d),y=G.getColumn(i,2,y),r.normalize(c,c),r.normalize(d,d),r.normalize(y,y),r.negate(c,c);let l=F.computeView(e,y,d,c,Me),o,h,q=f.projectionMatrix;if(n===I){let P=F.multiply(q,l,ne);h=F.inverse(P,ne)}else o=F.inverseTransformation(l,ne);a(h)?(U[0]=f.near,U[1]=f.far):(U[0]=0,U[1]=f.near,U[2]=f.far);for(let P=0;P<2;++P)for(let z=0;z<4;++z){let g=C.clone(Z[z],_e[z]);if(a(h)){g=F.multiplyByVector(h,g,g);let S=1/g.w;r.multiplyByScalar(g,S,g),r.subtract(g,e,g),r.normalize(g,g);let Y=r.dot(y,g);r.multiplyByScalar(g,U[P]/Y,g),r.add(g,e,g)}else{let S=f.offCenterFrustum;a(S)&&(f=S);let Y=U[P],K=U[P+1];g.x=(g.x*(f.right-f.left)+f.left+f.right)*.5,g.y=(g.y*(f.top-f.bottom)+f.bottom+f.top)*.5,g.z=(g.z*(Y-K)-Y-K)*.5,g.w=1,F.multiplyByVector(o,g,g)}s[12*P+z*3]=g.x,s[12*P+z*3+1]=g.y,s[12*P+z*3+2]=g.z}};u.createGeometry=function(e){let t=e._frustumType,n=e._frustum,f=e._origin,s=e._orientation,_=e._drawNearPlane,p=e._vertexFormat,m=_?6:5,i=new Float64Array(3*4*6);u._computeNearFarPlanes(f,s,t,n,i);let c=3*4*2;i[c]=i[3*4],i[c+1]=i[3*4+1],i[c+2]=i[3*4+2],i[c+3]=i[0],i[c+4]=i[1],i[c+5]=i[2],i[c+6]=i[3*3],i[c+7]=i[3*3+1],i[c+8]=i[3*3+2],i[c+9]=i[3*7],i[c+10]=i[3*7+1],i[c+11]=i[3*7+2],c+=3*4,i[c]=i[3*5],i[c+1]=i[3*5+1],i[c+2]=i[3*5+2],i[c+3]=i[3],i[c+4]=i[4],i[c+5]=i[5],i[c+6]=i[0],i[c+7]=i[1],i[c+8]=i[2],i[c+9]=i[3*4],i[c+10]=i[3*4+1],i[c+11]=i[3*4+2],c+=3*4,i[c]=i[3],i[c+1]=i[4],i[c+2]=i[5],i[c+3]=i[3*5],i[c+4]=i[3*5+1],i[c+5]=i[3*5+2],i[c+6]=i[3*6],i[c+7]=i[3*6+1],i[c+8]=i[3*6+2],i[c+9]=i[3*2],i[c+10]=i[3*2+1],i[c+11]=i[3*2+2],c+=3*4,i[c]=i[3*2],i[c+1]=i[3*2+1],i[c+2]=i[3*2+2],i[c+3]=i[3*6],i[c+4]=i[3*6+1],i[c+5]=i[3*6+2],i[c+6]=i[3*7],i[c+7]=i[3*7+1],i[c+8]=i[3*7+2],i[c+9]=i[3*3],i[c+10]=i[3*3+1],i[c+11]=i[3*3+2],_||(i=i.subarray(3*4));let d=new ae({position:new J({componentDatatype:H.DOUBLE,componentsPerAttribute:3,values:i})});if(a(p.normal)||a(p.tangent)||a(p.bitangent)||a(p.st)){let l=a(p.normal)?new Float32Array(12*m):void 0,o=a(p.tangent)?new Float32Array(3*4*m):void 0,h=a(p.bitangent)?new Float32Array(3*4*m):void 0,q=a(p.st)?new Float32Array(2*4*m):void 0,P=pe,z=le,g=we,S=r.negate(P,ke),Y=r.negate(z,Se),K=r.negate(g,De);c=0,_&&(W(c,l,o,h,q,K,P,z),c+=3*4),W(c,l,o,h,q,g,S,z),c+=3*4,W(c,l,o,h,q,S,K,z),c+=3*4,W(c,l,o,h,q,Y,K,S),c+=3*4,W(c,l,o,h,q,P,g,z),c+=3*4,W(c,l,o,h,q,z,g,S),a(l)&&(d.normal=new J({componentDatatype:H.FLOAT,componentsPerAttribute:3,values:l})),a(o)&&(d.tangent=new J({componentDatatype:H.FLOAT,componentsPerAttribute:3,values:o})),a(h)&&(d.bitangent=new J({componentDatatype:H.FLOAT,componentsPerAttribute:3,values:h})),a(q)&&(d.st=new J({componentDatatype:H.FLOAT,componentsPerAttribute:2,values:q}))}let y=new Uint16Array(6*m);for(let l=0;l<m;++l){let o=l*6,h=l*4;y[o]=h,y[o+1]=h+1,y[o+2]=h+2,y[o+3]=h,y[o+4]=h+2,y[o+5]=h+3}return new re({attributes:d,indices:y,primitiveType:oe.TRIANGLES,boundingSphere:ie.fromVertices(i)})};var Et=u;export{V as a,L as b,Et as c};
