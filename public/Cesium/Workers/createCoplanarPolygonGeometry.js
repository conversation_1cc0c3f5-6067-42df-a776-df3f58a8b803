/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as et}from"./chunk-YZ5FJSO3.js";import{a as Q}from"./chunk-GMDXIP2F.js";import"./chunk-UQ3ABODK.js";import{a as u}from"./chunk-OJD6C7EN.js";import"./chunk-PJQOB6JM.js";import{a as I}from"./chunk-6NNCMAGT.js";import{a as $}from"./chunk-X76FJHF4.js";import"./chunk-NYNUONZK.js";import"./chunk-5YCKGPZ3.js";import{a as f}from"./chunk-KA7ZX4BQ.js";import"./chunk-C7EXJG2O.js";import"./chunk-LM5DN6BS.js";import{b as ot}from"./chunk-452MW5E6.js";import{a as tt}from"./chunk-IOJRZZG4.js";import"./chunk-QF5X4OGE.js";import"./chunk-S3NLG5WM.js";import"./chunk-AAOMPF7M.js";import{a as J}from"./chunk-PFXLBIMV.js";import{a as X}from"./chunk-PSBTKXXJ.js";import{b as W,c as Y,d as z}from"./chunk-UJOKCDQH.js";import{d as K}from"./chunk-KYREMICR.js";import{f as q}from"./chunk-VBRVI5XI.js";import{a as B}from"./chunk-5ZFOKSDK.js";import{a as i,c as H,d as m,e as g,f as U}from"./chunk-7L2LUDC3.js";import{a as V}from"./chunk-77KFIUJG.js";import"./chunk-7W3OTLHS.js";import"./chunk-X52A3GF7.js";import{b as G}from"./chunk-PX3QTMVS.js";import{e as d}from"./chunk-FE4HG5RY.js";var st=new i,at=new et,pt=new H,lt=new H,mt=new i,ft=new i,ut=new i,M=new i,yt=new i,ht=new i,nt=new q,dt=new g,gt=new g,Pt=new i;function wt(e,t,o,a,y,_,r,n,p){let A=e.positions,c=ot.triangulate(e.positions2D,e.holes);c.length<3&&(c=[0,1,2]);let P=J.createTypedArray(A.length,c.length);P.set(c);let E=dt;if(a!==0){let h=q.fromAxisAngle(r,a,nt);if(E=g.fromQuaternion(h,E),t.tangent||t.bitangent){h=q.fromAxisAngle(r,-a,nt);let O=g.fromQuaternion(h,gt);n=i.normalize(g.multiplyByVector(O,n,n),n),t.bitangent&&(p=i.normalize(i.cross(r,n,p),p))}}else E=g.clone(g.IDENTITY,E);let N=lt;t.st&&(N.x=o.x,N.y=o.y);let b=A.length,k=b*3,C=new Float64Array(k),L=t.normal?new Float32Array(k):void 0,D=t.tangent?new Float32Array(k):void 0,F=t.bitangent?new Float32Array(k):void 0,T=t.st?new Float32Array(b*2):void 0,j=0,R=0,l=0,S=0,s=0;for(let h=0;h<b;h++){let O=A[h];if(C[j++]=O.x,C[j++]=O.y,C[j++]=O.z,t.st)if(d(y)&&y.positions.length===b)T[s++]=y.positions[h].x,T[s++]=y.positions[h].y;else{let it=g.multiplyByVector(E,O,st),v=_(it,pt);H.subtract(v,N,v);let rt=V.clamp(v.x/o.width,0,1),ct=V.clamp(v.y/o.height,0,1);T[s++]=rt,T[s++]=ct}t.normal&&(L[R++]=r.x,L[R++]=r.y,L[R++]=r.z),t.tangent&&(D[S++]=n.x,D[S++]=n.y,D[S++]=n.z),t.bitangent&&(F[l++]=p.x,F[l++]=p.y,F[l++]=p.z)}let w=new X;return t.position&&(w.position=new z({componentDatatype:B.DOUBLE,componentsPerAttribute:3,values:C})),t.normal&&(w.normal=new z({componentDatatype:B.FLOAT,componentsPerAttribute:3,values:L})),t.tangent&&(w.tangent=new z({componentDatatype:B.FLOAT,componentsPerAttribute:3,values:D})),t.bitangent&&(w.bitangent=new z({componentDatatype:B.FLOAT,componentsPerAttribute:3,values:F})),t.st&&(w.st=new z({componentDatatype:B.FLOAT,componentsPerAttribute:2,values:T})),new Y({attributes:w,indices:P,primitiveType:W.TRIANGLES})}function x(e){e=e??U.EMPTY_OBJECT;let t=e.polygonHierarchy,o=e.textureCoordinates;G.defined("options.polygonHierarchy",t);let a=e.vertexFormat??f.DEFAULT;this._vertexFormat=f.clone(a),this._polygonHierarchy=t,this._stRotation=e.stRotation??0,this._ellipsoid=m.clone(e.ellipsoid??m.default),this._workerName="createCoplanarPolygonGeometry",this._textureCoordinates=o,this.packedLength=u.computeHierarchyPackedLength(t,i)+f.packedLength+m.packedLength+(d(o)?u.computeHierarchyPackedLength(o,H):1)+2}x.fromPositions=function(e){e=e??U.EMPTY_OBJECT,G.defined("options.positions",e.positions);let t={polygonHierarchy:{positions:e.positions},vertexFormat:e.vertexFormat,stRotation:e.stRotation,ellipsoid:e.ellipsoid,textureCoordinates:e.textureCoordinates};return new x(t)};x.pack=function(e,t,o){return G.typeOf.object("value",e),G.defined("array",t),o=o??0,o=u.packPolygonHierarchy(e._polygonHierarchy,t,o,i),m.pack(e._ellipsoid,t,o),o+=m.packedLength,f.pack(e._vertexFormat,t,o),o+=f.packedLength,t[o++]=e._stRotation,d(e._textureCoordinates)?o=u.packPolygonHierarchy(e._textureCoordinates,t,o,H):t[o++]=-1,t[o++]=e.packedLength,t};var _t=m.clone(m.UNIT_SPHERE),At=new f,bt={polygonHierarchy:{}};x.unpack=function(e,t,o){G.defined("array",e),t=t??0;let a=u.unpackPolygonHierarchy(e,t,i);t=a.startingIndex,delete a.startingIndex;let y=m.unpack(e,t,_t);t+=m.packedLength;let _=f.unpack(e,t,At);t+=f.packedLength;let r=e[t++],n=e[t]===-1?void 0:u.unpackPolygonHierarchy(e,t,H);d(n)?(t=n.startingIndex,delete n.startingIndex):t++;let p=e[t++];return d(o)||(o=new x(bt)),o._polygonHierarchy=a,o._ellipsoid=m.clone(y,o._ellipsoid),o._vertexFormat=f.clone(_,o._vertexFormat),o._stRotation=r,o._textureCoordinates=n,o.packedLength=p,o};x.createGeometry=function(e){let t=e._vertexFormat,o=e._polygonHierarchy,a=e._stRotation,y=e._textureCoordinates,_=d(y),r=o.positions;if(r=tt(r,i.equalsEpsilon,!0),r.length<3)return;let n=mt,p=ft,A=ut,c=yt,P=ht;if(!Q.computeProjectTo2DArguments(r,M,c,P))return;if(n=i.cross(c,P,n),n=i.normalize(n,n),!i.equalsEpsilon(M,i.ZERO,V.EPSILON6)){let s=e._ellipsoid.geodeticSurfaceNormal(M,Pt);i.dot(n,s)<0&&(n=i.negate(n,n),c=i.negate(c,c))}let N=Q.createProjectPointsTo2DFunction(M,c,P),b=Q.createProjectPointTo2DFunction(M,c,P);t.tangent&&(p=i.clone(c,p)),t.bitangent&&(A=i.clone(P,A));let k=u.polygonsFromHierarchy(o,_,N,!1),C=k.hierarchy,L=k.polygons,D=function(s){return s},F=_?u.polygonsFromHierarchy(y,!0,D,!1).polygons:void 0;if(C.length===0)return;r=C[0].outerRing;let T=K.fromPoints(r),j=u.computeBoundingRectangle(n,b,r,a,at),R=[];for(let s=0;s<L.length;s++){let w=new I({geometry:wt(L[s],t,j,a,_?F[s]:void 0,b,n,p,A)});R.push(w)}let l=$.combineInstances(R)[0];l.attributes.position.values=new Float64Array(l.attributes.position.values),l.indices=J.createTypedArray(l.attributes.position.values.length/3,l.indices);let S=l.attributes;return t.position||delete S.position,new Y({attributes:S,indices:l.indices,primitiveType:l.primitiveType,boundingSphere:T})};var Z=x;function kt(e,t){return d(t)&&(e=Z.unpack(e,t)),Z.createGeometry(e)}var Xt=kt;export{Xt as default};
