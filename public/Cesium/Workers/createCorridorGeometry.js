/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as M}from"./chunk-7UX2ZCQK.js";import{a as ut}from"./chunk-2BFZMXUG.js";import"./chunk-WCO3MWIK.js";import"./chunk-BQNAZUCV.js";import{a as bt}from"./chunk-O4MLFQKO.js";import{a as J}from"./chunk-KA7ZX4BQ.js";import"./chunk-C7EXJG2O.js";import"./chunk-LM5DN6BS.js";import{b as _t}from"./chunk-452MW5E6.js";import{a as Pt}from"./chunk-IOJRZZG4.js";import"./chunk-QF5X4OGE.js";import"./chunk-S3NLG5WM.js";import"./chunk-AAOMPF7M.js";import{a as Nt}from"./chunk-PFXLBIMV.js";import{a as Dt}from"./chunk-PSBTKXXJ.js";import{b as Mt,c as Ct,d as F}from"./chunk-UJOKCDQH.js";import{d as St}from"./chunk-KYREMICR.js";import{h as Et}from"./chunk-VBRVI5XI.js";import{a as v}from"./chunk-5ZFOKSDK.js";import{a as o,b as At,d as Y,f as Tt}from"./chunk-7L2LUDC3.js";import{a as it}from"./chunk-77KFIUJG.js";import"./chunk-7W3OTLHS.js";import"./chunk-X52A3GF7.js";import{b as ot}from"./chunk-PX3QTMVS.js";import{e as Q}from"./chunk-FE4HG5RY.js";var Rt=new o,kt=new o,yt=new o,wt=new o,jt=new o,Ht=new o,st=new o,dt=new o;function Ut(t,e){for(let i=0;i<t.length;i++)t[i]=e.scaleToGeodeticSurface(t[i],t[i]);return t}function X(t,e,i,l,d,f){let h=t.normals,O=t.tangents,g=t.bitangents,r=o.normalize(o.cross(i,e,st),st);f.normal&&M.addAttribute(h,e,l,d),f.tangent&&M.addAttribute(O,r,l,d),f.bitangent&&M.addAttribute(g,i,l,d)}function Bt(t,e,i){let l=t.positions,d=t.corners,f=t.endPositions,h=t.lefts,O=t.normals,g=new Dt,r,a=0,s=0,n,A=0,u;for(n=0;n<l.length;n+=2)u=l[n].length-3,a+=u,A+=u*2,s+=l[n+1].length-3;for(a+=3,s+=3,n=0;n<d.length;n++){r=d[n];let c=d[n].leftPositions;Q(c)?(u=c.length,a+=u,A+=u):(u=d[n].rightPositions.length,s+=u,A+=u)}let b=Q(f),p;b&&(p=f[0].length-3,a+=p,s+=p,p/=3,A+=p*6);let z=a+s,m=new Float64Array(z),D=e.normal?new Float32Array(z):void 0,V=e.tangent?new Float32Array(z):void 0,rt=e.bitangent?new Float32Array(z):void 0,R={normals:D,tangents:V,bitangents:rt},y=0,E=z-1,S,C,H,q,P=Rt,w=kt,et,nt,pt=p/2,_=Nt.createTypedArray(z/3,A),T=0;if(b){nt=yt,et=wt;let c=f[0];for(P=o.fromArray(O,0,P),w=o.fromArray(h,0,w),n=0;n<pt;n++)nt=o.fromArray(c,(pt-1-n)*3,nt),et=o.fromArray(c,(pt+n)*3,et),M.addAttribute(m,et,y),M.addAttribute(m,nt,void 0,E),X(R,P,w,y,E,e),C=y/3,q=C+1,S=(E-2)/3,H=S-1,_[T++]=S,_[T++]=C,_[T++]=H,_[T++]=H,_[T++]=C,_[T++]=q,y+=3,E-=3}let mt=0,ft=0,at=l[mt++],j=l[mt++];m.set(at,y),m.set(j,E-j.length+1),w=o.fromArray(h,ft,w);let lt,ct;for(u=j.length-3,n=0;n<u;n+=3)lt=i.geodeticSurfaceNormal(o.fromArray(at,n,st),st),ct=i.geodeticSurfaceNormal(o.fromArray(j,u-n,dt),dt),P=o.normalize(o.add(lt,ct,P),P),X(R,P,w,y,E,e),C=y/3,q=C+1,S=(E-2)/3,H=S-1,_[T++]=S,_[T++]=C,_[T++]=H,_[T++]=H,_[T++]=C,_[T++]=q,y+=3,E-=3;for(lt=i.geodeticSurfaceNormal(o.fromArray(at,u,st),st),ct=i.geodeticSurfaceNormal(o.fromArray(j,u,dt),dt),P=o.normalize(o.add(lt,ct,P),P),ft+=3,n=0;n<d.length;n++){let c;r=d[n];let Z=r.leftPositions,$=r.rightPositions,L,U,N=Ht,k=yt,B=wt;if(P=o.fromArray(O,ft,P),Q(Z)){for(X(R,P,w,void 0,E,e),E-=3,L=q,U=H,c=0;c<Z.length/3;c++)N=o.fromArray(Z,c*3,N),_[T++]=L,_[T++]=U-c-1,_[T++]=U-c,M.addAttribute(m,N,void 0,E),k=o.fromArray(m,(U-c-1)*3,k),B=o.fromArray(m,L*3,B),w=o.normalize(o.subtract(k,B,w),w),X(R,P,w,void 0,E,e),E-=3;N=o.fromArray(m,L*3,N),k=o.subtract(o.fromArray(m,U*3,k),N,k),B=o.subtract(o.fromArray(m,(U-c)*3,B),N,B),w=o.normalize(o.add(k,B,w),w),X(R,P,w,y,void 0,e),y+=3}else{for(X(R,P,w,y,void 0,e),y+=3,L=H,U=q,c=0;c<$.length/3;c++)N=o.fromArray($,c*3,N),_[T++]=L,_[T++]=U+c,_[T++]=U+c+1,M.addAttribute(m,N,y),k=o.fromArray(m,L*3,k),B=o.fromArray(m,(U+c)*3,B),w=o.normalize(o.subtract(k,B,w),w),X(R,P,w,y,void 0,e),y+=3;N=o.fromArray(m,L*3,N),k=o.subtract(o.fromArray(m,(U+c)*3,k),N,k),B=o.subtract(o.fromArray(m,U*3,B),N,B),w=o.normalize(o.negate(o.add(B,k,w),w),w),X(R,P,w,void 0,E,e),E-=3}for(at=l[mt++],j=l[mt++],at.splice(0,3),j.splice(j.length-3,3),m.set(at,y),m.set(j,E-j.length+1),u=j.length-3,ft+=3,w=o.fromArray(h,ft,w),c=0;c<j.length;c+=3)lt=i.geodeticSurfaceNormal(o.fromArray(at,c,st),st),ct=i.geodeticSurfaceNormal(o.fromArray(j,u-c,dt),dt),P=o.normalize(o.add(lt,ct,P),P),X(R,P,w,y,E,e),q=y/3,C=q-1,H=(E-2)/3,S=H+1,_[T++]=S,_[T++]=C,_[T++]=H,_[T++]=H,_[T++]=C,_[T++]=q,y+=3,E-=3;y-=3,E+=3}if(P=o.fromArray(O,O.length-3,P),X(R,P,w,y,E,e),b){y+=3,E-=3,nt=yt,et=wt;let c=f[1];for(n=0;n<pt;n++)nt=o.fromArray(c,(p-n-1)*3,nt),et=o.fromArray(c,n*3,et),M.addAttribute(m,nt,void 0,E),M.addAttribute(m,et,y),X(R,P,w,y,E,e),q=y/3,C=q-1,H=(E-2)/3,S=H+1,_[T++]=S,_[T++]=C,_[T++]=H,_[T++]=H,_[T++]=C,_[T++]=q,y+=3,E-=3}if(g.position=new F({componentDatatype:v.DOUBLE,componentsPerAttribute:3,values:m}),e.st){let c=new Float32Array(z/3*2),Z,$,L=0;if(b){a/=3,s/=3;let U=Math.PI/(p+1);$=1/(a-p+1),Z=1/(s-p+1);let N,k=p/2;for(n=k+1;n<p+1;n++)N=it.PI_OVER_TWO+U*n,c[L++]=Z*(1+Math.cos(N)),c[L++]=.5*(1+Math.sin(N));for(n=1;n<s-p+1;n++)c[L++]=n*Z,c[L++]=0;for(n=p;n>k;n--)N=it.PI_OVER_TWO-n*U,c[L++]=1-Z*(1+Math.cos(N)),c[L++]=.5*(1+Math.sin(N));for(n=k;n>0;n--)N=it.PI_OVER_TWO-U*n,c[L++]=1-$*(1+Math.cos(N)),c[L++]=.5*(1+Math.sin(N));for(n=a-p;n>0;n--)c[L++]=n*$,c[L++]=1;for(n=1;n<k+1;n++)N=it.PI_OVER_TWO+U*n,c[L++]=$*(1+Math.cos(N)),c[L++]=.5*(1+Math.sin(N))}else{for(a/=3,s/=3,$=1/(a-1),Z=1/(s-1),n=0;n<s;n++)c[L++]=n*Z,c[L++]=0;for(n=a;n>0;n--)c[L++]=(n-1)*$,c[L++]=1}g.st=new F({componentDatatype:v.FLOAT,componentsPerAttribute:2,values:c})}return e.normal&&(g.normal=new F({componentDatatype:v.FLOAT,componentsPerAttribute:3,values:R.normals})),e.tangent&&(g.tangent=new F({componentDatatype:v.FLOAT,componentsPerAttribute:3,values:R.tangents})),e.bitangent&&(g.bitangent=new F({componentDatatype:v.FLOAT,componentsPerAttribute:3,values:R.bitangents})),{attributes:g,indices:_}}function qt(t,e){if(!e.normal&&!e.tangent&&!e.bitangent&&!e.st)return t;let i=t.position.values,l,d;(e.normal||e.bitangent)&&(l=t.normal.values,d=t.bitangent.values);let f=t.position.values.length/18,h=f*3,O=f*2,g=h*2,r;if(e.normal||e.bitangent||e.tangent){let a=e.normal?new Float32Array(h*6):void 0,s=e.tangent?new Float32Array(h*6):void 0,n=e.bitangent?new Float32Array(h*6):void 0,A=Rt,u=kt,b=yt,p=wt,z=jt,m=Ht,D=g;for(r=0;r<h;r+=3){let V=D+g;A=o.fromArray(i,r,A),u=o.fromArray(i,r+h,u),b=o.fromArray(i,(r+3)%h,b),u=o.subtract(u,A,u),b=o.subtract(b,A,b),p=o.normalize(o.cross(u,b,p),p),e.normal&&(M.addAttribute(a,p,V),M.addAttribute(a,p,V+3),M.addAttribute(a,p,D),M.addAttribute(a,p,D+3)),(e.tangent||e.bitangent)&&(m=o.fromArray(l,r,m),e.bitangent&&(M.addAttribute(n,m,V),M.addAttribute(n,m,V+3),M.addAttribute(n,m,D),M.addAttribute(n,m,D+3)),e.tangent&&(z=o.normalize(o.cross(m,p,z),z),M.addAttribute(s,z,V),M.addAttribute(s,z,V+3),M.addAttribute(s,z,D),M.addAttribute(s,z,D+3))),D+=6}if(e.normal){for(a.set(l),r=0;r<h;r+=3)a[r+h]=-l[r],a[r+h+1]=-l[r+1],a[r+h+2]=-l[r+2];t.normal.values=a}else t.normal=void 0;if(e.bitangent?(n.set(d),n.set(d,h),t.bitangent.values=n):t.bitangent=void 0,e.tangent){let V=t.tangent.values;s.set(V),s.set(V,h),t.tangent.values=s}}if(e.st){let a=t.st.values,s=new Float32Array(O*6);s.set(a),s.set(a,O);let n=O*2;for(let A=0;A<2;A++){for(s[n++]=a[0],s[n++]=a[1],r=2;r<O;r+=2){let u=a[r],b=a[r+1];s[n++]=u,s[n++]=b,s[n++]=u,s[n++]=b}s[n++]=a[0],s[n++]=a[1]}t.st.values=s}return t}function Ot(t,e,i){i[e++]=t[0],i[e++]=t[1],i[e++]=t[2];for(let l=3;l<t.length;l+=3){let d=t[l],f=t[l+1],h=t[l+2];i[e++]=d,i[e++]=f,i[e++]=h,i[e++]=d,i[e++]=f,i[e++]=h}return i[e++]=t[0],i[e++]=t[1],i[e++]=t[2],i}function Jt(t,e){let i=new J({position:e.position,normal:e.normal||e.bitangent||t.shadowVolume,tangent:e.tangent,bitangent:e.normal||e.bitangent,st:e.st}),l=t.ellipsoid,d=M.computePositions(t),f=Bt(d,i,l),h=t.height,O=t.extrudedHeight,g=f.attributes,r=f.indices,a=g.position.values,s=a.length,n=new Float64Array(s*6),A=new Float64Array(s);A.set(a);let u=new Float64Array(s*4);a=_t.scaleToGeodeticHeight(a,h,l),u=Ot(a,0,u),A=_t.scaleToGeodeticHeight(A,O,l),u=Ot(A,s*2,u),n.set(a),n.set(A,s),n.set(u,s*2),g.position.values=n,g=qt(g,e);let b,p=s/3;if(t.shadowVolume){let S=g.normal.values;s=S.length;let C=new Float32Array(s*6);for(b=0;b<s;b++)S[b]=-S[b];C.set(S,s),C=Ot(S,s*4,C),g.extrudeDirection=new F({componentDatatype:v.FLOAT,componentsPerAttribute:3,values:C}),e.normal||(g.normal=void 0)}if(Q(t.offsetAttribute)){let S=new Uint8Array(p*6);if(t.offsetAttribute===bt.TOP)S=S.fill(1,0,p).fill(1,p*2,p*4);else{let C=t.offsetAttribute===bt.NONE?0:1;S=S.fill(C)}g.applyOffset=new F({componentDatatype:v.UNSIGNED_BYTE,componentsPerAttribute:1,values:S})}let z=r.length,m=p+p,D=Nt.createTypedArray(n.length/3,z*2+m*3);D.set(r);let V=z;for(b=0;b<z;b+=3){let S=r[b],C=r[b+1],H=r[b+2];D[V++]=H+p,D[V++]=C+p,D[V++]=S+p}let rt,R,y,E;for(b=0;b<m;b+=2)rt=b+m,R=rt+m,y=rt+1,E=R+1,D[V++]=rt,D[V++]=R,D[V++]=y,D[V++]=y,D[V++]=R,D[V++]=E;return{attributes:g,indices:D}}var Vt=new o,ht=new o,K=new At;function zt(t,e,i,l,d,f){let h=o.subtract(e,t,Vt);o.normalize(h,h);let O=i.geodeticSurfaceNormal(t,ht),g=o.cross(h,O,Vt);o.multiplyByScalar(g,l,g);let r=d.latitude,a=d.longitude,s=f.latitude,n=f.longitude;o.add(t,g,ht),i.cartesianToCartographic(ht,K);let A=K.latitude,u=K.longitude;r=Math.min(r,A),a=Math.min(a,u),s=Math.max(s,A),n=Math.max(n,u),o.subtract(t,g,ht),i.cartesianToCartographic(ht,K),A=K.latitude,u=K.longitude,r=Math.min(r,A),a=Math.min(a,u),s=Math.max(s,A),n=Math.max(n,u),d.latitude=r,d.longitude=a,f.latitude=s,f.longitude=n}var G=new o,gt=new o,I=new At,W=new At;function Yt(t,e,i,l,d){t=Ut(t,e);let f=Pt(t,o.equalsEpsilon),h=f.length;if(h<2||i<=0)return new Et;let O=i*.5;I.latitude=Number.POSITIVE_INFINITY,I.longitude=Number.POSITIVE_INFINITY,W.latitude=Number.NEGATIVE_INFINITY,W.longitude=Number.NEGATIVE_INFINITY;let g,r;if(l===ut.ROUNDED){let n=f[0];o.subtract(n,f[1],G),o.normalize(G,G),o.multiplyByScalar(G,O,G),o.add(n,G,gt),e.cartesianToCartographic(gt,K),g=K.latitude,r=K.longitude,I.latitude=Math.min(I.latitude,g),I.longitude=Math.min(I.longitude,r),W.latitude=Math.max(W.latitude,g),W.longitude=Math.max(W.longitude,r)}for(let n=0;n<h-1;++n)zt(f[n],f[n+1],e,O,I,W);let a=f[h-1];o.subtract(a,f[h-2],G),o.normalize(G,G),o.multiplyByScalar(G,O,G),o.add(a,G,gt),zt(a,gt,e,O,I,W),l===ut.ROUNDED&&(e.cartesianToCartographic(gt,K),g=K.latitude,r=K.longitude,I.latitude=Math.min(I.latitude,g),I.longitude=Math.min(I.longitude,r),W.latitude=Math.max(W.latitude,g),W.longitude=Math.max(W.longitude,r));let s=Q(d)?d:new Et;return s.north=W.latitude,s.south=I.latitude,s.east=W.longitude,s.west=I.longitude,s}function tt(t){t=t??Tt.EMPTY_OBJECT;let e=t.positions,i=t.width;ot.defined("options.positions",e),ot.defined("options.width",i);let l=t.height??0,d=t.extrudedHeight??l;this._positions=e,this._ellipsoid=Y.clone(t.ellipsoid??Y.default),this._vertexFormat=J.clone(t.vertexFormat??J.DEFAULT),this._width=i,this._height=Math.max(l,d),this._extrudedHeight=Math.min(l,d),this._cornerType=t.cornerType??ut.ROUNDED,this._granularity=t.granularity??it.RADIANS_PER_DEGREE,this._shadowVolume=t.shadowVolume??!1,this._workerName="createCorridorGeometry",this._offsetAttribute=t.offsetAttribute,this._rectangle=void 0,this.packedLength=1+e.length*o.packedLength+Y.packedLength+J.packedLength+7}tt.pack=function(t,e,i){ot.defined("value",t),ot.defined("array",e),i=i??0;let l=t._positions,d=l.length;e[i++]=d;for(let f=0;f<d;++f,i+=o.packedLength)o.pack(l[f],e,i);return Y.pack(t._ellipsoid,e,i),i+=Y.packedLength,J.pack(t._vertexFormat,e,i),i+=J.packedLength,e[i++]=t._width,e[i++]=t._height,e[i++]=t._extrudedHeight,e[i++]=t._cornerType,e[i++]=t._granularity,e[i++]=t._shadowVolume?1:0,e[i]=t._offsetAttribute??-1,e};var It=Y.clone(Y.UNIT_SPHERE),Wt=new J,x={positions:void 0,ellipsoid:It,vertexFormat:Wt,width:void 0,height:void 0,extrudedHeight:void 0,cornerType:void 0,granularity:void 0,shadowVolume:void 0,offsetAttribute:void 0};tt.unpack=function(t,e,i){ot.defined("array",t),e=e??0;let l=t[e++],d=new Array(l);for(let u=0;u<l;++u,e+=o.packedLength)d[u]=o.unpack(t,e);let f=Y.unpack(t,e,It);e+=Y.packedLength;let h=J.unpack(t,e,Wt);e+=J.packedLength;let O=t[e++],g=t[e++],r=t[e++],a=t[e++],s=t[e++],n=t[e++]===1,A=t[e];return Q(i)?(i._positions=d,i._ellipsoid=Y.clone(f,i._ellipsoid),i._vertexFormat=J.clone(h,i._vertexFormat),i._width=O,i._height=g,i._extrudedHeight=r,i._cornerType=a,i._granularity=s,i._shadowVolume=n,i._offsetAttribute=A===-1?void 0:A,i):(x.positions=d,x.width=O,x.height=g,x.extrudedHeight=r,x.cornerType=a,x.granularity=s,x.shadowVolume=n,x.offsetAttribute=A===-1?void 0:A,new tt(x))};tt.computeRectangle=function(t,e){t=t??Tt.EMPTY_OBJECT;let i=t.positions,l=t.width;ot.defined("options.positions",i),ot.defined("options.width",l);let d=t.ellipsoid??Y.default,f=t.cornerType??ut.ROUNDED;return Yt(i,d,l,f,e)};tt.createGeometry=function(t){let e=t._positions,i=t._width,l=t._ellipsoid;e=Ut(e,l);let d=Pt(e,o.equalsEpsilon);if(d.length<2||i<=0)return;let f=t._height,h=t._extrudedHeight,O=!it.equalsEpsilon(f,h,0,it.EPSILON2),g=t._vertexFormat,r={ellipsoid:l,positions:d,width:i,cornerType:t._cornerType,granularity:t._granularity,saveAttributes:!0},a;if(O)r.height=f,r.extrudedHeight=h,r.shadowVolume=t._shadowVolume,r.offsetAttribute=t._offsetAttribute,a=Jt(r,g);else{let A=M.computePositions(r);if(a=Bt(A,g,l),a.attributes.position.values=_t.scaleToGeodeticHeight(a.attributes.position.values,f,l),Q(t._offsetAttribute)){let u=t._offsetAttribute===bt.NONE?0:1,b=a.attributes.position.values.length,p=new Uint8Array(b/3).fill(u);a.attributes.applyOffset=new F({componentDatatype:v.UNSIGNED_BYTE,componentsPerAttribute:1,values:p})}}let s=a.attributes,n=St.fromVertices(s.position.values,void 0,3);return g.position||(a.attributes.position.values=void 0),new Ct({attributes:s,indices:a.indices,primitiveType:Mt.TRIANGLES,boundingSphere:n,offsetAttribute:t._offsetAttribute})};tt.createShadowVolume=function(t,e,i){let l=t._granularity,d=t._ellipsoid,f=e(l,d),h=i(l,d);return new tt({positions:t._positions,width:t._width,cornerType:t._cornerType,ellipsoid:d,granularity:l,extrudedHeight:f,height:h,vertexFormat:J.POSITION_ONLY,shadowVolume:!0})};Object.defineProperties(tt.prototype,{rectangle:{get:function(){return Q(this._rectangle)||(this._rectangle=Yt(this._positions,this._ellipsoid,this._width,this._cornerType)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return[0,0,0,1,1,0]}}});var Lt=tt;function Gt(t,e){return Q(e)&&(t=Lt.unpack(t,e)),t._ellipsoid=Y.clone(t._ellipsoid),Lt.createGeometry(t)}var Ae=Gt;export{Ae as default};
