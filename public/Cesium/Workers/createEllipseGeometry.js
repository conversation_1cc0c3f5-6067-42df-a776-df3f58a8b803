/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.117
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import {
  EllipseGeometry_default
} from "./chunk-ZBVO332W.js";
import "./chunk-EFLW3JFO.js";
import "./chunk-FNE3YI5Q.js";
import "./chunk-XW5T55IP.js";
import "./chunk-B6DVPPVM.js";
import "./chunk-AWI5FW5A.js";
import "./chunk-S4XDCPKD.js";
import "./chunk-4KIUON73.js";
import "./chunk-PY47RVW2.js";
import "./chunk-7BXRTKIJ.js";
import "./chunk-RYSTSU5Z.js";
import "./chunk-RL73GOEF.js";
import "./chunk-2AGL26L2.js";
import "./chunk-ZH4VWHBN.js";
import "./chunk-YNVHHE7P.js";
import "./chunk-TMMOULW3.js";
import {
  Cartesian3_default,
  Ellipsoid_default
} from "./chunk-K7CQGY7R.js";
import "./chunk-FNL3V3ZY.js";
import "./chunk-PEABJLCK.js";
import "./chunk-WFICTTOE.js";
import "./chunk-UCPPWV64.js";
import "./chunk-U4IMCOF5.js";
import {
  defined_default
} from "./chunk-BDUJXBVF.js";

// packages/engine/Source/Workers/createEllipseGeometry.js
function createEllipseGeometry(ellipseGeometry, offset) {
  if (defined_default(offset)) {
    ellipseGeometry = EllipseGeometry_default.unpack(ellipseGeometry, offset);
  }
  ellipseGeometry._center = Cartesian3_default.clone(ellipseGeometry._center);
  ellipseGeometry._ellipsoid = Ellipsoid_default.clone(ellipseGeometry._ellipsoid);
  return EllipseGeometry_default.createGeometry(ellipseGeometry);
}
var createEllipseGeometry_default = createEllipseGeometry;
export {
  createEllipseGeometry_default as default
};
