/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as i}from"./chunk-22ASS7YB.js";import"./chunk-IQ3P7OYE.js";import"./chunk-O4MLFQKO.js";import"./chunk-PFXLBIMV.js";import"./chunk-PSBTKXXJ.js";import"./chunk-UJOKCDQH.js";import"./chunk-KYREMICR.js";import"./chunk-VBRVI5XI.js";import"./chunk-5ZFOKSDK.js";import{a as t,d as c}from"./chunk-7L2LUDC3.js";import"./chunk-77KFIUJG.js";import"./chunk-7W3OTLHS.js";import"./chunk-X52A3GF7.js";import"./chunk-PX3QTMVS.js";import{e as o}from"./chunk-FE4HG5RY.js";function a(r,n){return o(n)&&(r=i.unpack(r,n)),r._center=t.clone(r._center),r._ellipsoid=c.clone(r._ellipsoid),i.createGeometry(r)}var p=a;export{p as default};
