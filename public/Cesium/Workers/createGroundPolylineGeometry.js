/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as _e}from"./chunk-3BNGKZ2F.js";import{a as W}from"./chunk-PJQOB6JM.js";import{a as Ct}from"./chunk-5YCKGPZ3.js";import{a as Oe}from"./chunk-BQNAZUCV.js";import{a as Le}from"./chunk-IOJRZZG4.js";import{a as te}from"./chunk-QF5X4OGE.js";import{b as Qt}from"./chunk-S3NLG5WM.js";import{a as jt}from"./chunk-AAOMPF7M.js";import{c as Ce,d as zt}from"./chunk-UJOKCDQH.js";import{a as Pt,d as U}from"./chunk-KYREMICR.js";import{d as xe,e as Ne,f as Kt,h as Y}from"./chunk-VBRVI5XI.js";import{a as Bt}from"./chunk-5ZFOKSDK.js";import{a as t,b as u,c as Rt,d as B,e as bt,f as vt}from"./chunk-7L2LUDC3.js";import{a as p}from"./chunk-77KFIUJG.js";import"./chunk-7W3OTLHS.js";import"./chunk-X52A3GF7.js";import{a as wt,b as tt}from"./chunk-PX3QTMVS.js";import{e as g}from"./chunk-FE4HG5RY.js";function it(e){e=e??vt.EMPTY_OBJECT,this._ellipsoid=e.ellipsoid??B.default,this._rectangle=e.rectangle??Y.MAX_VALUE,this._projection=new Pt(this._ellipsoid),this._numberOfLevelZeroTilesX=e.numberOfLevelZeroTilesX??2,this._numberOfLevelZeroTilesY=e.numberOfLevelZeroTilesY??1}Object.defineProperties(it.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},rectangle:{get:function(){return this._rectangle}},projection:{get:function(){return this._projection}}});it.prototype.getNumberOfXTilesAtLevel=function(e){return this._numberOfLevelZeroTilesX<<e};it.prototype.getNumberOfYTilesAtLevel=function(e){return this._numberOfLevelZeroTilesY<<e};it.prototype.rectangleToNativeRectangle=function(e,i){tt.defined("rectangle",e);let r=p.toDegrees(e.west),n=p.toDegrees(e.south),o=p.toDegrees(e.east),a=p.toDegrees(e.north);return g(i)?(i.west=r,i.south=n,i.east=o,i.north=a,i):new Y(r,n,o,a)};it.prototype.tileXYToNativeRectangle=function(e,i,r,n){let o=this.tileXYToRectangle(e,i,r,n);return o.west=p.toDegrees(o.west),o.south=p.toDegrees(o.south),o.east=p.toDegrees(o.east),o.north=p.toDegrees(o.north),o};it.prototype.tileXYToRectangle=function(e,i,r,n){let o=this._rectangle,a=this.getNumberOfXTilesAtLevel(r),c=this.getNumberOfYTilesAtLevel(r),l=o.width/a,s=e*l+o.west,d=(e+1)*l+o.west,h=o.height/c,N=o.north-i*h,S=o.north-(i+1)*h;return g(n)||(n=new Y(s,S,d,N)),n.west=s,n.south=S,n.east=d,n.north=N,n};it.prototype.positionToTileXY=function(e,i,r){let n=this._rectangle;if(!Y.contains(n,e))return;let o=this.getNumberOfXTilesAtLevel(i),a=this.getNumberOfYTilesAtLevel(i),c=n.width/o,l=n.height/a,s=e.longitude;n.east<n.west&&(s+=p.TWO_PI);let d=(s-n.west)/c|0;d>=o&&(d=o-1);let h=(n.north-e.latitude)/l|0;return h>=a&&(h=a-1),g(r)?(r.x=d,r.y=h,r):new Rt(d,h)};var De=it;var Ie=new t,He=new t,ke=new u,ee=new t,hn=new t,ye=new U,pn=new De,_t=[new u,new u,new u,new u],Lt=new Rt,w={};w.initialize=function(){let e=w._initPromise;return g(e)||(e=xe.fetchJson(Ne("Assets/approximateTerrainHeights.json")).then(function(i){w._terrainHeights=i}),w._initPromise=e),e};w.getMinimumMaximumHeights=function(e,i){if(tt.defined("rectangle",e),!g(w._terrainHeights))throw new wt("You must call ApproximateTerrainHeights.initialize and wait for the promise to resolve before using this function");i=i??B.default;let r=Ae(e),n=w._defaultMinTerrainHeight,o=w._defaultMaxTerrainHeight;if(g(r)){let a=`${r.level}-${r.x}-${r.y}`,c=w._terrainHeights[a];g(c)&&(n=c[0],o=c[1]),i.cartographicToCartesian(Y.northeast(e,ke),Ie),i.cartographicToCartesian(Y.southwest(e,ke),He),t.midpoint(He,Ie,ee);let l=i.scaleToGeodeticSurface(ee,hn);if(g(l)){let s=t.distance(ee,l);n=Math.min(n,-s)}else n=w._defaultMinTerrainHeight}return n=Math.max(w._defaultMinTerrainHeight,n),{minimumTerrainHeight:n,maximumTerrainHeight:o}};w.getBoundingSphere=function(e,i){if(tt.defined("rectangle",e),!g(w._terrainHeights))throw new wt("You must call ApproximateTerrainHeights.initialize and wait for the promise to resolve before using this function");i=i??B.default;let r=Ae(e),n=w._defaultMaxTerrainHeight;if(g(r)){let a=`${r.level}-${r.x}-${r.y}`,c=w._terrainHeights[a];g(c)&&(n=c[1])}let o=U.fromRectangle3D(e,i,0);return U.fromRectangle3D(e,i,n,ye),U.union(o,ye,o)};function Ae(e){u.fromRadians(e.east,e.north,0,_t[0]),u.fromRadians(e.west,e.north,0,_t[1]),u.fromRadians(e.east,e.south,0,_t[2]),u.fromRadians(e.west,e.south,0,_t[3]);let i=0,r=0,n=0,o=0,a=w._terrainHeightsMaxLevel,c;for(c=0;c<=a;++c){let l=!1;for(let s=0;s<4;++s){let d=_t[s];if(pn.positionToTileXY(d,c,Lt),s===0)n=Lt.x,o=Lt.y;else if(n!==Lt.x||o!==Lt.y){l=!0;break}}if(l)break;i=n,r=o}if(c!==0)return{x:i,y:r,level:c>a?a:c-1}}w._terrainHeightsMaxLevel=6;w._defaultMaxTerrainHeight=9e3;w._defaultMinTerrainHeight=-1e5;w._terrainHeights=void 0;w._initPromise=void 0;Object.defineProperties(w,{initialized:{get:function(){return g(w._terrainHeights)}}});var Ft=w;var le=[Pt,_e],dn=le.length,Ke=Math.cos(p.toRadians(30)),Me=Math.cos(p.toRadians(150)),Qe=0,tn=1e3;function at(e){e=e??vt.EMPTY_OBJECT;let i=e.positions;if(!g(i)||i.length<2)throw new wt("At least two positions are required.");if(g(e.arcType)&&e.arcType!==W.GEODESIC&&e.arcType!==W.RHUMB)throw new wt("Valid options for arcType are ArcType.GEODESIC and ArcType.RHUMB.");this.width=e.width??1,this._positions=i,this.granularity=e.granularity??9999,this.loop=e.loop??!1,this.arcType=e.arcType??W.GEODESIC,this._ellipsoid=B.default,this._projectionIndex=0,this._workerName="createGroundPolylineGeometry",this._scene3DOnly=!1}Object.defineProperties(at.prototype,{packedLength:{get:function(){return 1+this._positions.length*3+1+1+1+B.packedLength+1+1}}});at.setProjectionAndEllipsoid=function(e,i){let r=0;for(let n=0;n<dn;n++)if(i instanceof le[n]){r=n;break}e._projectionIndex=r,e._ellipsoid=i.ellipsoid};var fn=new t,Re=new t,Pe=new t;function se(e,i,r,n,o){let a=R(n,e,0,fn),c=R(n,e,r,Re),l=R(n,i,0,Pe),s=et(c,a,Re),d=et(l,a,Pe);return t.cross(d,s,o),t.normalize(o,o)}var mn=new u,gn=new t,un=new t,wn=new t;function ne(e,i,r,n,o,a,c,l,s,d,h){if(o===0)return;let N;a===W.GEODESIC?N=new Oe(e,i,c):a===W.RHUMB&&(N=new te(e,i,c));let S=N.surfaceDistance;if(S<o)return;let M=se(e,i,n,c,wn),O=Math.ceil(S/o),D=S/O,z=D,G=O-1,f=l.length;for(let j=0;j<G;j++){let b=N.interpolateUsingSurfaceDistance(z,mn),k=R(c,b,r,gn),I=R(c,b,n,un);t.pack(M,l,f),t.pack(k,s,f),t.pack(I,d,f),h.push(b.latitude),h.push(b.longitude),f+=3,z+=D}}var oe=new u;function R(e,i,r,n){return u.clone(i,oe),oe.height=r,u.toCartesian(oe,e,n)}at.pack=function(e,i,r){tt.typeOf.object("value",e),tt.defined("array",i);let n=r??0,o=e._positions,a=o.length;i[n++]=a;for(let c=0;c<a;++c){let l=o[c];t.pack(l,i,n),n+=3}return i[n++]=e.granularity,i[n++]=e.loop?1:0,i[n++]=e.arcType,B.pack(e._ellipsoid,i,n),n+=B.packedLength,i[n++]=e._projectionIndex,i[n++]=e._scene3DOnly?1:0,i};at.unpack=function(e,i,r){tt.defined("array",e);let n=i??0,o=e[n++],a=new Array(o);for(let S=0;S<o;S++)a[S]=t.unpack(e,n),n+=3;let c=e[n++],l=e[n++]===1,s=e[n++],d=B.unpack(e,n);n+=B.packedLength;let h=e[n++],N=e[n++]===1;return g(r)||(r=new at({positions:a})),r._positions=a,r.granularity=c,r.loop=l,r.arcType=s,r._ellipsoid=d,r._projectionIndex=h,r._scene3DOnly=N,r};function et(e,i,r){return t.subtract(e,i,r),t.normalize(r,r),r}function be(e,i,r,n){return n=et(e,i,n),n=t.cross(n,r,n),n=t.normalize(n,n),n=t.cross(r,n,n),n}var Sn=new t,Tn=new t,En=new t,en=new t,xn=0,Nn=-1;function ie(e,i,r,n,o){let a=et(r,i,en),c=be(e,i,a,Sn),l=be(n,i,a,Tn);if(p.equalsEpsilon(t.dot(c,l),Nn,p.EPSILON5))return o=t.cross(a,c,o),o=t.normalize(o,o),o;o=t.add(l,c,o),o=t.normalize(o,o);let s=t.cross(a,o,En);return t.dot(l,s)<xn&&(o=t.negate(o,o)),o}var Gt=jt.fromPointNormal(t.ZERO,t.UNIT_Y),Cn=new t,_n=new t,Ln=new t,On=new t,Dn=new t,Xt=new t,Yt=new u,ve=new u,Be=new u;at.createGeometry=function(e){let i=!e._scene3DOnly,r=e.loop,n=e._ellipsoid,o=e.granularity,a=e.arcType,c=new le[e._projectionIndex](n),l=Qe,s=tn,d,h,N=e._positions,S=N.length;S===2&&(r=!1);let M,O,D,z,G=new te(void 0,void 0,n),f,j,b,k=[N[0]];for(h=0;h<S-1;h++)M=N[h],O=N[h+1],f=Qt.lineSegmentPlane(M,O,Gt,Xt),g(f)&&!t.equalsEpsilon(f,M,p.EPSILON7)&&!t.equalsEpsilon(f,O,p.EPSILON7)&&(e.arcType===W.GEODESIC?k.push(t.clone(f)):e.arcType===W.RHUMB&&(b=n.cartesianToCartographic(f,Yt).longitude,D=n.cartesianToCartographic(M,Yt),z=n.cartesianToCartographic(O,ve),G.setEndPoints(D,z),j=G.findIntersectionWithLongitude(b,Be),f=n.cartographicToCartesian(j,Xt),g(f)&&!t.equalsEpsilon(f,M,p.EPSILON7)&&!t.equalsEpsilon(f,O,p.EPSILON7)&&k.push(t.clone(f)))),k.push(O);r&&(M=N[S-1],O=N[0],f=Qt.lineSegmentPlane(M,O,Gt,Xt),g(f)&&!t.equalsEpsilon(f,M,p.EPSILON7)&&!t.equalsEpsilon(f,O,p.EPSILON7)&&(e.arcType===W.GEODESIC?k.push(t.clone(f)):e.arcType===W.RHUMB&&(b=n.cartesianToCartographic(f,Yt).longitude,D=n.cartesianToCartographic(M,Yt),z=n.cartesianToCartographic(O,ve),G.setEndPoints(D,z),j=G.findIntersectionWithLongitude(b,Be),f=n.cartographicToCartesian(j,Xt),g(f)&&!t.equalsEpsilon(f,M,p.EPSILON7)&&!t.equalsEpsilon(f,O,p.EPSILON7)&&k.push(t.clone(f)))));let I=k.length,C=new Array(I);for(h=0;h<I;h++){let X=u.fromCartesian(k[h],n);X.height=0,C[h]=X}if(C=Le(C,u.equalsEpsilon),I=C.length,I<2)return;let H=[],P=[],v=[],E=[],x=Cn,L=_n,_=Ln,F=On,y=Dn,m=C[0],Z=C[1],Dt=C[I-1];for(x=R(n,Dt,l,x),F=R(n,Z,l,F),L=R(n,m,l,L),_=R(n,m,s,_),r?y=ie(x,L,_,F,y):y=se(m,Z,s,n,y),t.pack(y,P,0),t.pack(L,v,0),t.pack(_,E,0),H.push(m.latitude),H.push(m.longitude),ne(m,Z,l,s,o,a,n,P,v,E,H),h=1;h<I-1;++h){x=t.clone(L,x),L=t.clone(F,L);let X=C[h];R(n,X,s,_),R(n,C[h+1],l,F),ie(x,L,_,F,y),d=P.length,t.pack(y,P,d),t.pack(L,v,d),t.pack(_,E,d),H.push(X.latitude),H.push(X.longitude),ne(C[h],C[h+1],l,s,o,a,n,P,v,E,H)}let A=C[I-1],St=C[I-2];if(L=R(n,A,l,L),_=R(n,A,s,_),r){let X=C[0];x=R(n,St,l,x),F=R(n,X,l,F),y=ie(x,L,_,F,y)}else y=se(St,A,s,n,y);if(d=P.length,t.pack(y,P,d),t.pack(L,v,d),t.pack(_,E,d),H.push(A.latitude),H.push(A.longitude),r){for(ne(A,m,l,s,o,a,n,P,v,E,H),d=P.length,h=0;h<3;++h)P[d+h]=P[h],v[d+h]=v[h],E[d+h]=E[h];H.push(m.latitude),H.push(m.longitude)}return Qn(r,c,v,E,P,H,i)};var In=new t,Hn=new bt,kn=new Kt;function ze(e,i,r,n){let o=et(r,i,In),a=t.dot(o,e);if(a>Ke||a<Me){let c=et(n,r,en),l=a<Me?p.PI_OVER_TWO:-p.PI_OVER_TWO,s=Kt.fromAxisAngle(c,l,kn),d=bt.fromQuaternion(s,Hn);return bt.multiplyByVector(d,e,e),!0}return!1}var je=new u,yn=new t,Fe=new t;function Ot(e,i,r,n,o){let a=u.toCartesian(i,e._ellipsoid,yn),c=t.add(a,r,Fe),l=!1,s=e._ellipsoid,d=s.cartesianToCartographic(c,je);Math.abs(i.longitude-d.longitude)>p.PI_OVER_TWO&&(l=!0,c=t.subtract(a,r,Fe),d=s.cartesianToCartographic(c,je)),d.height=0;let h=e.project(d,o);return o=t.subtract(h,n,o),o.z=0,o=t.normalize(o,o),l&&t.negate(o,o),o}var An=new t,Xe=new t;function Ye(e,i,r,n,o,a){let c=t.subtract(i,e,An);t.normalize(c,c);let l=r-Qe,s=t.multiplyByScalar(c,l,Xe);t.add(e,s,o);let d=n-tn;s=t.multiplyByScalar(c,d,Xe),t.add(i,s,a)}var Mn=new t;function Ut(e,i){let r=jt.getPointDistance(Gt,e),n=jt.getPointDistance(Gt,i),o=Mn;p.equalsEpsilon(r,0,p.EPSILON2)?(o=et(i,e,o),t.multiplyByScalar(o,p.EPSILON2,o),t.add(e,o,e)):p.equalsEpsilon(n,0,p.EPSILON2)&&(o=et(e,i,o),t.multiplyByScalar(o,p.EPSILON2,o),t.add(i,o,i))}function Rn(e,i){let r=Math.abs(e.longitude),n=Math.abs(i.longitude);if(p.equalsEpsilon(r,p.PI,p.EPSILON11)){let o=p.sign(i.longitude);return e.longitude=o*(r-p.EPSILON11),1}else if(p.equalsEpsilon(n,p.PI,p.EPSILON11)){let o=p.sign(e.longitude);return i.longitude=o*(n-p.EPSILON11),2}return 0}var nn=new u,on=new u,Ue=new t,re=new t,Ge=new t,qe=new t,Pn=new t,We=new t,bn=[nn,on],vn=new Y,Bn=new t,zn=new t,jn=new t,Fn=new t,Xn=new t,Yn=new t,ae=new t,ce=new t,Un=new t,Gn=new t,qn=new t,Ze=new t,Wn=new t,Zn=new t,Vn=new Ct,$n=new Ct,Ve=new t,Jn=new t,$e=new t,Kn=[new U,new U],rn=[0,2,1,0,3,2,0,7,3,0,4,7,0,5,4,0,1,5,5,7,4,5,6,7,5,2,6,5,1,2,3,6,2,3,7,6],Je=rn.length;function Qn(e,i,r,n,o,a,c){let l,s,d=i._ellipsoid,h=r.length/3-1,N=h*8,S=N*4,M=h*36,O=N>65535?new Uint32Array(M):new Uint16Array(M),D=new Float64Array(N*3),z=new Float32Array(S),G=new Float32Array(S),f=new Float32Array(S),j=new Float32Array(S),b=new Float32Array(S),k,I,C,H;c&&(k=new Float32Array(S),I=new Float32Array(S),C=new Float32Array(S),H=new Float32Array(N*2));let P=a.length/2,v=0,E=nn;E.height=0;let x=on;x.height=0;let L=Ue,_=re;if(c)for(s=0,l=1;l<P;l++)E.latitude=a[s],E.longitude=a[s+1],x.latitude=a[s+2],x.longitude=a[s+3],L=i.project(E,L),_=i.project(x,_),v+=t.distance(L,_),s+=2;let F=n.length/3;_=t.unpack(n,0,_);let y=0;for(s=3,l=1;l<F;l++)L=t.clone(_,L),_=t.unpack(n,s,_),y+=t.distance(L,_),s+=3;let m;s=3;let Z=0,Dt=0,A=0,St=0,X=!1,ct=t.unpack(r,0,qe),st=t.unpack(n,0,re),q=t.unpack(o,0,We);if(e){let lt=t.unpack(r,r.length-6,Ge);ze(q,lt,ct,st)&&(q=t.negate(q,q))}let pe=0,de=0,qt=0;for(l=0;l<h;l++){let lt=t.clone(ct,Ge),Zt=t.clone(st,Ue),ft=t.clone(q,Pn);X&&(ft=t.negate(ft,ft)),ct=t.unpack(r,s,qe),st=t.unpack(n,s,re),q=t.unpack(o,s,We),X=ze(q,lt,ct,st),E.latitude=a[Z],E.longitude=a[Z+1],x.latitude=a[Z+2],x.longitude=a[Z+3];let ht,mt,nt,ot;if(c){let T=Rn(E,x);ht=i.project(E,Xn),mt=i.project(x,Yn);let ut=et(mt,ht,Ve);ut.y=Math.abs(ut.y),nt=ae,ot=ce,T===0||t.dot(ut,t.UNIT_Y)>Ke?(nt=Ot(i,E,ft,ht,ae),ot=Ot(i,x,q,mt,ce)):T===1?(ot=Ot(i,x,q,mt,ce),nt.x=0,nt.y=p.sign(E.longitude-Math.abs(x.longitude)),nt.z=0):(nt=Ot(i,E,ft,ht,ae),ot.x=0,ot.y=p.sign(E.longitude-x.longitude),ot.z=0)}let ge=t.distance(Zt,st),ue=Ct.fromCartesian(lt,Vn),It=t.subtract(ct,lt,Un),an=t.normalize(It,Ze),Et=t.subtract(Zt,lt,Gn);Et=t.normalize(Et,Et);let gt=t.cross(an,Et,Ze);gt=t.normalize(gt,gt);let Ht=t.cross(Et,ft,Wn);Ht=t.normalize(Ht,Ht);let kt=t.subtract(st,ct,qn);kt=t.normalize(kt,kt);let yt=t.cross(q,kt,Zn);yt=t.normalize(yt,yt);let cn=ge/y,sn=pe/y,Vt=0,xt,At,pt,we=0,Se=0;if(c){Vt=t.distance(ht,mt),xt=Ct.fromCartesian(ht,$n),At=t.subtract(mt,ht,Ve),pt=t.normalize(At,Jn);let T=pt.x;pt.x=pt.y,pt.y=-T,we=Vt/v,Se=de/v}for(m=0;m<8;m++){let T=St+m*4,ut=Dt+m*2,Nt=T+3,Ee=m<4?1:-1,Mt=m===2||m===3||m===6||m===7?1:-1;t.pack(ue.high,z,T),z[Nt]=It.x,t.pack(ue.low,G,T),G[Nt]=It.y,t.pack(Ht,f,T),f[Nt]=It.z,t.pack(yt,j,T),j[Nt]=cn*Ee,t.pack(gt,b,T);let dt=sn*Mt;dt===0&&Mt<0&&(dt=9),b[Nt]=dt,c&&(k[T]=xt.high.x,k[T+1]=xt.high.y,k[T+2]=xt.low.x,k[T+3]=xt.low.y,C[T]=-nt.y,C[T+1]=nt.x,C[T+2]=ot.y,C[T+3]=-ot.x,I[T]=At.x,I[T+1]=At.y,I[T+2]=pt.x,I[T+3]=pt.y,H[ut]=we*Ee,dt=Se*Mt,dt===0&&Mt<0&&(dt=9),H[ut+1]=dt)}let V=jn,$=Fn,J=Bn,K=zn,ln=Y.fromCartographicArray(bn,vn),Te=Ft.getMinimumMaximumHeights(ln,d),$t=Te.minimumTerrainHeight,Jt=Te.maximumTerrainHeight;qt+=Math.abs($t),qt+=Math.abs(Jt),Ye(lt,Zt,$t,Jt,V,J),Ye(ct,st,$t,Jt,$,K);let Q=t.multiplyByScalar(gt,p.EPSILON5,$e);t.add(V,Q,V),t.add($,Q,$),t.add(J,Q,J),t.add(K,Q,K),Ut(V,$),Ut(J,K),t.pack(V,D,A),t.pack($,D,A+3),t.pack(K,D,A+6),t.pack(J,D,A+9),Q=t.multiplyByScalar(gt,-2*p.EPSILON5,$e),t.add(V,Q,V),t.add($,Q,$),t.add(J,Q,J),t.add(K,Q,K),Ut(V,$),Ut(J,K),t.pack(V,D,A+12),t.pack($,D,A+15),t.pack(K,D,A+18),t.pack(J,D,A+21),Z+=2,s+=3,Dt+=16,A+=24,St+=32,pe+=ge,de+=Vt}s=0;let fe=0;for(l=0;l<h;l++){for(m=0;m<Je;m++)O[s+m]=rn[m]+fe;fe+=8,s+=Je}let Wt=Kn;U.fromVertices(r,t.ZERO,3,Wt[0]),U.fromVertices(n,t.ZERO,3,Wt[1]);let me=U.fromBoundingSpheres(Wt);me.radius+=qt/(h*2);let Tt={position:new zt({componentDatatype:Bt.DOUBLE,componentsPerAttribute:3,normalize:!1,values:D}),startHiAndForwardOffsetX:rt(z),startLoAndForwardOffsetY:rt(G),startNormalAndForwardOffsetZ:rt(f),endNormalAndTextureCoordinateNormalizationX:rt(j),rightNormalAndTextureCoordinateNormalizationY:rt(b)};return c&&(Tt.startHiLo2D=rt(k),Tt.offsetAndRight2D=rt(I),Tt.startEndNormals2D=rt(C),Tt.texcoordNormalization2D=new zt({componentDatatype:Bt.FLOAT,componentsPerAttribute:2,normalize:!1,values:H})),new Ce({attributes:Tt,indices:O,boundingSphere:me})}function rt(e){return new zt({componentDatatype:Bt.FLOAT,componentsPerAttribute:4,normalize:!1,values:e})}at._projectNormal=Ot;var he=at;function to(e,i){return Ft.initialize().then(function(){return g(i)&&(e=he.unpack(e,i)),he.createGeometry(e)})}var Qo=to;export{Qo as default};
