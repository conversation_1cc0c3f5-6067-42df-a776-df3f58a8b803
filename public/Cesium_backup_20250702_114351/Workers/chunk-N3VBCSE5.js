/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as wt}from"./chunk-O4MLFQKO.js";import{a as C}from"./chunk-KA7ZX4BQ.js";import{a as kt}from"./chunk-PFXLBIMV.js";import{a as Ct}from"./chunk-PSBTKXXJ.js";import{b as dt,c as _t,d as N}from"./chunk-UJOKCDQH.js";import{d as ht}from"./chunk-KYREMICR.js";import{a as M}from"./chunk-5ZFOKSDK.js";import{a as s,c as lt,d as it,f as pt}from"./chunk-7L2LUDC3.js";import{a as E}from"./chunk-77KFIUJG.js";import{a as g}from"./chunk-PX3QTMVS.js";import{e as v}from"./chunk-FE4HG5RY.js";var yt=new s,vt=new s,Et=new s,Mt=new s,Nt=new s,Rt=new s(1,1,1),Pt=Math.cos,At=Math.sin;function x(n){n=n??pt.EMPTY_OBJECT;let m=n.radii??Rt,i=n.innerRadii??m,w=n.minimumClock??0,O=n.maximumClock??E.TWO_PI,P=n.minimumCone??0,A=n.maximumCone??E.PI,a=Math.round(n.stackPartitions??64),u=Math.round(n.slicePartitions??64),p=n.vertexFormat??C.DEFAULT;if(u<3)throw new g("options.slicePartitions cannot be less than three.");if(a<3)throw new g("options.stackPartitions cannot be less than three.");this._radii=s.clone(m),this._innerRadii=s.clone(i),this._minimumClock=w,this._maximumClock=O,this._minimumCone=P,this._maximumCone=A,this._stackPartitions=a,this._slicePartitions=u,this._vertexFormat=C.clone(p),this._offsetAttribute=n.offsetAttribute,this._workerName="createEllipsoidGeometry"}x.packedLength=2*s.packedLength+C.packedLength+7;x.pack=function(n,m,i){if(!v(n))throw new g("value is required");if(!v(m))throw new g("array is required");return i=i??0,s.pack(n._radii,m,i),i+=s.packedLength,s.pack(n._innerRadii,m,i),i+=s.packedLength,C.pack(n._vertexFormat,m,i),i+=C.packedLength,m[i++]=n._minimumClock,m[i++]=n._maximumClock,m[i++]=n._minimumCone,m[i++]=n._maximumCone,m[i++]=n._stackPartitions,m[i++]=n._slicePartitions,m[i]=n._offsetAttribute??-1,m};var bt=new s,xt=new s,Ot=new C,L={radii:bt,innerRadii:xt,vertexFormat:Ot,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,offsetAttribute:void 0};x.unpack=function(n,m,i){if(!v(n))throw new g("array is required");m=m??0;let w=s.unpack(n,m,bt);m+=s.packedLength;let O=s.unpack(n,m,xt);m+=s.packedLength;let P=C.unpack(n,m,Ot);m+=C.packedLength;let A=n[m++],a=n[m++],u=n[m++],p=n[m++],t=n[m++],o=n[m++],e=n[m];return v(i)?(i._radii=s.clone(w,i._radii),i._innerRadii=s.clone(O,i._innerRadii),i._vertexFormat=C.clone(P,i._vertexFormat),i._minimumClock=A,i._maximumClock=a,i._minimumCone=u,i._maximumCone=p,i._stackPartitions=t,i._slicePartitions=o,i._offsetAttribute=e===-1?void 0:e,i):(L.minimumClock=A,L.maximumClock=a,L.minimumCone=u,L.maximumCone=p,L.stackPartitions=t,L.slicePartitions=o,L.offsetAttribute=e===-1?void 0:e,new x(L))};x.createGeometry=function(n){let m=n._radii;if(m.x<=0||m.y<=0||m.z<=0)return;let i=n._innerRadii;if(i.x<=0||i.y<=0||i.z<=0)return;let w=n._minimumClock,O=n._maximumClock,P=n._minimumCone,A=n._maximumCone,a=n._vertexFormat,u=n._slicePartitions+1,p=n._stackPartitions+1;u=Math.round(u*Math.abs(O-w)/E.TWO_PI),p=Math.round(p*Math.abs(A-P)/E.PI),u<2&&(u=2),p<2&&(p=2);let t,o,e=0,z=[P],D=[w];for(t=0;t<p;t++)z.push(P+t*(A-P)/(p-1));for(z.push(A),o=0;o<u;o++)D.push(w+o*(O-w)/(u-1));D.push(O);let f=z.length,c=D.length,S=0,U=1,q=i.x!==m.x||i.y!==m.y||i.z!==m.z,H=!1,mt=!1,rt=!1;q&&(U=2,P>0&&(H=!0,S+=u-1),A<Math.PI&&(mt=!0,S+=u-1),(O-w)%E.TWO_PI?(rt=!0,S+=(p-1)*2+1):S+=1);let k=c*f*U,b=new Float64Array(k*3),J=new Array(k).fill(!1),ct=new Array(k).fill(!1),st=u*p*U,Tt=6*(st+S+1-(u+p)*U),r=kt.createTypedArray(st,Tt),V=a.normal?new Float32Array(k*3):void 0,Y=a.tangent?new Float32Array(k*3):void 0,j=a.bitangent?new Float32Array(k*3):void 0,X=a.st?new Float32Array(k*2):void 0,B=new Array(f),Z=new Array(f);for(t=0;t<f;t++)B[t]=At(z[t]),Z[t]=Pt(z[t]);let G=new Array(c),K=new Array(c);for(o=0;o<c;o++)K[o]=Pt(D[o]),G[o]=At(D[o]);for(t=0;t<f;t++)for(o=0;o<c;o++)b[e++]=m.x*B[t]*K[o],b[e++]=m.y*B[t]*G[o],b[e++]=m.z*Z[t];let Q=k/2;if(q)for(t=0;t<f;t++)for(o=0;o<c;o++)b[e++]=i.x*B[t]*K[o],b[e++]=i.y*B[t]*G[o],b[e++]=i.z*Z[t],J[Q]=!0,t>0&&t!==f-1&&o!==0&&o!==c-1&&(ct[Q]=!0),Q++;e=0;let T,F;for(t=1;t<f-2;t++)for(T=t*c,F=(t+1)*c,o=1;o<c-2;o++)r[e++]=F+o,r[e++]=F+o+1,r[e++]=T+o+1,r[e++]=F+o,r[e++]=T+o+1,r[e++]=T+o;if(q){let R=f*c;for(t=1;t<f-2;t++)for(T=R+t*c,F=R+(t+1)*c,o=1;o<c-2;o++)r[e++]=F+o,r[e++]=T+o,r[e++]=T+o+1,r[e++]=F+o,r[e++]=T+o+1,r[e++]=F+o+1}let h,l;if(q){if(H)for(l=f*c,t=1;t<c-2;t++)r[e++]=t,r[e++]=t+1,r[e++]=l+t+1,r[e++]=t,r[e++]=l+t+1,r[e++]=l+t;if(mt)for(h=f*c-c,l=f*c*U-c,t=1;t<c-2;t++)r[e++]=h+t+1,r[e++]=h+t,r[e++]=l+t,r[e++]=h+t+1,r[e++]=l+t,r[e++]=l+t+1}if(rt){for(t=1;t<f-2;t++)l=c*f+c*t,h=c*t,r[e++]=l,r[e++]=h+c,r[e++]=h,r[e++]=l,r[e++]=l+c,r[e++]=h+c;for(t=1;t<f-2;t++)l=c*f+c*(t+1)-1,h=c*(t+1)-1,r[e++]=h+c,r[e++]=l,r[e++]=h,r[e++]=h+c,r[e++]=l+c,r[e++]=l}let y=new Ct;a.position&&(y.position=new N({componentDatatype:M.DOUBLE,componentsPerAttribute:3,values:b}));let at=0,$=0,I=0,tt=0,Ft=k/2,ft,ut=it.fromCartesian3(m),Lt=it.fromCartesian3(i);if(a.st||a.normal||a.tangent||a.bitangent){for(t=0;t<k;t++){ft=J[t]?Lt:ut;let R=s.fromArray(b,t*3,yt),_=ft.geodeticSurfaceNormal(R,vt);if(ct[t]&&s.negate(_,_),a.st){let d=lt.negate(_,Nt);X[at++]=Math.atan2(d.y,d.x)/E.TWO_PI+.5,X[at++]=Math.asin(_.z)/Math.PI+.5}if(a.normal&&(V[$++]=_.x,V[$++]=_.y,V[$++]=_.z),a.tangent||a.bitangent){let d=Et,nt=0,et;if(J[t]&&(nt=Ft),!H&&t>=nt&&t<nt+c*2?et=s.UNIT_X:et=s.UNIT_Z,s.cross(et,_,d),s.normalize(d,d),a.tangent&&(Y[I++]=d.x,Y[I++]=d.y,Y[I++]=d.z),a.bitangent){let W=s.cross(_,d,Mt);s.normalize(W,W),j[tt++]=W.x,j[tt++]=W.y,j[tt++]=W.z}}}a.st&&(y.st=new N({componentDatatype:M.FLOAT,componentsPerAttribute:2,values:X})),a.normal&&(y.normal=new N({componentDatatype:M.FLOAT,componentsPerAttribute:3,values:V})),a.tangent&&(y.tangent=new N({componentDatatype:M.FLOAT,componentsPerAttribute:3,values:Y})),a.bitangent&&(y.bitangent=new N({componentDatatype:M.FLOAT,componentsPerAttribute:3,values:j}))}if(v(n._offsetAttribute)){let R=b.length,_=n._offsetAttribute===wt.NONE?0:1,d=new Uint8Array(R/3).fill(_);y.applyOffset=new N({componentDatatype:M.UNSIGNED_BYTE,componentsPerAttribute:1,values:d})}return new _t({attributes:y,indices:r,primitiveType:dt.TRIANGLES,boundingSphere:ht.fromEllipsoid(ut),offsetAttribute:n._offsetAttribute})};var ot;x.getUnitEllipsoid=function(){return v(ot)||(ot=x.createGeometry(new x({radii:new s(1,1,1),vertexFormat:C.POSITION_ONLY}))),ot};var Kt=x;export{Kt as a};
