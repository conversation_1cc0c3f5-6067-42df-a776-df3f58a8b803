/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as F}from"./chunk-O4MLFQKO.js";import{a as Z}from"./chunk-PFXLBIMV.js";import{a as X}from"./chunk-PSBTKXXJ.js";import{b as K,c as Q,d as U}from"./chunk-UJOKCDQH.js";import{d as H}from"./chunk-KYREMICR.js";import{a as y}from"./chunk-5ZFOKSDK.js";import{a as r,d as J,f as V}from"./chunk-7L2LUDC3.js";import{a as N}from"./chunk-77KFIUJG.js";import{a as b}from"./chunk-PX3QTMVS.js";import{e as R}from"./chunk-FE4HG5RY.js";var ii=new r(1,1,1),B=Math.cos,S=Math.sin;function v(t){t=t??V.EMPTY_OBJECT;let o=t.radii??ii,n=t.innerRadii??o,C=t.minimumClock??0,P=t.maximumClock??N.TWO_PI,h=t.minimumCone??0,k=t.maximumCone??N.PI,s=Math.round(t.stackPartitions??10),w=Math.round(t.slicePartitions??8),m=Math.round(t.subdivisions??128);if(s<1)throw new b("options.stackPartitions cannot be less than 1");if(w<0)throw new b("options.slicePartitions cannot be less than 0");if(m<0)throw new b("options.subdivisions must be greater than or equal to zero.");if(R(t.offsetAttribute)&&t.offsetAttribute===F.TOP)throw new b("GeometryOffsetAttribute.TOP is not a supported options.offsetAttribute for this geometry.");this._radii=r.clone(o),this._innerRadii=r.clone(n),this._minimumClock=C,this._maximumClock=P,this._minimumCone=h,this._maximumCone=k,this._stackPartitions=s,this._slicePartitions=w,this._subdivisions=m,this._offsetAttribute=t.offsetAttribute,this._workerName="createEllipsoidOutlineGeometry"}v.packedLength=2*r.packedLength+8;v.pack=function(t,o,n){if(!R(t))throw new b("value is required");if(!R(o))throw new b("array is required");return n=n??0,r.pack(t._radii,o,n),n+=r.packedLength,r.pack(t._innerRadii,o,n),n+=r.packedLength,o[n++]=t._minimumClock,o[n++]=t._maximumClock,o[n++]=t._minimumCone,o[n++]=t._maximumCone,o[n++]=t._stackPartitions,o[n++]=t._slicePartitions,o[n++]=t._subdivisions,o[n]=t._offsetAttribute??-1,o};var $=new r,G=new r,l={radii:$,innerRadii:G,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,subdivisions:void 0,offsetAttribute:void 0};v.unpack=function(t,o,n){if(!R(t))throw new b("array is required");o=o??0;let C=r.unpack(t,o,$);o+=r.packedLength;let P=r.unpack(t,o,G);o+=r.packedLength;let h=t[o++],k=t[o++],s=t[o++],w=t[o++],m=t[o++],f=t[o++],T=t[o++],a=t[o];return R(n)?(n._radii=r.clone(C,n._radii),n._innerRadii=r.clone(P,n._innerRadii),n._minimumClock=h,n._maximumClock=k,n._minimumCone=s,n._maximumCone=w,n._stackPartitions=m,n._slicePartitions=f,n._subdivisions=T,n._offsetAttribute=a===-1?void 0:a,n):(l.minimumClock=h,l.maximumClock=k,l.minimumCone=s,l.maximumCone=w,l.stackPartitions=m,l.slicePartitions=f,l.subdivisions=T,l.offsetAttribute=a===-1?void 0:a,new v(l))};v.createGeometry=function(t){let o=t._radii;if(o.x<=0||o.y<=0||o.z<=0)return;let n=t._innerRadii;if(n.x<=0||n.y<=0||n.z<=0)return;let C=t._minimumClock,P=t._maximumClock,h=t._minimumCone,k=t._maximumCone,s=t._subdivisions,w=J.fromCartesian3(o),m=t._slicePartitions+1,f=t._stackPartitions+1;m=Math.round(m*Math.abs(P-C)/N.TWO_PI),f=Math.round(f*Math.abs(k-h)/N.PI),m<2&&(m=2),f<2&&(f=2);let T=0,a=1,x=n.x!==o.x||n.y!==o.y||n.z!==o.z,W=!1,Y=!1;x&&(a=2,h>0&&(W=!0,T+=m),k<Math.PI&&(Y=!0,T+=m));let q=s*a*(f+m),u=new Float64Array(q*3),g=2*(q+T-(m+f)*a),d=Z.createTypedArray(q,g),i,e,E,z,c=0,_=new Array(f),A=new Array(f);for(i=0;i<f;i++)z=h+i*(k-h)/(f-1),_[i]=S(z),A[i]=B(z);let O=new Array(s),M=new Array(s);for(i=0;i<s;i++)E=C+i*(P-C)/(s-1),O[i]=S(E),M[i]=B(E);for(i=0;i<f;i++)for(e=0;e<s;e++)u[c++]=o.x*_[i]*M[e],u[c++]=o.y*_[i]*O[e],u[c++]=o.z*A[i];if(x)for(i=0;i<f;i++)for(e=0;e<s;e++)u[c++]=n.x*_[i]*M[e],u[c++]=n.y*_[i]*O[e],u[c++]=n.z*A[i];for(_.length=s,A.length=s,i=0;i<s;i++)z=h+i*(k-h)/(s-1),_[i]=S(z),A[i]=B(z);for(O.length=m,M.length=m,i=0;i<m;i++)E=C+i*(P-C)/(m-1),O[i]=S(E),M[i]=B(E);for(i=0;i<s;i++)for(e=0;e<m;e++)u[c++]=o.x*_[i]*M[e],u[c++]=o.y*_[i]*O[e],u[c++]=o.z*A[i];if(x)for(i=0;i<s;i++)for(e=0;e<m;e++)u[c++]=n.x*_[i]*M[e],u[c++]=n.y*_[i]*O[e],u[c++]=n.z*A[i];for(c=0,i=0;i<f*a;i++){let p=i*s;for(e=0;e<s-1;e++)d[c++]=p+e,d[c++]=p+e+1}let L=f*s*a;for(i=0;i<m;i++)for(e=0;e<s-1;e++)d[c++]=L+i+e*m,d[c++]=L+i+(e+1)*m;if(x)for(L=f*s*a+m*s,i=0;i<m;i++)for(e=0;e<s-1;e++)d[c++]=L+i+e*m,d[c++]=L+i+(e+1)*m;if(x){let p=f*s*a,D=p+s*m;if(W)for(i=0;i<m;i++)d[c++]=p+i,d[c++]=D+i;if(Y)for(p+=s*m-m,D+=s*m-m,i=0;i<m;i++)d[c++]=p+i,d[c++]=D+i}let j=new X({position:new U({componentDatatype:y.DOUBLE,componentsPerAttribute:3,values:u})});if(R(t._offsetAttribute)){let p=u.length,D=t._offsetAttribute===F.NONE?0:1,I=new Uint8Array(p/3).fill(D);j.applyOffset=new U({componentDatatype:y.UNSIGNED_BYTE,componentsPerAttribute:1,values:I})}return new Q({attributes:j,indices:d,primitiveType:K.LINES,boundingSphere:H.fromEllipsoid(w),offsetAttribute:t._offsetAttribute})};var Ci=v;export{Ci as a};
