/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as J}from"./chunk-C7EXJG2O.js";import{a as I}from"./chunk-AAOMPF7M.js";import{b as B,c as G,d as k}from"./chunk-KYREMICR.js";import{b as j,h as X}from"./chunk-VBRVI5XI.js";import{a as t,b as S,c as z,d as F,e as d}from"./chunk-7L2LUDC3.js";import{a as g}from"./chunk-77KFIUJG.js";import{a as b,b as W}from"./chunk-PX3QTMVS.js";import{e as p}from"./chunk-FE4HG5RY.js";function w(n,e){this.center=t.clone(n??t.ZERO),this.halfAxes=d.clone(e??d.ZERO)}w.packedLength=t.packedLength+d.packedLength;w.pack=function(n,e,c){return W.typeOf.object("value",n),W.defined("array",e),c=c??0,t.pack(n.center,e,c),d.pack(n.halfAxes,e,c+t.packedLength),e};w.unpack=function(n,e,c){return W.defined("array",n),e=e??0,p(c)||(c=new w),t.unpack(n,e,c.center),d.unpack(n,e+t.packedLength,c.halfAxes),c};var pt=new t,Ct=new t,ut=new t,Pt=new t,xt=new t,yt=new t,At=new d,Nt={unitary:new d,diagonal:new d};w.fromPoints=function(n,e){if(p(e)||(e=new w),!p(n)||n.length===0)return e.halfAxes=d.ZERO,e.center=t.ZERO,e;let c,h=n.length,r=t.clone(n[0],pt);for(c=1;c<h;c++)t.add(r,n[c],r);let o=1/h;t.multiplyByScalar(r,o,r);let i=0,C=0,m=0,l=0,u=0,a=0,s;for(c=0;c<h;c++)s=t.subtract(n[c],r,Ct),i+=s.x*s.x,C+=s.x*s.y,m+=s.x*s.z,l+=s.y*s.y,u+=s.y*s.z,a+=s.z*s.z;i*=o,C*=o,m*=o,l*=o,u*=o,a*=o;let f=At;f[0]=i,f[1]=C,f[2]=m,f[3]=C,f[4]=l,f[5]=u,f[6]=m,f[7]=u,f[8]=a;let A=d.computeEigenDecomposition(f,Nt),M=d.clone(A.unitary,e.halfAxes),x=d.getColumn(M,0,Pt),y=d.getColumn(M,1,xt),N=d.getColumn(M,2,yt),P=-Number.MAX_VALUE,O=-Number.MAX_VALUE,L=-Number.MAX_VALUE,U=Number.MAX_VALUE,R=Number.MAX_VALUE,q=Number.MAX_VALUE;for(c=0;c<h;c++)s=n[c],P=Math.max(t.dot(x,s),P),O=Math.max(t.dot(y,s),O),L=Math.max(t.dot(N,s),L),U=Math.min(t.dot(x,s),U),R=Math.min(t.dot(y,s),R),q=Math.min(t.dot(N,s),q);x=t.multiplyByScalar(x,.5*(U+P),x),y=t.multiplyByScalar(y,.5*(R+O),y),N=t.multiplyByScalar(N,.5*(q+L),N);let T=t.add(x,y,e.center);t.add(T,N,T);let E=ut;return E.x=P-U,E.y=O-R,E.z=L-q,t.multiplyByScalar(E,.5,E),d.multiplyByScale(e.halfAxes,E,e.halfAxes),e};var et=new t,Mt=new t;function K(n,e,c,h,r,o,i,C,m,l,u){if(!p(r)||!p(o)||!p(i)||!p(C)||!p(m)||!p(l))throw new b("all extents (minimum/maximum X/Y/Z) are required.");p(u)||(u=new w);let a=u.halfAxes;d.setColumn(a,0,e,a),d.setColumn(a,1,c,a),d.setColumn(a,2,h,a);let s=et;s.x=(r+o)/2,s.y=(i+C)/2,s.z=(m+l)/2;let f=Mt;f.x=(o-r)/2,f.y=(C-i)/2,f.z=(l-m)/2;let A=u.center;return s=d.multiplyByVector(a,s,s),t.add(n,s,A),d.multiplyByScale(a,f,a),u}var Q=new S,Ot=new t,bt=new S,Tt=new S,St=new S,gt=new S,Et=new S,Rt=new t,$=new t,Wt=new t,H=new t,zt=new t,It=new z,Lt=new z,Ut=new z,qt=new z,jt=new z,Bt=new t,vt=new t,_t=new t,Vt=new t,Dt=new z,Xt=new t,kt=new t,Zt=new t,Yt=new I(t.UNIT_X,0);w.fromRectangle=function(n,e,c,h,r){if(!p(n))throw new b("rectangle is required");if(n.width<0||n.width>g.TWO_PI)throw new b("Rectangle width must be between 0 and 2 * pi");if(n.height<0||n.height>g.PI)throw new b("Rectangle height must be between 0 and pi");if(p(h)&&!g.equalsEpsilon(h.radii.x,h.radii.y,g.EPSILON15))throw new b("Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)");e=e??0,c=c??0,h=h??F.default;let o,i,C,m,l,u,a;if(n.width<=g.PI){let R=X.center(n,Q),q=h.cartographicToCartesian(R,Ot),T=new J(q,h);a=T.plane;let E=R.longitude,rt=n.south<0&&n.north>0?0:R.latitude,ot=S.fromRadians(E,n.north,c,bt),v=S.fromRadians(n.west,n.north,c,Tt),st=S.fromRadians(n.west,rt,c,St),_=S.fromRadians(n.west,n.south,c,gt),it=S.fromRadians(E,n.south,c,Et),ht=h.cartographicToCartesian(ot,Rt),V=h.cartographicToCartesian(v,$),ft=h.cartographicToCartesian(st,Wt),D=h.cartographicToCartesian(_,H),dt=h.cartographicToCartesian(it,zt),wt=T.projectPointToNearestOnPlane(ht,It),Z=T.projectPointToNearestOnPlane(V,Lt),mt=T.projectPointToNearestOnPlane(ft,Ut),Y=T.projectPointToNearestOnPlane(D,qt),lt=T.projectPointToNearestOnPlane(dt,jt);return o=Math.min(Z.x,mt.x,Y.x),i=-o,m=Math.max(Z.y,wt.y),C=Math.min(Y.y,lt.y),v.height=_.height=e,V=h.cartographicToCartesian(v,$),D=h.cartographicToCartesian(_,H),l=Math.min(I.getPointDistance(a,V),I.getPointDistance(a,D)),u=c,K(T.origin,T.xAxis,T.yAxis,T.zAxis,o,i,C,m,l,u,r)}let s=n.south>0,f=n.north<0,A=s?n.south:f?n.north:0,M=X.center(n,Q).longitude,x=t.fromRadians(M,A,c,h,Bt);x.z=0;let N=Math.abs(x.x)<g.EPSILON10&&Math.abs(x.y)<g.EPSILON10?t.UNIT_X:t.normalize(x,vt),P=t.UNIT_Z,O=t.cross(N,P,_t);a=I.fromPointNormal(x,N,Yt);let L=t.fromRadians(M+g.PI_OVER_TWO,A,c,h,Vt);i=t.dot(I.projectPointOntoPlane(a,L,Dt),O),o=-i,m=t.fromRadians(0,n.north,f?e:c,h,Xt).z,C=t.fromRadians(0,n.south,s?e:c,h,kt).z;let U=t.fromRadians(n.east,A,c,h,Zt);return l=I.getPointDistance(a,U),u=0,K(x,O,P,N,o,i,C,m,l,u,r)};w.fromTransformation=function(n,e){return W.typeOf.object("transformation",n),p(e)||(e=new w),e.center=j.getTranslation(n,e.center),e.halfAxes=j.getMatrix3(n,e.halfAxes),e.halfAxes=d.multiplyByScalar(e.halfAxes,.5,e.halfAxes),e};w.clone=function(n,e){if(p(n))return p(e)?(t.clone(n.center,e.center),d.clone(n.halfAxes,e.halfAxes),e):new w(n.center,n.halfAxes)};w.intersectPlane=function(n,e){if(!p(n))throw new b("box is required.");if(!p(e))throw new b("plane is required.");let c=n.center,h=e.normal,r=n.halfAxes,o=h.x,i=h.y,C=h.z,m=Math.abs(o*r[d.COLUMN0ROW0]+i*r[d.COLUMN0ROW1]+C*r[d.COLUMN0ROW2])+Math.abs(o*r[d.COLUMN1ROW0]+i*r[d.COLUMN1ROW1]+C*r[d.COLUMN1ROW2])+Math.abs(o*r[d.COLUMN2ROW0]+i*r[d.COLUMN2ROW1]+C*r[d.COLUMN2ROW2]),l=t.dot(h,c)+e.distance;return l<=-m?B.OUTSIDE:l>=m?B.INSIDE:B.INTERSECTING};var nt=new t,at=new t,ct=new t,Ft=new t,tt=new t,Gt=new t;w.distanceSquaredTo=function(n,e){if(!p(n))throw new b("box is required.");if(!p(e))throw new b("cartesian is required.");let c=t.subtract(e,n.center,et),h=n.halfAxes,r=d.getColumn(h,0,nt),o=d.getColumn(h,1,at),i=d.getColumn(h,2,ct),C=t.magnitude(r),m=t.magnitude(o),l=t.magnitude(i),u=!0,a=!0,s=!0;C>0?t.divideByScalar(r,C,r):u=!1,m>0?t.divideByScalar(o,m,o):a=!1,l>0?t.divideByScalar(i,l,i):s=!1;let f=!u+!a+!s,A,M,x;if(f===1){let O=r;A=o,M=i,a?s||(O=i,M=r):(O=o,A=r),x=t.cross(A,M,tt),O===r?r=x:O===o?o=x:O===i&&(i=x)}else if(f===2){A=r,a?A=o:s&&(A=i);let O=t.UNIT_Y;O.equalsEpsilon(A,g.EPSILON3)&&(O=t.UNIT_X),M=t.cross(A,O,Ft),t.normalize(M,M),x=t.cross(A,M,tt),t.normalize(x,x),A===r?(o=M,i=x):A===o?(i=M,r=x):A===i&&(r=M,o=x)}else f===3&&(r=t.UNIT_X,o=t.UNIT_Y,i=t.UNIT_Z);let y=Gt;y.x=t.dot(c,r),y.y=t.dot(c,o),y.z=t.dot(c,i);let N=0,P;return y.x<-C?(P=y.x+C,N+=P*P):y.x>C&&(P=y.x-C,N+=P*P),y.y<-m?(P=y.y+m,N+=P*P):y.y>m&&(P=y.y-m,N+=P*P),y.z<-l?(P=y.z+l,N+=P*P):y.z>l&&(P=y.z-l,N+=P*P),N};var Jt=new t,Kt=new t;w.computePlaneDistances=function(n,e,c,h){if(!p(n))throw new b("box is required.");if(!p(e))throw new b("position is required.");if(!p(c))throw new b("direction is required.");p(h)||(h=new G);let r=Number.POSITIVE_INFINITY,o=Number.NEGATIVE_INFINITY,i=n.center,C=n.halfAxes,m=d.getColumn(C,0,nt),l=d.getColumn(C,1,at),u=d.getColumn(C,2,ct),a=t.add(m,l,Jt);t.add(a,u,a),t.add(a,i,a);let s=t.subtract(a,e,Kt),f=t.dot(c,s);return r=Math.min(f,r),o=Math.max(f,o),t.add(i,m,a),t.add(a,l,a),t.subtract(a,u,a),t.subtract(a,e,s),f=t.dot(c,s),r=Math.min(f,r),o=Math.max(f,o),t.add(i,m,a),t.subtract(a,l,a),t.add(a,u,a),t.subtract(a,e,s),f=t.dot(c,s),r=Math.min(f,r),o=Math.max(f,o),t.add(i,m,a),t.subtract(a,l,a),t.subtract(a,u,a),t.subtract(a,e,s),f=t.dot(c,s),r=Math.min(f,r),o=Math.max(f,o),t.subtract(i,m,a),t.add(a,l,a),t.add(a,u,a),t.subtract(a,e,s),f=t.dot(c,s),r=Math.min(f,r),o=Math.max(f,o),t.subtract(i,m,a),t.add(a,l,a),t.subtract(a,u,a),t.subtract(a,e,s),f=t.dot(c,s),r=Math.min(f,r),o=Math.max(f,o),t.subtract(i,m,a),t.subtract(a,l,a),t.add(a,u,a),t.subtract(a,e,s),f=t.dot(c,s),r=Math.min(f,r),o=Math.max(f,o),t.subtract(i,m,a),t.subtract(a,l,a),t.subtract(a,u,a),t.subtract(a,e,s),f=t.dot(c,s),r=Math.min(f,r),o=Math.max(f,o),h.start=r,h.stop=o,h};var Qt=new t,$t=new t,Ht=new t;w.computeCorners=function(n,e){W.typeOf.object("box",n),p(e)||(e=[new t,new t,new t,new t,new t,new t,new t,new t]);let c=n.center,h=n.halfAxes,r=d.getColumn(h,0,Qt),o=d.getColumn(h,1,$t),i=d.getColumn(h,2,Ht);return t.clone(c,e[0]),t.subtract(e[0],r,e[0]),t.subtract(e[0],o,e[0]),t.subtract(e[0],i,e[0]),t.clone(c,e[1]),t.subtract(e[1],r,e[1]),t.subtract(e[1],o,e[1]),t.add(e[1],i,e[1]),t.clone(c,e[2]),t.subtract(e[2],r,e[2]),t.add(e[2],o,e[2]),t.subtract(e[2],i,e[2]),t.clone(c,e[3]),t.subtract(e[3],r,e[3]),t.add(e[3],o,e[3]),t.add(e[3],i,e[3]),t.clone(c,e[4]),t.add(e[4],r,e[4]),t.subtract(e[4],o,e[4]),t.subtract(e[4],i,e[4]),t.clone(c,e[5]),t.add(e[5],r,e[5]),t.subtract(e[5],o,e[5]),t.add(e[5],i,e[5]),t.clone(c,e[6]),t.add(e[6],r,e[6]),t.add(e[6],o,e[6]),t.subtract(e[6],i,e[6]),t.clone(c,e[7]),t.add(e[7],r,e[7]),t.add(e[7],o,e[7]),t.add(e[7],i,e[7]),e};var te=new d;w.computeTransformation=function(n,e){W.typeOf.object("box",n),p(e)||(e=new j);let c=n.center,h=d.multiplyByUniformScale(n.halfAxes,2,te);return j.fromRotationTranslation(h,c,e)};var ee=new k;w.isOccluded=function(n,e){if(!p(n))throw new b("box is required.");if(!p(e))throw new b("occluder is required.");let c=k.fromOrientedBoundingBox(n,ee);return!e.isBoundingSphereVisible(c)};w.prototype.intersectPlane=function(n){return w.intersectPlane(this,n)};w.prototype.distanceSquaredTo=function(n){return w.distanceSquaredTo(this,n)};w.prototype.computePlaneDistances=function(n,e,c){return w.computePlaneDistances(this,n,e,c)};w.prototype.computeCorners=function(n){return w.computeCorners(this,n)};w.prototype.computeTransformation=function(n){return w.computeTransformation(this,n)};w.prototype.isOccluded=function(n){return w.isOccluded(this,n)};w.equals=function(n,e){return n===e||p(n)&&p(e)&&t.equals(n.center,e.center)&&d.equals(n.halfAxes,e.halfAxes)};w.prototype.clone=function(n){return w.clone(this,n)};w.prototype.equals=function(n){return w.equals(this,n)};var Pe=w;export{Pe as a};
