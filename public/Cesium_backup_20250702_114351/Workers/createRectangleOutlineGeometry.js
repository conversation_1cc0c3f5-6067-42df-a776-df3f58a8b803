/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as R}from"./chunk-TG7CPYPR.js";import{a as y}from"./chunk-O4MLFQKO.js";import{b as z}from"./chunk-452MW5E6.js";import"./chunk-QF5X4OGE.js";import{a as q}from"./chunk-PFXLBIMV.js";import{a as F}from"./chunk-PSBTKXXJ.js";import{b as v,c as B,d as T}from"./chunk-UJOKCDQH.js";import{d as P}from"./chunk-KYREMICR.js";import{h as b}from"./chunk-VBRVI5XI.js";import{a as D}from"./chunk-5ZFOKSDK.js";import{a as x,b as U,d as w,f as M}from"./chunk-7L2LUDC3.js";import{a as S}from"./chunk-77KFIUJG.js";import"./chunk-7W3OTLHS.js";import"./chunk-X52A3GF7.js";import{a as C}from"./chunk-PX3QTMVS.js";import{e as H}from"./chunk-FE4HG5RY.js";var j=new P,K=new P,Q=new x,W=new b;function Y(t,e){let i=t._ellipsoid,m=e.height,s=e.width,h=e.northCap,p=e.southCap,_=m,A=2,o=0,g=4;h&&(A-=1,_-=1,o+=1,g-=2),p&&(A-=1,_-=1,o+=1,g-=2),o+=A*s+2*_-g;let n=new Float64Array(o*3),l=0,f=0,a,r=Q;if(h)R.computePosition(e,i,!1,f,0,r),n[l++]=r.x,n[l++]=r.y,n[l++]=r.z;else for(a=0;a<s;a++)R.computePosition(e,i,!1,f,a,r),n[l++]=r.x,n[l++]=r.y,n[l++]=r.z;for(a=s-1,f=1;f<m;f++)R.computePosition(e,i,!1,f,a,r),n[l++]=r.x,n[l++]=r.y,n[l++]=r.z;if(f=m-1,!p)for(a=s-2;a>=0;a--)R.computePosition(e,i,!1,f,a,r),n[l++]=r.x,n[l++]=r.y,n[l++]=r.z;for(a=0,f=m-2;f>0;f--)R.computePosition(e,i,!1,f,a,r),n[l++]=r.x,n[l++]=r.y,n[l++]=r.z;let u=n.length/3*2,c=q.createTypedArray(n.length/3,u),E=0;for(let L=0;L<n.length/3-1;L++)c[E++]=L,c[E++]=L+1;c[E++]=n.length/3-1,c[E++]=0;let d=new B({attributes:new F,primitiveType:v.LINES});return d.attributes.position=new T({componentDatatype:D.DOUBLE,componentsPerAttribute:3,values:n}),d.indices=c,d}function X(t,e){let i=t._surfaceHeight,m=t._extrudedHeight,s=t._ellipsoid,h=Y(t,e),p=e.height,_=e.width,A=z.scaleToGeodeticHeight(h.attributes.position.values,i,s,!1),o=A.length,g=new Float64Array(o*2);g.set(A);let n=z.scaleToGeodeticHeight(h.attributes.position.values,m,s);g.set(n,o),h.attributes.position.values=g;let l=e.northCap,f=e.southCap,a=4;l&&(a-=1),f&&(a-=1);let r=(g.length/3+a)*2,u=q.createTypedArray(g.length/3,r);o=g.length/6;let c=0;for(let d=0;d<o-1;d++)u[c++]=d,u[c++]=d+1,u[c++]=d+o,u[c++]=d+o+1;u[c++]=o-1,u[c++]=0,u[c++]=o+o-1,u[c++]=o,u[c++]=0,u[c++]=o;let E;if(l)E=p-1;else{let d=_-1;u[c++]=d,u[c++]=d+o,E=_+p-2}if(u[c++]=E,u[c++]=E+o,!f){let d=_+E-1;u[c++]=d,u[c]=d+o}return h.indices=u,h}function k(t){t=t??M.EMPTY_OBJECT;let e=t.rectangle,i=t.granularity??S.RADIANS_PER_DEGREE,m=t.ellipsoid??w.default,s=t.rotation??0;if(!H(e))throw new C("rectangle is required.");if(b._validate(e),e.north<e.south)throw new C("options.rectangle.north must be greater than options.rectangle.south");let h=t.height??0,p=t.extrudedHeight??h;this._rectangle=b.clone(e),this._granularity=i,this._ellipsoid=m,this._surfaceHeight=Math.max(h,p),this._rotation=s,this._extrudedHeight=Math.min(h,p),this._offsetAttribute=t.offsetAttribute,this._workerName="createRectangleOutlineGeometry"}k.packedLength=b.packedLength+w.packedLength+5;k.pack=function(t,e,i){if(!H(t))throw new C("value is required");if(!H(e))throw new C("array is required");return i=i??0,b.pack(t._rectangle,e,i),i+=b.packedLength,w.pack(t._ellipsoid,e,i),i+=w.packedLength,e[i++]=t._granularity,e[i++]=t._surfaceHeight,e[i++]=t._rotation,e[i++]=t._extrudedHeight,e[i]=t._offsetAttribute??-1,e};var J=new b,V=w.clone(w.UNIT_SPHERE),N={rectangle:J,ellipsoid:V,granularity:void 0,height:void 0,rotation:void 0,extrudedHeight:void 0,offsetAttribute:void 0};k.unpack=function(t,e,i){if(!H(t))throw new C("array is required");e=e??0;let m=b.unpack(t,e,J);e+=b.packedLength;let s=w.unpack(t,e,V);e+=w.packedLength;let h=t[e++],p=t[e++],_=t[e++],A=t[e++],o=t[e];return H(i)?(i._rectangle=b.clone(m,i._rectangle),i._ellipsoid=w.clone(s,i._ellipsoid),i._surfaceHeight=p,i._rotation=_,i._extrudedHeight=A,i._offsetAttribute=o===-1?void 0:o,i):(N.granularity=h,N.height=p,N.rotation=_,N.extrudedHeight=A,N.offsetAttribute=o===-1?void 0:o,new k(N))};var Z=new U;k.createGeometry=function(t){let e=t._rectangle,i=t._ellipsoid,m=R.computeOptions(e,t._granularity,t._rotation,0,W,Z),s,h;if(S.equalsEpsilon(e.north,e.south,S.EPSILON10)||S.equalsEpsilon(e.east,e.west,S.EPSILON10))return;let p=t._surfaceHeight,_=t._extrudedHeight,A=!S.equalsEpsilon(p,_,0,S.EPSILON2),o;if(A){if(s=X(t,m),H(t._offsetAttribute)){let l=s.attributes.position.values.length/3,f=new Uint8Array(l);t._offsetAttribute===y.TOP?f=f.fill(1,0,l/2):(o=t._offsetAttribute===y.NONE?0:1,f=f.fill(o)),s.attributes.applyOffset=new T({componentDatatype:D.UNSIGNED_BYTE,componentsPerAttribute:1,values:f})}let g=P.fromRectangle3D(e,i,p,K),n=P.fromRectangle3D(e,i,_,j);h=P.union(g,n)}else{if(s=Y(t,m),s.attributes.position.values=z.scaleToGeodeticHeight(s.attributes.position.values,p,i,!1),H(t._offsetAttribute)){let g=s.attributes.position.values.length;o=t._offsetAttribute===y.NONE?0:1;let n=new Uint8Array(g/3).fill(o);s.attributes.applyOffset=new T({componentDatatype:D.UNSIGNED_BYTE,componentsPerAttribute:1,values:n})}h=P.fromRectangle3D(e,i,p)}return new B({attributes:s.attributes,indices:s.indices,primitiveType:v.LINES,boundingSphere:h,offsetAttribute:t._offsetAttribute})};var O=k;function $(t,e){return H(e)&&(t=O.unpack(t,e)),t._ellipsoid=w.clone(t._ellipsoid),t._rectangle=b.clone(t._rectangle),O.createGeometry(t)}var Et=$;export{Et as default};
