/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.130.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as nt}from"./chunk-QF6LI2WV.js";import"./chunk-WCO3MWIK.js";import"./chunk-BQNAZUCV.js";import{a as w}from"./chunk-KA7ZX4BQ.js";import"./chunk-IOJRZZG4.js";import"./chunk-QF5X4OGE.js";import"./chunk-S3NLG5WM.js";import"./chunk-AAOMPF7M.js";import{a as ot}from"./chunk-PFXLBIMV.js";import{a as it}from"./chunk-PSBTKXXJ.js";import{b as tt,c as et,d as R}from"./chunk-UJOKCDQH.js";import{d as I}from"./chunk-KYREMICR.js";import"./chunk-VBRVI5XI.js";import{a as N}from"./chunk-5ZFOKSDK.js";import{a as s,d as l,f as K}from"./chunk-7L2LUDC3.js";import{a as V}from"./chunk-77KFIUJG.js";import"./chunk-7W3OTLHS.js";import"./chunk-X52A3GF7.js";import{a as y}from"./chunk-PX3QTMVS.js";import{e as r}from"./chunk-FE4HG5RY.js";var Q=new s,J=new s,lt=new s,st=new s,pt=new s,ut=new s,ft=new s;function E(t){t=t??K.EMPTY_OBJECT;let e=t.positions,o=t.maximumHeights,n=t.minimumHeights;if(!r(e))throw new y("options.positions is required.");if(r(o)&&o.length!==e.length)throw new y("options.positions and options.maximumHeights must have the same length.");if(r(n)&&n.length!==e.length)throw new y("options.positions and options.minimumHeights must have the same length.");let i=t.vertexFormat??w.DEFAULT,c=t.granularity??V.RADIANS_PER_DEGREE,a=t.ellipsoid??l.default;this._positions=e,this._minimumHeights=n,this._maximumHeights=o,this._vertexFormat=w.clone(i),this._granularity=c,this._ellipsoid=l.clone(a),this._workerName="createWallGeometry";let m=1+e.length*s.packedLength+2;r(n)&&(m+=n.length),r(o)&&(m+=o.length),this.packedLength=m+l.packedLength+w.packedLength+1}E.pack=function(t,e,o){if(!r(t))throw new y("value is required");if(!r(e))throw new y("array is required");o=o??0;let n,i=t._positions,c=i.length;for(e[o++]=c,n=0;n<c;++n,o+=s.packedLength)s.pack(i[n],e,o);let a=t._minimumHeights;if(c=r(a)?a.length:0,e[o++]=c,r(a))for(n=0;n<c;++n)e[o++]=a[n];let m=t._maximumHeights;if(c=r(m)?m.length:0,e[o++]=c,r(m))for(n=0;n<c;++n)e[o++]=m[n];return l.pack(t._ellipsoid,e,o),o+=l.packedLength,w.pack(t._vertexFormat,e,o),o+=w.packedLength,e[o]=t._granularity,e};var mt=l.clone(l.UNIT_SPHERE),rt=new w,U={positions:void 0,minimumHeights:void 0,maximumHeights:void 0,ellipsoid:mt,vertexFormat:rt,granularity:void 0};E.unpack=function(t,e,o){if(!r(t))throw new y("array is required");e=e??0;let n,i=t[e++],c=new Array(i);for(n=0;n<i;++n,e+=s.packedLength)c[n]=s.unpack(t,e);i=t[e++];let a;if(i>0)for(a=new Array(i),n=0;n<i;++n)a[n]=t[e++];i=t[e++];let m;if(i>0)for(m=new Array(i),n=0;n<i;++n)m[n]=t[e++];let S=l.unpack(t,e,mt);e+=l.packedLength;let _=w.unpack(t,e,rt);e+=w.packedLength;let f=t[e];return r(o)?(o._positions=c,o._minimumHeights=a,o._maximumHeights=m,o._ellipsoid=l.clone(S,o._ellipsoid),o._vertexFormat=w.clone(_,o._vertexFormat),o._granularity=f,o):(U.positions=c,U.minimumHeights=a,U.maximumHeights=m,U.granularity=f,new E(U))};E.fromConstantHeights=function(t){t=t??K.EMPTY_OBJECT;let e=t.positions;if(!r(e))throw new y("options.positions is required.");let o,n,i=t.minimumHeight,c=t.maximumHeight,a=r(i),m=r(c);if(a||m){let _=e.length;o=a?new Array(_):void 0,n=m?new Array(_):void 0;for(let f=0;f<_;++f)a&&(o[f]=i),m&&(n[f]=c)}let S={positions:e,maximumHeights:n,minimumHeights:o,ellipsoid:t.ellipsoid,vertexFormat:t.vertexFormat};return new E(S)};E.createGeometry=function(t){let e=t._positions,o=t._minimumHeights,n=t._maximumHeights,i=t._vertexFormat,c=t._granularity,a=t._ellipsoid,m=nt.computePositions(a,e,n,o,c,!0);if(!r(m))return;let S=m.bottomPositions,_=m.topPositions,f=m.numCorners,q=_.length,A=q*2,h=i.position?new Float64Array(A):void 0,P=i.normal?new Float32Array(A):void 0,b=i.tangent?new Float32Array(A):void 0,x=i.bitangent?new Float32Array(A):void 0,B=i.st?new Float32Array(A/3*2):void 0,k=0,v=0,T=0,C=0,W=0,g=ft,d=ut,H=pt,Y=!0;q/=3;let p,Z=0,ct=1/(q-f-1);for(p=0;p<q;++p){let L=p*3,u=s.fromArray(_,L,Q),M=s.fromArray(S,L,J);if(i.position&&(h[k++]=M.x,h[k++]=M.y,h[k++]=M.z,h[k++]=u.x,h[k++]=u.y,h[k++]=u.z),i.st&&(B[W++]=Z,B[W++]=0,B[W++]=Z,B[W++]=1),i.normal||i.tangent||i.bitangent){let O=s.clone(s.ZERO,st),j=s.subtract(u,a.geodeticSurfaceNormal(u,J),J);if(p+1<q&&(O=s.fromArray(_,L+3,st)),Y){let G=s.subtract(O,u,lt),at=s.subtract(j,u,Q);g=s.normalize(s.cross(at,G,g),g),Y=!1}s.equalsEpsilon(u,O,V.EPSILON10)?Y=!0:(Z+=ct,i.tangent&&(d=s.normalize(s.subtract(O,u,d),d)),i.bitangent&&(H=s.normalize(s.cross(g,d,H),H))),i.normal&&(P[v++]=g.x,P[v++]=g.y,P[v++]=g.z,P[v++]=g.x,P[v++]=g.y,P[v++]=g.z),i.tangent&&(b[C++]=d.x,b[C++]=d.y,b[C++]=d.z,b[C++]=d.x,b[C++]=d.y,b[C++]=d.z),i.bitangent&&(x[T++]=H.x,x[T++]=H.y,x[T++]=H.z,x[T++]=H.x,x[T++]=H.y,x[T++]=H.z)}}let z=new it;i.position&&(z.position=new R({componentDatatype:N.DOUBLE,componentsPerAttribute:3,values:h})),i.normal&&(z.normal=new R({componentDatatype:N.FLOAT,componentsPerAttribute:3,values:P})),i.tangent&&(z.tangent=new R({componentDatatype:N.FLOAT,componentsPerAttribute:3,values:b})),i.bitangent&&(z.bitangent=new R({componentDatatype:N.FLOAT,componentsPerAttribute:3,values:x})),i.st&&(z.st=new R({componentDatatype:N.FLOAT,componentsPerAttribute:2,values:B}));let $=A/3;A-=6*(f+1);let F=ot.createTypedArray($,A),D=0;for(p=0;p<$-2;p+=2){let L=p,u=p+2,M=s.fromArray(h,L*3,Q),O=s.fromArray(h,u*3,J);if(s.equalsEpsilon(M,O,V.EPSILON10))continue;let j=p+1,G=p+3;F[D++]=j,F[D++]=L,F[D++]=G,F[D++]=G,F[D++]=L,F[D++]=u}return new et({attributes:z,indices:F,primitiveType:tt.TRIANGLES,boundingSphere:new I.fromVertices(h)})};var X=E;function ht(t,e){return r(e)&&(t=X.unpack(t,e)),t._ellipsoid=l.clone(t._ellipsoid),X.createGeometry(t)}var Ot=ht;export{Ot as default};
