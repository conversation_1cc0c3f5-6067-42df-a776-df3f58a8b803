import * as fs from 'fs-extra';
import * as path from 'node:path';
import { fileURLToPath } from 'node:url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function copyCesium(): Promise<void> {
  const source = path.resolve(__dirname, '../src/Cesium');
  const dest = path.resolve(__dirname, '../dist/src/Cesium');
  
  try {
    await fs.ensureDir(dest);
    await fs.copy(source, dest);
    console.log('Cesium static resources copied successfully!');
  } catch (err) {
    console.error('Error copying Cesium resources:', err);
    process.exit(1);
  }
}

copyCesium();