<template>
  <div ref="chartRef" class="crossing-chart" style="width: 100%; height: 400px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useResizeObserver } from '@vueuse/core';
import { createResizeHandler } from '@/utils/style-helper';
import * as echarts from 'echarts';
import { crossStatistics } from '@/js/common/requestData';

// Props
interface Props {
  projectId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  projectId: ''
});

// Refs
const chartRef = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;
let resizeHandler: { handleResize: () => void; cleanup: () => void } | null = null;
let stopResizeObserver: (() => void) | null = null;

// 获取图表数据
const exchartsdata = async () => {
  try {
    if (!props.projectId) {
      console.warn('CrossingChart: projectId is required');
      return;
    }

    const response = await crossStatistics({ projectId: props.projectId });

    // 安全检查：确保response和response.data存在
    if (!response || !response.data) {
      console.warn('CrossingChart: No data received from API');
      return;
    }

    const data = response.data;

    // 安全检查：确保crossPlanCount存在
    if (!data.crossPlanCount) {
      console.warn('CrossingChart: crossPlanCount is null or undefined');
      return;
    }

    // 更新图表数据
    updateChart(data);
  } catch (error) {
    console.error('CrossingChart: Error fetching data:', error);
  }
};

// 更新图表
const updateChart = (data: any) => {
  if (!chartInstance) return;

  const options = {
    title: {
      text: '交叉跨越统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '交叉跨越',
        type: 'pie',
        radius: '50%',
        data: [
          { value: data.crossPlanCount || 0, name: '交叉跨越计划' },
          { value: data.completedCount || 0, name: '已完成' },
          { value: data.pendingCount || 0, name: '待处理' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  chartInstance.setOption(options);
};

onMounted(async () => {
  if (chartRef.value) {
    // 初始化图表
    chartInstance = echarts.init(chartRef.value);

    // 设置默认选项
    const defaultOptions = {
      title: {
        text: '交叉跨越统计',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '交叉跨越',
          type: 'pie',
          radius: '50%',
          data: [],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    chartInstance.setOption(defaultOptions);

    // 创建 resize 处理器
    resizeHandler = createResizeHandler(chartInstance, chartRef.value);

    // 使用 ResizeObserver 监听容器大小变化
    const { stop } = useResizeObserver(chartRef.value, () => {
      resizeHandler?.handleResize();
    });
    stopResizeObserver = stop;

    // 加载数据
    await exchartsdata();
  }
});

onUnmounted(() => {
  resizeHandler?.cleanup();
  stopResizeObserver?.();
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

// 暴露方法供外部调用
defineExpose({
  refreshData: exchartsdata
});
</script>

<style scoped>
.crossing-chart {
  width: 100%;
  height: 400px;
}
</style>