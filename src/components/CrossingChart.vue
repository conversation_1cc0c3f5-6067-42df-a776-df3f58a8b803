import { defineComponent, ref, onMounted, onUnmounted } from 'vue';
import { useResizeObserver } from '@vueuse/core';
import { createResizeHandler } from '@/utils/style-helper';
import * as echarts from 'echarts';

export default defineComponent({
    name: 'CrossingChart',
    props: {
        // ... your props
    },
    setup() {
        const chartRef = ref<HTMLElement | null>(null);
        let chartInstance: echarts.ECharts | null = null;
        let resizeHandler: { handleResize: () => void; cleanup: () => void } | null = null;
        let stopResizeObserver: (() => void) | null = null;

        onMounted(() => {
            if (chartRef.value) {
                // 初始化图表
                chartInstance = echarts.init(chartRef.value);
                
                // 设置图表选项
                const options = {
                    // ... your chart options
                };
                chartInstance.setOption(options);

                // 创建 resize 处理器
                resizeHandler = createResizeHandler(chartInstance, chartRef.value);

                // 使用 ResizeObserver 监听容器大小变化
                const { stop } = useResizeObserver(chartRef.value, () => {
                    resizeHandler?.handleResize();
                });
                stopResizeObserver = stop;
            }
        });

        onUnmounted(() => {
            resizeHandler?.cleanup();
            stopResizeObserver?.();
            if (chartInstance) {
                chartInstance.dispose();
                chartInstance = null;
            }
        });

        return {
            chartRef
        };
    }
});