<template>
  <div ref="playerContainer" style="width: 100%; height: 100%;"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
  videoUrl: {
    type: String,
    required: true
  },
  options: {
    type: Object,
    default: () => ({})
  }
});

const playerContainer = ref(null);
let jessibuca = null;

import { JESSICUBA_DEFAULT_OPTIONS } from '@/config/jessibucaConfig';

onMounted(async () => {
  if (playerContainer.value) {
    try {
      // 检查Jessibuca是否可用
      if (typeof window !== 'undefined' && window.Jessibuca) {
        // 使用全局Jessibuca
        const Jessibuca = window.Jessibuca;
        initPlayer(Jessibuca);
      } else {
        // 尝试动态导入
        try {
          const script = document.createElement('script');
          script.src = `${import.meta.env.BASE_URL}jessibuca/jessibuca.js`;
          script.onload = () => {
            if (window.Jessibuca) {
              initPlayer(window.Jessibuca);
            }
          };
          script.onerror = () => {
            console.error('无法加载Jessibuca脚本');
          };
          document.head.appendChild(script);
        } catch (error) {
          console.error('Jessibuca加载失败:', error);
        }
      }
    } catch (error) {
      console.error('Jessibuca初始化失败:', error);
    }
  }
});

function initPlayer(Jessibuca) {
  const finalOptions = {
    container: playerContainer.value,
    decoder: `${import.meta.env.BASE_URL}jessibuca/decoder.js`,
    ...JESSICUBA_DEFAULT_OPTIONS,
    ...props.options
  };
  
  // 确保sampleRate是有效的数字
  if (finalOptions.sampleRate === undefined || isNaN(finalOptions.sampleRate)) {
    finalOptions.sampleRate = 48000;
  }
  
  jessibuca = new Jessibuca(finalOptions);

  if (props.videoUrl) {
    jessibuca.play(props.videoUrl);
  }
}

watch(() => props.videoUrl, (newUrl) => {
  if (jessibuca && newUrl) {
    jessibuca.play(newUrl);
  }
});

onUnmounted(() => {
  if (jessibuca) {
    jessibuca.destroy();
    jessibuca = null;
  }
});

// Expose player instance if needed
defineExpose({ jessibuca });
</script>