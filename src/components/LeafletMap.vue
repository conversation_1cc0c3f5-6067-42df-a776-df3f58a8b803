<template>
    <div style="background-color: rgba(9, 37, 51, 0.4);border: 2px solid;border-color: #1563bf;">
      <div id="leafletMapDiv" style="display: flex;justify-content: center;margin-top: 2%;">
          <div id="userButton" style="position: absolute;top: 2%;left: 2%;z-index: 102;">
              <el-button type="primary" plain @click="changeMode">{{ mapFollowMmode == 0 ? '跟随模式' : '全局模式' }}</el-button>
          </div>
          <div id="MapDiv" style="position: absolute;width: 96%;height: 96%;z-index: 101;">
          </div>
      </div>
    </div>
</template>

<script>
import 'leaflet/dist/leaflet.css'
import L from 'leaflet'
import { LOCAL_MAP_URL } from '@/config/global';

const DefaultCenter = [22.61220474313551, 114.41292899937217]
const DefaultLevel = 11.9
const DefaultMapUrlIndex = 1 // 配置选择使用在线或离线地图 0本地地图资源 1天地图 2高德地图

export default {
    data () {
        return {
            map: null,
            center: null,
            marker: null,
            polylines: [],
            points: [],
            mapFollowMmode: 0 // 0表示全局模式 1表示跟随模式
        }
    },
    mounted () {
        this.map = L.map('MapDiv', {
            zoomSnap: 0.1, // 开启地图的缩放级别可以为小数
            zoomControl: false,
            dragging: false, // 禁止鼠标拖拽移动
            doubleClickZoom: false, // 禁止双击缩放
            scrollWheelZoom: false// 禁止鼠标滑轮缩放
        })
        this.initMapLayer()
        this.map.setView(DefaultCenter, DefaultLevel)
        this.map.on('zoomend', this.onZoomEnd)
    },
    methods: {
        initMapLayer () {
            switch (DefaultMapUrlIndex) {
                case 0: // 使用本地geoserver资源
                    let geoserverLayer = L.tileLayer.wms('http://127.0.0.1:5030/geoserver/localMap1/wms?', {
                        layers: 'localMap1:map1_WGS84_L18',
                        format: 'image/png',
                        transparent: true,
                    })
                    this.map.addLayer(geoserverLayer)
                    break
                case 1: // 使用天地图在线资源
                    let tiandituLayer1 = L.tileLayer('http://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=56b81006f361f6406d0e940d2f89a39c', {
                        subdomains: ['0', '1', '2', '3', '4', '5', '6', '7']
                    })


                    let tiandituLayer2 = L.tileLayer('http://t{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=56b81006f361f6406d0e940d2f89a39c', {
                        subdomains: ['0', '1', '2', '3', '4', '5', '6', '7']
                    })
                    this.map.addLayer(tiandituLayer1)
                    this.map.addLayer(tiandituLayer2)
                    break
                case 2:
                    let layer = L.tileLayer('http://webst0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=6&x={x}&y={y}&z={z}', {
                        subdomains: ['1', '2', '3', '4'],
                        minZoom: 1, // 最小放缩级别
                        maxZoom: 18 // 最大放缩级别
                    })
                    this.map.addLayer(layer)
                    break
                default:
                    console.error('小地图默认地图资源创建失败 DefaultMapUrlIndex=', DefaultMapUrlIndex)
                    break
            }
        },
        setCenter (center) {
            this.center = center
            if (this.mapFollowMmode == 1) {
                this.map.panTo(center.position)
            } else {
                this.map.panTo(DefaultCenter)
            }
            this.updateCenterIcon()
        },

        updateCenterIcon (strHtml) {
            // 自定义图标
            let imgUrl = './img/camera.jpg'
            let position = this.center?.position || DefaultCenter
            let angle = this.center?.angle || 0
            angle = angle % 360 // 确保数值在 0- 360 之间
            let divHtml = strHtml || `<div id="testdiv" style="transform: rotate(${angle}deg);"><div id="shap"></div><img src=${imgUrl} style="width:30px; transform: rotate(270deg);"></div>`
            const customIcon = L.divIcon({
                className: 'custom-icon',
                html: divHtml,
                iconAnchor: [50, 50]
            })
            // 使用自定义图标创建标记
            if (!this.marker) {
                this.marker = L.marker(position, { icon: customIcon }).addTo(this.map);
            } else {
                this.marker.setLatLng(position)
                this.marker.setIcon(customIcon)
            }
        },
        removeLines (lineIDs) {
            if (!lineIDs) {
                // 该参数为空则移除全部线路
                for (let id in this.polylines) {
                    this.map.removeLayer(this.polylines[id])
                }
                this.polylines = {}
            } else {
                for (let i = 0; i < lineIDs.length; i++) {
                    if (this.polylines[lineIDs[i]]) {
                        this.map.removeLayer(this.polylines[lineIDs[i]])
                        delete this.polylines[lineIDs[i]]
                    }
                }
            }
        },
        loadPowerLine (id, positions) {
            let polyline = L.polyline(positions, {
                color: 'green', // 折线颜色
                weight: 3,     // 折线宽度
                opacity: 0.6,  // 折线透明度
                smoothFactor: 1 // 折线平滑度
            }).addTo(this.map)
            this.polylines[id] = polyline
        },
        loadPointIcons (id, positions, names) {
            if (!this.points[id]) {
                this.points[id] = { markers: [], names: names }
            }
            for (let i = 0; i < positions.length; i++) {
                let htmlStr = `<div style="background-color: red; color: #000; padding: 0px; border-radius: 50%; width: 10px; height: 10px; text-align: center;"></div>`
                let _icon = L.divIcon({
                    className: 'custom-marker-point',
                    interactive: false, // 禁用交互功能，使图标无法点击
                    html: htmlStr,
                    iconAnchor: [5, 5]
                })
                let marker = L.marker(positions[i], { icon: _icon })
                marker.on('mouseover', (e) => {
                    // 鼠标悬停时候显示文本内容
                    marker.bindTooltip(`${names[i]}`, {
                        permanent: false,
                        interactive: false,
                        offset: [0, 0] // 设置文本的偏移量
                    }).openTooltip();
                })
                marker.on('mouseout', (e) => {
                    marker.closeTooltip()
                })
                // marker.addTo(this.map) // 创建完成不加载到地图 统一由onZoomEnd函数进行判断是否添加到地图中
                this.points[id].markers.push(marker)
            }
            this.onZoomEnd()
        },
        updatePointIcons (isShouPointName) {
            for (let id in this.points) {
                for (let i = 0; i < this.points[id].markers.length; i++) {
                    let _icon = L.divIcon({
                        className: 'custom-marker-point',
                        interactive: false, // 禁用交互功能，使图标无法点击
                        html: isShouPointName == 1 ?
                            `<div style="background-color: red; color: #000; padding-left: 10px; border-radius: 50%; width: 10px; height: 10px;">${this.points[id].names[i]}</div>` :
                            `<div style="background-color: red; color: #000; padding: 0px; border-radius: 50%; width: 10px; height: 10px; text-align: center;"></div>`,
                        iconAnchor: [5, 5]
                    })
                    this.points[id].markers[i].setIcon(_icon)
                }
            }
        },
        onZoomEnd () {
            let zoomLevel = this.map.getZoom()
            console.log('leaflet zoom', zoomLevel, this.center)
            if (zoomLevel > 12) {
                for (let id in this.points) {
                    for (let i = 0; i < this.points[id].markers.length; i++) {
                        if (!this.map.hasLayer(this.points[id].markers[i])) {
                            this.map.addLayer(this.points[id].markers[i])
                        }
                    }
                }
            } else {
                for (let id in this.points) {
                    for (let i = 0; i < this.points[id].markers.length; i++) {
                        if (this.map.hasLayer(this.points[id].markers[i])) {
                            this.map.removeLayer(this.points[id].markers[i])
                        }
                    }
                }
            }
        },
        changeMode () {
            this.mapFollowMmode = !this.mapFollowMmode
            this.updatePointIcons(this.mapFollowMmode)
            if (this.mapFollowMmode == 0) {
                this.map.setView(DefaultCenter, DefaultLevel)
            } else {
                this.map.setView(this.center.position, DefaultLevel + 1.2)
            }
        }
    }
}
</script>

<style>
/* 隐藏 Leaflet 的默认 Logo */
.leaflet-control-attribution {
    display: none;
}

.custom-icon {
    height: 10px;
}

#testdiv {
    margin: 0 auto;
    width: 100px;
    height: 100px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    /* background:#d8d8d88f;
  border: 1px solid rgba(255, 0, 0, 0.9); */
}

#shap {
    position: absolute;
    width: 100px;
    height: 100%;
    border-top: 100px solid #ff000070;
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
    bottom: 50px;
    border-radius: 20%;
}

.userButton {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    background: yellow;
    z-index: 500;
}

.leafletMapDiv {
    position: relative;
    width: 100%;
    height: 100%;
    background: red;
    z-index: 101;
}

.MapDiv {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    background: green;
    z-index: 500;
}
</style>
