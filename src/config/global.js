import {useRoute} from "vue-router"; 
export const MODEL_STATIC_URL =  import.meta.env.VITE_MODEL_STATIC_URL;
export const LOCALMAP_VALUE = ""+import.meta.env.VITE_LOCALMAP;
export const MAPROUTE_URL =  import.meta.env.VITE_MAPROUTE_URL;
export const FixTowerHeight = true;
// 使用环境变量来设置LOCAL_MAP_URL，让Vite代理来处理跨域问题
export const LOCAL_MAP_URL = import.meta.env.VITE_LOCALMAP_URL;

/**
 * 获取地图服务完整URL
 * 使用相对路径，由代理系统处理跨域问题
 * @returns {string} 地图服务URL
 */
export function getFullMapServiceUrl() {
  // 日志输出当前环境变量
  console.log('地图服务环境变量:', {
    VITE_LOCALMAP_URL: import.meta.env.VITE_LOCALMAP_URL,
    LOCAL_MAP_URL: LOCAL_MAP_URL
  });
  
  // 检查并记录有效的URL
  const geoserverUrl = LOCAL_MAP_URL || '/geoserver';
  console.log('使用GeoServer URL:', geoserverUrl);
  
  // 使用相对路径，避免硬编码
  return geoserverUrl;
}

export let WGS84_to_GCJ02 = false; // 如果使用GCJ02坐标系的地图影像 则需要进行坐标转换

export const LINE_POINTS_NUM = 15;
// 后端API路由
export const API_URL = {
    checkToken: '/admin-api/manage-backstage/user/token',
    login: '/admin-api/manage-backstage/user/login',
    towerInfo: '/admin-api/manage-backstage/data/tower',
    towersInfo: '/admin-api/manage-backstage/tdtowerModel/tdtowerModellist',
    projectDetail: '/admin-api/manage-backstage/tdprojectDetail/tdprojectDetaillist',
    lines: '/admin-api/manage-backstage/tdline/tdlinelist',
    lineDetail: '/admin-api/manage-backstage/tdlineDetail/tdlineDetaillist',
    insulatorsInfo: '/admin-api/manage-backstage/tdinsulatorModel/tdinsulatorModellist',
    insulatorAllocation: '/admin-api/manage-backstage/tdinsulatorAllocation/tdinsulatorAllocationlist',

    // 版本V2.0线路数据API
    swinsulatorAllocationlist: '/admin-api/manage-backstage/swinsulatorAllocation/swinsulatorAllocationlist', // 获得配串信息
    swtowerMountslist: '/admin-api/manage-backstage/swtowerMounts/swtowerMountslist', // 挂点信息
    swinsulatorModellist: '/admin-api/manage-backstage/swinsulatorModel/swinsulatorModellist',   // 绝缘子模型信息
    swlinelist: '/admin-api/manage-backstage/swline/swlinelist',   // 线路线序信息
    swtowerModellist: '/admin-api/manage-backstage/swtowerModel/swtowerModellist',   // 杆塔模型信息
    swtowerList: '/admin-api/manage-backstage/tower/list',   // 杆塔列表
};

export const CONTEXT = import.meta.env.VITE_CONTEXT;

export const BACKEN_URL = import.meta.env.VITE_BACKEN_URL;

export const MODEL_SHOW_DISTANCE = {
    tower: 10000,
    label: 10000,
    insulator: 500,
}
let queryObj;
/**获取访问后台Token */
export function getToken(){
    queryObj=queryObj? queryObj : useRoute().query
    return queryObj.token
}
/**获取初始化时项目ID */
export function getInitProjectId(){
    queryObj=queryObj? queryObj : useRoute().query
    console.log("getInitProjectId",queryObj.projectId)
    return queryObj.projectId
}

// 全息投影相机初始位置
export const CAMERA_POSITION ={
  x:4000,y:0,z:3000
}