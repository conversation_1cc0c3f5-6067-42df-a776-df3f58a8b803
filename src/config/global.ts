export interface GlobalConfig {
    MODEL_STATIC_URL: string;
}

const config: GlobalConfig = {
    MODEL_STATIC_URL: '/modelStatic'
};

export { config as globalConfig };

export const LOCALMAP_VALUE = 0;
export const MODEL_STATIC_URL = import.meta.env.VITE_SERVER_MODELSTATIC_TARGET || 'http://127.0.0.1:5030/modelStatic'
export const LOCAL_MAP_URL = import.meta.env.VITE_LOCAL_MAP_URL || 'http://127.0.0.1:5030/mapService'

export const getMapServiceUrl = () => {
    return import.meta.env.VITE_MAP_SERVICE_URL || 'http://127.0.0.1:5030/mapService'
} 

export const LINE_POINTS_NUM = 15;

export const API_URL = {
    checkToken: '/admin-api/manage-backstage/user/token',
    login: '/admin-api/manage-backstage/user/login',
    towerInfo: '/admin-api/manage-backstage/data/tower',
    towersInfo: '/admin-api/manage-backstage/tdtowerModel/tdtowerModellist',
    projectDetail: '/admin-api/manage-backstage/tdprojectDetail/tdprojectDetaillist',
    lines: '/admin-api/manage-backstage/tdline/tdlinelist',
    lineDetail: '/admin-api/manage-backstage/tdlineDetail/tdlineDetaillist',
    insulatorsInfo: '/admin-api/manage-backstage/tdinsulatorModel/tdinsulatorModellist',
    insulatorAllocation: '/admin-api/manage-backstage/tdinsulatorAllocation/tdinsulatorAllocationlist',

    // 版本V2.0线路数据API
    swinsulatorAllocationlist: '/admin-api/manage-backstage/swinsulatorAllocation/swinsulatorAllocationlist', // 获得配串信息
    swtowerMountslist: '/admin-api/manage-backstage/swtowerMounts/swtowerMountslist', // 挂点信息
    swinsulatorModellist: '/admin-api/manage-backstage/swinsulatorModel/swinsulatorModellist',   // 绝缘子模型信息
    swlinelist: '/admin-api/manage-backstage/swline/swlinelist',   // 线路线序信息
    swtowerModellist: '/admin-api/manage-backstage/swtowerModel/swtowerModellist',   // 杆塔模型信息
    swtowerList: '/admin-api/manage-backstage/tower/list',   // 杆塔列表
};

export const FixTowerHeight = true;
export const CONTEXT = 'td-power-3d';

export const MODEL_SHOW_DISTANCE = {
    tower: 10000,
    label: 10000,
    insulator: 500,
}

export const WGS84_to_GCJ02 = true;

export const CAMERA_POSITION = {
    x: 1500,
    y: 1500,
    z: 1500
};