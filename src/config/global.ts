export interface GlobalConfig {
    MODEL_STATIC_URL: string;
}

const config: GlobalConfig = {
    MODEL_STATIC_URL: '/modelStatic'
};

export { config as globalConfig };

export const MODEL_STATIC_URL = import.meta.env.VITE_SERVER_MODELSTATIC_TARGET || 'http://127.0.0.1:5030/modelStatic'
export const LOCAL_MAP_URL = import.meta.env.VITE_LOCAL_MAP_URL || 'http://127.0.0.1:5030/mapService'

export const getMapServiceUrl = () => {
    return import.meta.env.VITE_MAP_SERVICE_URL || 'http://127.0.0.1:5030/mapService'
} 