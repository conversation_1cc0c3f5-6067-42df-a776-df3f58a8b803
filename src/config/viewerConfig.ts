// 地图数据来源标志：
// 0 - 使用在线地图 (高德)
// 1 - 使用本地GeoServer地图
export const useLocalMapDataFlag = import.meta.env.VITE_LOCALMAP === '1' ? 1 : 0;

// 是否在本地地图失败时自动使用备用地图
export const useFallbackMap = import.meta.env.VITE_USE_FALLBACK_MAP === 'true';

// 高德地图配置选项
export const AmapImageryProviderOptions = {
    style: 'img', // style: img(卫星图)、elec(路网图)、cva(标注图)、local1/local2(本地图)
    crs: 'WGS84', // 使用84坐标系，默认为：GCJ02
    maximumLevel: 18, // 最大层级，超过此级别的请求会被优化掉
    subdomains: ['01', '02', '03', '04'], // 子域名，用于负载均衡
    enablePickFeatures: false, // 禁用拾取功能，减少不必要的请求
    retryAttempts: 3, // 失败重试次数
    timeoutInterval: 10000, // 请求超时时间
    credit: '高德电子地图' // 版权信息
}
