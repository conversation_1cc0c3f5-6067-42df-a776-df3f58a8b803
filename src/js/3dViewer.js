import * as Cesium from '@/Cesium'
// import * as Cesium from 'cesium';
import {useRoute} from "vue-router"; 
import {LOCALMAP_VALUE, MODEL_STATIC_URL} from '@/config/global.js';
// import { AmapImageryProvider,BaiduImageryProvider, GeoVisImageryProvider, TdtImageryProvider }  from '@dvgis/cesium-map'
import AmapImageryProvider from '@/js/cesium-map/AmapImageryProvider.js'

import ModelCache from '@/js/common/modelCache.ts'

let localModelCache = new ModelCache();

let useLocalMapDataFlag = null;

// const terrainProvider = Cesium.createWorldTerrain()
function isLocalMap(){
    // debugger
    const localMap = +(useRoute().query.localMap==undefined ? LOCALMAP_VALUE:useRoute().query.localMap)
    console.log(`localMap=${localMap}`)
    return localMap
}

export function createViewer (container) {
    // 设置Cesium基础路径
    window.CESIUM_BASE_URL = './Cesium/'; // 使用相对路径

    Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.oGKBB47s-CAIDhbfPGgczcj3NSmSq6EJt5NVv_a2TS4'
    // Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.ir1Iiehq-Jb4RP0K-BwePOxoZE-l-70OilX7XN2XiEg'
    // Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.8SRPscNWn-IlEe7W8Z6P3ALQ1IXfogzZvDP113jt0o0'
    // 影像底图加载
    // var options = {
    //     style: 'img', // style: img、vec、normal、dark
    //     key: `7711a24780452f03bb7c02fba98183b9`,
    //     crs: 'WGS84',
    //     maximumLevel: 18,
    // }
    // const imageryProvider = new Cesium.AmapImageryProvider(options)
    //加载高德地图影像
    function loadGaoDeImageryLayers(type){
        let tdtLayer = null;
        if (type == 0) {
            //高德矢量图
            tdtLayer = new Cesium.UrlTemplateImageryProvider({
                url: "http://webrd02.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}",
                minimumLevel: 3,
                maximumLevel: 18
            })
            // this.viewer.imageryLayers.addImageryProvider(tdtLayer);
        } else if (type == 1) {
            //高德影像
            tdtLayer = new Cesium.UrlTemplateImageryProvider({
                url: "https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
                minimumLevel: 3,
                maximumLevel: 18
            })
            // this.viewer.imageryLayers.addImageryProvider(tdtLayer);
        } else if (type == 2) {
            //高德路网中文注记
            tdtLayer = new Cesium.UrlTemplateImageryProvider({
                url: "http://webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8",
                minimumLevel: 3,
                maximumLevel: 18
            })
            // this.viewer.imageryLayers.addImageryProvider(tdtLayer);
        }
        return tdtLayer;
    }
    // WGS84_to_GCJ02 = true;
    const imageryProvider_gaode = loadGaoDeImageryLayers(1);
    // const imageryProvider = new Cesium.WebMapTileServiceImageryProvider({
    //     url:
    //     // 'http://t0.tianditu.com/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=7711a24780452f03bb7c02fba98183b9',
    //         `http://t{s}.tianditu.com/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=7711a24780452f03bb7c02fba98183b9`,
    //     layer: 'img',
    //     style: 'default',
    //     format: 'tiles',
    //     tileMatrixSetID: 'w',
    //     subdomains: [0, 1, 2, 3, 4, 5, 6, 7],
    //     credit: new Cesium.Credit('天地图全球影像服务'),
    //     maximumLevel: 16,
    //     show: false
    // })

    // const imageryProvider = new Cesium.ArcGisMapServerImageryProvider({
    //     url: "http://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer",
    //     maximumLevel: 16,
    // })
    // const imageryProvider = new Cesium.UrlTemplateImageryProvider({
    //     url: "https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
    //     maximumLevel: 18,
    // })

    // 创建默认的高程地形数据
    const terrainProvider_default = Cesium.createWorldTerrain({
        // requestWaterMask: true,
        // requestVertexNormals: true
    })

    // 创建地形提供者
    let dem_url = MODEL_STATIC_URL+"/DEM/";
    var terrainProvider_local = new Cesium.CesiumTerrainProvider({
        url: Cesium.buildModuleUrl(`${dem_url}`) // 指定地形文件的路径
    });
    let imageryProvider_local = new Cesium.UrlTemplateImageryProvider({
        // url: MODEL_STATIC_URL+"/MAP/{z}/{x}/{y}.png",
        url: MODEL_STATIC_URL+"/MAP1/{z}/{y}/{x}.png",
        maximumLevel: 16,
        crs: new Cesium.WebMercatorProjection()
    })
    // let imageryProvider_local = new Cesium.TileMapServiceImageryProvider({
    //     url: MODEL_STATIC_URL+"/MAP",
    //     maximumLevel: 15,
    //     crs: new Cesium.WebMercatorProjection()
    // })
    // 根据输入参数加载地图影像数据 
    useLocalMapDataFlag = isLocalMap();
    let options_style = null;
    let maximumLevel_set = null;
    switch(useLocalMapDataFlag){
        case 0:
            options_style = 'img';
            maximumLevel_set = 18;
            break;
        case 1:
            options_style = 'local1';
            maximumLevel_set = 17;
            break;
        case 2:
            options_style = 'local2';
            maximumLevel_set = 18;
            break;
        default:
            options_style = 'img';
            maximumLevel_set = 18;
            break;
    }
    console.log(`useLocalMapDataFlag=${useLocalMapDataFlag}, ${options_style}`);

    var options = {
        style: options_style, // style: img、elec、cva local1 local2 其中local和local1分别是加载本地地图影像数据
        crs: 'WGS84', // 使用84坐标系，默认为：GCJ02
        maximumLevel: maximumLevel_set,
        // key: '7711a24780452f03bb7c02fba98183b9' // 天地图的key
    }
    let imageProvider = new AmapImageryProvider(options)
    // viewer.imageryLayers.add(new Cesium.ImageryLayer(new AmapImageryProvider(options)));
    let viewer = new Cesium.Viewer(container, {
        imageryProvider: imageProvider, // 地图影像底图加载
        // imageryProvider: useLocalMapDataFlag==1 ? imageryProvider_local : null, // 地图影像底图加载
        // imageryProvider: null, 
        animation: false, // 动画控制不显示
        timeline: true, // 时间线显示
        baseLayerPicker: false, // 右上角图层选择控件
        geocoder: false, // 搜索框
        homeButton: false, // 视角返回初始位置
        navigationHelpButton: false, // 导航帮助(手势，鼠标)
        sceneModePick: false, // 模式切换按钮
        fullscreenButton: false, //全屏按钮不显示
        infoBox: false,
        terrainProvider: useLocalMapDataFlag==0 ? terrainProvider_default : terrainProvider_local,
        selectionIndicator: false,   // 选中实体出现绿色选择框
    })
    if (useLocalMapDataFlag == 0) {
        // 添加地图道路信息 - 改为添加卫星图层而非道路图层
        var options = {
            style: 'img', // 使用卫星图像而不是道路图层
            crs: 'WGS84', // 使用84坐标系，默认为：GCJ02
            maximumLevel: 18,
            // 添加额外参数以避免连接重置问题
            customHeaders: {
                'User-Agent': 'Mozilla/5.0',
                'Accept': 'image/webp,image/*,*/*;q=0.8',
                'Referer': 'https://webst.is.autonavi.com/'
            }
        }
        // 不添加额外图层，避免覆盖卫星图
        // viewer.imageryLayers.add(new Cesium.ImageryLayer(new AmapImageryProvider(options)));
    }


    /*********** 腾讯地图 开始************/
    /*
    let base1 = new Cesium.UrlTemplateImageryProvider({
        url:
            'https://p2.map.gtimg.com/sateTiles/{z}/{sx}/{sy}/{x}_{reverseY}.jpg?version=244',
        customTags: {
            sx: function (imageryProvider, x, y, level) {
                return x >> 4
            },
            sy: function (imageryProvider, x, y, level) {
                return ((1 << level) - y) >> 4
            }
        }
    })
    viewer.imageryLayers.addImageryProvider(base1)

    //腾讯标注
    let base2 = new Cesium.UrlTemplateImageryProvider({
        url:
            'https://rt3.map.gtimg.com/tile?z={z}&x={x}&y={reverseY}&styleid=2&version=859',


    })
    viewer.imageryLayers.addImageryProvider(base2)

    let base3 = new Cesium.UrlTemplateImageryProvider({
        url:
            'https://rtt2b.map.qq.com/rtt/?z={z}&x={x}&y={reverseY}&times=2&time=1631504424185'
    })
    viewer.imageryLayers.addImageryProvider(base3)
    */

    /*********** 腾讯地图 结束************/



    /*********** 天地图标注 开始************/

    let tianditu = new Cesium.WebMapTileServiceImageryProvider({

        url: "http://t0.tianditu.com/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=7711a24780452f03bb7c02fba98183b9",

        layer: "tiandituImgMarker",

        style: "default",

        format: "image/jpeg",

        tileMatrixSetID: "tiandituImgMarker",

        show: true,

        maximumLevel: 16

    });
    // WGS84_to_GCJ02 = flase;

    // viewer.imageryLayers.addImageryProvider(tianditu); // 请求标注资源 

    /*********** 天地图标注 结束************/
    viewer.cesiumWidget.creditContainer.style.display = 'none'
    viewer.scene.globe.depthTestAgainstTerrain = true // 地形遮挡模型效果开启
    // let stages = viewer.scene.postProcessStages
    // viewer.scene.brightness =
    // viewer.scene.brightness || stages.add(Cesium.PostProcessStageLibrary.createBrightnessStage())
    // viewer.scene.brightness.enabled = true
    // viewer.scene.brightness.uniforms.brightness = Number(1.4)
    // 调整天空大气效果是否显示
    viewer.scene.skyAtmosphere.show = false
    // viewer.scene.globe.showGroundAtmosphere = false // 启用从 lightingFadeInDistance 和 lightingFadeOutDistance 之间的距离观察时绘制在地球上的地面大气。
    viewer.scene.fog.enabled = false
    // viewer.scene.screenSpaceCameraController.maximumZoomDistance = 200000
    // viewer.scene.screenSpaceCameraController.minimumZoomDistance = 50
    viewer.scene.debugShowFramesPerSecond = true // 页面右上角fps数值显示
    
    // viewer.scene.globe.show = false; // 关闭地球模型 用于调试

    viewer.scene.globe.translucency.enabled = false; // 开启地面半透明效果
    viewer.scene.globe.translucency.frontFaceAlphaByDistance = new Cesium.NearFarScalar(100, 0.6, 500, 1.0); // 地面半透明程度随距离变化
    // viewer.scene.globe.translucency.frontFaceAlpha = 0.8; // 固定地面半透明程度

    viewer.scene.screenSpaceCameraController.enableCollisionDetection = true; // 禁止相机视角进入底下 false为不禁止进入

    return viewer
}

export function flyTo (viewer, options, callback = function () { }) {
    const camera = viewer.scene.camera
    camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(options.longitude, options.latitude, options.height),
        duration: options.duration,
        complete: callback,
        orientation: {
            heading: options.heading,
            pitch: options.pitch,
            roll: options.roll,
        }
    })
}

export function clickGetPoint (viewer) {
    // 点击地图 输出具体经纬度
    const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)
    handler.setInputAction(movement => {
        // 点击地图获取经纬度
        let position = viewer.scene.pickPosition(movement.position)
        position = Cesium.Cartographic.fromCartesian(position)
        console.log('click point:', [(position.longitude / Math.PI) * 180, (position.latitude / Math.PI) * 180, position.height])
        // console.log('click point2:', position)
        // 点击模型获取id
        const pick = viewer.scene.pick(movement.position)
        if (Cesium.defined(pick)) {
            const modelID = pick.id.id;
            console.log('pick: ', modelID);
        }
        // 点击输出当前相机的各个参数
        const camera = viewer.camera
        const cameraPosition = Cesium.Cartographic.fromCartesian(camera.position)
        // console.log('camera-position:', camera.position)
        console.log('camera-position:', [(cameraPosition.longitude / Math.PI) * 180, (cameraPosition.latitude / Math.PI) * 180, cameraPosition.height])
        console.log('camera-pitch-heading :', camera.pitch, camera.heading)
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
}

/**
 * 获取具体坐标位置的海拔高度
 * @param {*} viewer cesium实体
 * @param {*} positionArray 坐标信息的对象数组，包含 longitude  position
 * @returns 返回对应高度的数组
 */
export function getTowerHeight (positionArray) {
    let positions = positionArray.map((el) => {
        return Cesium.Cartographic.fromDegrees(el.longitude, el.latitude)
    })
    // const promise = Cesium.sampleTerrain(terrainProvider, 11, positions);
    // Promise.resolve(promise).then(function(updatedPositions) {
    //     // positions[0].height and positions[1].height have been updated.
    //     // updatedPositions is just a reference to positions.
    //         // resolve(updatedPositions)
    //     console.log(updatedPositions)
    // })

    return new Promise((resolve, reject) => {
        Cesium.sampleTerrain(terrainProvider, 13, positions).then((updatedPositions) => {
            // positions[].height have been updated.
            // updatedPositions is just a reference to positions.
            // console.log('position height:', positions)
            resolve(positions)
        })
    })
}

export async function addModel_glb (viewer, modelInfo) {
    const modelPosition = Cesium.Cartesian3.fromDegrees(...modelInfo.position);
    const hpr = new Cesium.HeadingPitchRoll(modelInfo.angle, 0.0, 0.0) // 旋转角 俯仰角 翻滚角
    const orientation = Cesium.Transforms.headingPitchRollQuaternion(modelPosition, hpr)
    // 从本地缓存获取模型文件 并生成内存url
    let modelData = await localModelCache.getOneModel(modelInfo.url)
    let modelUrl = URL.createObjectURL(new Blob([modelData]))
    
    const glbModel = {
        id: modelInfo.id,
        name: modelInfo.name,
        position: modelPosition,
        orientation: orientation,
        model: {
            uri: modelUrl,
            scale: modelInfo.scale,
        },
    }
    const _entity = viewer.entities.add(glbModel)
    return _entity;
}

export async function createEntity_glb (modelInfo, color='#dfe0e2') {
    const modelPosition = Cesium.Cartesian3.fromDegrees(...modelInfo.position);
    const hpr = new Cesium.HeadingPitchRoll(modelInfo.angle, 0.0, 0.0) // 旋转角 俯仰角 翻滚角
    const orientation = Cesium.Transforms.headingPitchRollQuaternion(modelPosition, hpr)
    // 从本地缓存获取模型文件 并生成内存url
    let modelData = await localModelCache.getOneModel(modelInfo.url)
    let modelUrl = URL.createObjectURL(new Blob([modelData]))
    
    const glbModel = {
        id: modelInfo.id,
        name: modelInfo.name,
        position: modelPosition,
        orientation: orientation,
        model: {
            uri: modelUrl,
            scale: modelInfo.scale,
        },
        properties: modelInfo.properties,
        userData:{
            // 额外记录用户自定义数据 用于后续的计算
            position: modelPosition,
            angle: modelInfo.angle,
            procedureStatus: modelInfo.procedureStatus
        }
    
    }
    const entity = new Cesium.Entity(glbModel);
    entity.model.color = Cesium.Color.fromCssColorString(color);//设置模型颜色与透明度
    // entity.model.colorBlendMode = Cesium.ColorBlendMode.REPLACE; //设置颜色替换材质。
    entity.model.colorBlendMode = Cesium.ColorBlendMode.MIX; //材质与设置颜色混合得到的颜色
    // entity.model.colorBlendMode = Cesium.ColorBlendMode.HIGHLIGHT;  //材质与设置颜色相乘得到的颜色

    return entity
}

/**
 * 实现按需加载实体
 * @param {cesium.viewer} viewer cesium场景对象
 * @param {cesium.entities} entities cesium实体的集合
 * @param {array} positions 实体对应的坐标二维数组
 * @param {int} level 显示的等级 该值越大 则需要越靠近才会显示实体
 */
export function modelShowHandle (viewer, entities, positions, level=13) {
    // console.time('modelShowHandle');
    // 获取当前正在渲染的瓦片集合
    const tilesToRender = viewer.scene.globe._surface._tilesToRender
    let count_add = 0, count_remove = 0;
    // 当前正在渲染的瓦片求并集为一个大的rectangle
    let renderRectangle = new Cesium.Rectangle()
    if (tilesToRender.length > 0) {
        // 初始化总的渲染的矩形瓦片
        Cesium.Rectangle.clone(tilesToRender[0].rectangle, renderRectangle)
        let num = 0
        tilesToRender.forEach(item => {
            /**
             * 获取当前层级大于等于12级的瓦片，该处可自定，具体层级可根据加载的实体多少决定
             * 一般情况下由于相机视角的不同，我们能看见的瓦片数量也不同，所以尽量考虑瓦片数量
             * 最多的情况下每块瓦片上的显示数量
             * 该值越大 则需要越靠近才会显示实体
            */
            if (item.level >= level) {
                // 用来判断当前是否有符合条件的瓦片正在渲染
                num += 1
                // 所有大于12级的瓦片求交集
                Cesium.Rectangle.union(item.rectangle, renderRectangle, renderRectangle)
            }
        })
        if (num > 0) {
            // 判断点是否在所需渲染的瓦片内
            for (let i = 0; i < entities.length; i++) {
                // 将度转为弧度
                let cartographic = Cesium.Cartographic.fromDegrees(positions[i][0], positions[i][1])
                // 判断点是否在矩形内
                if (Cesium.Rectangle.contains(renderRectangle, cartographic)) {
                    count_add++;
                    // 判断当前实体是否已经被加载，有则跳过，无则加载
                    if (viewer.entities.contains(entities[i])) continue
                    viewer.entities.add(entities[i])
                } else {
                    // 不在点内，则判断是否被加载，有则删除
                    if (viewer.entities.contains(entities[i])) {
                        viewer.entities.remove(entities[i])
                    }
                    count_remove++;
                }
            }
        } else {
            for (let i = 0; i < entities.length; i++) {
                /**
                 *  当前可视范围内没有符合条件的瓦片加载，删除掉所欲瓦片
                 * 不能使用removeAll,我们只需要管理我们自己加载的部分即可
                */
                if (viewer.entities.contains(entities[i])) {
                    viewer.entities.remove(entities[i])
                }
                count_remove++;
            }
        }
        // console.log('entities add=', count_add, 'remove=', count_remove);
    }
    // console.timeEnd('modelShowHandle');
}

export function getCameraPosition(viewer){
    const position = Cesium.Cartographic.fromCartesian(viewer.camera.position);
    let res = {};
    res.longitude = (position.longitude / Math.PI) * 180;
    res.latitude = (position.latitude / Math.PI) * 180;
    res.height = position.height;
    res.pitch = viewer.camera.pitch;
    res.heading = viewer.camera.heading;
    res.roll = viewer.camera.roll;
    return res;
}

/**
 *
 * @param {object} viewer cesium场景对象
 * @returns 返回创建的线集合
 */
export function createLineCollection (viewer) {
    let polylineCollection = new Cesium.PolylineCollection({
        // 开启深度测试
        depthTest: true,
        // 开启渲染优化
        dynamic: false,
    })
    // 设置可见距离
    polylineCollection.distanceDisplayCondition = new Cesium.DistanceDisplayCondition(0.0, 2000.0);

    viewer.scene.primitives.add(polylineCollection);
    return polylineCollection;
}


export async function getPositionHeight(viewer, positions){
    let positions_degrees = [];
    positions.forEach(pos => {
        positions_degrees.push(Cesium.Cartographic.fromDegrees(...pos));
    });
    // let useLocalMapDataFlag = viewer.usePar.useLocalMapDataFlag;
    // console.log('debug:', viewer.usePar)
    let _terrainProvider = null;
    switch(useLocalMapDataFlag){
        case 0:
            _terrainProvider = Cesium.Terrain.fromWorldTerrain();
            break;
        case 1:
        case 2:
            _terrainProvider = viewer.terrainProvider;
            break;
        default:
            _terrainProvider = Cesium.Terrain.fromWorldTerrain();
            break;
    }
    // const _terrainProvider = viewer.terrainProvider;
    // const _terrainProvider = Cesium.createWorldTerrain()
    var promise = Cesium.sampleTerrainMostDetailed(_terrainProvider, positions_degrees);
    // var promise = Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, positions_degrees);
    return new Promise((resolve, reject) => {
        // 可能会引发异常的代码
        promise.then((updatedPositions) => {
            // positions[0].height and positions[1].height have been updated.
            // updatedPositions is just a reference to positions.
            resolve(updatedPositions);
        }).catch(function (error) {
            // 捕捉到异常的处理代码
            console.log("处理高程数据时发生异常：" + error);
            resolve(false);
        });
    })
}
