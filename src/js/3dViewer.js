import * as Cesium from 'cesium'

/**
 * 添加GLB模型到场景中
 * @param {Cesium.Viewer} viewer - Cesium查看器实例
 * @param {Object} options - 模型配置选项
 * @param {Array} options.position - 位置坐标 [经度, 纬度, 高度]
 * @param {number} options.angle - 旋转角度
 * @param {string} options.url - 模型URL
 * @param {number} options.scale - 缩放比例
 * @param {string} options.id - 模型ID
 * @param {string} options.name - 模型名称
 * @returns {Cesium.Entity} 返回创建的实体
 */
export function addModel_glb(viewer, options) {
    const { position, angle, url, scale, id, name } = options;
    
    // 创建位置
    const position3 = Cesium.Cartesian3.fromDegrees(position[0], position[1], position[2] || 0);
    
    // 创建方向
    const heading = Cesium.Math.toRadians(angle || 0);
    const pitch = Cesium.Math.toRadians(0);
    const roll = Cesium.Math.toRadians(0);
    const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
    const orientation = Cesium.Transforms.headingPitchRollQuaternion(position3, hpr);

    // 创建实体
    const entity = viewer.entities.add({
        id: id,
        name: name,
        position: position3,
        orientation: orientation,
        model: {
            uri: url,
            scale: scale || 1.0,
            minimumPixelSize: 32,
            maximumScale: 20000,
            incrementallyLoadTextures: false,
            runAnimations: false,
            clampAnimations: true,
            shadows: Cesium.ShadowMode.ENABLED,
            heightReference: Cesium.HeightReference.NONE,
            silhouetteColor: Cesium.Color.LIME,
            silhouetteSize: 2.0,
            color: Cesium.Color.WHITE,
            colorBlendMode: Cesium.ColorBlendMode.HIGHLIGHT,
            colorBlendAmount: 0.5,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 20000.0)
        },
    });
    
    return entity;
}

/**
 * 移除模型
 * @param {Cesium.Viewer} viewer - Cesium查看器实例
 * @param {string} id - 模型ID
 */
export function removeModel(viewer, id) {
    const entity = viewer.entities.getById(id);
    if (entity) {
        viewer.entities.remove(entity);
        return true;
    }
    return false;
}

/**
 * 更新模型位置
 * @param {Cesium.Viewer} viewer - Cesium查看器实例
 * @param {string} id - 模型ID
 * @param {Array} position - 新位置 [经度, 纬度, 高度]
 * @param {number} angle - 新角度
 */
export function updateModelPosition(viewer, id, position, angle) {
    const entity = viewer.entities.getById(id);
    if (entity) {
        const position3 = Cesium.Cartesian3.fromDegrees(position[0], position[1], position[2] || 0);
        const heading = Cesium.Math.toRadians(angle || 0);
        const pitch = Cesium.Math.toRadians(0);
        const roll = Cesium.Math.toRadians(0);
        const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
        const orientation = Cesium.Transforms.headingPitchRollQuaternion(position3, hpr);
        
        entity.position = position3;
        entity.orientation = orientation;
        return true;
    }
    return false;
}

/**
 * 批量添加模型
 * @param {Cesium.Viewer} viewer - Cesium查看器实例
 * @param {Array} models - 模型配置数组
 */
export function addModels(viewer, models) {
    const entities = [];
    models.forEach(model => {
        try {
            const entity = addModel_glb(viewer, model);
            entities.push(entity);
        } catch (error) {
            console.error('添加模型失败:', model, error);
        }
    });
    return entities;
}

/**
 * 清除所有模型
 * @param {Cesium.Viewer} viewer - Cesium查看器实例
 */
export function clearAllModels(viewer) {
    viewer.entities.removeAll();
}
