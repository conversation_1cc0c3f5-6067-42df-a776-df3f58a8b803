export default class Animation {
    viewer: any
    positions: [number, number, number][]
    modelPosition: any
    numberOfSamples: number
    startPositionTime: any
    constructor(viewer: any, startTime?: DateConstructor) {
        this.viewer = viewer
        this.positions = []
        this.modelPosition = new Cesium.SampledPositionProperty()
        this.numberOfSamples = 100 // 模型位置插值的采样次数
        this.startPositionTime = startTime ? Cesium.JulianDate.fromDate(startTime) : viewer.clock.currentTime.clone() // 设定第一次位置时间
    }
    addPosition(position: [number, number, number], seconds: number) {
        if(!position || seconds==undefined){
            console.error('addPosition error', 'positions:', this.positions, 'seconds:', seconds)
            return
        }
        this.positions.push(position)
        // 每增加一个位置参数 则计算一次位置插值 实现位置动画
        if(this.positions.length == 1){
            return // 第一次添加位置为起始位 不用计算
        }
        let start = new Cesium.Cartesian3.fromDegrees(this.positions[this.positions.length - 2][0], this.positions[this.positions.length - 2][1], this.positions[this.positions.length - 2][2])
        let end = new Cesium.Cartesian3.fromDegrees(this.positions[this.positions.length - 1][0], this.positions[this.positions.length - 1][1], this.positions[this.positions.length - 1][2])
        let time
        for (let i = 0; i <= this.numberOfSamples; i++) {
            const factor = i / this.numberOfSamples;
            time = Cesium.JulianDate.addSeconds(
                this.startPositionTime,
                factor * seconds,
                new Cesium.JulianDate()
            )
            const location = Cesium.Cartesian3.lerp(
                start,
                end,
                factor,
                new Cesium.Cartesian3()
            ) // 计算位置的插值
            this.modelPosition.addSample(time, location)
        }
        this.startPositionTime = time.clone() // 记录上一次位置时间
    }
    setNumberOfSamples(numberOfSamples: number) {
        this.numberOfSamples = numberOfSamples
    }
    setModel(url: string) {
        const entity = this.viewer.entities.add({
            position: this.modelPosition,
            orientation: new Cesium.VelocityOrientationProperty(this.modelPosition), // Automatically set the vehicle's orientation to the direction it's facing.
            model: {
                uri: url,
                runAnimations: false
            }
        })
        // this.viewer.trackedEntity = entity
        // entity.viewFrom = new Cesium.Cartesian3(-10.0, 7.0, 4.0);
        return entity
    }
    startAnimation() {
        this.viewer.clock.shouldAnimate = true
    }
    stopAnimation() {
        this.viewer.clock.shouldAnimate = false
    }
}

