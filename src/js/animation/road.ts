import * as Cesium from 'cesium';

interface positionsType {
    seconds: number
    position: [number, number, number]
}
export default class Road {
    viewer: any
    startTime: any
    numberOfSamples: number // 模型位置插值的采样次数
    entity: any
    roadPositions: any
    private __positions: any
    private __property: any
    allTime: any
    private __color:any
    private __width: number
    private __time: any
        constructor(viewer: any, startTime?: Date) {
        this.viewer = viewer
        this.startTime = startTime ? Cesium.JulianDate.fromDate(startTime) : viewer.clock.currentTime.clone() // 设定起始时间
        this.numberOfSamples = 100
        this.__width = 10;
    }
    setColor(color: string) {
        this.__color= Cesium.Color.fromCssColorString(color)
    }
    setWidth(width: number) {
        this.__width = width
    }
    setPosition(positions: [number, number, number][] | positionsType[]) {
        if (this.entity) {
            console.error('setPosition error', 'entity is exist')
            return false
        }
        // 判断传入的参数类型是否为 positionsType[]
                if (Array.isArray(positions) && positions.length > 0 && 'seconds' in positions[0]) {
            // this.roadPositions = new 
            this.__property = new Cesium.SampledPositionProperty()
            let time = this.startTime.clone()
                        const typedPositions = positions as positionsType[];
            for (let i = 0; i < typedPositions.length; i++) {
                time = Cesium.JulianDate.addSeconds(time, typedPositions[i].seconds, new Cesium.JulianDate()) // 在 time 基础上 增加 seconds 秒 
                const location = Cesium.Cartesian3.fromDegrees(...(typedPositions[i].position))
                this.__property.addSample(time, location)
            }
            this.allTime = time
            this.__positions = []
                        this.roadPositions = new Cesium.CallbackProperty((time, result) => {
                if (!time) return;

                                if (this.allTime && Cesium.JulianDate.secondsDifference(time, this.allTime) > 0) {
                    return this.__positions
                }
                if (Cesium.JulianDate.equalsEpsilon(time, this.startTime, 0.1) && this.__positions.length > 10) {
                    this.__positions = []
                }
                if (Cesium.JulianDate.equalsEpsilon(time, this.__time, 0.1)) {
                    return this.__positions
                }
                this.__time = time
                this.__positions.push(this.__property.getValue(time))
                return this.__positions
            }, false)
        } else if (Array.isArray(positions) && positions.length > 2 && Array.isArray(positions[0]) && positions[0].length === 3) {
            this.roadPositions = []
            for (let i = 0; i < positions.length; i++) {
                this.roadPositions.push(Cesium.Cartesian3.fromDegrees(...(positions[i] as [number, number, number])))
            }
        } else {
            console.error('setPosition error', 'positions:', positions)
            return false
        }
        return true
    }
    startAnimation() {
        if (this.roadPositions == undefined) {
            console.error('startAnimation error', 'roadPositions:', this.roadPositions)
            return false
        }
        if (this.entity == undefined) {
            this.entity = this.viewer.entities.add({
                name: 'road',
                polyline: {
                    positions: this.roadPositions,
                    width: (this.__width)? this.__width : 10,
                    material: (this.__color)? this.__color : Cesium.Color.fromCssColorString('#ffff007f')
                }
            })
        }
        this.viewer.clock.shouldAnimate = true
    }
}