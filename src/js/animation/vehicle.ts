interface positionsType {
    position: [number, number, number],
    seconds: number
}
interface nodeTransformationType {
    angle: number,
    seconds: number
}

export default class Vehicle {
    viewer: any
    modelPosition: any
    numberOfSamples: number // 模型位置插值的采样次数
    startPositionTime: any
    rotationProperty: any
    modelOrientation: any
    modelUrl: string
    entity: any
    nodeTransformations: object
    constructor(viewer: any, modelUrl: string, startTime?: DateConstructor) {
        this.viewer = viewer
        this.startPositionTime = startTime ? Cesium.JulianDate.fromDate(startTime) : viewer.clock.currentTime.clone() // 设定第一次位置时间
        this.modelUrl = modelUrl
        this.numberOfSamples = 100
        this.nodeTransformations = {}
    }
    setHeadingPitchRoll(heading: number, pitch: number, roll: number) {
        if (this.modelPosition instanceof Cesium.Cartesian3) {
            const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll) // 旋转角 俯仰角 翻滚角
            this.modelOrientation = Cesium.Transforms.headingPitchRollQuaternion(this.modelPosition, hpr)
            return true
        } else {
            console.error('setHeadingPitchRoll error, modelPosition is not Cartesian3!')
            return false
        }
    }
    setPosition(positions: positionsType[] | [number, number, number]) {
        // 设置位置参数需要在动画创建前完成
        if (this.entity) {
            console.error('setPosition error, animation is started!')
            return false
        }
        // 判断传入参数的类型是否为positionsType类型
        if (typeof positions[0] === 'object' && positions.length > 2) {
            this.modelPosition = new Cesium.SampledPositionProperty() // 创建一个位置 Property 对象
            let time = this.startPositionTime.clone()
            for (let i = 0; i < positions.length; i++) {
                time = Cesium.JulianDate.addSeconds(
                    time,
                    positions[i].seconds,
                    new Cesium.JulianDate()
                )
                const location = Cesium.Cartesian3.fromDegrees(...(positions[i].position))
                this.modelPosition.addSample(time, location)
            }
            this.modelOrientation = new Cesium.VelocityOrientationProperty(this.modelPosition)
        } else if (positions.length == 3) {
            this.modelPosition = new Cesium.Cartesian3.fromDegrees(...positions)
        } else {
            console.error('setPosition error', 'positions:', positions)
            return false
        }
        return true
    }
    setNumberOfSamples(numberOfSamples: number) {
        this.numberOfSamples = numberOfSamples
    }
    setNodeTransformation(nodeName: string, options: nodeTransformationType[], axis: string) {
        if (options.length < 2) {
            console.error('setNodeTransformation error', 'options:', options)
            return false
        }
        if (!(['x', 'y', 'z', 'X', 'Y', 'Z'].includes(axis))) {
            console.error('setNodeTransformation error', 'axis:', axis)
            return false
        }
        let localAxis:object
        if (axis == 'x' || axis == 'X') {
            localAxis = Cesium.Cartesian3.UNIT_X
        } else if (axis == 'y' || axis == 'Y') {
            localAxis = Cesium.Cartesian3.UNIT_Y
        } else if (axis == 'z' || axis == 'Z') {
            localAxis = Cesium.Cartesian3.UNIT_Z
        }
        // 创建一个角度 property 对象
        const angleProperty = new Cesium.SampledProperty(Number)
        let time = this.startPositionTime.clone()
        for(let i=0; i<options.length; i++){
            time = Cesium.JulianDate.addSeconds(time, options[i].seconds, new Cesium.JulianDate())
            angleProperty.addSample(time, options[i].angle)
        }
        // 创建一个位置 CallbackProperty 对象
        const rotationProperty = new Cesium.CallbackProperty((time, result) => {
            return Cesium.Quaternion.fromAxisAngle(localAxis, angleProperty.getValue(time)/180*Math.PI, result)
        }, false)
        this.nodeTransformations[nodeName] = new Cesium.NodeTransformationProperty({
            rotation: rotationProperty,
        })
        // console.log('setNodeTransformation', 'nodeTransformations:', this.nodeTransformations)
    }
    startAnimation() {
        if (this.modelUrl == undefined || this.modelUrl == '') {
            console.error('startAnimation error', 'modelUrl:', this.modelUrl)
            return
        }
        if (this.modelPosition == undefined) {
            console.error('startAnimation error', 'modelPosition:', this.modelPosition)
            return
        }
        if (this.entity == undefined) {
            this.entity = this.viewer.entities.add({
                position: this.modelPosition,
                // orientation: (this.modelPosition instanceof Cesium.Cartesian3) ? undefined : new Cesium.VelocityOrientationProperty(this.modelPosition), // Automatically set the vehicle's orientation to the direction it's facing.
                orientation: this.modelOrientation,
                model: {
                    uri: this.modelUrl,
                    runAnimations: false,
                    nodeTransformations: this.nodeTransformations,
                }
            })
        }
        this.viewer.clock.shouldAnimate = true
    }
    stopAnimation() {
        this.viewer.clock.shouldAnimate = false
    }
}

