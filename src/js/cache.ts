export default class Cache {
  private data: any[];
  private maxSize: number;

  constructor(data: any[] = [], maxSize: number = 100) {
    this.data = data;
    this.maxSize = maxSize;
  }

  get(key: string): any {
    return this.data.find(item => item.key === key)?.value;
  }

  set(key: string, value: any): void {
    const existingIndex = this.data.findIndex(item => item.key === key);
    
    if (existingIndex !== -1) {
      this.data[existingIndex].value = value;
    } else {
      if (this.data.length >= this.maxSize) {
        this.data.shift(); // Remove oldest item
      }
      this.data.push({ key, value });
    }
  }

  has(key: string): boolean {
    return this.data.some(item => item.key === key);
  }

  delete(key: string): boolean {
    const index = this.data.findIndex(item => item.key === key);
    if (index !== -1) {
      this.data.splice(index, 1);
      return true;
    }
    return false;
  }

  clear(): void {
    this.data = [];
  }

  size(): number {
    return this.data.length;
  }
}
