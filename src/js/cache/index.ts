import type Project from "./project";
import require from "@/utils/requires";
import {useLineProjStore} from '@/store/lineProj.js'
import {CONTEXT,MODEL_STATIC_URL} from "@/config/global.js";
export default class Cache{
    CONTEXT: string = import.meta.env.VITE_CONTEXT;
    projects: Project[];
    projectIndex?: number;
    tower?:any;
    extraFeatures:any;
    constructor(projects : Project[],projectIndex : number){
        this.projects = projects
        this.projectIndex= projectIndex
        this.tower = null
    }
    setProjects = (projects : Project[])=>{
        this.projects = projects
        this.projectIndex = 0
    }
    setProject = (project: Project)=>{
        this.projectIndex = project? this.projects.findIndex(item => item.id == project.id) : 0
    }
    setProjectId = (projectId: string)=>{
        this.projectIndex = this.projects.findIndex(item => item.id == projectId)
    }
    setIndex = (index : number)=>{
        this.projectIndex = index
    }
    getCurrentProject = (): Project | null => {
        if (!this.projects || this.projects.length === 0) {
            return null;
        }
        return this.projects[this.projectIndex || 0] || null;
    }
    getProject = (id:string): Project | undefined => {
        return this.projects.find(item => item.id == id)
    }
    getProjectCode = () => {
        const currentProject = this.getCurrentProject();
        if (!currentProject || !currentProject.projectCode) {
            return 'DEMO'; // 演示模式
        }
        return currentProject.projectCode;
    }
    getDefaultView = () => {
        // 演示模式下返回默认视图
        if (this.getProjectCode() === 'DEMO') {
            return {
                longitude: 114.0,
                latitude: 22.5,
                height: 1000,
                heading: 0,
                pitch: -45,
                duration: 3
            };
        }

        const currentProject = this.getCurrentProject();
        if (!currentProject) {
            return {
                longitude: 114.0,
                latitude: 22.5,
                height: 1000,
                heading: 0,
                pitch: -45,
                duration: 3
            };
        }

        const lineLocation = currentProject.lineLocation
        if(lineLocation){
            try{
                return JSON.parse(lineLocation)
            }catch(e){
                console.log("转换lineLocation失败，检查lineLocation格式")
                return null
            }
        }else{
            console.log("没有配置lineLocation")
            return null
        }
    }
    getTenders= async () =>{
        if(!this.getCurrentProject().tenders){
            const res = (await require({
                url: "admin-api/manage-intranet/common/all-tenders",
                method: "get",
                params: {projectId: this.getCurrentProject().id}
            }))
            this.getCurrentProject().tenders = (res.data.data as any[]).map(item=>{return {value:item.id, label:item.tendersName}})
        }
        return Promise.resolve(this.getCurrentProject().tenders)
    }
    // 加载倾斜摄影数据
    getObliquePhotography = async () => {
      if((this.projects || []).length == 0)
            return Promise.resolve(null)
        if(!this.getCurrentProject().obliquePhotography){
            const res = await this.loadJsdata("obliquePhotography.json")
            this.getCurrentProject().obliquePhotography = (res)
        }
        return Promise.resolve(this.getCurrentProject().obliquePhotography)
    }
    getBuildingConfig= async () =>{
        if((this.projects || []).length == 0)
            return Promise.resolve(null)
        if(!this.getCurrentProject().buildingConfig){
            console.log('buildingccc', this.getCurrentProject());
            
            const res = await this.loadJsdata("building.json")
            this.getCurrentProject().buildingConfig = (res )
        }
        return Promise.resolve(this.getCurrentProject().buildingConfig)
    }
    getTowerModels= async () =>{
        //     const res = (await require({
        //         url: `${location.protocol}//${location.host}${this.CONTEXT}static/jsdata/${this.getProjectCode()}/towerModels.json`,
        //         method: "get",
        //         params: null
        //     }))
        // return Promise.resolve(res.data)
        const store_lineProj = useLineProjStore();
        // store_lineProj.convertData2NewFormat_towers();
        return Promise.resolve(store_lineProj.towers);
        //return this.loadJsdata("towerModels.json")
    }
    getVrdata= async () =>{
        var data = await this.loadJsdata("vrdata.json")
        data.images.forEach((item:any)=>{
            item.url = `${MODEL_STATIC_URL}/${this.getProjectCode()}${item.url}`
            item.viewUrl = `${MODEL_STATIC_URL}/${this.getProjectCode()}${item.viewUrl}`
        })
        return Promise.resolve(data)
    }
    getInnovateData= async () =>{
        return this.loadJsdata("innovate.json")
    }
    getProjectInfo= async () =>{
        return this.loadJsdata("project.json")
    }
    getSubstationInfo = async() =>{
        return this.loadJsdata("substation.json")
    
    }
    getSubstationConnect = async() =>{
        return this.loadJsdata("substation_connect.json")
    }
    getLinePhaseSequence = async () =>{
        return this.loadJsdata("linePhaseSequence.json")
    }

    // 添加缺失的方法
    setDefaultView = (view: any) => {
        const currentProject = this.getCurrentProject();
        if (currentProject) {
            currentProject.defaultView = view;
        }
        console.log('setDefaultView called with:', view);
    }

    setProjectCode = (code: string) => {
        const currentProject = this.getCurrentProject();
        if (currentProject) {
            currentProject.projectCode = code;
        }
        console.log('setProjectCode called with:', code);
    }

    clearProjectSpecificCache = () => {
        this.projects = [];
        this.projectIndex = 0;
        console.log('Project-specific cache cleared');
    }
    loadJsdata = async (fileName:string):Promise<any> =>{
        // 演示模式下返回模拟数据
        if (this.getProjectCode() === 'DEMO') {
            return this.getDemoData(fileName);
        }

        return new Promise((resolve,reject)=>{
            require({
                url: `${location.protocol}//${location.host}${this.CONTEXT}static/jsdata/${this.getProjectCode()}/${fileName}`,
                method: "get",
                params: null
            }).then(res=>{
                resolve(res.data)
            }).catch(e=>{
                console.warn(`Failed to load ${fileName}, returning demo data:`, e);
                resolve(this.getDemoData(fileName));
            })
        });
    }

    private getDemoData = (filename: string): any => {
        // 根据文件名返回不同的演示数据
        switch (filename) {
            case 'protectedArea02.geojson':
                return {
                    type: "FeatureCollection",
                    features: []
                };
            case 'protectedAreaLabel.json':
                return [];
            case 'constructionLabel.json':
                return [];
            case 'excavateSurface.json':
                return [];
            case 'underGroundPolylineVolume.json':
                return [];
            case 'groundLine.json':
                return [];
            case 'cableway.json':
                return { cableGroups: [] };
            case 'cablewayMounts.json':
                return [];
            case 'linePhaseSequence.json':
                return [];
            case 'building.json':
                return { keepLoading: [] };
            case 'obliquePhotography.json':
                return { models: [] };
            default:
                console.log(`No demo data for ${filename}, returning empty object`);
                return {};
        }
    }
}
