/**
 * 高德地图图层提供者
 * 支持影像、电子、中间图层，以及本地图层
 */

import GCJ02TilingScheme from '@/js/cesium-map/GCJ02TilingScheme'
// import { UrlTemplateImageryProvider } from '@cesium/engine'
import * as Cesium from '@/Cesium'
// import * as Cesium from 'cesium'
import {MODEL_STATIC_URL} from '@/config/global.js'; 

// 检查环境变量，确定是否使用备用地图服务
// vite环境变量全部以字符串形式注入，需要解析
const USE_FALLBACK_MAP = import.meta.env.VITE_USE_FALLBACK_MAP === 'true';
// 开发调试开关
const DEBUG_WMTS = import.meta.env.VITE_DEBUG_WMTS === 'true';

// 高德瓦片位置计算逻辑，避免请求超出范围的瓦片
function calculateValidTileXY(x, y, level) {
  // 计算当前层级的合法瓦片范围
  const maxTileIndex = Math.pow(2, level) - 1;
  
  // 将坐标钳制在合法范围内
  return {
    x: Math.max(0, Math.min(x, maxTileIndex)),
    y: Math.max(0, Math.min(y, maxTileIndex))
  };
}

// 地图瓦片URL配置 - 多个备用服务器和不同格式
export const TILE_URL = {
  // 主服务器
  tianditu: "https://t0.tianditu.com/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=7711a24780452f03bb7c02fba98183b9",
  // 高德卫星影像 - 确保使用正确的卫星图URL
  img: 'https://webst{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
  // 高德矢量路网图
  elec: 'https://webrd{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
  cva: 'https://webst{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
  local1: MODEL_STATIC_URL+"/MAP1/{z}/{y}/{x}.png",
  local2: MODEL_STATIC_URL+"/MAP2/{z}/{x}/{y}.png",
  
  // 备用服务器 - 多种格式 - 确保所有备用也使用卫星图
  img_backup1: 'https://wprd0{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}', // 备用卫星图
  // 增加传入时间戳和随机参数，避免缓存和连接重置问题
  img_backup2: 'https://webst{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}&scl=2&ts=' + new Date().getTime(),
  img_backup3: 'https://webst{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}&scl=1&v=' + Math.floor(Math.random() * 10000),
  
  // 添加电子地图备用服务器
  elec_backup1: 'https://wprd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', // 备用电子地图
  elec_backup2: 'https://webrd{s}.is.autonavi.com/appmaptile?scl=1&lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', // 带scl参数的电子地图
  
  // 添加百度地图作为备用 - 需要坐标转换
  // baidu_img: 'https://maponline{s}.bdimg.com/tile/?qt=tile&x={x}&y={y}&z={z}&styles=sl&scaler=1&udt=20220428',
  
  // 备用方案 - OpenStreetMap，不受高德API限制
  osm: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
  osm_cycle: 'https://tile.thunderforest.com/cycle/{z}/{x}/{y}.png',
  
  // ArcGIS卫星图层 - 备用选项
  arcgis_img: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
  
  // 另一种高德格式，提高兼容性
  gaode_vector: "https://webrd0{s}.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8",
  gaode_image: "https://webst0{s}.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=6",
  gaode_label: "https://webst0{s}.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8",
  
  // 天地图备用
  tianditu_vec: "https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=7711a24780452f03bb7c02fba98183b9",
  tianditu_img: "https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=7711a24780452f03bb7c02fba98183b9"
}

// 瓦片错误管理 - 全局配置
const tileErrorCounts = {}; // 记录每个瓦片位置的错误次数
const MAX_RETRY = 3; // 单个瓦片最大重试次数
const ERROR_THRESHOLD = 30; // 全局错误阈值，超过此值使用备用服务器
let totalErrorCount = 0; // 全局错误计数
let usingBackupServer = false; // 是否正在使用备用服务器
let currentBackupIndex = 0; // 当前使用的备用服务器索引

// 创建轻量级瓦片缓存 - 避免重复请求相同的失败瓦片
const tileCache = new Map();
const MAX_CACHE_SIZE = 1000; // 缓存大小限制

/**
 * 高德地图图层提供者类
 * 支持影像、电子地图以及本地图层
 */
class AmapImageryProvider extends Cesium.UrlTemplateImageryProvider {
  /**
   * 创建高德地图图层提供者
   * @param {Object} options 配置选项
   * @param {string} options.style 地图样式 img|elec|cva|local1|local2
   * @param {string} options.crs 坐标系 WGS84|GCJ02
   */
  constructor(options = {}) {
    // 记录原始选项，以便后续可能的重建
    const originalOptions = { ...options };
    
    // 防止直接修改传入的选项对象
    options = { ...options };
    
    // 设置切片方案 - 避免坐标系问题
    if (options.crs === 'WGS84') {
      options['tilingScheme'] = new GCJ02TilingScheme();
    }
    
    // 设置URL模板
    let selectedStyle = options.style || 'img'; // 默认使用卫星影像地图而不是电子地图
    options['url'] =
      options.url ||
      (function() {
        // 使用正确的URL拼接方式
        let url = TILE_URL[selectedStyle] || TILE_URL['img'];
        // 如果URL已经包含协议，就直接使用
        if (url.startsWith('http://') || url.startsWith('https://')) {
          return url;
        }
        // 修复: 确保没有重复的协议前缀
        const protocol = options.protocol || 'https://';
        // 移除可能存在的前导 '//'
        const cleanUrl = url.replace(/^\/\//, '');
        // 确保没有协议前缀的重复 (避免https:https://)
        return protocol + cleanUrl;
      })();
    
    // 确保URL中没有{style}占位符
    if (options['url'].includes('{style}')) {
      if (selectedStyle === 'img') {
        options['url'] = options['url'].replace('{style}', '6');
      } else if (selectedStyle === 'elec') {
        options['url'] = options['url'].replace('{style}', '8');
      } else if (selectedStyle === 'cva') {
        options['url'] = options['url'].replace('{style}', '8');
      } else {
        // 默认样式
        options['url'] = options['url'].replace('{style}', '7');
      }
    }
    
    // 扩展子域名，提高负载均衡能力
    if (!options.subdomains || !options.subdomains.length) {
      // 使用更多子域名，避免单一子域名请求过多
      if (selectedStyle.includes('tianditu')) {
        // 天地图子域
        options['subdomains'] = ['0', '1', '2', '3', '4', '5', '6', '7'];
      } else {
        // 高德子域
        options['subdomains'] = ['01', '02', '03', '04', '1', '2', '3', '4'];
      }
    }
    
    // 限制最大级别，降低出错率
    const defaultMaxLevel = 18; // 提高默认最大级别，确保可以看到更清晰的3D卫星图细节
    if (!options.maximumLevel || options.maximumLevel > defaultMaxLevel) {
      options['maximumLevel'] = defaultMaxLevel;
    }
    
    // 显式设置最小级别，避免尝试加载不必要的低级别瓦片
    if (!options.minimumLevel) {
      options['minimumLevel'] = 1;
    }

    // 添加错误处理参数
    options.enablePickFeatures = false; // 禁用拾取功能，减少不必要的请求
    options.retryAttempts = MAX_RETRY; // 增加重试次数
    
    // 添加矩形限制 - 只请求中国区域的瓦片，减少无效请求
    if (!options.rectangle) {
      options.rectangle = Cesium.Rectangle.fromDegrees(73.0, 18.0, 135.0, 54.0); // 中国大陆范围
    }
    
    // 自定义瓦片转换函数 - 确保请求合法的瓦片坐标
    const customGetTileCredits = options.getTileCredits;
    options.getTileCredits = function(x, y, level) {
      // 确保瓦片坐标在有效范围内
      const validTile = calculateValidTileXY(x, y, level);
      
      // 调用原始函数，如果有的话
      if (customGetTileCredits) {
        return customGetTileCredits(validTile.x, validTile.y, level);
      }
      return undefined;
    };
    
    // 自定义URL模板处理函数，拦截并修正无效请求
    const urlTemplateBuilder = options.urlSchemeZeroPadding;
    if (!urlTemplateBuilder) {
      options.customUrlFunction = function(imageryProvider, x, y, level) {
        // 检查缓存中是否有该瓦片的记录
        const tileKey = `${x}_${y}_${level}`;
        if (tileCache.has(tileKey) && tileCache.get(tileKey).failed) {
          // 如果该瓦片已经多次请求失败，直接返回错误瓦片URL或透明瓦片
          return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
        }
        
        // 验证并修正瓦片坐标
        const validTile = calculateValidTileXY(x, y, level);
        
        // 计算子域名索引
        const subdomains = imageryProvider._subdomains;
        const subdomainIndex = (validTile.x + validTile.y + level) % subdomains.length;
        const currentSubdomain = subdomains[subdomainIndex];
        
        // 构建URL
        return imageryProvider._url
          .replace('{s}', currentSubdomain)
          .replace('{x}', validTile.x.toString())
          .replace('{y}', validTile.y.toString())
          .replace('{z}', level.toString())
          .replace('{TileMatrix}', level.toString())
          .replace('{TileRow}', validTile.y.toString())
          .replace('{TileCol}', validTile.x.toString());
      };
    }
    
    // 设置自定义的requestImage方法，添加错误处理
    const originalRequestImage = Cesium.UrlTemplateImageryProvider.prototype.requestImage;
    
    // 调用父类构造函数
    super(options);
    
    // 保存原始选项用于可能的重建
    this._originalOptions = originalOptions;
    this._currentStyle = selectedStyle;
    this._subdomains = options.subdomains;
    this._url = options.url;
    this._backupUrls = this._prepareBackupUrls(selectedStyle);
    
    // 处理瓦片请求错误
    this.errorEvent.addEventListener((err) => {
      // 跳过错误处理，如果禁用了备用地图
      if (!USE_FALLBACK_MAP) {
        return true;
      }
      
      // 解析错误的瓦片信息
      const tileInfo = this._extractTileInfo(err);
      if (tileInfo) {
        // 记录错误并检查是否需要切换到备用服务器
        this._handleTileError(tileInfo);
      }
      return true; // 不中断后续瓦片加载
    });
    
    // 调试模式下打印初始化信息
    if (DEBUG_WMTS) {
      console.log(`地图图层初始化: 
      - 样式: ${selectedStyle}
      - 坐标系: ${options.crs || 'GCJ02'}
      - 允许备用服务器: ${USE_FALLBACK_MAP}
      - 备用服务器数量: ${this._backupUrls.length}
      - 初始URL: ${this._url}`);
    } else {
      // 非调试模式下只打印简要信息
      console.log(`创建高德地图图层 (${selectedStyle}), URL: ${this._url.substring(0, 60)}${this._url.length > 60 ? '...' : ''}`);
    }
    
    // 监听准备就绪事件
    if (this.readyPromise) {
      this.readyPromise
        .then(() => {
          console.log(`地图图层 (${selectedStyle}) 准备就绪`);
        })
        .catch((error) => {
          console.warn(`地图图层 (${selectedStyle}) 初始化失败: ${error.message}`);
          // 自动尝试切换到备用URL，如果允许
          if (USE_FALLBACK_MAP) {
            this._switchToBackupServer();
          }
        });
    }
  }
  
  /**
   * 准备备用URL列表
   * @private
   * @param {string} style 当前样式
   * @returns {Array<string>} 备用URL列表
   */
  _prepareBackupUrls(style) {
    const backupUrls = [];
    
    // 添加当前样式的备用URL
    if (style === 'img') {
      // 为卫星地图准备一系列备用服务器
      backupUrls.push(TILE_URL.img_backup1);
      backupUrls.push(TILE_URL.img_backup2);
      backupUrls.push(TILE_URL.img_backup3);
      backupUrls.push(TILE_URL.gaode_image);
      backupUrls.push(TILE_URL.tianditu_img);
      backupUrls.push(TILE_URL.arcgis_img); // ArcGIS全球卫星图层作为备选
    } else if (style === 'elec') {
      backupUrls.push(TILE_URL.elec_backup1);
      backupUrls.push(TILE_URL.elec_backup2);
      backupUrls.push(TILE_URL.gaode_vector);
      backupUrls.push(TILE_URL.tianditu_vec);
      backupUrls.push(TILE_URL.osm);
      // 如果原本显示电子地图，则添加卫星地图作为备用
      backupUrls.push(TILE_URL.img);
    } else {
      // 对于其他样式，使用通用备用顺序
      if (TILE_URL[style + '_backup1']) backupUrls.push(TILE_URL[style + '_backup1']);
      if (TILE_URL[style + '_backup2']) backupUrls.push(TILE_URL[style + '_backup2']);
      backupUrls.push(TILE_URL.img); // 默认回退到卫星图
      backupUrls.push(TILE_URL.tianditu_img);
    }
    
    return backupUrls;
  }
  
  /**
   * 解析瓦片信息
   * @private
   * @param {Error|Object} err 错误对象或事件对象
   * @returns {Object|undefined} 提取的瓦片信息或undefined
   */
  _extractTileInfo(err) {
    // 检测是否为连接重置错误，如果是则切换服务器
    if (err && err.message && (err.message.includes('ECONNRESET') || 
                       err.message.includes('ERR_CONNECTION_RESET') || 
                       err.message.includes('timeout') || 
                       err.message.includes('socket hang up') ||
                       err.message.includes('network error'))) {
      console.warn(`网络连接重置错误，准备切换服务器: ${this._currentServerIndex} -> ${this._currentServerIndex + 1}`);
      this._switchServer();
      
      // 短暂等待后自动重置错误状态，以允许更多尝试
      setTimeout(() => {
        this._resetErrorState(false);
      }, 5000);
    }
    
    // 增加错误计数
    if (this._errorCount === undefined) {
      this._errorCount = 0;
    }
    this._errorCount++;
    
    if (this._errorCount > 50) { // 降低阈值，更早重置
      // 错误太多，尝试重置状态
      this._resetErrorState();
    }
    
    // 尝试从错误消息中提取瓦片坐标信息
    if (err && err.message) {
      const matches = err.message.match(/X: (\d+) Y: (\d+) Level: (\d+)/);
      if (matches && matches.length >= 4) {
        return {
          x: parseInt(matches[1]),
          y: parseInt(matches[2]),
          level: parseInt(matches[3]),
          key: `${matches[1]}_${matches[2]}_${matches[3]}`
        };
      }
    }
    
    return undefined;
  }
  
  /**
   * 处理瓦片错误
   * @private
   * @param {Object} tileInfo 瓦片信息
   */
  _handleTileError(tileInfo) {
    // 跳过错误处理，如果禁用了备用地图
    if (!USE_FALLBACK_MAP) {
      return;
    }
    
    // 增加全局错误计数
    totalErrorCount++;
    
    // 记录当前瓦片位置的错误次数
    if (!tileErrorCounts[tileInfo.key]) {
      tileErrorCounts[tileInfo.key] = 0;
    }
    tileErrorCounts[tileInfo.key]++;
    
    // 将该瓦片标记为失败，并添加到缓存
    tileCache.set(tileInfo.key, {
      failed: true,
      errorCount: tileErrorCounts[tileInfo.key],
      lastError: new Date().getTime()
    });
    
    // 限制缓存大小
    if (tileCache.size > MAX_CACHE_SIZE) {
      // 删除最早添加的条目
      const firstKey = tileCache.keys().next().value;
      tileCache.delete(firstKey);
    }
    
    // 如果错误次数过多，切换到备用服务器
    if (totalErrorCount > ERROR_THRESHOLD && !usingBackupServer) {
      this._switchToBackupServer();
    } else if (usingBackupServer && totalErrorCount > ERROR_THRESHOLD * 2) {
      // 如果已经使用了备用服务器但仍然有很多错误，尝试下一个备用服务器
      this._switchToNextBackupServer();
    }
    
    // 超出最大级别的瓦片，不再重试
    if (tileInfo.level > this.maximumLevel) {
      console.warn(`瓦片级别超出限制: ${tileInfo.level} > ${this.maximumLevel}`);
      return;
    }
    
    // 如果单个瓦片错误次数超过阈值，不再重试
    if (tileErrorCounts[tileInfo.key] > MAX_RETRY) {
      return;
    }
    
    // 低错误率时，打印简化的日志
    if (totalErrorCount < 10 || totalErrorCount % 20 === 0) {
      console.warn(`地图瓦片加载错误: x=${tileInfo.x}, y=${tileInfo.y}, level=${tileInfo.level}, 总错误数: ${totalErrorCount}`);
    }
  }
  
  /**
   * 切换到备用服务器
   * @private
   */
  _switchToBackupServer() {
    // 如果禁用了备用地图，则不切换
    if (!USE_FALLBACK_MAP) {
      console.warn('备用地图服务已禁用，不会切换服务器');
      return;
    }
    
    // 标记已切换到备用服务器
    usingBackupServer = true;
    currentBackupIndex = 0;
    
    console.warn('检测到大量瓦片加载错误，切换到备用地图服务器');
    
    try {
      // 如果有备用URL，使用第一个
      if (this._backupUrls && this._backupUrls.length > 0) {
        const newUrl = this._backupUrls[currentBackupIndex];
        this._updateUrlTemplate(newUrl);
        console.log(`已切换到备用地图服务器 #${currentBackupIndex + 1}: ${newUrl}`);
      } else {
        // 如果没有备用URL，尝试使用不同的样式
        const alternativeStyle = this._currentStyle === 'img' ? 'elec' : 'img';
        const newUrl = TILE_URL[alternativeStyle];
        this._updateUrlTemplate(newUrl);
        console.log(`已切换到不同样式的地图: ${alternativeStyle}`);
      }
      
      // 修复：直接重置错误计数而不调用可能不存在的方法
      // 重置错误计数，给新服务器一个机会
      totalErrorCount = 0;
      
      // 清除瓦片错误记录
      Object.keys(tileErrorCounts).forEach(key => {
        delete tileErrorCounts[key];
      });
      
      // 清除瓦片缓存
      tileCache.clear();
    } catch (e) {
      console.error('切换备用服务器失败:', e);
    }
  }
  
  /**
   * 切换到下一个备用服务器
   * @private
   */
  _switchToNextBackupServer() {
    if (!this._backupUrls || this._backupUrls.length === 0) {
      console.warn('没有更多的备用服务器可用');
      return;
    }
    
    try {
      // 增加备用索引
      currentBackupIndex = (currentBackupIndex + 1) % this._backupUrls.length;
      
      // 使用新的备用URL
      const newUrl = this._backupUrls[currentBackupIndex];
      this._updateUrlTemplate(newUrl);
      
      console.log(`尝试切换到下一个备用服务器 #${currentBackupIndex + 1}: ${newUrl}`);
      
      // 部分重置错误计数
      totalErrorCount = Math.floor(totalErrorCount / 2);
    } catch (e) {
      console.error('切换到下一个备用服务器失败:', e);
    }
  }
  
  /**
   * 更新URL模板
   * @private
   * @param {string} newUrl 新的URL模板
   */
  _updateUrlTemplate(newUrl) {
    if (!newUrl) return;
    
    try {
      // 更新URL模板
      if (this.updateProperties) {
        this.updateProperties({ url: newUrl });
      } else if (this._url) {
        // 直接更新URL属性
        this._url = newUrl;
        if (this._resource && this._resource.url) {
          this._resource.url = newUrl;
        }
      }
      
      // 清除可能存在的瓦片缓存
      if (this._tileCache) {
        this._tileCache = {};
      }
    } catch (e) {
      console.error('更新URL模板失败:', e);
    }
  }
  
  /**
   * 重置错误状态
   * @private
   * @param {boolean} resetBackupFlag 是否重置备用服务器标志，默认为true
   */
  _resetErrorState(resetBackupFlag = true) {
    // 重置错误计数
    this._errorCount = 0;
    this._consecutiveErrors = 0;
    
    // 根据参数决定是否重置服务器索引
    if (resetBackupFlag) {
      this._currentServerIndex = 0;
      
      // 使用默认服务器
      if (this._defaultUrl) {
        this._updateUrlTemplate(this._defaultUrl);
        console.log('已恢复默认服务器');
      }
      
      // 重置备用服务器状态
      if (typeof usingBackupServer !== 'undefined') {
        usingBackupServer = false;
      }
      if (typeof currentBackupIndex !== 'undefined') {
        currentBackupIndex = 0;
      }
    }
    
    // 清空瓦片缓存的错误状态
    for (const [key, value] of tileCache.entries()) {
      if (value.failed) {
        tileCache.delete(key);
      }
    }
    
    // 清除全局错误计数
    if (typeof totalErrorCount !== 'undefined') {
      totalErrorCount = 0;
    }
    
    console.log(`🔄 重置错误状态完成${resetBackupFlag ? ' (包括备用服务器)' : ' (保留服务器设置)'}`);
  }
  
  /**
   * 获取加载统计信息
   * @public
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      totalErrorCount: totalErrorCount,
      uniqueTileErrors: Object.keys(tileErrorCounts).length,
      usingBackupServer: usingBackupServer,
      currentBackupIndex: usingBackupServer ? currentBackupIndex + 1 : 0,
      cachedTiles: tileCache.size
    };
  }
  
  /**
   * 获取当前使用的URL模板
   * @public
   * @returns {string} 当前URL模板
   */
  getCurrentUrl() {
    return this._url;
  }
  
  /**
   * 获取可用的备用服务器列表
   * @public
   * @returns {Array<string>} 备用服务器列表
   */
  getBackupServers() {
    return this._backupUrls || [];
  }
  
  /**
   * 直接切换到指定的URL模板
   * @public
   * @param {string} url 新的URL模板
   * @returns {boolean} 切换是否成功
   */
  switchToUrl(url) {
    if (!url) return false;
    
    try {
      this._updateUrlTemplate(url);
      console.log(`已手动切换到地图服务: ${url}`);
      return true;
    } catch (e) {
      console.error('手动切换地图服务失败:', e);
      return false;
    }
  }
  
  /**
   * 直接切换到指定索引的备用服务器
   * @public
   * @param {number} index 备用服务器索引
   * @returns {boolean} 切换是否成功
   */
  switchToBackupByIndex(index) {
    if (!this._backupUrls || !this._backupUrls.length) {
      console.warn('没有可用的备用服务器');
      return false;
    }
    
    // 确保索引在有效范围内
    index = Math.max(0, Math.min(index, this._backupUrls.length - 1));
    
    try {
      const newUrl = this._backupUrls[index];
      currentBackupIndex = index;
      usingBackupServer = true;
      
      this._updateUrlTemplate(newUrl);
      console.log(`已手动切换到备用服务器 #${index + 1}: ${newUrl}`);
      return true;
    } catch (e) {
      console.error(`切换到备用服务器 #${index + 1} 失败:`, e);
      return false;
    }
  }
  
  /**
   * 切换回原始服务器
   * @public
   * @returns {boolean} 切换是否成功
   */
  switchToOriginalServer() {
    try {
      this.resetErrorState(true);
      return true;
    } catch (e) {
      console.error('切换回原始服务器失败:', e);
      return false;
    }
  }
  
  /**
   * 尝试修复图层问题的通用方法
   * @public
   * @returns {boolean} 修复尝试是否成功
   */
  tryRepair() {
    try {
      console.log('尝试修复地图图层问题...');
      
      // 如果当前使用备用服务器且备用服务器有多个，则尝试下一个
      if (usingBackupServer && this._backupUrls && this._backupUrls.length > 1) {
        this._switchToNextBackupServer();
        return true;
      }
      
      // 如果未使用备用服务器，则尝试第一个备用服务器
      if (!usingBackupServer && this._backupUrls && this._backupUrls.length > 0) {
        this._switchToBackupServer();
        return true;
      }
      
      // 以上都失败，尝试重置
      this.resetErrorState(true);
      return true;
    } catch (e) {
      console.error('修复地图图层失败:', e);
      return false;
    }
  }
  
  /**
   * 请求指定坐标的图像，兼容最新版Cesium的接口
   * 暂时不使用，因为cesium 1.115.0 以后的版本不支持  by lkj
   * @public
   * @param {number} x 瓦片X坐标
   * @param {number} y 瓦片Y坐标
   * @param {number} level 瓦片级别
   * @param {Object} [request] 请求参数
   * @returns {Promise<HTMLImageElement|HTMLCanvasElement>} 图像Promise
   */
  // requestImage(x, y, level, request) {
  //   if (!this._ready) {
  //     return undefined;
  //   }
    
  //   return this._requestImage(x, y, level, request);
  // }

  /**
   * 请求图片瓦片
   * @private
   */
  _requestImage(x, y, level, request) {
    if (!this._ready) {
      return undefined;
    }
    
    const url = this._buildImageResource(x, y, level);
    
    return this._doRequest(url, x, y, level, request);
  }

  /**
   * 实际执行图像请求的方法
   * @private
   * @param {string} url 请求URL
   * @param {number} x X坐标
   * @param {number} y Y坐标
   * @param {number} level 级别
   * @param {Request} request 请求对象
   * @returns {Promise<HTMLImageElement|HTMLCanvasElement>|undefined} 图像或undefined
   */
  _doRequest(url, x, y, level, request) {
    try {
      // 记录瓦片请求
      const tileKey = `${x}_${y}_${level}`;
      
      // 检查是否是已知失败的瓦片
      if (tileCache.has(tileKey) && tileCache.get(tileKey).failed && 
          tileCache.get(tileKey).errorCount > MAX_RETRY) {
        return undefined;
      }
      
      // 创建资源
      const resource = new Cesium.Resource({
        url: url,
        request: request,
        retryAttempts: MAX_RETRY,
        retryCallback: (resource, error) => {
          try {
            // 使用重试次数属性（在较新版本的Cesium中可能是不同的属性名）
            const retryCount = resource.retryCount !== undefined ? resource.retryCount : (resource._retryCount || 0);
            console.warn(`瓦片请求重试 (${x}, ${y}, ${level}): 第${retryCount + 1}次重试`);
            // 超过重试次数，尝试切换服务器
            if (retryCount >= MAX_RETRY - 1) {
              this._switchServer();
            }
            return true;
          } catch (e) {
            console.error(`处理重试回调时出错:`, e);
            return false;
          }
        }
      });
      
      try {
        // 在loadImage可能失败的情况下增加错误处理
        const result = Cesium.ImageryProvider.loadImage(this, resource);
        
        // 确保返回的是Promise或undefined，且有完整的错误处理
        if (result === undefined) {
          return undefined;
        }
        
        // 检查result是否有Promise特性
        if (result && typeof result.then === 'function') {
          // 添加自己的错误处理来避免未捕获的Promise错误
          return result.then(
            (image) => {
              // 成功加载，返回图像
              if (image) {
                return image;
              }
              // 如果image为空，返回空白透明图像
              return this.createTransparentImage();
            },
            (error) => {
              console.error(`请求图片时发生错误: (${x}, ${y}, ${level})`, error);
              
              // 记录错误瓦片
              if (!tileCache.has(tileKey)) {
                tileCache.set(tileKey, { failed: true, errorCount: 1 });
              } else {
                const tileInfo = tileCache.get(tileKey);
                tileInfo.failed = true;
                tileInfo.errorCount = (tileInfo.errorCount || 0) + 1;
              }
              
              // 限制缓存大小
              if (tileCache.size > MAX_CACHE_SIZE) {
                // 删除最早加入的项
                const firstKey = tileCache.keys().next().value;
                tileCache.delete(firstKey);
              }
              
              // 返回空白透明图像
              return this.createTransparentImage();
            }
          ).catch(finalError => {
            // 兜底错误处理，防止Promise链中的未捕获错误
            console.error(`Promise链中发生未捕获错误: (${x}, ${y}, ${level})`, finalError);
            return this.createTransparentImage();
          });
        }
        
        // 如果不是Promise但是有合法结果（旧版本Cesium可能是这样）
        if (result) {
          return result;
        }
        
        // 默认回退，返回空白透明图像
        return this.createTransparentImage();
      } catch (loadError) {
        console.error(`loadImage调用失败: (${x}, ${y}, ${level})`, loadError);
        return this.createTransparentImage(); 
      }
    } catch (e) {
      console.error(`_doRequest方法发生未捕获错误: (${x}, ${y}, ${level})`, e);
      return this.createTransparentImage(); 
    }
  }
  
  /**
   * 创建一个透明的图像作为占位符
   * @private
   * @returns {HTMLCanvasElement} 透明图像
   */
  createTransparentImage() {
    try {
      const canvas = document.createElement('canvas');
      canvas.width = 256;
      canvas.height = 256;
      return canvas;
    } catch (e) {
      console.error('创建透明图像失败:', e);
      return undefined;
    }
  }
  
  /**
   * 切换到下一个服务器
   * @private
   */
  _switchServer() {
    // 如果禁用了备用地图，则不切换
    if (!USE_FALLBACK_MAP) {
      return;
    }
    
    // 如果没有备用服务器，则不切换
    if (!this._backupUrls || this._backupUrls.length === 0) {
      return;
    }
    
    try {
      // 增加当前服务器索引
      this._currentServerIndex = (this._currentServerIndex || 0) + 1;
      
      // 如果超出最大索引，回到第一个备用服务器
      if (this._currentServerIndex >= this._backupUrls.length) {
        this._currentServerIndex = 0;
        // 如果所有备用服务器都已尝试，重置错误状态
        this._resetErrorState();
      }
      
      // 使用新的备用URL
      const newUrl = this._backupUrls[this._currentServerIndex];
      this._updateUrlTemplate(newUrl);
      
      // 调试信息
      if (DEBUG_WMTS) {
        console.log(`已切换到备用服务器 #${this._currentServerIndex + 1}: ${newUrl}`);
      }
    } catch (e) {
      console.error('切换服务器失败:', e);
    }
  }

  /**
   * 构建图像资源URL
   * @private
   * @param {number} x 瓦片X坐标
   * @param {number} y 瓦片Y坐标
   * @param {number} level 瓦片级别
   * @returns {string} 图像URL
   */
  _buildImageResource(x, y, level) {
    // 检查缓存中是否有该瓦片的记录
    const tileKey = `${x}_${y}_${level}`;
    if (tileCache.has(tileKey) && tileCache.get(tileKey).failed) {
      // 如果该瓦片已经多次请求失败，直接返回透明瓦片
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
    }
    
    // 验证并修正瓦片坐标
    const validTile = calculateValidTileXY(x, y, level);
    
    // 计算子域名索引
    const subdomains = this._subdomains;
    const subdomainIndex = (validTile.x + validTile.y + level) % subdomains.length;
    const currentSubdomain = subdomains[subdomainIndex];
    
    // 构建URL
    let url = this._url
      .replace('{s}', currentSubdomain)
      .replace('{x}', validTile.x.toString())
      .replace('{y}', validTile.y.toString())
      .replace('{z}', level.toString())
      .replace('{TileMatrix}', level.toString())
      .replace('{TileRow}', validTile.y.toString())
      .replace('{TileCol}', validTile.x.toString());
      
    // 处理可能剩余的{style}占位符
    if (url.includes('{style}')) {
      if (this._currentStyle === 'img') {
        url = url.replace('{style}', '6');
      } else if (this._currentStyle === 'elec') {
        url = url.replace('{style}', '8');
      } else if (this._currentStyle === 'cva') {
        url = url.replace('{style}', '8');
      } else {
        // 默认样式
        url = url.replace('{style}', '7');
      }
    }
    
    return url;
  }

  /**
   * 重置错误状态（公共方法）
   * @public
   * @param {boolean} resetBackupFlag 是否重置备用服务器标志，默认为true
   */
  resetErrorState(resetBackupFlag = true) {
    // 执行内部错误状态重置
    this._resetErrorState(resetBackupFlag);
    
    // 额外处理全局错误计数和缓存
    totalErrorCount = 0;
     
    // 清除瓦片错误记录
    Object.keys(tileErrorCounts).forEach(key => {
      delete tileErrorCounts[key];
    });
     
    // 清除瓦片缓存
    tileCache.clear();
    
    // 根据参数决定是否重置备用服务器状态
    if (resetBackupFlag) {
      // 尝试恢复到原始URL
      if (this._originalOptions && this._originalOptions.url) {
        this._updateUrlTemplate(this._originalOptions.url);
      } else {
        const originalStyleUrl = TILE_URL[this._currentStyle];
        if (originalStyleUrl) {
          this._updateUrlTemplate(originalStyleUrl);
        }
      }
    }
    
    console.log('已重置图层错误状态' + (resetBackupFlag ? '并恢复原始服务器' : ''));
  }
}

export default AmapImageryProvider
