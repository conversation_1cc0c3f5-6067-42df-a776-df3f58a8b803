import * as Cesium from 'cesium';
import {MODEL_STATIC_URL} from '@/config/global.js'; 

export interface AmapImageryProviderOptions {
    style?: string;
    url?: string;
    subdomains?: string[];
    minimumLevel?: number;
    maximumLevel?: number;
    credit?: string;
    protocol?: string;
}

// 地图瓦片URL配置 - 多个备用服务器和不同格式
export const TILE_URL = {
    // 主服务器
    tianditu: "https://t0.tianditu.com/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=7711a24780452f03bb7c02fba98183b9",
    // 高德卫星影像 - 确保使用正确的卫星图URL
    img: 'https://webst{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
    // 高德矢量路网图
    elec: 'https://webrd{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
    cva: 'https://webst{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
    local1: MODEL_STATIC_URL+"/MAP1/{z}/{y}/{x}.png",
    local2: MODEL_STATIC_URL+"/MAP2/{z}/{x}/{y}.png",
    
    // 备用服务器 - 多种格式 - 确保所有备用也使用卫星图
    img_backup1: 'https://wprd0{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}', // 备用卫星图
    // 增加传入时间戳和随机参数，避免缓存和连接重置问题
    img_backup2: 'https://webst{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}&scl=2&ts=' + new Date().getTime(),
    img_backup3: 'https://webst{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}&scl=1&v=' + Math.floor(Math.random() * 10000),
    
    // 添加电子地图备用服务器
    elec_backup1: 'https://wprd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', // 备用电子地图
    elec_backup2: 'https://webrd{s}.is.autonavi.com/appmaptile?scl=1&lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', // 带scl参数的电子地图
    
    // 添加百度地图作为备用 - 需要坐标转换
    // baidu_img: 'https://maponline{s}.bdimg.com/tile/?qt=tile&x={x}&y={y}&z={z}&styles=sl&scaler=1&udt=20220428',
    
    // 备用方案 - OpenStreetMap，不受高德API限制
    osm: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
    osm_cycle: 'https://tile.thunderforest.com/cycle/{z}/{x}/{y}.png',
    
    // ArcGIS卫星图层 - 备用选项
    arcgis_img: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    
    // 另一种高德格式，提高兼容性
    gaode_vector: "https://webrd0{s}.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8",
    gaode_image: "https://webst0{s}.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=6",
    gaode_label: "https://webst0{s}.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8",
    
    // 天地图备用
    tianditu_vec: "https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=7711a24780452f03bb7c02fba98183b9",
    tianditu_img: "https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=7711a24780452f03bb7c02fba98183b9"
  }
  
export default class AmapImageryProvider {
    private _tilingScheme: Cesium.WebMercatorTilingScheme;
    private _tileWidth: number;
    private _tileHeight: number;
    private _maximumLevel: number;
    private _minimumLevel: number;
    private _url: string;
    private _subdomains: string[];
    private _credit: Cesium.Credit | undefined;
    private _ready: boolean;

    constructor(options: AmapImageryProviderOptions) {
    // 设置URL模板
        let selectedStyle = options.style || 'img'; // 默认使用卫星影像地图而不是电子地图
        options['url'] =
            options.url ||
            (function () {
                // 使用正确的URL拼接方式
                let url = TILE_URL[selectedStyle as keyof typeof TILE_URL] || TILE_URL['img'];
                // 如果URL已经包含协议，就直接使用
                if (url.startsWith('http://') || url.startsWith('https://')) {
                    return url;
                }
                // 修复: 确保没有重复的协议前缀
                const protocol = options.protocol || 'https://';
                // 移除可能存在的前导 '//'
                const cleanUrl = url.replace(/^\/\//, '');
                // 确保没有协议前缀的重复 (避免https:https://)
                return protocol + cleanUrl;
            })();

        console.log('options.url:', options.url)    
        this._url = options.url;
        this._subdomains = options.subdomains || ['01', '02', '03', '04'];
        this._minimumLevel = options.minimumLevel || 0;
        this._maximumLevel = options.maximumLevel || 18;
        this._tilingScheme = new Cesium.WebMercatorTilingScheme();
        this._tileWidth = 256;
        this._tileHeight = 256;
        this._ready = true;

        if (options.credit) {
            this._credit = new Cesium.Credit(options.credit);
        }
    }

    get url(): string {
        return this._url;
    }

    get tileWidth(): number {
        return this._tileWidth;
    }

    get tileHeight(): number {
        return this._tileHeight;
    }

    get maximumLevel(): number {
        return this._maximumLevel;
    }

    get minimumLevel(): number {
        return this._minimumLevel;
    }

    get tilingScheme(): Cesium.WebMercatorTilingScheme {
        return this._tilingScheme;
    }

    get ready(): boolean {
        return this._ready;
    }

    get credit(): Cesium.Credit | undefined {
        return this._credit;
    }

    get hasAlphaChannel(): boolean {
        return true;
    }

    getTileCredits(x: number, y: number, level: number): Cesium.Credit[] {
        return this._credit ? [this._credit] : [];
    }

    requestImage(x: number, y: number, level: number): Promise<HTMLImageElement | HTMLCanvasElement> {
        if (!this.ready) {
            throw new Error('Provider is not ready');
        }

        const url = this._url
            .replace('{s}', this._subdomains[(x + y + level) % this._subdomains.length])
            .replace('{x}', x.toString())
            .replace('{y}', y.toString())
            .replace('{z}', level.toString());

        return new Promise((resolve, reject) => {
            const image = new Image();
            image.crossOrigin = 'anonymous';
            image.onload = () => resolve(image);
            image.onerror = (e) => reject(e);
            image.src = url;
        });
    }
} 