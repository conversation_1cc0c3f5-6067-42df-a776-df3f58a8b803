/**
 * 简单的Cesium Viewer初始化
 * 专注于电力线路3D展示的核心功能
 */

// 直接从window获取Cesium，避免模块导入问题
declare global {
  interface Window {
    Cesium: any;
  }
}

export interface SimpleViewerOptions {
  containerId: string;
  homePosition?: {
    longitude: number;
    latitude: number;
    height: number;
  };
}

export class SimpleCesiumViewer {
  public viewer: any;
  private Cesium: any;

  constructor(options: SimpleViewerOptions) {
    // 确保Cesium已加载
    if (!window.Cesium) {
      throw new Error('Cesium未加载，请确保Cesium脚本已正确引入');
    }
    
    this.Cesium = window.Cesium;
    this.initViewer(options);
  }

  private initViewer(options: SimpleViewerOptions) {
    const { containerId, homePosition } = options;
    
    try {
      // 创建最简单的Viewer配置
      this.viewer = new this.Cesium.Viewer(containerId, {
        // 禁用所有不必要的UI组件
        animation: false,
        baseLayerPicker: false,
        fullscreenButton: false,
        vrButton: false,
        geocoder: false,
        homeButton: false,
        infoBox: false,
        sceneModePicker: false,
        selectionIndicator: false,
        timeline: false,
        navigationHelpButton: false,
        
        // 使用简单的地形提供者
        terrainProvider: this.Cesium.createWorldTerrain ? 
          this.Cesium.createWorldTerrain() : 
          new this.Cesium.EllipsoidTerrainProvider(),
          
        // 使用默认的影像提供者
        imageryProvider: new this.Cesium.OpenStreetMapImageryProvider({
          url: 'https://a.tile.openstreetmap.org/'
        })
      });

      // 设置初始相机位置
      if (homePosition) {
        this.viewer.camera.setView({
          destination: this.Cesium.Cartesian3.fromDegrees(
            homePosition.longitude, 
            homePosition.latitude, 
            homePosition.height
          )
        });
      }

      // 启用深度测试，避免线路悬空
      this.viewer.scene.globe.depthTestAgainstTerrain = true;

      console.log('SimpleCesiumViewer初始化成功');
      
    } catch (error) {
      console.error('SimpleCesiumViewer初始化失败:', error);
      throw error;
    }
  }

  /**
   * 添加简单的Polyline
   */
  addPolyline(positions: number[][], options: any = {}) {
    try {
      const cartesianPositions = this.Cesium.Cartesian3.fromDegreesArrayHeights(
        positions.flat()
      );

      const polyline = this.viewer.entities.add({
        name: options.name || '电力线路',
        polyline: {
          positions: cartesianPositions,
          width: options.width || 3,
          material: options.color || this.Cesium.Color.YELLOW,
          clampToGround: options.clampToGround !== false,
          show: true
        }
      });

      console.log('Polyline添加成功:', polyline);
      return polyline;
      
    } catch (error) {
      console.error('添加Polyline失败:', error);
      throw error;
    }
  }

  /**
   * 添加杆塔标记
   */
  addTowerMarker(longitude: number, latitude: number, height: number, options: any = {}) {
    try {
      const position = this.Cesium.Cartesian3.fromDegrees(longitude, latitude, height);
      
      const marker = this.viewer.entities.add({
        name: options.name || '杆塔',
        position: position,
        point: {
          pixelSize: options.size || 10,
          color: options.color || this.Cesium.Color.RED,
          outlineColor: this.Cesium.Color.WHITE,
          outlineWidth: 2,
          heightReference: this.Cesium.HeightReference.CLAMP_TO_GROUND
        },
        label: options.showLabel ? {
          text: options.name || '杆塔',
          font: '12pt sans-serif',
          fillColor: this.Cesium.Color.WHITE,
          outlineColor: this.Cesium.Color.BLACK,
          outlineWidth: 2,
          style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new this.Cesium.Cartesian2(0, -40)
        } : undefined
      });

      console.log('杆塔标记添加成功:', marker);
      return marker;
      
    } catch (error) {
      console.error('添加杆塔标记失败:', error);
      throw error;
    }
  }

  /**
   * 飞行到指定位置
   */
  flyTo(longitude: number, latitude: number, height: number = 1000) {
    try {
      this.viewer.camera.flyTo({
        destination: this.Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
        duration: 2.0
      });
    } catch (error) {
      console.error('飞行失败:', error);
    }
  }

  /**
   * 清除所有实体
   */
  clearAll() {
    this.viewer.entities.removeAll();
  }

  /**
   * 销毁viewer
   */
  destroy() {
    if (this.viewer) {
      this.viewer.destroy();
      this.viewer = null;
    }
  }
}

export default SimpleCesiumViewer;
