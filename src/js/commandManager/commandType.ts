export interface changeProjectType {
    id: string,
    type: string,
    projectID: string,
    projectCode?: string,
    token?: string
}

export interface flyToType {
    id: string,
    type: string,
    longitude: number,
    latitude: number,
    height: number,
    heading?: number,
    pitch?: number,
    duration?: number,
}

export interface mapMoveType {
    id: string,
    type: string,
    operation: string,
    amount: number
}

export interface load3DTilesType {
    id: string,
    type: string,
    url: string,
    name?: string
}

export interface remove3DTilesType {
    id: string,
    type: string, 
    name: string
}

export interface measureToolsType {
    id: string,
    type: string,
    operation: string
}

export interface groundPerspectiveType {
    id: string,
    type: string,
    enable: boolean
}

export interface terrainCutingType {
    id: string,
    type: string,
    enable: boolean
}

export interface loadGeojsonType {
    id: string,
    type: string,
    url: string,
    name?: string
}

export interface removeGeojsonType {
    id: string,
    type: string,
    name: string
}

export interface startRoamingType {
    id: string,
    type: string,
    name: string
}

export interface roaming {
    id: string,
    type: string,
    enable: boolean
}

export interface removeLabelType {
    id: string, 
    type: string,
    names: string[],
}

export interface loadLabelType {
    id: string,
    type: string,
    options: object[]
}

export interface updateToken {
    id: string,
    type: string,
    token: string
}