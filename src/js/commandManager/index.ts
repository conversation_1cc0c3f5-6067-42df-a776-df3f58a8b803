import * as Cesium from 'cesium';
import { getCurrentInstance } from 'vue'
import { getRefreshToken, setToken } from '@/utils/auth.js'
import { refreshToken } from "@/utils/login"
import { getprojectStructure } from '@/js/common/requestData'
import { useLineProjStore } from '@/store/lineProj.js'
import type {
    changeProjectType,
    flyToType,
    mapMoveType,
    load3DTilesType,
    groundPerspectiveType,
    loadGeojsonType,
    measureToolsType,
    remove3DTilesType,
    removeGeojsonType,
    roaming,
    startRoamingType,
    terrainCutingType,
    loadLabelType,
    updateTokenType,
    removeLabelType
} from './commandType'
import { flyTo, flyToTower } from '@/js/common/viewer'
import { S_Measure } from '@/js/common/measure'
import { addLabelNum, deleteLabelById } from "../common/divLabel";
import { setSimpleToken } from '@/utils/auth.js'
import TerrainCutting from '@/js/terrainExcavation/TerrainCutting'

interface Loaded3DTile {
    name: string;
    tileset: Cesium.Cesium3DTileset;
}

export default class CommandManager {
    viewer: Cesium.Viewer
    map3d_v2: any
    private _measure: S_Measure | undefined
    private _userTerrainCutting: TerrainCutting | undefined
    $cache: any
    load3DTiles: Loaded3DTile[]
    busy: boolean
    constructor(viewer: Cesium.Viewer, cache: any, map3d_v2: any) {
        this.viewer = viewer
        this.$cache = cache
        this.map3d_v2 = map3d_v2
        this.load3DTiles = []
        this.busy = false
        this._measure = undefined
        console.log('cache:', this.$cache)
    }
    sendMessageToUE(objToUe: any) {
        if ((window as any).ue) {
            console.log("$receiveEEEEEEEEE")
            if(navigator.userAgent.indexOf("ZdnWebView")>=0){
                (window as any).ue.call('',objToUe)
            }else{
                (window as any).ue.ueobject.jstoue(JSON.stringify(objToUe))
            }
        } else {
            console.log('window.parent')
            // window.parent.HandleResponse(JSON.stringify(objToUe))
            window.parent.postMessage(JSON.stringify(objToUe), '*')
        }
    }
    async parseCommand(command: changeProjectType | flyToType | mapMoveType | load3DTilesType | groundPerspectiveType | loadGeojsonType | measureToolsType | remove3DTilesType | removeGeojsonType | roaming | startRoamingType | terrainCutingType) {
        // console.log("parseCommand command:", command)
        return new Promise((resolve, reject) => {
            if (this.busy) {
                resolve([{
                    id: command.id,
                    type: command.type,
                    success: false,
                    msg: "command system is busy"
                }])
            }
            let returnMsg = {
                id: command.id,
                type: command.type,
                success: true,
                msg: "success"
            }
            switch (command.type) {
                case "changeProject":
                    this.busy = true
                    this.changeProject((command as changeProjectType).projectID, (command as changeProjectType).token).then((res) => {
                        console.log("changeProject res:", res)
                        this.busy = false
                        returnMsg.msg = "change project success"
                        resolve([returnMsg, res])
                    })
                    break
                case "flyTo":
                    this.busy = true
                    flyTo(this.viewer, { ...(command as flyToType), duration: (command as flyToType).duration || 1.5 }).then(() => {
                        console.log('flyToHome done')
                        this.busy = false
                        resolve([returnMsg])
                    })
                    break
                case "flyToTower":
                    this.busy = true
                    flyToTower(this.viewer, { ...(command as flyToType), duration: (command as flyToType).duration || 1.5 }).then(() => {
                        console.log('flyToHome done')
                        this.busy = false
                        resolve([returnMsg])
                    })
                    break
                case 'mapMove':
                    if (this._mapMove(command as mapMoveType) == false) {
                        returnMsg.success = false
                        returnMsg.msg = "operation error"
                    }
                    resolve([returnMsg])
                    break
                case 'load3DTiles':
                    this.busy = true
                    const name = (command as load3DTilesType).name || this.generateUniqueId()
                    this._load3DTiles((command as load3DTilesType).url, name).then((res) => {
                        this.busy = false
                        returnMsg.msg = res as string
                        resolve(returnMsg)
                    })
                    break
                case 'remove3DTiles':
                    this.busy = true
                    const success = this._remove3DTiles((command as remove3DTilesType).name)
                    this.busy = false
                    returnMsg.success = success
                    resolve([returnMsg])
                    break
                case 'measureTools':
                    if ('operation' in command) {
                        this._measureTools(command.operation)
                    }
                    resolve([returnMsg])
                    break
                case 'terrainCuting':
                    if ('enable' in command && typeof command.enable === 'boolean') {
                         this._terrainCutting(command.enable)
                     }
                    resolve([returnMsg])
                    break
                case 'groundPerspective':
                    if ('enable' in command && typeof command.enable === 'boolean') {
                        this._groundPerspective(command.enable)
                    }
                    resolve([returnMsg])
                    break
                case 'protectedArea':
                    const dataSource = this.viewer.dataSources.getByName('protectedArea')[0]
                    if (dataSource) {
                        if ('enable' in command && typeof command.enable === 'boolean') {
                            dataSource.show = command.enable
                        }
                    } else {
                        returnMsg.success = false
                        returnMsg.msg = 'protectedArea not found'
                    }
                    resolve([returnMsg])
                    break
                case 'removeLabel':
                    (command as unknown as removeLabelType).names.forEach((name: string) => {
                        deleteLabelById(this.viewer, name)
                    })
                    resolve([returnMsg])
                    break
                case 'loadLabel':
                    let options = (command as unknown as loadLabelType).options
                    options.forEach((option: any) => {
                        option.id = option.name
                        addLabelNum(this.viewer, option)
                    })
                    resolve([returnMsg])
                    break
                case 'updateToken':
                    setSimpleToken({ accessToken: (command as unknown as updateTokenType).token })
                    resolve([returnMsg])
                    break
                case 'startRoaming':

                    let res1: { lineName: string; enable: boolean; } | undefined = {
                        lineName: (command as unknown as startRoamingType).name,
                        enable: true
                    }
                    const store_lineProj = useLineProjStore()
                    console.log(store_lineProj.proLines)
                    if (!store_lineProj.proLines.some((item: any) => item.name === (command as unknown as startRoamingType).name)) {
                        returnMsg.success = false
                        returnMsg.msg = 'lineName ont found in project'
                        res1 = undefined
                    }
                    resolve([returnMsg, res1])
                    break
                case 'roaming':
                    let roaming_res2: { lineName: string | null; enable: boolean; } = {
                        lineName: (command as unknown as roaming).lineName,
                        enable: (command as unknown as roaming).enable
                    }
                    resolve([returnMsg, roaming_res2])
                    break
                case 'showBim':
                    resolve([returnMsg])
                    break
                case 'loadLineBuffer':
                    resolve([returnMsg])
                    break
                case 'loadInsulator':
                    resolve([returnMsg])
                    break   
                case 'zhongNanDian':
                    resolve([returnMsg])
                    break
                case 'jiaoChaKuaYue':
                    resolve([returnMsg])
                    break
                case 'shigongjinduLabel':
                    resolve([returnMsg])
                    break
                case 'shejiMode':
                    resolve([returnMsg])
                    break
                case 'shigongMode':
                    resolve([returnMsg])
                    break
                case 'constructionLabel':
                    resolve([returnMsg])
                    break
                default:
                    returnMsg.success = false
                    returnMsg.msg = 'command not found'
                    resolve([returnMsg])
                    break
            }
        })
    }
    async changeProject(pId: string, token?: string) {
        console.log("changeProject pId:", pId)
        // await this._getRefreshTokenByServer(pId)
        if (token) {
            setSimpleToken({ accessToken: token })
        }
        const p = this.$cache.getProject(pId)

        this.$cache.setProject(p || {})
        let params = {
            projectId: pId
        }
        return new Promise((resolve, reject) => {
            getprojectStructure(params).then((res) => {
                const projectStructure = res.data.data
                if (res.data.data && res.data.data.children) {
                    const store_lineProj = useLineProjStore()
                    store_lineProj.setProLines(res.data.data.children)
                }
                console.log('projectStructure', projectStructure)
                resolve(projectStructure)
            }).catch((res) => {
                console.log('catch:', pId, res)
                resolve(null)
            })
        })

    }
    private async _getRefreshTokenByServer(pId: string) {
        let data = {
            defaultProjectId: parseInt(pId),
            refreshToken: getRefreshToken(),
        }
        await refreshToken(data).then((res: any) => {
            if (res.data.code != 0) {
                return "login defeat";
            } //登录失败同样返回成功
            setToken(res.data.data)
            return "login success";
        })
    }
    private _mapMove(command: mapMoveType) {
        if (command.operation === 'moveForward') {
            this.viewer.camera.moveForward(command.amount)
            return true
        }
        if (command.operation === 'moveBackward') {
            this.viewer.camera.moveBackward(command.amount)
            return true
        }
        return false
    }
    private async _load3DTiles(url: string, name: string) {
        console.log('load3DTiles url:', url)
        if (name == undefined) {
            name = this.generateUniqueId()
        }
        try {
            const tileset = await Cesium.Cesium3DTileset.fromUrl(url);
            this.viewer.scene.primitives.add(tileset);
            console.log('3DTiles loaded', tileset);
            this.viewer.flyTo(tileset);
            this.load3DTiles.push({
                name: name as string,
                tileset: tileset
            });
            return name;
        } catch (error) {
            console.error(`Error loading tileset: ${error}`);
            throw error;
        }
    }
    private _remove3DTiles(name: string) {
        const target = this.load3DTiles.find(item => item.name === name)
    
        if (target) {
            this.load3DTiles.splice(this.load3DTiles.indexOf(target), 1)
            this.viewer.scene.primitives.remove(target.tileset)
            return true
        }
        return false
    }
    generateUniqueId() {
        const timestamp = Date.now(); // 获取当前时间戳
        const randomPart = Math.floor(Math.random() * 10000); // 生成一个0到10000之间的随机数
        return `${timestamp}-${randomPart}`;
    }
    private _measureTools(operation: string) {
        if (this._measure == undefined) {
            this._measure = new S_Measure(this.viewer)
        }
        console.log(operation, this._measure)
        if (operation === 'distance') {
            this._measure.measurePolyLine()
        }
        if (operation === 'area') {
            this._measure.measurePolygon()
        }
        if (operation === 'height') {
            this._measure.measureHeight()
        }
        if (operation === 'clear') {
            console.log('measureTools clear')
            this._measure.destroy()
            this._measure = undefined
        }
    }
    private _groundPerspective(enable: boolean) {
        const nearFarScalar = false;
        const alpha = 0.9
        const near = 100;
        const far = 500;
        const near_translucency = 0.8;
        const far_translucency = 1.0;

        this.viewer.scene.globe.translucency.enabled = enable; // 开启地面半透明效果
        if (enable) {
            console.log("debug:开启地面半透明效果");
            if (nearFarScalar) {
                this.viewer.scene.globe.translucency.frontFaceAlphaByDistance = new Cesium.NearFarScalar(near, near_translucency, far, far_translucency); // 地面半透明程度随距离变化
                this.viewer.scene.globe.translucency.frontFaceAlpha = 1.0; // 固定地面半透明程度
            } else {
                this.viewer.scene.globe.translucency.frontFaceAlphaByDistance = new Cesium.NearFarScalar();
                this.viewer.scene.globe.translucency.frontFaceAlpha = alpha; // 固定地面半透明程度
            }
        }
    }
    private _terrainCutting(enable: boolean) {
        if (this._userTerrainCutting == undefined) {
            this._userTerrainCutting = new TerrainCutting(this.viewer)
        }
        if (enable) {
            this._userTerrainCutting.create()
        } else {
            this._userTerrainCutting.stop()
            this._userTerrainCutting = undefined
        }
    }

    roaming(lineName: String) {
        // 1- 根据 lineName 获取对应的巡航路径杆塔
        const store_lineProj = useLineProjStore()
        console.log('roaming', store_lineProj.lineDataV20)
        let towers = store_lineProj.proLines.find(item => item.name == lineName)?.children
        console.log('roaming', towers)
        if (towers == undefined) {
            return false
        }
        return true
        // 2- 根据杆塔 获取对应的坐标
        let allLineTowerDetail = store_lineProj.lineDataV20.lineDetail
        console.log('roaming allLineTowerDetail', allLineTowerDetail)
        let positions = []
        for (let i = 0; i < towers.length; i++) {
            let tower = towers[i]
            let towerDetail = allLineTowerDetail.find((item: any) => item.towerNumber == tower.name)
            if (towerDetail != undefined) {
                console.log('towerDetail', towerDetail)
                positions.push([towerDetail.longitude, towerDetail.latitude, towerDetail.height + 100])
            }
        }
        console.log('roaming positions', positions)
        // 3- 根据杆塔 将无关线路及其杆塔进行隐藏
        let lineCollection = this.map3d_v2.getLineCollection()
        let linesInfo = this.map3d_v2.getAllLineInfo()
        console.log('lineCollection', lineCollection)
        for (let i = 0; i < towers.length; i++) {
            let towerName = towers[i].name
            console.log('towerName', towerName)
            for (let j = 0; j < linesInfo.length; j++) {
                let lineInfo = linesInfo[j]
                if (lineInfo.insulatorAllocation.some((item: any) => item.towerID == towerName)) {
                    linesInfo.pop(lineInfo)
                }
            }
        }
        console.log('linesInfo', linesInfo)

    }
}
