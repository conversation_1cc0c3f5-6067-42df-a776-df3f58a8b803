import * as Cesium from '@/Cesium'
// import * as Cesium from "cesium"
import { getCurrentInstance } from 'vue'
import { getRefreshToken, setToken } from '@/utils/auth.js'
import { refreshToken } from "@/utils/login"
import { getprojectStructure } from '@/js/common/requestData.ts'
import { useLineProjStore } from '@/store/lineProj.js'
import type {
    changeProjectType,
    flyToType,
    mapMoveType,
    load3DTilesType,
    groundPerspectiveType,
    loadGeojsonType,
    measureToolsType,
    remove3DTilesType,
    removeGeojsonType,
    roaming,
    startRoamingType,
    terrainCutingType
} from './commandType'
import { flyTo, flyToTower } from '@/js/common/viewer'
import { S_Measure } from '@/js/common/measure'
import { DivLabel, deleteLabelById } from "@/js/common/divLabel.js";
import { setSimpleToken } from '@/utils/auth.js'
import TerrainCutting from '@/js/terrainExcavation/TerrainCutting'

export default class CommandManager {
    viewer: Cesium.Viewer
    private _measure: S_Measure | undefined
    private _userTerrainCutting: TerrainCutting | undefined
    $cache: any
    load3DTiles: object[]
    busy: boolean
    constructor(viewer: Cesium.Viewer, cache: any) {
        this.viewer = viewer
        this.$cache = cache
        this.load3DTiles = []
        this.busy = false
        this._measure = undefined
        console.log('cache:', this.$cache)
    }
    sendMessageToUE(objToUe: any) {
        if (window.ue) {
            console.log("$receiveEEEEEEEEE")
            if(navigator.userAgent.indexOf("ZdnWebView")>=0){
                ue.call('',objToUe)
            }else{
                ue.ueobject.jstoue(JSON.stringify(objToUe))
            }
        } else {
            console.log('window.parent')
            // window.parent.HandleResponse(JSON.stringify(objToUe))
            window.parent.postMessage(JSON.stringify(objToUe), '*')
        }
    }
    async parseCommand(command: changeProjectType | flyToType | mapMoveType | load3DTilesType | groundPerspectiveType | loadGeojsonType | measureToolsType | remove3DTilesType | removeGeojsonType | roaming | startRoamingType | terrainCutingType) {
        // console.log("parseCommand command:", command)
        return new Promise((resolve, reject) => {
            if (this.busy) {
                resolve([{
                    id: command.id,
                    type: command.type,
                    success: false,
                    msg: "command system is busy"
                }])
            }
            let returnMsg = {
                id: command.id,
                type: command.type,
                success: true,
                msg: "success"
            }
            switch (command.type) {
                case "changeProject":
                    this.busy = true
                    this.changeProject(command.projectID, command.token).then((res) => {
                        console.log("changeProject res:", res)
                        this.busy = false
                        returnMsg.msg = "change project success"
                        resolve([returnMsg, res])
                    })
                    break
                case "flyTo":
                    this.busy = true
                    flyTo(this.viewer, command).then(() => {
                        console.log('flyToHome done')
                        this.busy = false
                        resolve([returnMsg])
                    })
                    break
                case "flyToTower":
                    this.busy = true
                    flyToTower(this.viewer, command).then(() => {
                        console.log('flyToHome done')
                        this.busy = false
                        resolve([returnMsg])
                    })
                    break
                case 'mapMove':
                    if (this._mapMove(command) == false) {
                        returnMsg.success = false
                        returnMsg.msg = "operation error"
                    }
                    resolve([returnMsg])
                    break
                case 'load3DTiles':
                    this.busy = true
                    this._load3DTiles(command.url, command.name).then((res: string) => {
                        this.busy = false
                        returnMsg.msg = res
                        resolve(returnMsg)
                    })
                    break
                case 'remove3DTiles':
                    this.busy = true
                    this._remove3DTiles(command.name).then(res => {
                        this.busy = false
                        returnMsg.success = res
                        resolve([returnMsg])
                    })
                    break
                case 'measureTools':
                    this._measureTools(command.operation)
                    resolve([returnMsg])
                    break
                case 'terrainCuting':
                    this._terrainCutting(command.enable)
                    resolve([returnMsg])
                    break
                case 'groundPerspective':
                    this._groundPerspective(command.enable)
                    resolve([returnMsg])
                    break
                case 'protectedArea':
                    const dataSource = this.viewer.dataSources.getByName('protectedArea')[0]
                    if (dataSource) {
                        dataSource.show = command.enable
                    } else {
                        returnMsg.success = false
                        returnMsg.msg = 'protectedArea not found'
                    }
                    resolve([returnMsg])
                    break
                case 'removeLabel':
                    command.names.forEach(name => {
                        deleteLabelById(this.viewer, name)
                    })
                    resolve([returnMsg])
                    break
                case 'loadLabel':
                    let options = command.options
                    let label = new DivLabel()
                    options.forEach(option => {
                        option.id = option.name
                        let labelEntity = label.addDynamicLabel_cesium(this.viewer, option)
                    })
                    resolve([returnMsg])
                    break
                case 'updateToken':
                    setSimpleToken({ accessToken: command.token })
                    resolve([returnMsg])
                    break
                case 'startRoaming':

                    let res1 = {
                        lineName: command.name,
                        enable: true
                    }
                    const store_lineProj = useLineProjStore()
                    console.log(store_lineProj.proLines)
                    if (!store_lineProj.proLines.some(item => item.name === command.name)) {
                        returnMsg.success = false
                        returnMsg.msg = 'lineName ont found in project'
                        res1 = undefined
                    }
                    resolve([returnMsg, res1])
                    break
                case 'roaming':
                    let roaming_res2 = {
                        lineName: null,
                        enable: command.enable
                    }
                    resolve([returnMsg, roaming_res2])
                    break
                case 'showBim':
                    resolve([returnMsg])
                    break
                case 'loadLineBuffer':
                    resolve([returnMsg])
                    break
                case 'loadInsulator':
                    resolve([returnMsg])
                    break   
                case 'zhongNanDian':
                    resolve([returnMsg])
                    break
                case 'jiaoChaKuaYue':
                    resolve([returnMsg])
                    break
                case 'shigongjinduLabel':
                    resolve([returnMsg])
                    break
                case 'shejiMode':
                    resolve([returnMsg])
                    break
                case 'shigongMode':
                    resolve([returnMsg])
                    break
                case 'constructionLabel':
                    resolve([returnMsg])
                    break
                default:
                    returnMsg.success = false
                    returnMsg.msg = 'command not found'
                    resolve([returnMsg])
                    break
            }
        })
    }
    async changeProject(pId: string, token?: string) {
        console.log("changeProject pId:", pId)
        // await this._getRefreshTokenByServer(pId)
        if (token) {
            setSimpleToken({ accessToken: token })
        }
        const p = this.$cache.getProject(pId)

        this.$cache.setProject(p || {})
        let params = {
            projectId: pId
        }
        return new Promise((resolve, reject) => {
            getprojectStructure(params).then((res) => {
                const projectStructure = res.data.data
                if (res.data.data && res.data.data.children) {
                    const store_lineProj = useLineProjStore()
                    store_lineProj.setProLines(res.data.data.children)
                }
                console.log('projectStructure', projectStructure)
                resolve(projectStructure)
            }).catch((res) => {
                console.log('catch:', pId, res)
                resolve(null)
            })
        })

    }
    private async _getRefreshTokenByServer(pId: string) {
        let data = {
            defaultProjectId: parseInt(pId),
            refreshToken: getRefreshToken(),
        }
        await refreshToken(data).then((res: any) => {
            if (res.data.code != 0) {
                return "login defeat";
            } //登录失败同样返回成功
            setToken(res.data.data)
            return "login success";
        })
    }
    private _mapMove(command: mapMoveType) {
        if (command.operation === 'moveForward') {
            this.viewer.camera.moveForward(command.amount)
            return true
        }
        if (command.operation === 'moveBackward') {
            this.viewer.camera.moveBackward(command.amount)
            return true
        }
        return false
    }
    private _load3DTiles(url: string, name: string) {
        console.log('load3DTiles url:', url)
        const tileset = new Cesium.Cesium3DTileset({
            url: url,
        })
        if (name == undefined) {
            name = this.generateUniqueId()
        }
        this.viewer.scene.primitives.add(tileset)
        return new Promise((resolve, reject) => {
            tileset.readyPromise.then((tileset) => {
                console.log('3DTiles loaded', tileset)
                this.viewer.flyTo(tileset)
                this.load3DTiles.push({
                    name: name,
                    tileset: tileset
                })
                resolve(name)
            })
        })
    }
    private _remove3DTiles(name: string) {
        const target = this.load3DTiles.find((item: any) => {
            return item.name === name
        })
        if (target) {
            this.load3DTiles.splice(this.load3DTiles.indexOf(target), 1)
            this.viewer.scene.primitives.remove(target.tileset)
            return true
        }
        return false
    }
    generateUniqueId() {
        const timestamp = Date.now(); // 获取当前时间戳
        const randomPart = Math.floor(Math.random() * 10000); // 生成一个0到10000之间的随机数
        return `${timestamp}-${randomPart}`;
    }
    private _measureTools(operation: string) {
        if (this._measure == undefined) {
            this._measure = new S_Measure(this.viewer)
        }
        console.log(operation, this._measure)
        if (operation === 'distance') {
            this._measure.measurePolyLine()
        }
        if (operation === 'area') {
            this._measure.measurePolygon()
        }
        if (operation === 'height') {
            this._measure.measureHeight()
        }
        if (operation === 'clear') {
            console.log('measureTools clear')
            this._measure.destroy()
            this._measure = undefined
        }
    }
    private _groundPerspective(enable: boolean) {
        const nearFarScalar = false;
        const alpha = 0.9
        const near = 100;
        const far = 500;
        const near_translucency = 0.8;
        const far_translucency = 1.0;

        this.viewer.scene.globe.translucency.enabled = enable; // 开启地面半透明效果
        if (enable) {
            console.log("debug:开启地面半透明效果");
            if (nearFarScalar) {
                this.viewer.scene.globe.translucency.frontFaceAlphaByDistance = new Cesium.NearFarScalar(near, near_translucency, far, far_translucency); // 地面半透明程度随距离变化
                this.viewer.scene.globe.translucency.frontFaceAlpha = 1.0; // 固定地面半透明程度
            } else {
                this.viewer.scene.globe.translucency.frontFaceAlphaByDistance = undefined; // 地面半透明程度随距离变化
                this.viewer.scene.globe.translucency.frontFaceAlpha = alpha; // 固定地面半透明程度
            }
        }
    }
    private _terrainCutting(enable: boolean) {
        if (this._userTerrainCutting == undefined) {
            this._userTerrainCutting = new TerrainCutting(this.viewer)
        }
        if (enable) {
            this._userTerrainCutting.create()
        } else {
            this._userTerrainCutting.stop()
            this._userTerrainCutting = undefined
        }
    }

    roaming(lineName: String) {
        // 1- 根据 lineName 获取对应的巡航路径杆塔
        const store_lineProj = useLineProjStore()
        console.log('roaming', store_lineProj.lineDataV20)
        let towers = store_lineProj.proLines.find(item => item.name == lineName)?.children
        console.log('roaming', towers)
        if (towers == undefined) {
            return false
        }
        return true
        // 2- 根据杆塔 获取对应的坐标
        let allLineTowerDetail = store_lineProj.lineDataV20.lineDetail
        console.log('roaming allLineTowerDetail', allLineTowerDetail)
        let positions = []
        for (let i = 0; i < towers.length; i++) {
            let tower = towers[i]
            let towerDetail = allLineTowerDetail.find(item => item.towerNumber == tower.name)
            if (towerDetail != undefined) {
                console.log('towerDetail', towerDetail)
                positions.push([towerDetail.longitude, towerDetail.latitude, towerDetail.height + 100])
            }
        }
        console.log('roaming positions', positions)
        // 3- 根据杆塔 将无关线路及其杆塔进行隐藏
        let lineCollection = this.map3d_v2.getLineCollection()
        let linesInfo = this.map3d_v2.getAllLineInfo()
        console.log('lineCollection', lineCollection)
        for (let i = 0; i < towers.length; i++) {
            let towerName = towers[i].name
            console.log('towerName', towerName)
            for (let j = 0; j < linesInfo.length; j++) {
                let lineInfo = linesInfo[j]
                if (lineInfo.insulatorAllocation.some(item => item.towerID == towerName)) {
                    linesInfo.pop(lineInfo)
                }
            }
        }
        console.log('linesInfo', linesInfo)

    }
}
