interface Point {
    x: number;
    y: number;
}

interface CatenaryOptions {
    segments?: number;
    iterationLimit?: number;
}

interface CatenaryCurveResult {
    start: [number, number];
    curves?: [number, number, number, number][];
}

export function catenaryLineArr(StartPoint: any, EndPoint: any, SagMinHeight: number, PointsNum: number): [number, number][];
export function getCatenaryCurve(point1: any, point2: any, chainLength: number, options?: CatenaryOptions): CatenaryCurveResult;