import {ElMessage} from "element-plus";

const EPSILON = 1e-6

/**
 * calculate and return the points within the number of PointNum based on catenary function which go
 * from StartPoint to EndPoint.
 * reference URL:   https://zhuanlan.zhihu.com/p/375108301
 *                  https://blog.csdn.net/Hanford/article/details/54411553
 *                  https://wuli.wiki/online/Catena.html#Catena_eq6
 *                  https://en.wikipedia.org/wiki/Catenary#cite_note-58
 *                  https://gist.github.com/sketchpunk/cbfe82229234f5ccc58f6b2dd9fa98b0
 *                  https://www.alanzucconi.com/2020/12/13/catenary-1/
 *                  https://www.alanzucconi.com/2020/12/13/catenary-2/
 *                  https://segmentfault.com/a/1190000012670045   --贝塞尔曲线
 *                  https://mpewsey.github.io/2021/12/17/sag-tension-algorithm.html
 */
export function catenaryLineArr(StartPoint, EndPoint, SagMinHeight, PointsNum) {

    const distance = getDistanceBetweenPoints(StartPoint, EndPoint);
    const segments = PointsNum,
        iterationLimit = 50,
        stretch_factor = 1.006,
        // chainLength = stretch_factor * (Math.sqrt(Math.pow(StartPoint[0], 2) + Math.pow(StartPoint[1]-SagMinHeight, 2)) +
        //     Math.sqrt(Math.pow(EndPoint[0], 2) + Math.pow(EndPoint[1]-SagMinHeight, 2)));
        // chainLength = stretch_factor * (Math.sqrt(Math.pow(StartPoint.x, 2) + Math.pow(StartPoint.y - SagMinHeight, 2)) +
        //     Math.sqrt(Math.pow(EndPoint.x, 2) + Math.pow(EndPoint.y - SagMinHeight, 2)));
        chainLength = distance * stretch_factor;

    // console.log("catenaryLineArr chainLength:", chainLength, "distance from StartPoint to EndPoint:", distance);

    // const pullOffset = Math.max(distance - chainLength, -0.1)
    // const stretchFactor = pullOffset / chainLength + 1

    // const a = computeCatenaryParameter(StartPoint, EndPoint, SagMinHeight);
    // console.log("###########computeCatenaryParameter(p1,p2,SagMinHeight):", a);
    // Create the catenary path.
    const result = getCatenaryCurve(StartPoint, EndPoint, chainLength, {
        segments: segments,
        iterationLimit: iterationLimit
    });

    let CatenaryArr = [];
    CatenaryArr.push(result.start);
    result.curves?.forEach(function (value, index, any) {
        CatenaryArr.push([value[2], value[3]]);
    })
    return CatenaryArr;

}

/**
 * Calculate the catenary curve.
 * Increasing the segments value will produce a catenary closer
 * to reality, but will require more calcluations.
 */
// return: [x, y][]
function getCurve(
    // The catenary parameter.
    a,
    // First point.
    p1,
    // Second point.
    p2,
    // The calculated offset on the x axis.
    offsetX,
    // The calculated offset on the y axis.
    offsetY,
    // How many "parts" the chain should be made of.
    segments
) {
    const data = [
        // Calculate the first point on the curve
        [p1.x, a * Math.cosh((p1.x - offsetX) / a) + offsetY]
    ]

    const d = p2.x - p1.x
    const length = segments - 1

    // Calculate the points in between the first and last point
    for (let i = 0; i < length; i++) {
        const x = p1.x + (d * (i + 0.5)) / length
        const y = a * Math.cosh((x - offsetX) / a) + offsetY
        data.push([x, y])
    }

    // Calculate the last point on the curve
    data.push([p2.x, a * Math.cosh((p2.x - offsetX) / a) + offsetY])

    return data
}

/**
 * Approximates the catenary curve between two points and returns the resulting
 * coordinates.
 *
 * If the curve would result in a single straight line, the approximation is
 * skipped and the input coordinates are returned.
 *
 * It returns an object with a property `type` to differenciate between `line`
 * and `quadraticCurve`. You can pass this object together with your 2D canvas
 * context to `drawResult` to directly draw it to the canvas.
 * reference URL:   https://zhuanlan.zhihu.com/p/375108301
 *                  https://blog.csdn.net/Hanford/article/details/54411553
 *                  https://wuli.wiki/online/Catena.html#Catena_eq6
 *                  https://en.wikipedia.org/wiki/Catenary#cite_note-58
 *                  https://gist.github.com/sketchpunk/cbfe82229234f5ccc58f6b2dd9fa98b0
 *                  https://www.alanzucconi.com/2020/12/13/catenary-1/
 *                  https://www.alanzucconi.com/2020/12/13/catenary-2/
 *                  https://segmentfault.com/a/1190000012670045   --贝塞尔曲线
 *                  https://mpewsey.github.io/2021/12/17/sag-tension-algorithm.html
 */
// return : CatenaryCurveResult
export function getCatenaryCurve(
    point1,
    point2,
    chainLength,
    options     //: CatenaryOptions = {}
) {
    const segments = options.segments || 25
    const iterationLimit = options.iterationLimit || 6

    // The curves are reversed
    const isFlipped = point1.x > point2.x

    const p1 = isFlipped ? point2 : point1
    const p2 = isFlipped ? point1 : point2

    const distance = getDistanceBetweenPoints(p1, p2);
    // console.log("two Points:", p1, "&", p2, " distance: ", distance, "origin chainLength:",chainLength);

    if (distance < chainLength) {
        const diff = p2.x - p1.x

        if (diff > 0.01) {
            const h = p2.x - p1.x
            const v = p2.y - p1.y


            const a = getCatenaryParameter(h, v, chainLength, iterationLimit);

            // const lb = (p2.x - p1.x)/2 - a * Math.asinh((p2.y - p1.y)/(2*a*Math.sinh((p2.x - p1.x)/(2*a))));
            // //chainLength@a refer to https://mpewsey.github.io/2021/12/17/sag-tension-algorithm.html
            // console.log("a = getCatenaryParameter:", a, "lb:", lb,"SagMinH:", a * Math.cosh((0) / a),
            //     "chainLength@a:", Math.sqrt((Math.pow((2*a*Math.sinh((p2.x - p1.x - lb)/a)), 2) +Math.pow(p2.y - p1.y, 2))));

            const x = (a * Math.log((chainLength + v) / (chainLength - v)) - h) * 0.5;
            const y = a * Math.cosh(x / a);

            const offsetX = p1.x - x;
            const offsetY = p1.y - y;
            const curveData = getCurve(a, p1, p2, offsetX, offsetY, segments);
            if (isFlipped) {
                curveData.reverse();
            }
            return getCurveResult(curveData);
        }

        // console.log("两点相差距离太近！")
        const mx = (p1.x + p2.x) * 0.5
        const my = (p1.y + p2.y + chainLength) * 0.5

        return getLineResult([
            [p1.x, p1.y],
            [mx, my],
            [p2.x, p2.y]
        ])
    }

    console.log("线长度小于两点间距离！",p1,p2);
    //ElMessage("线长度小于两点间距离！");
    return getLineResult([
        [p1.x, p1.y],
        [p2.x, p2.y]
    ])
}

/**
 * Draws a quadratic curve between every calculated catenary segment,
 * so that the segments don't look like straight lines.
 */
function getCurveResult(data) {
    let length = data.length - 1
    let ox = data[1][0]
    let oy = data[1][1]

    const start = [data[0][0], data[0][1]]
    const curves = []

    for (let i = 2; i < length; i++) {
        const x = data[i][0]
        const y = data[i][1]
        const mx = (x + ox) * 0.5
        const my = (y + oy) * 0.5
        curves.push([ox, oy, mx, my])
        ox = x
        oy = y
    }

    length = data.length
    curves.push([
        data[length - 2][0],
        data[length - 2][1],
        data[length - 1][0],
        data[length - 1][1]
    ])

    return {type: 'quadraticCurve', start, curves}
}

/**
 * Draws a straight line between two points.
 *
 */
function getLineResult(data) {
    return {
        type: 'line',
        start: data[0],
        lines: data.slice(1)
    }
}

/**
 * Determines catenary parameter.
 * reference: unique formula 44 page 6 in THE HANGING CABLE PROBLEM FOR PRACTICAL APPLICATION.pdf
 * a = (z1 + z2) [ y2 . (z1 . z2)2 ] . 2y √ z1z2 [y2 . (z1 . z2)2] 2(z1 . z2)2
 *
 */
function computeCatenaryParameter(p1,p2,SagMinHeight){
    let z1 = p1.y - SagMinHeight,
        z2 = p2.y -SagMinHeight;
    let a = computeCatenaryParameter(p1,p2,SagMinHeight);
    let chainLength = a * (Math.sinh(p2.x/a) - Math.sinh(p1.x/a));

    return ((z1 + z2)*(Math.pow(chainLength,2) - Math.pow((z1 - z2),2))
        - 2 * chainLength * Math.sqrt(z1 * z2 * (Math.pow(chainLength,2)) - Math.pow((z1-z2),2)))
        / (2 * Math.pow((z1-z2),2))
}
/**
 * Determines catenary parameter.
 *
 */
function getCatenaryParameter(
    h,
    v,
    length,
    limit
) {
    const m = Math.sqrt(length * length - v * v) / h
    let x = Math.acosh(m) + 1
    let prevx = -1
    let count = 0

    // Iterate until we find a suitable catenary parameter or reach the iteration
    // limit
    while (Math.abs(x - prevx) > EPSILON && count < limit) {
        prevx = x
        x = x - (Math.sinh(x) - m * x) / (Math.cosh(x) - m)
        count++
    }

    return h / (2 * x)
}

/**
 * Get the difference for x and y axis to another point
 */
//return : Point
function getDifferenceTo(p1, p2) {
    // console.log("getDifferenceTo x:", p1.x - p2.x, "y:", p1.y - p2.y);
    return {x: p1.x - p2.x, y: p1.y - p2.y}
}

export function getDistanceBetweenPoints(p1, p2) {
    const diff = getDifferenceTo(p1, p2)
    // console.log("getDistanceBetweenPoints diff:", diff);

    return Math.sqrt(Math.pow(diff.x, 2) + Math.pow(diff.y, 2))
}

// 基于x,y,z三维空间算法实现
export  function calcHangerLine(startPoint, endPoint, sagMinHeight, pointsNum) {
    var x1 = startPoint[0], y1 = startPoint[1], z1 = startPoint[2];
    var x2 = endPoint[0], y2 = endPoint[1], z2 = endPoint[2];
    var L = Math.sqrt((x2-x1)*(x2-x1) + (y2-y1)*(y2-y1) + (z2-z1)*(z2-z1));
    var h = sagMinHeight;
    var a = Math.sqrt(L*L - h*h);
    var b = h;
    var dx = (x2-x1) / (pointsNum-1);
    var dy = (y2-y1) / (pointsNum-1);
    var dz = (z2-z1) / (pointsNum-1);
    var points = [];
    for (var i = 0; i < pointsNum; i++) {
        var x = x1 + dx*i;
        var y = y1 + dy*i;
        var z = z1 + dz*i;
        var k = b/a;
        var r = a * Math.sqrt(1 + k*k*(x-x1)*(x-x1)/(a*a));
        var theta = Math.atan(k*(x-x1)/a);
        var phi = Math.atan2(y-y1, r*Math.cos(theta)-x1);
        var point = [r*Math.sin(phi)*Math.cos(theta), r*Math.sin(phi)*Math.sin(theta), z];
        points.push(point);
    }
    return points;
}

//基于x,y二位空间下的算法实现
export function catenaryCurve(startPoint, endPoint, sagMinHeight, pointsNum) {
    const deltaX = endPoint[0] - startPoint[0];
    const deltaY = endPoint[1] - startPoint[1];
    const length = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const span = length / (pointsNum - 1);
    const halfSpan = span / 2;
    const beta = Math.asinh(sagMinHeight / (2 * halfSpan));
    const a = halfSpan / beta;
    const points = [];
    for (let i = 0; i < pointsNum; i++) {
        const distance = i * span;
        const x = startPoint[0] + distance * deltaX / length;
        const y = startPoint[1] + distance * deltaY / length;
        const sag = a * Math.cosh((length / 2 - distance) / a);
        const z = sagMinHeight - sag;
        points.push([x, y, z]);
    }
    return points;
}


// 在XOY平面实现悬链线函数
function catenary(x, startPoint, endPoint, sagMinHeight) {
    const { x: x1, y: y1 } = startPoint;
    const { x: x2, y: y2 } = endPoint;
    const len = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    const a = (y2 - y1) / len;
    const b = (x2 - x1) / len;
    const h = sagMinHeight;
    const d = Math.sqrt(h ** 2 + (len / 2) ** 2);
    const cosh = Math.cosh((x - (x1 + x2) / 2) / d);
    const y = a * (x - x1) + y1 - h * cosh;
    return y;
}

// 根据起始点、终止点、最低高度和点数量生成悬链线上的点坐标数组
export function generateCatenaryPoints(startPoint, endPoint, sagMinHeight, pointsNum) {
    const { x: x1, y: y1 } = startPoint;
    const { x: x2, y: y2 } = endPoint;
    const minX = Math.min(x1, x2);
    const maxX = Math.max(x1, x2);
    const step = (maxX - minX) / (pointsNum - 1);
    const points = [];
    for (let i = 0; i < pointsNum; i++) {
        const x = minX + i * step;
        const y = catenary(x, startPoint, endPoint, sagMinHeight);
        points.push([x, y]);
    }
    return points;
}

export function catenaryLine(startPoint, endPoint, sagMinHeight, pointsNum) {
    const [x1, y1] = [startPoint.x, startPoint.y];
    const [x2, y2] = [endPoint.x, endPoint.y];
    const L = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2); // 跨距
    const a = (y2 - y1) / (x2 - x1); // 直线斜率
    const b = y1 - a * x1; // 直线截距
    const D = sagMinHeight; // 下垂距离
    const H = (x2 - x1) / 2; // 半跨距
    const T = (D / H) * L; // 张力
    const delta = L / (pointsNum - 1); // 步长
    let points = [];
    for (let i = 0; i < pointsNum; i++) {
        const x = x1 + i * delta;
        const y = a * x + b + T / 2 * (Math.cosh((y2 - y1) / (2 * T) * (x - (x1 + x2) / 2)) - 1);
        points.push([x, y]);
    }
    return points;
}
/**
 * Pass in the return value from getCatenaryCurve and your canvas context to
 * draw the curve.
 */
// export function drawResult(
//     result: CatenaryCurveResult,
//     context: CanvasRenderingContext2D
// ) {
//     if (result.type === 'quadraticCurve') {
//         drawResultCurve(result, context)
//     } else if (result.type === 'line') {
//         drawResultLine(result, context)
//     }
// }
//
// /**
//  * Draw the curve using lineTo.
//  */
// export function drawResultLine(
//     result: CatenaryCurveLineResult,
//     context: CanvasRenderingContext2D
// ) {
//     context.moveTo(...result.start)
//     for (let i = 0; i < result.lines.length; i++) {
//         context.lineTo(...result.lines[i])
//     }
// }
//
// /**
//  * Draw the curve using quadraticCurveTo.
//  */
// export function drawResultCurve(
//     result: CatenaryCurveQuadraticResult,
//     context: CanvasRenderingContext2D
// ) {
//     context.moveTo(...result.start)
//
//     for (let i = 0; i < result.curves.length; i++) {
//         context.quadraticCurveTo(...result.curves[i])
//     }
// }