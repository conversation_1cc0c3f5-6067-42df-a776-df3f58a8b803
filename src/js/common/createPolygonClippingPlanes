/**
 * 从点数组创建多边形裁剪平面
 * @param viewer Cesium查看器
 * @param pointArray 多边形点数组
 * @param height 高度
 */
private static createPolygonClippingPlanes(viewer: any, pointArray: number[], height: number): any {
    try {
        console.log('开始创建多边形裁剪平面...');
        // 将经纬度数组转换为Cartesian3数组
        console.log('转换点数组为Cartesian3数组...');
        const positions = this.pointArrayToCartesian3Array(pointArray, height);
        if (!positions || positions.length < 3) {
            console.error('转换后的点数组无效，无法创建裁剪平面');
            return null;
        }
        console.log('成功创建Cartesian3数组，点数:', positions.length);
        
        // 计算多边形中心点
        console.log('计算多边形中心点...');
        const centerCoords = this.calculatePolygonCenter(pointArray);
        if (!centerCoords || centerCoords.length < 2) {
            console.error('计算多边形中心点失败');
            return null;
        }
        console.log('多边形中心点:', centerCoords);
        
        // 创建中心点的Cartesian3坐标
        const ellipsoid = viewer.scene.globe.ellipsoid;
        const center = Cesium.Cartesian3.fromDegrees(centerCoords[0], centerCoords[1], height);
        
        // 获取中心点的地表高度
        const centerCartographic = Cesium.Cartographic.fromDegrees(centerCoords[0], centerCoords[1]);
        let surfaceHeight = 0;
        try {
            // 尝试获取地形高度
            surfaceHeight = viewer.scene.globe.getHeight(centerCartographic) || 0;
            console.log('中心点地表高度:', surfaceHeight);
        } catch (e) {
            console.warn('无法获取地表高度，使用0作为默认值');
        }
        
        // 计算实际裁剪高度（相对于地表）
        // 如果height是负值，表示地下深度；如果是正值，表示地上高度
        const clipHeight = height < 0 ? height : height - surfaceHeight;
        console.log('计算得到的裁剪高度:', clipHeight, '(原始高度:', height, ')');
        
        // 创建平面集合
        const planes: any[] = [];
        
        // 创建水平裁剪平面（朝上）- 用于压平地形
        console.log('创建水平裁剪平面...');
        try {
            // 使用UP单位向量作为法向量，更可靠
            const upNormal = Cesium.Cartesian3.normalize(
                Cesium.Cartesian3.clone(ellipsoid.geodeticSurfaceNormal(center)),
                new Cesium.Cartesian3()
            );
            
            // 计算平面距离 - 正确处理水平平面
            const centerWithHeight = Cesium.Cartesian3.fromDegrees(
                centerCoords[0], centerCoords[1], surfaceHeight + clipHeight
            );
            const distance = -Cesium.Cartesian3.dot(upNormal, centerWithHeight);
            
            planes.push(new Cesium.ClippingPlane(upNormal, distance));
            console.log('水平裁剪平面创建成功，高度:', clipHeight, '距离:', distance);
        } catch (err) {
            console.error('创建水平裁剪平面失败:', err);
            return null;
        }
        
        // 为多边形每条边创建垂直裁剪平面 - 用于创建多边形边界
        // 这样可以确保只有多边形内部区域被裁剪，外部不受影响
        console.log('开始创建垂直裁剪平面...');
        let successCount = 0;
        let totalEdges = positions.length - 1;
        
        if (positions.length >= 3) {
            for (let i = 0; i < positions.length - 1; i++) {
                try {
                    // 获取边的两个端点
                    const p1 = positions[i];
                    const p2 = positions[i + 1];
                    
                    // 计算从p1到p2的向量
                    const p1p2 = Cesium.Cartesian3.subtract(p2, p1, new Cesium.Cartesian3());
                    
                    // 使用地球椭球体的法向量（指向正上方）
                    const upVector = ellipsoid.geodeticSurfaceNormal(center, new Cesium.Cartesian3());
                    
                    // 计算法向量 - 边向量与上向量的叉积得到水平方向的法向量
                    const normal = Cesium.Cartesian3.cross(p1p2, upVector, new Cesium.Cartesian3());
                    
                    // 规范化法向量
                    if (Cesium.Cartesian3.magnitude(normal) < 0.00001) {
                        // 如果法向量接近零向量，跳过此边
                        console.warn(`边 ${i+1} 法向量接近零，跳过此边的裁剪平面`);
                        continue;
                    }
                    
                    Cesium.Cartesian3.normalize(normal, normal);
                    
                    // 确保法向量朝内 - 使用中心点检查方向
                    const toCenter = Cesium.Cartesian3.subtract(center, p1, new Cesium.Cartesian3());
                    const dot = Cesium.Cartesian3.dot(normal, toCenter);
                    
                    if (dot < 0) {
                        Cesium.Cartesian3.negate(normal, normal);
                    }
                    
                    // 计算平面距离（平面到原点的距离）
                    const distance = -Cesium.Cartesian3.dot(normal, p1);
                    
                    // 创建裁剪平面
                    planes.push(new Cesium.ClippingPlane(normal, distance));
                    successCount++;
                    
                    // 只记录第一个和最后一个平面的详细信息，避免日志过多
                    if (i === 0 || i === positions.length - 2) {
                        console.log(`垂直裁剪平面 ${i+1}/${totalEdges} 创建成功, 法向量:`, 
                                    normal, '距离:', distance);
                    }
                } catch (err) {
                    console.error(`创建垂直裁剪平面 ${i+1} 失败:`, err);
                }
            }
        }
        
        console.log(`垂直裁剪平面创建完成: 成功 ${successCount}/${totalEdges}`);
        
        // 如果没有创建任何垂直平面，返回失败
        if (successCount === 0) {
            console.error('未能创建任何有效的垂直裁剪平面');
            return null;
        }
        
        // 优化：合并近似平行的平面以减少平面数量
        console.log('开始优化裁剪平面...');
        console.log('优化前平面数量:', planes.length);
        
        // 创建并返回ClippingPlaneCollection
        try {
            const clippingPlanes = new Cesium.ClippingPlaneCollection({
                planes: planes,
                edgeWidth: 1.0,
                edgeColor: Cesium.Color.WHITE,
                enabled: true,
                unionClippingRegions: false, // 确保使用交集模式，只有所有平面内部区域才会显示
                modelMatrix: Cesium.Matrix4.IDENTITY,
                // 添加可视化效果，便于调试
                debugShowBoundingVolume: false
            });
            console.log('成功创建裁剪平面集合，平面总数:', planes.length);
            return clippingPlanes;
        } catch (err) {
            console.error('创建裁剪平面集合失败:', err);
            return null;
        }
    } catch (error) {
        console.error('创建多边形裁剪平面过程中发生错误:', error);
        return null;
    }
} 