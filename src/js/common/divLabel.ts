import * as Cesium from 'cesium';
import {getCurrentInstance} from "vue";

interface LabelData {
    id: string;
    divHTML?: string;
    position: [number, number, number];
    text?: string;
    fillColor?: string;
    bgColor?: string;
    font?: string;
    img?: string;
    scale?: number;
    url?: string;
}

/**
 * @descripion: 广告牌封装js
 * @param {Viewer} viewer
 * @param {Cartesian2} position
 * @return {*}
*/

var labelNum: any[] = [];
var labelZndNum: any[] = [];

export class DivLabel {
    constructor(val?: any) {
    }

    addDynamicLabel(viewer: any, data: LabelData, isxf: boolean | null = null, num: number | null = null) {
        let div = document.createElement("div");
        div.id = data.id;
        div.style.position = "absolute";
        let divHTML = data.divHTML;
        div.innerHTML = divHTML || '';
        viewer.cesiumWidget.container.appendChild(div);
        let gisPosition = Cesium.Cartesian3.fromDegrees(
            data.position[0],
            data.position[1],
            data.position[2]
        );
        //实时更新位置
        viewer.scene.postRender.addEventListener(() => {
            const canvasHeight = viewer.scene.canvas.height;
            const windowPosition = new Cesium.Cartesian2();
            Cesium.SceneTransforms.worldToWindowCoordinates(
                viewer.scene,
                gisPosition,
                windowPosition
            );
            div.style.bottom = canvasHeight - windowPosition.y - 15 + "px";
            const elWidth = div.offsetWidth;
            div.style.left = windowPosition.x - elWidth / 2 + "px";

            //解决滚动不隐藏问题
            const camerPosition = viewer.camera.position;

            let height = viewer.scene.globe.ellipsoid.cartesianToCartographic(camerPosition).height;

            var position = viewer.scene.camera.positionCartographic;
            var longitude = Cesium.Math.toDegrees(position.longitude).toFixed(6);
            height += viewer.scene.globe.ellipsoid.maximumRadius;

            if (viewer.camera.positionCartographic.height < 50000000) {
                let maxHeight = 6379523  // 广告牌可视的最大高度
                if (height > 0 && height < maxHeight) {
                    div.style.display = "block"
                }else {
                    div.style.display = "none"
                }
                if (isxf && parseFloat(longitude) - data.position[0] > 0.006)  {
                    div.style.display = "none"
                }
            } else {
                div.style.display = "none"
            }
        });
        addLabelNum(div)
    }

    // 重难点场景label
    addDynamicLabelZnd(viewer: any, data: LabelData) {
        let div = document.createElement("div");
        div.id = data.id;
        div.style.position = "absolute";
        let divHTML = data.divHTML;
        div.innerHTML = divHTML || '';
        viewer.cesiumWidget.container.appendChild(div);
        let gisPosition = Cesium.Cartesian3.fromDegrees(
            data.position[0],
            data.position[1],
            data.position[2]
        );
        //实时更新位置
        viewer.scene.postRender.addEventListener(() => {
            const canvasHeight = viewer.scene.canvas.height;
            const windowPosition = new Cesium.Cartesian2();
            Cesium.SceneTransforms.worldToWindowCoordinates(
                viewer.scene,
                gisPosition,
                windowPosition
            );
            div.style.bottom = canvasHeight - windowPosition.y - 15 + "px";
            const elWidth = div.offsetWidth;
            div.style.left = windowPosition.x - elWidth / 2 + "px";

            //解决滚动不隐藏问题
            const camerPosition = viewer.camera.position;
            let height = viewer.scene.globe.ellipsoid.cartesianToCartographic(camerPosition).height;
            height += viewer.scene.globe.ellipsoid.maximumRadius;

            if (viewer.camera.positionCartographic.height < 50000000) {
                let maxHeight = 6379523  // 广告牌可视的最大高度
                if (height > 0 && height < maxHeight) {
                    div.style.display = "block"
                } else {
                    div.style.display = "none"
                }
            } else {
                div.style.display = "none"
            }
        });
        addLabelZndNum(div)
    }

    // 基于cesium引擎添加label by lkj
    addDynamicLabel_cesium(viewer: any, data: LabelData, isZND: boolean = false){
        let label_id = data.id
        if(viewer.entities.getById(label_id) != undefined){
            // 待添加的标签已经存在 不重复添加
            return;
        }

        // 如果data中有给定颜色则按给定颜色 否则使用默认
        let fillColor = data.fillColor ? new Cesium.Color.fromCssColorString(data.fillColor) : Cesium.Color.YELLOW;
        let bgColor = data.bgColor ? new Cesium.Color.fromCssColorString(data.bgColor) : new Cesium.Color(0.165, 0.165, 0.165, 0.3);
        let font = data.font ? data.font : '24px Helvetica';

        let options: any = {
            position: Cesium.Cartesian3.fromDegrees(...data.position),
            id: label_id,
            label: {
                text: data.text,
                font: font,
                fillColor: fillColor,
                showBackground: true,
                backgroundColor: bgColor,
                backgroundPadding : new Cesium.Cartesian2(7, 5),
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                scaleByDistance : new Cesium.NearFarScalar(0, 2.5, 2000, 0.0),
            }
        }

        if(data.img){
            options.label.horizontalOrigin = Cesium.HorizontalOrigin.RIGHT
            options.billboard = {
                image: data.img,
                scale: data.scale ? data.scale : 1.0,
                scaleByDistance : new Cesium.NearFarScalar(0, 2.5, 2000, 0.0),
                horizontalOrigin :Cesium.HorizontalOrigin.LEFT,
            }
            options.userMsg = data.url;
            let billbaord_id = 'billboard_'+data.id;
            options.userName = billbaord_id;
        }
        let label_entity = viewer.entities.add(options);
        isZND ? addLabelZndNum(label_id) : addLabelNum(label_id)
        return label_entity;
    }
}

//统计广告牌数量
export function addLabelNum(div: any) {
    labelNum.push(div)
}

//添加重难点场景广告牌数量
export function addLabelZndNum(div: any) {
    labelZndNum.push(div)
}

export function deleteZndLabel(viewer: any) {
    console.log("deleteZndLabel=",labelZndNum)
    for (let i = 0; i < labelZndNum.length; i++) {
        viewer.entities.removeById(labelZndNum[i])
    }
    labelZndNum = []
}

//隐匿或删除广告牌
export function deleteLabel () {
    console.log('deletelabel:', labelNum)
    for (let i = 0; i < labelNum.length; i++) {
        if(document.getElementById(labelNum[i].id) && document.getElementById(labelNum[i].id) !== undefined) {
            document.getElementById(labelNum[i].id)!.remove()
        }
    }
    labelNum = []
}

export function deleteLabelById (viewer: any, id: string) {
    viewer.entities.removeById(id)
}

export function deleteAllLabels(viewer: any) {
  const entities = viewer.entities.values;
  for (let i = entities.length - 1; i >= 0; i--) {
      const entity = entities[i];
      if (entity.label) {
          viewer.entities.remove(entity);
      }
  }
}
