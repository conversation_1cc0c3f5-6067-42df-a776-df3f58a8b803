import * as Cesium from 'cesium';
// import * as Cesium from 'cesium';


interface LabelData {
    id: string;
    divHTML?: string;
    position: [number, number, number];
    text?: string;
    fillColor?: string;
    bgColor?: string;
    font?: string;
    img?: string;
    scale?: number;
    url?: string;
}
/**
 * @descripion: 广告牌封装js
 * @param {Viewer} viewer
 * @param {Cartesian2} position
 * @return {*}
*/

let labelNum: any[] = [];
let labelZndNum: any[] = [];

//统计广告牌数量
export function addLabelNum(viewer: any, data: LabelData, isxf: boolean | null = null, num: number | null = null) {
    if (data.divHTML) {
        let div = document.createElement("div");
        div.id = data.id;
        div.style.position = "absolute";
        div.innerHTML = data.divHTML;
        viewer.cesiumWidget.container.appendChild(div);
        let gisPosition = Cesium.Cartesian3.fromDegrees(
            data.position[0],
            data.position[1],
            data.position[2]
        );
        //实时更新位置
        viewer.scene.postRender.addEventListener(() => {
            const canvasHeight = viewer.scene.canvas.height;
            const windowPosition = new Cesium.Cartesian2();
            Cesium.SceneTransforms.worldToWindowCoordinates(
                viewer.scene,
                gisPosition,
                windowPosition
            );
            div.style.bottom = canvasHeight - windowPosition.y - 15 + "px";
            const elWidth = div.offsetWidth;
            div.style.left = windowPosition.x - elWidth / 2 + "px";

            //解决滚动不隐藏问题
            const camerPosition = viewer.camera.position;

            let height = viewer.scene.globe.ellipsoid.cartesianToCartographic(camerPosition).height;

            var position = viewer.scene.camera.positionCartographic; //经纬度单位为弧度，高程单位为米.
            // 弧度转经纬度
            var longitude = parseFloat(Cesium.Math.toDegrees(position.longitude).toFixed(6));
            height += viewer.scene.globe.ellipsoid.maximumRadius;
            if (viewer.camera.positionCartographic.height < 50000000) {
                let maxHeight = 6379523  // 广告牌可视的最大高度
                if (height > 0 && height < maxHeight) {
                    div.style.display = "block"
                }else {
                    div.style.display = "none"
                }
                if (isxf && longitude - data.position[0] > 0.006)  {
                    div.style.display = "none"
                }
            } else {
                div.style.display = "none"
            }
        });
        labelNum.push(div);
    } else {
        let label_id = data.id
        if(viewer.entities.getById (label_id) != undefined){
            return;
        }

        let fillColor = data.fillColor ? Cesium.Color.fromCssColorString(data.fillColor) : Cesium.Color.YELLOW;
        let bgColor = data.bgColor ? Cesium.Color.fromCssColorString(data.bgColor) : new Cesium.Color(0.165, 0.165, 0.165, 0.3);
        let font = data.font?data.font:'24px Helvetica';

        let options: any = {
            position: Cesium.Cartesian3.fromDegrees(data.position[0], data.position[1], data.position[2]),
            id: label_id,
            label: {
                text: data.text,
                font: font,
                fillColor: fillColor,
                showBackground: true,
                backgroundColor: bgColor,
                backgroundPadding : new Cesium.Cartesian2(7, 5),
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                scaleByDistance : new Cesium.NearFarScalar(0, 2.5, 2000, 0.0),
            }
        }

        if(data.img){
            options.label.horizontalOrigin = Cesium.HorizontalOrigin.RIGHT
            options.billboard = {
                image: data.img,
                scale: data.scale?data.scale:1.0,
                scaleByDistance : new Cesium.NearFarScalar(0, 2.5, 2000, 0.0),
                horizontalOrigin :Cesium.HorizontalOrigin.LEFT,
            }
        }
        let entity = viewer.entities.add(options);
        labelNum.push(entity);
    }
}

//添加重难点场景广告牌数量
export function addLabelZndNum(div: any) {
    labelZndNum.push(div)
    // console.log("labelNum",labelNum)
}



// 重难点场景label
//暂时不用这个方法
export function addDynamicLabelZnd(viewer: any, data: LabelData) {
    let div = document.createElement("div");
    div.id = data.id;
    div.style.position = "absolute";
    // div.style.width = "100px";
    // div.style.height = "100px";
    let divHTML = data.divHTML;
    if (divHTML) {
        div.innerHTML = divHTML;
    }
    // div.innerHTML = HTMLTable;
    viewer.cesiumWidget.container.appendChild(div);
    let gisPosition = Cesium.Cartesian3.fromDegrees(
        data.position[0],
        data.position[1],
        data.position[2]
    );
    //实时更新位置
    viewer.scene.postRender.addEventListener(() => {
        const canvasHeight = viewer.scene.canvas.height;
        const windowPosition = new Cesium.Cartesian2();
        Cesium.SceneTransforms.worldToWindowCoordinates(
            viewer.scene,
            gisPosition,
            windowPosition
        );
        div.style.bottom = canvasHeight - windowPosition.y - 15 + "px";
        const elWidth = div.offsetWidth;
        div.style.left = windowPosition.x - elWidth / 2 + "px";

        //解决滚动不隐藏问题
        const camerPosition = viewer.camera.position;
        let height = viewer.scene.globe.ellipsoid.cartesianToCartographic(camerPosition).height;
        height += viewer.scene.globe.ellipsoid.maximumRadius;
        // 此处因为开启巡航时候，广告牌会隐藏，所以删除了第一个判断
        // if ((!(Cesium.Cartesian3.distance(camerPosition, gisPosition) > height)) && viewer.camera.positionCartographic.height < 50000000) {
        if (viewer.camera.positionCartographic.height < 50000000) {
            let maxHeight = 6379523  // 广告牌可视的最大高度
            if (height > 0 && height < maxHeight) {
                div.style.display = "block"
            } else {
                div.style.display = "none"
            }
        } else {
            div.style.display = "none"
        }
    });
    addLabelZndNum(div);
}



export function deleteZndLabel(viewer: any) {
    console.log("deleteZndLabel=", labelZndNum)
    for (let i = 0; i < labelZndNum.length; i++) {
        // if(document.getElementById(labelZndNum[i].id) && document.getElementById(labelZndNum[i].id) !== undefined) {
        //     document.getElementById(labelZndNum[i].id).remove()
        // }
        viewer.entities.removeById(labelZndNum[i])
    }
    labelZndNum = []
}

export function deleteLabelById (viewer: any, id: string) {
    viewer.entities.removeById(id)
}

export function deleteAllLabels(viewer: any) {
  const entities = viewer.entities.values;
  for (let i = entities.length - 1; i >= 0; i--) {
      const entity = entities[i];
      if (entity.label) {
          viewer.entities.remove(entity);
      }
  }
}




