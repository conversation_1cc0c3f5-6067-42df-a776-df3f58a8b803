import * as Cesium from '@/Cesium'

/**
 * 获取变电站配置信息
 * @param stationName 变电站名称
 * @returns 变电站配置对象或undefined
 */
export async function getSubstationConfig(stationName: string): Promise<any> {
    try {
        // 尝试多种路径加载变电站信息
        const possiblePaths = [
            './static/jsdata/TEST/building.json',  // 成功路径放在首位
            './jsdata/TEST/building.json',
            '../static/jsdata/TEST/building.json',
            '../../static/jsdata/TEST/building.json',
            '/static/jsdata/TEST/building.json',
            '/jsdata/TEST/building.json'
        ];
        
        let response = null;
        let successPath = '';
        
        // 尝试所有可能的路径
        for (const path of possiblePaths) {
            try {
                console.log(`尝试从路径加载变电站配置: ${path}`);
                const resp = await fetch(path);
                if (resp.ok) {
                    response = resp;
                    successPath = path;
                    console.log(`成功从路径加载变电站配置: ${path}`);
                    break;
                }
            } catch (err) {
                console.log(`从 ${path} 加载失败: ${err}`);
            }
        }
        
        if (!response) {
            throw new Error('所有路径尝试均失败，无法加载变电站配置文件');
        }
        
        // 解析JSON数据
        let data;
        try {
            data = await response.json();
            console.log(`成功解析变电站配置文件，检查数据结构...`);
        } catch (parseError) {
            console.error('解析JSON数据失败:', parseError);
            throw new Error('配置文件格式无效，无法解析JSON');
        }
        
        // 打印数据结构，以便调试
        console.log('配置文件数据结构:', Object.keys(data));
        
        // 处理不同的数据结构
        // 情况1: 变电站作为顶级键
        if (data[stationName]) {
            console.log(`在顶级键中找到变电站 ${stationName} 的配置`);
            return data[stationName];
        }
        
        // 情况2: 在数组中查找
        if (Array.isArray(data)) {
            const station = data.find((item: any) => item.name === stationName);
            if (station) {
                console.log(`在数组中找到变电站 ${stationName} 的配置`);
                return station;
            }
        }
        
        // 情况3: 检查是否有menu项并检查各个索引键
        if (data.menu) {
            console.log('发现menu配置，查找匹配的变电站索引...');
            const menuItem = data.menu.find((item: any) => item.name === stationName);
            if (menuItem && menuItem.index && data[menuItem.index]) {
                console.log(`通过menu索引 ${menuItem.index} 找到变电站 ${stationName} 配置`);
                return data[menuItem.index];
            }
        }
        
        // 遍历所有子对象查找匹配的名称
        for (const key in data) {
            if (typeof data[key] === 'object' && data[key] !== null) {
                if (data[key].name === stationName) {
                    console.log(`在键 ${key} 下找到变电站 ${stationName} 配置`);
                    return data[key];
                }
            }
        }
        
        console.warn(`未找到名为 ${stationName} 的变电站配置`);
        console.log('可用的变电站有:');
        // 打印所有可能的变电站名称以供参考
        for (const key in data) {
            if (typeof data[key] === 'object' && data[key] !== null && data[key].name) {
                console.log(`- ${data[key].name} (键: ${key})`);
            }
        }
        
        return undefined;
    } catch (error) {
        console.error('获取变电站配置失败:', error);
        return undefined;
    }
}

/**
 * 为变电站1创建地形压平
 * @param viewer Cesium查看器
 * @param customHeight 可选的自定义压平高度
 * @returns 是否成功创建地形压平
 */
export async function flattenTerrainForSubstation1(viewer: any, customHeight?: number): Promise<boolean> {
    console.log('flattenTerrainForSubstation1开始执行');
    
    try {
        // 检查Cesium和viewer是否有效
        if (!viewer) {
            console.error('viewer对象无效，地形压平失败');
            return false;
        }
        
        if (!viewer.scene || !viewer.scene.globe) {
            console.error('viewer.scene或globe对象无效，地形压平失败');
            return false;
        }
        
        console.log('正在查询变电站1的配置信息');
        
        // 获取变电站1的配置
        const station = await getSubstationConfig('变电站1');
        if (!station) {
            console.error('无法获取变电站1配置，地形压平失败');
            return false;
        }
        
        console.log('成功获取变电站1配置', station);
        
        // 获取压平区域坐标
        let pointArray: number[] = [];
        
        // 首先检查是否有underdraughtPosition（地下区域坐标）
        if (station.underdraughtPosition && Array.isArray(station.underdraughtPosition) && 
            station.underdraughtPosition.length > 0) {
            // 使用地下区域坐标作为压平区域
            pointArray = station.underdraughtPosition;
            console.log('使用地下区域坐标作为压平区域', pointArray);
        } else if (station.position && Array.isArray(station.position) && station.position.length >= 2) {
            // 如果没有地下区域坐标，则使用位置创建一个矩形区域
            const lon = station.position[0];
            const lat = station.position[1];
            const size = 0.001; // 约100米的矩形区域
            
            // 创建矩形四个角的坐标
            pointArray = [
                lon - size, lat - size,
                lon + size, lat - size,
                lon + size, lat + size,
                lon - size, lat + size,
                lon - size, lat - size, // 闭合多边形
            ];
            console.log('使用位置创建矩形区域作为压平区域', pointArray);
        } else {
            console.error('变电站1配置中没有有效的位置信息，无法创建地形压平');
            return false;
        }
        
        // 获取压平高度 - 优先使用自定义高度
        let height: number;
        if (typeof customHeight === 'number') {
            height = customHeight;
            console.log('使用自定义高度作为压平高度', height);
        } else if (station.underdraughtHeight && Array.isArray(station.underdraughtHeight) && 
            station.underdraughtHeight.length > 0) {
            height = station.underdraughtHeight[0];
            console.log('使用配置文件中的地下高度作为压平高度', height);
        } else if (station.position && Array.isArray(station.position) && station.position.length >= 3) {
            // 使用位置的高度值
            height = station.position[2];
            console.log('使用位置高度作为压平高度', height);
        } else {
            // 默认高度
            height = 0;
            console.log('未找到高度信息，使用默认高度', height);
        }
        
        console.log('开始添加地形压平');
        // 添加地形压平
        const result = addFlattenTerrain(viewer, '变电站1', pointArray, height);
        
        if (result) {
            console.log('变电站1地形压平创建成功，高度:', height);
        } else {
            console.error('变电站1地形压平创建失败');
        }
        
        return result;
    } catch (error) {
        console.error('变电站1地形压平过程发生错误:', error);
        return false;
    }
}

/**
 * 地形编辑管理器 - 当标准地形编辑API不可用时提供替代实现
 */
class TerrainModifier {
    // 存储已创建的编辑
    static edits = new Map<string, {
        points?: number[];
        height?: number;
        clippingPlanes?: any;
        enabled?: boolean;
        visualization?: boolean;
    }>();
    
    /**
     * 使用裁剪平面模拟地形压平
     * @param viewer Cesium查看器
     * @param name 编辑名称
     * @param pointArray 多边形点数组
     * @param height 压平高度
     */
    static createFlattenArea(viewer: any, name: string, pointArray: number[], height: number): boolean {
        console.log('开始创建地形压平区域:', name);
        
        if (!viewer) {
            console.error('无效的viewer对象');
            return false;
        }
        
        if (!Array.isArray(pointArray) || pointArray.length < 6) {
            console.error('无效的点数组，至少需要3个点（6个坐标值）');
            return false;
        }
        
        if (typeof height !== 'number') {
            console.error('无效的高度值，必须是数字');
            return false;
        }
        console.log(`地形压平区域参数验证通过，点数组长度: ${pointArray.length}，高度: ${height}`);
        
        // 检查名称是否已存在
        if (this.edits.has(name)) {
            console.warn(`地形压平区域 "${name}" 已存在，将被替换`);
            this.removeFlattenArea(viewer, name);
        }
        
        try {
            // 创建多边形裁剪平面
            console.log('创建裁剪平面...');
            const clippingPlanes = this.createPolygonClippingPlanes(viewer, pointArray, height);
            
            if (!clippingPlanes) {
                console.error('创建裁剪平面失败');
                return false;
            }
            
            console.log('裁剪平面创建成功，应用到地球...');
            
            // 备份当前的裁剪平面集合
            const oldClippingPlanes = viewer.scene.globe.clippingPlanes;
            
            // 设置新的裁剪平面到Globe
            try {
                // 确保先克隆裁剪平面，防止重复引用
                viewer.scene.globe.clippingPlanes = clippingPlanes;
                console.log('裁剪平面已成功应用到地球');
            } catch (error) {
                console.error('应用裁剪平面到地球时发生错误:', error);
                // 恢复旧的裁剪平面
                viewer.scene.globe.clippingPlanes = oldClippingPlanes;
                return false;
            }
            
            // 添加多边形可视化
            let visualization = false;
            try {
                visualization = this.addPolygonVisualization(viewer, name, pointArray, height);
                console.log('多边形可视化添加', visualization ? '成功' : '失败');
            } catch (error) {
                console.warn('添加多边形可视化时发生错误（非致命）:', error);
            }
            
            // 记录编辑信息
            this.edits.set(name, {
                points: pointArray,
                height: height,
                clippingPlanes: clippingPlanes,
                enabled: true,
                visualization: visualization
            });
            
            console.log(`地形压平区域 "${name}" 创建成功，中心高度: ${height}`);
            return true;
        } catch (error) {
            console.error('创建地形压平区域失败:', error);
            return false;
        }
    }
    
    /**
     * 计算多边形中心点
     * @param pointArray 多边形点数组
     * @returns 中心点坐标 [经度, 纬度]
     */
    private static calculatePolygonCenter(pointArray: number[]): number[] {
        if (pointArray.length < 4) {
            // 至少需要两个点(一个闭合多边形至少有4个值：[lon1,lat1,lon2,lat2])
            return [pointArray[0], pointArray[1]];
        }
        
        // 使用更精确的多边形中心计算方法 - 使用重心法
        let area = 0;
        let cx = 0;
        let cy = 0;
        const points = [];
        
        // 提取经纬度点
        for (let i = 0; i < pointArray.length; i += 2) {
            if (i + 1 < pointArray.length) {
                points.push({
                    x: pointArray[i],
                    y: pointArray[i + 1]
                });
            }
        }
        
        // 确保多边形闭合
        if (points.length > 0 && 
            (points[0].x !== points[points.length - 1].x || 
             points[0].y !== points[points.length - 1].y)) {
            points.push(points[0]);
        }
        
        // 计算多边形面积和中心
        for (let i = 0; i < points.length - 1; i++) {
            const a = points[i].x * points[i + 1].y - points[i + 1].x * points[i].y;
            area += a;
            cx += (points[i].x + points[i + 1].x) * a;
            cy += (points[i].y + points[i + 1].y) * a;
        }
        
        area = area / 2;
        const factor = 1 / (6 * area);
        cx = cx * factor;
        cy = cy * factor;
        
        // 确保正确性
        if (isNaN(cx) || isNaN(cy)) {
            // 回退到简单平均值
            let sumLon = 0;
            let sumLat = 0;
            let count = 0;
            
            for (let i = 0; i < pointArray.length; i += 2) {
                if (i + 1 < pointArray.length) {
                    sumLon += pointArray[i];
                    sumLat += pointArray[i + 1];
                    count++;
                }
            }
            
            cx = sumLon / count;
            cy = sumLat / count;
        }
        
        console.log('多边形中心点计算:', {centerLon: cx, centerLat: cy});
        
        return [cx, cy];
    }
    
    /**
     * 将点数组转换为Cartesian3数组
     * @param pointArray 多边形点数组
     * @param height 高度
     * @returns Cartesian3数组
     */
    private static pointArrayToCartesian3Array(pointArray: number[], height: number): any[] {
        const positions: any[] = [];
        for (let i = 0; i < pointArray.length; i += 2) {
            if (i + 1 < pointArray.length) {
                positions.push(Cesium.Cartesian3.fromDegrees(pointArray[i], pointArray[i + 1], height));
            }
        }
        
        // 确保多边形闭合
        if (positions.length > 0 && 
            (pointArray[0] !== pointArray[pointArray.length - 2] || 
             pointArray[1] !== pointArray[pointArray.length - 1])) {
            positions.push(positions[0]);
        }
        
        return positions;
    }
    
    /**
     * 从点数组创建多边形裁剪平面
     * @param viewer Cesium查看器
     * @param pointArray 多边形点数组
     * @param height 高度
     */
    private static createPolygonClippingPlanes(viewer: any, pointArray: number[], height: number): any {
        try {
            console.log('开始创建多边形裁剪平面...');
            // 将经纬度数组转换为Cartesian3数组
            console.log('转换点数组为Cartesian3数组...');
            const positions = this.pointArrayToCartesian3Array(pointArray, height);
            if (!positions || positions.length < 3) {
                console.error('转换后的点数组无效，无法创建裁剪平面');
                return null;
            }
            console.log('成功创建Cartesian3数组，点数:', positions.length);
            
            // 计算多边形中心点
            console.log('计算多边形中心点...');
            const centerCoords = this.calculatePolygonCenter(pointArray);
            if (!centerCoords || centerCoords.length < 2) {
                console.error('计算多边形中心点失败');
                return null;
            }
            console.log('多边形中心点:', centerCoords);
            
            // 创建中心点的Cartesian3坐标
            const ellipsoid = viewer.scene.globe.ellipsoid;
            const center = Cesium.Cartesian3.fromDegrees(centerCoords[0], centerCoords[1], height);
            
            // 获取中心点的地表高度
            const centerCartographic = Cesium.Cartographic.fromDegrees(centerCoords[0], centerCoords[1]);
            let surfaceHeight = 0;
            try {
                // 尝试获取地形高度
                surfaceHeight = viewer.scene.globe.getHeight(centerCartographic) || 0;
                console.log('中心点地表高度:', surfaceHeight);
            } catch (e) {
                console.warn('无法获取地表高度，使用0作为默认值');
            }
            
            // 计算实际裁剪高度（相对于地表）
            // 如果height是负值，表示地下深度；如果是正值，表示地上高度
            const clipHeight = height < 0 ? height : height - surfaceHeight;
            console.log('计算得到的裁剪高度:', clipHeight, '(原始高度:', height, ')');
            
            // 创建平面集合
            const planes: any[] = [];
            
            // 创建水平裁剪平面（朝上）- 用于压平地形
            console.log('创建水平裁剪平面...');
            try {
                // 使用UP单位向量作为法向量，更可靠
                const upNormal = Cesium.Cartesian3.normalize(
                    Cesium.Cartesian3.clone(ellipsoid.geodeticSurfaceNormal(center)),
                    new Cesium.Cartesian3()
                );
                
                // 计算平面距离 - 正确处理水平平面
                const centerWithHeight = Cesium.Cartesian3.fromDegrees(
                    centerCoords[0], centerCoords[1], surfaceHeight + clipHeight
                );
                const distance = -Cesium.Cartesian3.dot(upNormal, centerWithHeight);
                
                planes.push(new Cesium.ClippingPlane(upNormal, distance));
                console.log('水平裁剪平面创建成功，高度:', clipHeight, '距离:', distance);
            } catch (err) {
                console.error('创建水平裁剪平面失败:', err);
                return null;
            }
            
            // 为多边形每条边创建垂直裁剪平面 - 用于创建多边形边界
            // 这样可以确保只有多边形内部区域被裁剪，外部不受影响
            console.log('开始创建垂直裁剪平面...');
            let successCount = 0;
            let totalEdges = positions.length - 1;
            
            if (positions.length >= 3) {
                for (let i = 0; i < positions.length - 1; i++) {
                    try {
                        // 获取边的两个端点
                        const p1 = positions[i];
                        const p2 = positions[i + 1];
                        
                        // 计算从p1到p2的向量
                        const p1p2 = Cesium.Cartesian3.subtract(p2, p1, new Cesium.Cartesian3());
                        
                        // 使用地球椭球体的法向量（指向正上方）
                        const upVector = ellipsoid.geodeticSurfaceNormal(center, new Cesium.Cartesian3());
                        
                        // 计算法向量 - 边向量与上向量的叉积得到水平方向的法向量
                        const normal = Cesium.Cartesian3.cross(p1p2, upVector, new Cesium.Cartesian3());
                        
                        // 规范化法向量
                        if (Cesium.Cartesian3.magnitude(normal) < 0.00001) {
                            // 如果法向量接近零向量，跳过此边
                            console.warn(`边 ${i+1} 法向量接近零，跳过此边的裁剪平面`);
                            continue;
                        }
                        
                        Cesium.Cartesian3.normalize(normal, normal);
                        
                        // 确保法向量朝内 - 使用中心点检查方向
                        const toCenter = Cesium.Cartesian3.subtract(center, p1, new Cesium.Cartesian3());
                        const dot = Cesium.Cartesian3.dot(normal, toCenter);
                        
                        if (dot < 0) {
                            Cesium.Cartesian3.negate(normal, normal);
                        }
                        
                        // 计算平面距离（平面到原点的距离）
                        const distance = -Cesium.Cartesian3.dot(normal, p1);
                        
                        // 创建裁剪平面
                        planes.push(new Cesium.ClippingPlane(normal, distance));
                        successCount++;
                        
                        // 只记录第一个和最后一个平面的详细信息，避免日志过多
                        if (i === 0 || i === positions.length - 2) {
                            console.log(`垂直裁剪平面 ${i+1}/${totalEdges} 创建成功, 法向量:`, 
                                        normal, '距离:', distance);
                        }
                    } catch (err) {
                        console.error(`创建垂直裁剪平面 ${i+1} 失败:`, err);
                    }
                }
            }
            
            console.log(`垂直裁剪平面创建完成: 成功 ${successCount}/${totalEdges}`);
            
            // 如果没有创建任何垂直平面，返回失败
            if (successCount === 0) {
                console.error('未能创建任何有效的垂直裁剪平面');
                return null;
            }
            
            // 优化：合并近似平行的平面以减少平面数量
            console.log('开始优化裁剪平面...');
            console.log('优化前平面数量:', planes.length);
            
            // 创建并返回ClippingPlaneCollection
            try {
                const clippingPlanes = new Cesium.ClippingPlaneCollection({
                    planes: planes,
                    edgeWidth: 1.0,
                    edgeColor: Cesium.Color.WHITE,
                    enabled: true,
                    unionClippingRegions: false, // 确保使用交集模式，只有所有平面内部区域才会显示
                    modelMatrix: Cesium.Matrix4.IDENTITY,
                    // 添加可视化效果，便于调试
                    debugShowBoundingVolume: false
                });
                console.log('成功创建裁剪平面集合，平面总数:', planes.length);
                return clippingPlanes;
            } catch (err) {
                console.error('创建裁剪平面集合失败:', err);
                return null;
            }
        } catch (error) {
            console.error('创建多边形裁剪平面过程中发生错误:', error);
            return null;
        }
    }
    
    /**
     * 移除地形平整区域
     * @param viewer Cesium查看器
     * @param name 编辑名称
     */
    static removeFlattenArea(viewer: any, name: string): boolean {
        if (this.edits.has(name)) {
            const edit = this.edits.get(name);
            
            // 禁用裁剪平面
            if (edit?.clippingPlanes) {
                edit.clippingPlanes.enabled = false;
                
                // 如果是当前应用的裁剪平面，则移除
                if (viewer.scene.globe.clippingPlanes === edit.clippingPlanes) {
                    viewer.scene.globe.clippingPlanes = undefined;
                }
            }
            
            // 移除可视化多边形
            viewer.entities.values.forEach((entity: any) => {
                if (entity.name === `flatten_area_${name}`) {
                    viewer.entities.remove(entity);
                }
            });
            
            // 从集合中移除
            this.edits.delete(name);
            
            // 强制重新渲染
            viewer.scene.requestRender();
            
            return true;
        }
        return false;
    }
    
    /**
     * 添加多边形可视化效果
     * @param viewer Cesium查看器
     * @param name 编辑名称
     * @param pointArray 多边形点数组
     * @param height 压平高度
     * @returns 是否成功添加
     */
    private static addPolygonVisualization(viewer: any, name: string, pointArray: number[], height: number): boolean {
        try {
            console.log('添加可视化多边形...');
            const positions = this.pointArrayToCartesian3Array(pointArray, height + 0.5);
            if (!positions || positions.length < 3) {
                console.log('无法创建可视化多边形：点数组转换失败');
                return false;
            }
            
            const entity = viewer.entities.add({
                name: `flatten_area_${name}`,
                polygon: {
                    hierarchy: new Cesium.PolygonHierarchy(positions),
                    material: new Cesium.ColorMaterialProperty(
                        Cesium.Color.fromCssColorString('#808080').withAlpha(0.1)
                    ),
                    outline: true,
                    outlineColor: Cesium.Color.fromCssColorString('#FFFFFF').withAlpha(0.5),
                    outlineWidth: 2.0,
                    height: height
                }
            });
            
            console.log('可视化多边形添加成功');
            return true;
        } catch (error) {
            console.error('创建可视化多边形失败:', error);
            return false;
        }
    }
}

/**
 * 添加地形平整区域
 * @param viewer Cesium查看器
 * @param name 地形编辑名称或配置对象
 * @param pointArray 多边形点数组
 * @param height 平整高度
 */
export function addFlattenTerrain(viewer: any, name: any, pointArray: number[], height: number | number[]): boolean {
    try {
        // 检查name是否为对象（处理变电站配置对象的情况）
        let editName = typeof name === 'string' ? name : (name.name || '未命名区域');
        
        // 处理高度参数
        let editHeight: number;
        if (Array.isArray(height)) {
            editHeight = height.length > 0 ? height[0] : 10; // 设置默认高度为10
        } else {
            editHeight = height;
        }
        
        console.log('尝试添加地形平整:', editName, '高度:', editHeight, '点数组长度:', pointArray.length);
        
        // 方法1：尝试使用标准Cesium API
        const terrainProvider = viewer.scene.terrainProvider;
        if (terrainProvider && typeof terrainProvider.addTerrainEditsData === 'function') {
            console.log('使用标准addTerrainEditsData方法');
            terrainProvider.addTerrainEditsData(editName, pointArray, editHeight);
            viewer.scene.globe._surface.invalidateAllTiles();
            return true;
        }
        
        // 方法2：使用裁剪平面替代实现
        console.log('标准方法不可用，使用裁剪平面替代');
        if (TerrainModifier.createFlattenArea(viewer, editName, pointArray, editHeight)) {
            // 刷新地形瓦片
            if (viewer.scene.globe._surface && typeof viewer.scene.globe._surface.invalidateAllTiles === 'function') {
                viewer.scene.globe._surface.invalidateAllTiles();
            }
            
            // 强制重新渲染
            viewer.scene.requestRender();
            
            return true;
        }
        
        console.warn('所有地形平整方法都失败，功能可能不可用');
        return false;
    } catch (error) {
        console.error('地形平整操作失败:', error, {
            nameType: typeof name,
            pointArrayLength: pointArray?.length,
            heightValue: height
        });
        return false;
    }
}

/**
 * 移除地形平整区域
 * @param viewer Cesium查看器
 * @param name 地形编辑名称
 */
export function removeFlattenTerrain(viewer: any, name: string): boolean {
    try {
        // 方法1：标准方法
        const terrainProvider = viewer.terrainProvider;
        if (terrainProvider && typeof terrainProvider.removeTerrainEditsData === 'function') {
            terrainProvider.removeTerrainEditsData(name);
            viewer.scene.globe._surface.invalidateAllTiles();
            return true;
        }
        
        // 方法2：使用替代实现
        if (TerrainModifier.removeFlattenArea(viewer, name)) {
            if (viewer.scene.globe._surface && typeof viewer.scene.globe._surface.invalidateAllTiles === 'function') {
                viewer.scene.globe._surface.invalidateAllTiles();
            }
            
            // 强制重新渲染
            viewer.scene.requestRender();
            
            return true;
        }
        
        console.warn('移除地形平整失败，未找到编辑:', name);
        return false;
    } catch (error) {
        console.error('移除地形平整操作失败:', error);
        return false;
    }
}