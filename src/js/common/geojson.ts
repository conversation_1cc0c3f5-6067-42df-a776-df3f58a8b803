import * as Cesium from 'cesium'
// import * as Cesium from 'cesium'

export default class GeojsonService {
    private viewer: any
    private transformFunc: any

    constructor(viewer: Cesium.Viewer) {
        this.viewer = viewer
        Cesium.GeoJsonDataSource.clampToGround = true // 贴地
    }

    loadGeoJson = async (jsonFile: any) => {
        // console.log('res:', jsonFile)
        let promise = Cesium.GeoJsonDataSource.load(jsonFile, {
            // stroke: Cesium.Color.HOTPINK,   
            stroke: Cesium.Color.fromCssColorString("#ff0000"),
            strokeWidth: 30,
            fill: Cesium.Color.fromCssColorString("#b4ebaf8f")
        })
        return new Promise((resolve, reject) => {
            promise.then((dataSource: any) => {
                this.viewer.dataSources.add(dataSource)
                if (jsonFile.name) {
                    dataSource.name = jsonFile.name
                }
                if (jsonFile.show != undefined) {
                    dataSource.show = jsonFile.show
                }
                console.log('dataSource:', dataSource)
                let entities = dataSource.entities.values;
                // 设置entity的样式
                entities.forEach((entity: any) => {
                    // let entityType = entity.properties.type.getValue(Cesium.JulianDate.now())
                    // if (entityType === 'fill') {
                    //     // 填充色块的类型
                    //     let colorStr = `${entity.properties.color.getValue(Cesium.JulianDate.now())}`
                    //     entity.polygon.material = Cesium.Color.fromCssColorString(colorStr)
                    // }
                    // entity.polygon.material = Cesium.Color.fromCssColorString("#b4ebaf")
                    let colorStr = `${entity.properties.color.getValue(Cesium.JulianDate.now())}`
                    if (colorStr) {
                        const colorArray = colorStr.split(",");
                        const colorNum = colorArray.map(Number);
                        if (colorNum.length === 4) {
                            entity.polygon.material = Cesium.Color.fromBytes(colorNum[0], colorNum[1], colorNum[2], colorNum[3]).withAlpha(0.3)
                        }
                    }
                    let zIndexStr = `${entity.properties.layer.getValue(Cesium.JulianDate.now())}`.substring(0, 2)
                    if (zIndexStr) {
                        console.log('zIndexStr:', zIndexStr)
                        entity.polygon.zIndex = Number(zIndexStr)
                    }
                })
                // this.viewer.zoomTo(entities)
                // console.log('entities:', entities)
                resolve(entities)
            })
        })
    }

}