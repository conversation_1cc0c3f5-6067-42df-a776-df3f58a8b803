import * as Cesium from 'cesium';

/**
 * @descripion: 工序展示
 * @param {viewer} viewer 地图视图
 * @param {array<T>} names 创建的entity实例名字数组
 * @param {array<T>} IDS 创建的entity实例ID数组
 * @param {array<T>} model 模型数组
 * @param {array<T>} nodeNames 节点名称数组
 * @param {boolean} isShow 展示选择 false为不显示，true为显示
 * @param {obj} attribute 画圆的属性(画圆速度较慢) color 颜色 radius 半径 position 位置
 * @return {*}}
*/
export class gltfShow {
    constructor(val?: any) {
    }

    selectModelNodesbyNames(viewer: any, names: string[]) {
        const primitives = viewer.scene.primitives;
        const newModel: any[] = [];
        if (!names || names.length < 1) {
            for (let i = 0; i < primitives.length; i++) {
                newModel.push(primitives.get(i));
            }
            return newModel;
        }

        const allPrimitives: any[] = [];
        for (let i = 0; i < primitives.length; i++) {
            allPrimitives.push(primitives.get(i));
        }

        names.forEach((element: string) => {
            const model2 = allPrimitives.filter((c: any) => c._id && c._id._name == element)
            newModel.push(...model2)
        });
        return newModel
    }

    selectModelNodesbyIDS(viewer: any, IDS: string[]) {
        const primitives = viewer.scene.primitives;
        let newModel: any[] = []
        if (!IDS || IDS.length < 1) {
            for (let i = 0; i < primitives.length; i++) {
                newModel.push(primitives.get(i));
            }
            return newModel;
        }

        const allPrimitives: any[] = [];
        for (let i = 0; i < primitives.length; i++) {
            allPrimitives.push(primitives.get(i));
        }

        IDS.forEach((element: string) => {
            const model2 = allPrimitives.filter((c: any) =>
                c._id && c._id._id == element
            )
            newModel.push(...model2)
        });

        return newModel
    }


    isShowModelNodes(model: any, nodeNames: string[], isShow: boolean) {
        if (!model || model.length < 1) {
            return model
        }
        model.forEach((element: any) => {
            if (!element || !element._nodesByName) {
                return
            }
            let node = element._nodesByName
            for (let val in node) {
                if (nodeNames.length > 0) {
                    if (nodeNames.includes(val)) {
                        node[val].show = isShow
                    }
                } else {
                    node[val].show = isShow
                }
            }
        })
        return model
    }

    getModelNodesArray(model: any): string[] {
        if (!model) {
            return []
        }
        let nodesArray: string[] = []
        let node = model._nodesByName
        for (let nodeName in node) {
            nodesArray.push(nodeName)
        }
        return nodesArray
    }

    isShowModel(model: any[], isShow: boolean) {
        if (!model || model.length < 1)
            return model

        model.forEach(element => {
            console.log("element", element)
            let node = element._nodesByName
            console.log("node", node)
            if (node)
                for (let val in node) {
                    node[val].show = isShow
                }
        })
        return model
    }

    addcmzlTest(viewer: any, attribute: { color: number[], radius: number, position: number[] }) {
        var czml = [{
            "id": "document",
            "name": "box",
            "version": "1.0"
        }, {
            "id": "shape2",
            "name": "Red box with black outline",
            "position": {
                "cartographicDegrees": attribute.position
            },
            "ellipse": {
                "height": attribute.position[2],
                "HeightReference": 0,
                "semiMajorAxis": attribute.radius,
                "semiMinorAxis": attribute.radius,
                "material": {
                    "solidColor": {
                        "color": {
                            "rgba": attribute.color
                        }
                    }
                },
                "outline": true,
                "outlineColor": {
                    "rgba": [0, 0, 0, 1]
                }
            }
        }];
        var dataSourcePromise = Cesium.CzmlDataSource.load(czml);
        viewer.dataSources.add(dataSourcePromise);
        // viewer.zoomTo(dataSourcePromise);
        return dataSourcePromise
    }

}