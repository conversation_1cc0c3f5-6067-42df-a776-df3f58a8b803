import {ElMessage} from 'element-plus'
import {useLineProjStore} from '@/store/lineProj'
import {MODEL_STATIC_URL, LINE_POINTS_NUM} from '@/config/global';
import {createEntity_glb, getPositionHeight} from "./viewer";
import * as Cesium from 'cesium'
// import * as Cesium from 'cesium';
import {catenaryLineArr} from "./catenary"
import {addLabelNum, addLabelZndNum, deleteZndLabel} from "./divLabel";
import {gltfShow} from "./gltfShow";

/**
 * 工序展示示例
 * @param {*} status 工序 0未开始,1塔基青赔，2基础施工，3铁塔组立，4架线施工
 * @param {*} location 位置 工序1,2以及模型重加载需要
 * @param {*} entityId 创建的entityId
 * @param {*} viewer 视图
 */
export function showModelByStatus(status: number, location: number[], entityId: string | number[], viewer: Cesium.Viewer) {
    let gltfShow1 = new gltfShow(viewer)
    let position = location
        let model = gltfShow1.selectModelNodesbyIDS(viewer, [entityId as string])
    if (!model) return;
    let taji = ["Box001", "Box002", "Box003", "Box004"]
    let partofTower = ["Box001", "Box002", "Box003", "Box004", "对象001"]
    switch (status) {
        case 0: {
            let attribute = {color: [255, 255, 255, 255], radius: 10, position: position}
            gltfShow1.addcmzlTest(viewer, attribute)
            gltfShow1.isShowModelNodes(model, [], false) as unknown as unknown
            // model._id.
            break;
        }
        case 1: {
            let attribute = {color: [0, 255, 0, 255], radius: 10, position: position}
            gltfShow1.addcmzlTest(viewer, attribute)
            gltfShow1.isShowModelNodes(model, [], false)
            break;
        }
        case 2: {
            model.forEach((model1: any) => {
                let allnodesArray = gltfShow1.getModelNodesArray(model1) || []
                gltfShow1.isShowModelNodes(model, allnodesArray, false)
                gltfShow1.isShowModelNodes(model, taji, true)
            })

            break;

        }
        case 3: {
            model.forEach((model1: any) => {
                let allnodesArray = gltfShow1.getModelNodesArray(model1) || []
                gltfShow1.isShowModelNodes(model, allnodesArray, false)
                gltfShow1.isShowModelNodes(model, partofTower, true)
            })

            break;

        }
        case 4: {
            model.forEach((m: any) => {
              let nodes = gltfShow1.getModelNodesArray(m) || []
              gltfShow1.isShowModelNodes([m], nodes, true)
            })
            break
        }
        case 5: {
           model.forEach((m: any) => {
              let nodes = gltfShow1.getModelNodesArray(m) || []
              gltfShow1.isShowModelNodes([m], nodes, true)
            })
            break
        }
    }
}

/**
 *
 * @param {object} viewer cesium场景
 * @param {array} towerEntities 存储塔模型实体的全局变量数组
 * @param {array} towerEntitiesPos 存储每个塔模型位置信息的数组 用于目录树跳转视角
 * @param {array} towersLable 记录当前加载模型的塔编号，便于其他函数调用添加塔号label
 * @returns
 */
export function addTowers(viewer: Cesium.Viewer, towerEntities: Cesium.Entity[], towerEntitiesPos: any[] = [], towersLable: any[] = []) {
    const store_lineProj = useLineProjStore();
    if (store_lineProj.lineDetail.length < 1) {
        ElMessage({
            message: '没有线路数据，请更新数据！',
            grouping: true,
            type: 'warning',
        })
        return;
    }
    store_lineProj.lineDetail.forEach(async (line: any) => {
        for (let ind = 0; ind < line.towers.length; ind++) {
            const tower = line.towers[ind]
            // 判断该杆塔模型是否已经加载
            let res = towerEntities.filter((t: any) => {
                return (t.id === tower.towerId)
            })
            if (res.length > 0) {
                continue;
            }
            const towerModel = store_lineProj.towers.find((t: any) => {
                return (t.towerModel === tower.towerModel);
            });
            if (towerModel == undefined) {
                console.error('无法找到杆塔模型数据', store_lineProj.towers, tower); // 没有杆塔模型的具体数据 无法加载杆塔模型到场景
                continue;
            }
            let modelInfo = {
                id: tower.towerId,
                name: tower.towerModel,
                position: [tower.longitude, tower.latitude, tower.height],
                // angle: 90*Math.PI/180,
                angle: tower.angle * Math.PI / 180,
                scale: 1,
                url: MODEL_STATIC_URL + towerModel.url,
            }
            let entity = await createEntity_glb(modelInfo);
            viewer.entities.add(entity as any);
            towerEntities.push(entity as any);
            towerEntitiesPos.push(modelInfo.position)
            console.log("hight:" + tower.height)

            // 将杆塔id作为label
            towersLable.push({
                position: [tower.longitude, tower.latitude, tower.height + towerModel.twoerHeight + 3],
                id: tower.towerId,
                procedureStatusName: tower.procedureStatusName
            });
        }
    });
}

/**
 * 添加施工状态下的杆塔，根据当前工序加载模型
 *
 * @param {object} viewer cesium场景
 * @param {array} towerEntities 存储塔模型实体的全局变量数组
 * @param {array} towerEntitiesPos 存储每个塔模型位置信息的数组 用于目录树跳转视角
 * @param {array} towersLable 记录当前加载模型的塔编号，便于其他函数调用添加塔号label
 * @param {boolean} flag 是否施工回溯标识，便于其他函数调用添加塔号label
 * @param {map} planRecordMap 施工回溯返回信息
 * @returns
 */
export function addSgTowers(viewer: Cesium.Viewer, towerEntities: Cesium.Entity[], towerEntitiesPos: any[] = [], towersLable: any[] = [], flag = false, planRecordMap: any = {}) {
    const store_lineProj = useLineProjStore();
    if (store_lineProj.lineDetail.length < 1) {
        ElMessage({
            message: '没有线路数据，请更新数据！',
            grouping: true,
            type: 'warning',
        })
        return;
    }
    store_lineProj.lineDetail.forEach(async (line: any) => {
        for (let ind = 0; ind < line.towers.length; ind++) {
            const tower = line.towers[ind]
            // 判断该杆塔模型是否已经加载
            let res = towerEntities.filter((t: any) => {
                return (t.id === tower.towerId)
            })
            if (res.length > 0) {
                continue;
            }
            const towerModel = store_lineProj.towers.find((t: any) => {
                return (t.towerModel === tower.towerModel);
            });
            if (towerModel == undefined) {
                console.error('无法找到杆塔模型数据', store_lineProj.towers, tower); // 没有杆塔模型的具体数据 无法加载杆塔模型到场景
                continue;
            }
            let modelInfo = {
                id: tower.towerId,
                name: tower.towerModel,
                position: [tower.longitude, tower.latitude, tower.height],
                // angle: 90*Math.PI/180,
                angle: tower.angle * Math.PI / 180,
                scale: 1,
                url: MODEL_STATIC_URL + towerModel.url,
            }
            let entity = await createEntity_glb(modelInfo);
            let procedureStatus = tower.procedureStatus
            if (flag) {
                if (planRecordMap.hasOwnProperty(tower.towerId)) {
                    procedureStatus = planRecordMap[tower.towerId].procedureStatus
                } else {
                    procedureStatus = 0
                }
            }
            if (procedureStatus === 0 || procedureStatus === 1) {
                showModelByStatus(procedureStatus, [tower.longitude, tower.latitude, tower.height], tower.towerId, viewer)
            } else {
                viewer.entities.add(entity as any);
                // setTimeout(() => {
                //     showModelByStatus(tower.procedureStatus, [tower.longitude, tower.latitude, tower.height], [tower.procedureStatus],viewer)
                //         }, 1000)
            }
            // if(ind === 0) {
            //     console.log(tower)
            //     //模型的加入是异步操作，等待1秒后执行
            //     setTimeout(() => {
            //         //工序处理
            //         showModelByStatus(0, [tower.longitude, tower.latitude, tower.height], [0],viewer)
            //     }, 1000)
            // }
            towerEntities.push(entity as any);
            towerEntitiesPos.push(modelInfo.position)
            // 将杆塔id作为label
            towersLable.push({
                position: [tower.longitude, tower.latitude, tower.height + towerModel.twoerHeight + 3],
                id: tower.towerId,
                procedureStatusName: tower.procedureStatusName
            });
        }
    });
}

export function addTowerLabel(viewer: any, towerLable: any[], labelEntities: any[] = []) {
    if (towerLable.length === 0) {
        console.log('场景中没有杆塔模型')
        // ElMessage({
        //     message: '场景中没有杆塔模型',
        //     grouping: true,
        //     type: 'warning',
        // })
        return;
    }
    // Create label
    for (let ind = 0; ind < towerLable.length; ind++) {
        let info = towerLable[ind];
        // 判断label是否已经加载
        let res = labelEntities.filter((l: any) => {
            return (l.id === info.id)
        })
        if (res.length > 0) {
            continue;
        }
        let label = viewer.entities.add({
            position: Cesium.Cartesian3.fromDegrees(info.position[0], info.position[1], info.position[2]),
            id: 'label_' + info.id,
            label: {
                text: info.id,
                font: "24px Helvetica",
                fillColor: Cesium.Color.YELLOW,
                // fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -5)
            }
        });
        labelEntities.push(label);
    }
}

// 显示施工状态下的label（在杆塔号旁边添加当前工序名称）
export function addSgLabels(viewer: any, towerLable: any[], labelEntities: any[] = []) {
    if (towerLable.length === 0) {
        console.log('场景中没有杆塔模型')
        return;
    }
    // Create label
    for (let ind = 0; ind < towerLable.length; ind++) {
        let info = towerLable[ind];
        // 判断label是否已经加载
        let res = labelEntities.filter((l: any) => {
            return (l.id === info.id)
        })
        if (res.length > 0) {
            continue;
        }
        let label = viewer.entities.add({
            position: Cesium.Cartesian3.fromDegrees(info.position[0], info.position[1], info.position[2]),
            id: 'label_' + info.id,
            label: {
                text: info.id + '(' + info.procedureStatusName + ')',
                font: "24px Helvetica",
                fillColor: Cesium.Color.YELLOW,
                // fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -5)
            }
        });
        labelEntities.push(label);
    }
}

/**
 * label封装
 * @param {object} viewer cesium场景
 * @param {array} towersLable 记录当前加载模型的塔编号，便于其他函数调用添加塔号label
 * @param {array} labelEntities 记录label数组
 * @param {boolean} showprocedureStatusName 是否展示工序名称
 * @param {boolean} flag 是否施工回溯标识，便于其他函数调用添加塔号label
 * @param {map} planRecordMap 施工回溯返回信息
 */
export function adddivHTML(viewer: any, towerLable: any[], labelEntities: any[] = [], showprocedureStatusName: boolean, flag = false, planRecordMap: any = {}) {
    if (towerLable.length === 0) {
        console.log('场景中没有杆塔模型')
        return;
    }
    // Create label
    for (let ind = 0; ind < towerLable.length; ind++) {
        let info = towerLable[ind];
        // 判断label是否已经加载
        let res = labelEntities.filter(l => {
            return (l.id === info.id)
        })
        if (res.length > 0) {
            continue;
        }
        const divHtmlStyle = '<div style="width:auto;height:auto;background:transparent;color: yellow;font-size: 22px">'
        let divHtml = divHtmlStyle
            + info.id + '<a  href="'+import.meta.env.VITE_CONTEXT+'Threejs3D" target="_blank"><img src="'+import.meta.env.VITE_CONTEXT+'static/img/Threejs3D/vr.png" style="width: 32px;height: 23px;"></a>'
            + `</div>`;
        // 是否展示工序名
        if (showprocedureStatusName) {
            let procedureStatusName = info.procedureStatusName
            if (flag) {
                if (planRecordMap.hasOwnProperty(info.id)) {
                    procedureStatusName = planRecordMap[info.id].procedureStatusName
                } else {
                    procedureStatusName = '未开始'
                }
            }
            divHtml = divHtmlStyle
                + info.id + `(` + procedureStatusName
                + `)`
                + '<a  href="'+import.meta.env.VITE_CONTEXT+'Threejs3D" target="_blank"><img src="'+import.meta.env.VITE_CONTEXT+'static/img/Threejs3D/vr.png" style="width: 32px;height: 23px;"></a>'
                + `</div>`
        }
        let val = {
            id: info.id,
            viewer: viewer,
            position: info.position as [number, number, number],
            // title: '广告牌'
            divHTML: divHtml
        }
        
        addLabelNum(viewer, val)
        labelEntities.push(val);
    }
}

/**
 *
 * @param {array} origin 原点坐标三维数组[lon, lat, height]
 * @param {array} mountXYZ 挂载点坐标三维数组 [x, y, z] 单位米
 * @param {var} ang 模型旋转角度(角度值)
 * @returns 计算得到基于原点的挂载点坐标[lon, lat, height]
 */
export function xyz2Radians(origin: any, XYZ: any, ang = 0) {
    // 将经纬度转成弧度值单位
    const origin_lon = origin[0] * Math.PI / 180;
    const origin_lat = origin[1] * Math.PI / 180;
    const origin_height = origin[2];

    let angRaid = ang * Math.PI / 180;
    const arc = 6371.393 * 1000; // 地球半径
    const x = XYZ[0] * Math.cos(angRaid) - XYZ[1] * Math.sin(angRaid);
    const y = XYZ[0] * Math.sin(angRaid) + XYZ[1] * Math.cos(angRaid);

    let lon = origin_lon + x / (arc * Math.cos(origin[1]) * 2 * Math.PI) * 2 * Math.PI;
    let lat = origin_lat + y / (arc * 2 * Math.PI) * 2 * Math.PI;
    let height = origin_height + XYZ[2];

    // 将计算结果转为经纬度单位
    lon = lon * 180 / Math.PI;
    lat = lat * 180 / Math.PI;
    return [lon, lat, height];
}

/**
 *
 * @param {array} point1 基准点坐标[lon, lat, height]
 * @param {array} point2 目标点坐标[lon, lat, height]
 * @returns 基准点到目标点的连线与正北方向的方位角 弧度
 */
export function calAzimuth(point1: any, point2: any) {
    const x1 = point1[0] * Math.PI / 180;
    const y1 = point1[1] * Math.PI / 180;
    const z1 = point1[2];
    const x2 = point2[0] * Math.PI / 180;
    const y2 = point2[1] * Math.PI / 180;
    const z2 = point2[2];
    let geodesic = new Cesium.EllipsoidGeodesic(Cesium.Cartographic.fromDegrees(x1, y1, z1), Cesium.Cartographic.fromDegrees(x2, y2, z2));
    return geodesic.endHeading;
}


export function getMountPosition(towerInfo: any, mountArray: any, viewer: any) {
    // - 原点坐标
    let origin = null
    let angRaid = null
    if(towerInfo.version == 'V2'){
        // V2版本兼容
        origin = Cesium.Cartographic.fromDegrees(towerInfo.longitude, towerInfo.latitude, towerInfo.height);
        angRaid = -1 * towerInfo.angle * Math.PI / 180; // 杆塔旋转坐标于cesium中方向相反 所以取负
    }else{
        origin = Cesium.Cartographic.fromDegrees(towerInfo.longitude, towerInfo.latitude, towerInfo.height);
        angRaid = -1 * towerInfo.angle * Math.PI / 180; // 杆塔旋转坐标于cesium中方向相反 所以取负
    }
    const originCartesian = viewer.scene.globe.ellipsoid.cartographicToCartesian(origin);


    let res = [];
    if (mountArray == undefined) {
        console.error('mountArray错误', towerInfo);
        return null;
    }
    for (let i = 0; i < mountArray.length; i++) {
        let _mountPosition = [];
        for(let j=0; j<mountArray[i].length; j++){
        // mountArray[i].forEach(targetPoint => {
            let targetPoint = mountArray[i][j]
            if (targetPoint.length < 3) {
                return null
                // targetPoint = [0, 0, 0]
            }
            // - 相对坐标
            let x, y, z
            if (towerInfo.version == 'V2') {
                x = targetPoint[0] * Math.cos(angRaid) + targetPoint[1] * Math.sin(angRaid);
                y = targetPoint[0] * Math.sin(angRaid) - targetPoint[1] * Math.cos(angRaid);
                z = targetPoint[2];
            } else {
                // 旧版本的模型Y轴坐标正负号取反了
                x = targetPoint[0] * Math.cos(angRaid) - targetPoint[1] * Math.sin(angRaid);
                y = targetPoint[0] * Math.sin(angRaid) + targetPoint[1] * Math.cos(angRaid);
                z = targetPoint[2];
            }
            const relativePosition = new Cesium.Cartesian3(x, y, z);
            try {
                // 计算相对位置的绝对位置坐标
                const target = Cesium.Matrix4.multiplyByPoint(
                    Cesium.Transforms.eastNorthUpToFixedFrame(originCartesian),
                    relativePosition,
                    new Cesium.Cartesian3()
                );
                // 将绝对位置坐标转换为经纬度高度坐标
                const absoluteCartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(target);
                // 转换经纬度高度坐标的度数表示
                const absoluteDegrees = [absoluteCartographic.longitude * 180 / Math.PI, absoluteCartographic.latitude * 180 / Math.PI, absoluteCartographic.height];
                _mountPosition.push(absoluteDegrees);
            } catch (ex) {
                console.log('getMountPosition error', relativePosition, ex)
            }
        }
        res.push(_mountPosition);
    }
    return res;
}

export function getInsulatorPos(towersList: any[], viewer: any) {
    let insulatorList: any[][] = [[], [], []];
    for (let ind = 0; ind < towersList.length - 1; ind++) {
        // 1 从回路表中拿到当前杆塔信息和下一个杆塔信息（包括位置坐标和配串信息）
        const _nowTower = towersList[ind];
        const _nextTower = towersList[ind + 1];
        if (!_nowTower || !_nextTower) {
            continue;
        }
        const _nowTowertLineLocationMount = getLineLocationMount(_nowTower.towerModel, _nowTower.lineLocation)
        const _nowTower_mount = getMountPosition(_nowTower, _nowTowertLineLocationMount, viewer);
        if (!_nowTower_mount) {
            continue;
        }
        const _nextTowerLineLocationMount = getLineLocationMount(_nextTower.towerModel, _nextTower.lineLocation)
        if (_nextTowerLineLocationMount) {
            const _nextTower_mount = getMountPosition(_nextTower, _nextTowerLineLocationMount, viewer);
            if (!_nextTower_mount) {
                continue;
            }
        for (let i = 0; i < 3; i++) {
            const startPoint = _nowTower_mount[i].length == 3 ? _nowTower_mount[i][2] : _nowTower_mount[i][0];
            const endPoint = _nextTower_mount[i][0];
            if (startPoint == undefined || endPoint == undefined) {
                console.error(_nowTower, _nextTower, startPoint, endPoint, '杆塔没有挂点信息');
                continue;
            }
            // 计算两个点之间的夹角，返回值单位为弧度
            // const angleRad = Cesium.Cartesian3.angleBetween(startPoint, endPoint);
            const angleRad = calAzimuth(startPoint, endPoint) + Math.PI / 2; //弧度
            if (_nowTower_mount[i].length == 3 || _nowTower.isEnd == 1) {
                let _modelName_str = _nowTower_mount[i].length == 3 ? _nowTower.tensionInsulator : _nowTower.overhangInsulator;
                // let _modelName = _modelName_str.split("、")[]
                // 避免重复加载悬垂塔的绝缘子
                insulatorList[i].push({
                    towerID: _nowTower.towerId,
                    modelName: _modelName_str,
                    position: startPoint,
                    angleRad: angleRad
                })
            }
            insulatorList[i].push({
                towerID: _nextTower.towerId,
                modelName: _nextTower_mount[i].length == 3 ? _nextTower.tensionInsulator : _nextTower.overhangInsulator,
                position: endPoint,
                angleRad: angleRad + Math.PI,
            })
            // 下一个杆塔是耐张塔且不是终点，则需要额外添加悬垂串
            if (_nextTower_mount[i].length == 3 && _nextTower.isEnd == 0) {
                insulatorList[i].push({
                    towerID: _nextTower.towerId,
                    modelName: _nextTower.overhangInsulator,
                    position: _nextTower_mount[i][1],
                    angleRad: _nextTower.angle * Math.PI / 180
                })
                if (_nextTower.overhangInsulator === '/') {
                console.log('/', _nextTower, _nextTower_mount[i])
            }
        }
    }
    }
    }
    return insulatorList;
}

export function getLineLocationMount(towerName: string, lineLocation: any) {
    const store_lineProj = useLineProjStore();
    const towerInfo = store_lineProj.towers.filter((t: any) => {
        return t.towerModel === towerName
    })[0];
    if (towerInfo.towerStyle === "悬垂塔") {
        // 根据同杆线路位置 返回对应的相对坐标
        switch (lineLocation) {
            case 1:
                return [[towerInfo.xuanchui1], [towerInfo.xuanchui3], [towerInfo.xuanchui5]];
            case 2:
                return [[towerInfo.xuanchui2], [towerInfo.xuanchui4], [towerInfo.xuanchui6]];
            case 3:
                return [[towerInfo.xuanchui7], [towerInfo.xuanchui9], [towerInfo.xuanchui11]];
            case 4:
                return [[towerInfo.xuanchui8], [towerInfo.xuanchui10], [towerInfo.xuanchui12]];
            default:
                console.log('getLineLocationMount error');
                return;
        }
    }
    if (towerInfo.towerStyle === "耐张塔") {
        // 根据同杆线路位置 返回对应的相对坐标
        switch (lineLocation) {
            case 1:
                return [[towerInfo.qiandao1, towerInfo.xuanchui1, towerInfo.houdao1], [towerInfo.qiandao3, towerInfo.xuanchui3, towerInfo.houdao3], [towerInfo.qiandao5, towerInfo.xuanchui5, towerInfo.houdao5]];
            case 2:
                return [[towerInfo.qiandao2, towerInfo.xuanchui2, towerInfo.houdao2], [towerInfo.qiandao4, towerInfo.xuanchui4, towerInfo.houdao4], [towerInfo.qiandao6, towerInfo.xuanchui6, towerInfo.houdao6]];
            case 3:
                return [[towerInfo.qiandao7, towerInfo.xuanchui7, towerInfo.houdao7], [towerInfo.qiandao9, towerInfo.xuanchui9, towerInfo.houdao9], [towerInfo.qiandao11, towerInfo.xuanchui11, towerInfo.houdao11]];
            case 4:
                return [[towerInfo.qiandao8, towerInfo.xuanchui8, towerInfo.houdao8], [towerInfo.qiandao10, towerInfo.xuanchui10, towerInfo.houdao10], [towerInfo.qiandao12, towerInfo.xuanchui12, towerInfo.houdao12]];
            default:
                console.log('getLineLocationMount error');
                return;
        }
    }
}

export function addInsulators(viewer: Cesium.Viewer, insulatorsList: any[], insulatorEntities: any[] = []) {
    const store_lineProj = useLineProjStore();
    insulatorsList.forEach(info => {
        if (info.modelName === '/') {
            console.log('addInsulators error:', info);
            return; // 跳过当前项
        }

        const hpr = new Cesium.HeadingPitchRoll(info.angleRad, 0.0, 0.0); // 旋转角 俯仰角 翻滚角
        const entityPosition = Cesium.Cartesian3.fromDegrees(info.position[0], info.position[1], info.position[2]);
        const orientation = Cesium.Transforms.headingPitchRollQuaternion(entityPosition, hpr);
        const model = store_lineProj.insulators.filter((i: any) => {
            return i.modelName === info.modelName
        })[0];
        if (model == undefined) {
            console.error('无法找到绝缘子模型', info)
            return;
        }
        if (info.towerID === 'B10') {
            console.log(`BBBBBBBB `, info.position, 'insulatorEntityID', entityPosition, info.angleRad)
        }
        const entityInfo = {
            // name: info.modelName,
            position: entityPosition,
            orientation: orientation,
            model: {
                uri: MODEL_STATIC_URL + model.url,
                color: Cesium.Color.WHITE
            }
        }
        let entity = new Cesium.Entity(entityInfo);
        if (entity.model) {
            entity.model.color = Cesium.Color.fromCssColorString('#ffffffff') as any;//设置模型颜色与透明度
            // entity.model.colorBlendMode = Cesium.ColorBlendMode.REPLACE; //设置颜色替换材质。
            entity.model.colorBlendMode = Cesium.ColorBlendMode.MIX as any; //材质与设置颜色混合得到的颜色
        }
        // entity.model.colorBlendMode = Cesium.ColorBlendMode.HIGHLIGHT;  //材质与设置颜色相乘得到的颜色
        entity.show = false;
        viewer.entities.add(entity);
        insulatorEntities.push(entity);
    });
    return insulatorEntities;
}

/**
 *
 * @param {obj} viewer 当前的cesium场景
 * @param {array} insulatorList 具体回路中其中一相的绝缘子列表，包含绝缘子的名称、位置和转角
 * @param {array} insulatorModels 绝缘子列表中涉及到的模型信息 模型名称、挂点表、分裂数SplitNum和连接数ConnectNum
 * 本函数返回计算得到的每个绝缘子挂点绝对坐标，即该回路中某一相的电缆起点和终点位置
 */
export function getLine(viewer: Cesium.Viewer, insulatorList: any[], insulatorModels: any[]) {
    let insulatorMountPos: any[] = [];
    insulatorList.forEach((_insulator: any) => {
        const _modelInfo = insulatorModels.filter((item: any, index: number) => {
            return item.modelName === _insulator.modelName
        })[0];
        let _mount = null;
        if (_modelInfo !== undefined && _modelInfo.connectNum) {
            // 根据分裂数SplitNum和连接点ConnectNum信息 整理挂点位置信息 其中连接点表示绝缘子进线出线点位是否分开 分裂数表示绝缘子连接的电缆数量
            if (_modelInfo.connectNum == 1) {
                switch (_modelInfo.splitNum) {
                    case 1:
                        if (_modelInfo.point0.length != 3) {
                            console.error('getLine error1:', _modelInfo);
                            return;
                        }
                        _mount = [[_modelInfo.point0]];
                        break;
                    case 2:
                        if (_modelInfo.point0.length != 3 && _modelInfo.point1.length != 3) {
                            console.error('getLine error1:', _modelInfo);
                            return;
                        }
                        _mount = [[_modelInfo.point0], [_modelInfo.point1]];
                        break;
                    case 4:
                        if (_modelInfo.point0.length != 3 && _modelInfo.point1.length != 3 && _modelInfo.point2.length != 3 && _modelInfo.point3.length != 3) {
                            console.error('getLine error1:', _modelInfo);
                            return;
                        }
                        _mount = [[_modelInfo.point0], [_modelInfo.point1], [_modelInfo.point2], [_modelInfo.point3]]
                        break;
                    default:
                        console.error('getLine error1:', _modelInfo);
                        return;
                        break;
                }
            } else if (_modelInfo.connectNum == 2) {
                switch (_modelInfo.splitNum) {
                    case 1:
                        if (_modelInfo.point0.length != 3 && _modelInfo.point1.length != 3) {
                            console.error('getLine error2:', _modelInfo);
                            return;
                        }
                        _mount = [[_modelInfo.point0, _modelInfo.point1]];
                        break;
                    case 2:
                        if (_modelInfo.point0.length != 3 && _modelInfo.point1.length != 3 && _modelInfo.point2.length != 3 && _modelInfo.point3.length != 3) {
                            console.error('getLine error2:', _modelInfo);
                            return;
                        }
                        _mount = [[_modelInfo.point0, _modelInfo.point1], [_modelInfo.point2, _modelInfo.point3]];
                        break;
                    case 4:
                        if (_modelInfo.point0.length != 3 && _modelInfo.point1.length != 3 && _modelInfo.point2.length != 3 && _modelInfo.point3.length != 3 &&
                            _modelInfo.point4.length != 3 && _modelInfo.point5.length != 3 && _modelInfo.point6.length != 3 && _modelInfo.point7.length != 3) {
                            console.error('getLine error2:', _modelInfo);
                            return;
                        }
                        _mount = [[_modelInfo.point0, _modelInfo.point1], [_modelInfo.point2, _modelInfo.point3], [_modelInfo.point4, _modelInfo.point5], [_modelInfo.point6, _modelInfo.point7]]
                        break;
                    default:
                        console.error('getLine error2:', _modelInfo);
                        return;
                        break;
                }
            } else {
                console.error('绝缘子串连接数connectNum错误', _modelInfo);
                return;
            }
            const originModel = {
                longitude: _insulator.position[0],
                latitude: _insulator.position[1],
                height: _insulator.position[2],
                angle: _insulator.angleRad * 180 / Math.PI + 90,
            }
            // console.log('getmountposition:', _modelInfo, _mount)
            let mountPos = getMountPosition(originModel, _mount, viewer);
            insulatorMountPos.push({mountPos: mountPos, towerID: _insulator.towerID});
        }
    });
    return insulatorMountPos;
}

export function addLine(lineCollection: any, lineInfo: any, color = '#000000', width = 0.5) {
    let polylines = [];
    let sameTowerFlag = 0;
    for (let ind = 0; ind < lineInfo.length - 1; ind++) {
        let startPoints = lineInfo[ind].mountPos;
        let startTower = lineInfo[ind].towerID;
        let endPoints = lineInfo[ind + 1].mountPos;
        let endTower = lineInfo[ind + 1].towerID;
        // 根据前后绝缘子是否在同一个杆塔中 确定电缆连接点的位置
        if (startTower == endTower) {
            sameTowerFlag += 1;
        } else {
            sameTowerFlag = 0;
        }
        for (let k = 0; k < startPoints.length; k++) {
            let point1 = null;
            let point2 = null;
            if (sameTowerFlag === 0) {
                point1 = startPoints[k][0];
                point2 = endPoints[k][0];
            } else if (sameTowerFlag === 1) {
                let flag = 3;
                point1 = startPoints[k][startPoints[k].length - 1];
                if ((k ^ flag) + 1 > endPoints.length) {
                    console.error('绝缘子挂点信息或分裂数有误', startPoints, endPoints, k);
                    continue;
                }
                if (k + 1 > endPoints.length) {
                    console.error('绝缘子挂点信息或分裂数有误', startPoints, endPoints, k);
                    continue;
                }
                point2 = endPoints[k ^ flag][endPoints[k].length - 1];
            } else {
                point1 = startPoints[k][startPoints[k].length - 1];
                point2 = endPoints[k][endPoints[k].length - 1];
            }
            // let point1 = (startTower == endTower) ? startPoints[k][startPoints[k].length - 1] : startPoints[k][0];
            // let point2 = (startTower == endTower) ? endPoints[k][endPoints[k].length - 1] : endPoints[k][0];
            if (point1 == undefined || point2 == undefined) {
                console.error('绝缘子挂点信息或分裂数有误', point1, point2, lineInfo[ind]);
                continue;
            }
            let sagHeight = (point1[2] < point2[2] ? point1[2] : point2[2]) - 5; // 设置弧垂最低点的高度
            let polyline = overheadLine(lineCollection, point1, point2, sagHeight, LINE_POINTS_NUM, color, width); // 绘制悬链线

            // 绘制直线，测试挂点信息时使用
            // let polyline = lineCollection.add({
            //     show: true,
            //     positions: [new Cesium.Cartesian3.fromDegrees(...point1),new Cesium.Cartesian3.fromDegrees(...point2)],
            //     width: width,
            //     material: new Cesium.Material.fromType('Color', {
            //         color: Cesium.Color.fromCssColorString(color)
            //     })
            // })
            polylines.push(polyline);
        }
    }
}

/**
 * 计算两点之间的水平距离和方位角
 * @param {array} origin [lon, lat, height]
 * @param {array} target [lon, lat, height]
 * @returns 水平距离和方位角
 */
export function distance_ang(origin: number[], target: number[]) {
    let geodesic = new Cesium.EllipsoidGeodesic(Cesium.Cartographic.fromDegrees(origin[0], origin[1], origin[2]), Cesium.Cartographic.fromDegrees(target[0], target[1], target[2]));
    return [geodesic.surfaceDistance, geodesic.endHeading];
}

export function overheadLine(entities: Cesium.EntityCollection, origin: number[], target: number[], sagHeight: number, pointNum = 10, color = '#0000ff', width = 0.5): Cesium.Entity {
    let len_ang = distance_ang(origin, target); // 计算两点距离和方向角

    let StartPoint = {x: 0, y: origin[2]};
    let EndPoint = {x: len_ang[0], y: target[2]};
    let res = catenaryLineArr(StartPoint, EndPoint, sagHeight, pointNum);
    // console.log('res:', res); // 计算得到x y数组

    // let res = catenaryLine(StartPoint, EndPoint, sagHeight, pointNum);
    // console.log("generateCatenaryPoints总耗时：", new Date().getTime() - start_time);
    // console.log('generateCatenaryPoints res:', res); // 计算得到x y数组

    let pointPos: number[][] = [];
    res.forEach(el => {
        let pos = getMountPoint3(origin[0] * Math.PI / 180, origin[1] * Math.PI / 180, len_ang[1], el[0])
        pointPos.push([pos.lon, pos.lat, el[1]]);
    });
    // console.log('pointPos', pointPos);

    // let line = addline(polylines, { positions: pointPos, color:color, width:width });
    const _pos: Cesium.Cartesian3[] = [];
    pointPos.forEach(p => {
        _pos.push(Cesium.Cartesian3.fromRadians(p[0], p[1]));
    });
    let line = entities.add({
        show: true,
        polyline: {
            positions: _pos,
            width: width,
            // material: Cesium.Material.fromType(Cesium.Material.PolylineOutlineType, {
            //     color: Cesium.Color.fromCssColorString(color),
            //     outlineColor: Cesium.Color.GHOSTWHITE ,
            //     outlineWidth : 1
            //   }),
            material: Cesium.Material.fromType('Color', {
                 color: Cesium.Color.fromCssColorString(color)
             }) as any
        }
    });

    // let line = new Cesium.Geometry({
    //     attributes: {
    //         position: new Cesium.GeometryAttribute({
    //             componentDatatype: Cesium.ComponentDatatype.DOUBLE,
    //             componentsPerAttribute: 3,
    //             values: _pos
    //         })
    //     }
    // })
    return line;

    //////////////////////////////////////////////////////////////////////////
    // start_time = new Date().getTime();
    // res = catenaryLineArr2(StartPoint, EndPoint, sagHeight, pointNum);
    // console.log("catenaryLineArr2总耗时：", new Date().getTime() - start_time);
    // console.log('res:', res); // 计算得到x y数组

    // pointPos = [];
    // res.forEach(el => {
    //     let pos = getMountPoint3(origin[0], origin[1], len_ang[1], el[0])
    //     pointPos.push([pos.lon, pos.lat, el[1]]);
    // });
    // let line2 = addline(polylines, { positions: pointPos, color: color, width: width });
    // return line2;
}

/**
 *
 * @param {*} lon 经度
 * @param {*} lat 纬度
 * @param {*} brng 方位角 0~360度
 * @param {*} dist 距离(米)
 *
 */
function getMountPoint3(lon: number, lat: number, brng: number, dist: number) {
    var a = 6378137;
    var b = 6356752.3142;
    var f = 1 / 298.257223563;

    var lon1 = lon * 180 / Math.PI;
    var lat1 = lat * 180 / Math.PI;
    var s = dist;
    var alpha1 = brng //  * (Math.PI / 180)

    var sinAlpha1 = Math.sin(alpha1);
    var cosAlpha1 = Math.cos(alpha1);
    var tanU1 = (1 - f) * Math.tan(lat1 * (Math.PI / 180));
    var cosU1 = 1 / Math.sqrt((1 + tanU1 * tanU1)), sinU1 = tanU1 * cosU1;
    var sigma1 = Math.atan2(tanU1, cosAlpha1);
    var sinAlpha = cosU1 * sinAlpha1;
    var cosSqAlpha = 1 - sinAlpha * sinAlpha;
    var uSq = cosSqAlpha * (a * a - b * b) / (b * b);
    var A = 1 + uSq / 16384 * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));
    var B = uSq / 1024 * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));
    var sigma = s / (b * A), sigmaP = 2 * Math.PI;
    let cos2SigmaM = 0, sinSigma = 0, cosSigma = 0;
    while (Math.abs(sigma - sigmaP) > 1e-12) {
        cos2SigmaM = Math.cos(2 * sigma1 + sigma);
        sinSigma = Math.sin(sigma);
        cosSigma = Math.cos(sigma);
        var deltaSigma = B * sinSigma * (cos2SigmaM + B / 4 * (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM) -
            B / 6 * cos2SigmaM * (-3 + 4 * sinSigma * sinSigma) * (-3 + 4 * cos2SigmaM * cos2SigmaM)));
        sigmaP = sigma;
        sigma = s / (b * A) + deltaSigma;
    }

    var tmp = sinU1 * sinSigma - cosU1 * cosSigma * cosAlpha1;
    var lat2 = Math.atan2(sinU1 * cosSigma + cosU1 * sinSigma * cosAlpha1,
        (1 - f) * Math.sqrt(sinAlpha * sinAlpha + tmp * tmp));
    var lambda = Math.atan2(sinSigma * sinAlpha1, cosU1 * cosSigma - sinU1 * sinSigma * cosAlpha1);
    var C = f / 16 * cosSqAlpha * (4 + f * (4 - 3 * cosSqAlpha));
    var L = lambda - (1 - C) * f * sinAlpha *
        (sigma + C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));

    var revAz = Math.atan2(sinAlpha, -tmp); // final bearing

    var lngLatObj = {lon: lon + L, lat: lat2}
    return lngLatObj;
}

const FixTowerHeight = true;
export async function computeHeight(viewer: Cesium.Viewer, finish = () => {
}) {
    console.log("computeHeight entering...");
    const store_lineProj = useLineProjStore();
    if (store_lineProj.lineDetail.length < 1) {
        ElMessage({
            message: '没有线路数据，请更新数据！',
            grouping: true,
            type: 'warning',
        })
        return;
    }
    
    // 确保地形数据已加载
    if (!viewer || !viewer.terrainProvider) {
        console.warn('地形提供者不可用，使用默认高度');
        finish();
        return;
    }
    
    // 如果地形提供者存在但还没准备好，等待它准备好
    if (!(viewer.terrainProvider as any).ready) {
        try {
            console.log('等待地形数据准备就绪...');
            await new Promise(resolve => {
                const interval = setInterval(() => {
                    if ((viewer.terrainProvider as any).ready) {
                        clearInterval(interval);
                        resolve(true);
                    }
                }, 100);
            });
            console.log('地形数据已准备就绪，继续高程调整');
        } catch (error) {
            console.error('等待地形数据准备就绪失败:', error);
            finish();
            return;
        }
    }
    
    // 增加适当的高度偏移，确保模型不会下沉
    const heightOffset = 1.0; // 1米偏移，可以根据需要调整
    
    if (store_lineProj.projMsg.version == 'V2'){
        // 兼容新工程的三维线路数据格式
        let positions = []
        for(let i=0; i<store_lineProj.lineDetail.length; i++){
            let towerInfo = store_lineProj.lineDetail[i]
            positions.push([towerInfo.longitude, towerInfo.latitude])
        }
        await getPositionHeight(viewer, positions).then((updatedPositions) => {
            if (updatedPositions) {
                for (let ind = 0; ind < store_lineProj.lineDetail.length; ind++) {
                    // 记录原始高度用于日志
                    const originalHeight = store_lineProj.lineDetail[ind].height;
                    
                    // 添加地形高度和偏移
                    store_lineProj.lineDetail[ind].height += updatedPositions[ind].height + heightOffset;
                    
                    // 输出日志，方便调试
                    console.log(`杆塔 ${ind} 高度调整: 原始=${originalHeight}, 地形=${updatedPositions[ind].height}, 偏移=${heightOffset}, 最终=${store_lineProj.lineDetail[ind].height}`);
                }
            }
        });
        finish()
        return
    }
    
    if (FixTowerHeight) {
        for (let lineNo = 0; lineNo < store_lineProj.lineDetail.length; lineNo++) {
            let line = store_lineProj.lineDetail[lineNo];
            // console.log('line:', line);
            let positions = [];
            for (let ind = 0; ind < line.towers.length; ind++) {
                let tower = line.towers[ind];
                positions.push([tower.longitude, tower.latitude])
            }
            await getPositionHeight(viewer, positions).then((updatedPositions) => {
                if (updatedPositions) {
                    for (let ind = 0; ind < line.towers.length; ind++) {
                        let tower = line.towers[ind];
                        
                        // 记录原始高度用于日志
                        const originalHeight = tower.height;
                        
                        // 添加地形高度和偏移
                        tower.height += updatedPositions[ind].height + heightOffset;
                        
                        // 输出日志，方便调试
                        console.log(`线路 ${lineNo}, 杆塔 ${ind} 高度调整: 原始=${originalHeight}, 地形=${updatedPositions[ind].height}, 偏移=${heightOffset}, 最终=${tower.height}`);
                    }
                }
            });
            // console.log('line++:', line);
        }
        finish();
    } else {
        finish();
    }
}

export function straight_line(polylines: Cesium.PolylineCollection, point1: number[], point2: number[], color: string, width: number) {
    let line = polylines.add({
        show: true,
        positions: [Cesium.Cartesian3.fromDegrees(point1[0], point1[1], point1[2]), Cesium.Cartesian3.fromDegrees(point2[0], point2[1], point2[2])],
        width: width,
        material: Cesium.Material.fromType('Color', {
            color: Cesium.Color.fromCssColorString(color)
        })
    });
}
