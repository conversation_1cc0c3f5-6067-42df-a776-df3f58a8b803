import Dexie from 'dexie'
export default class ModelCache {
    private dbTable: Dexie.Table<any, string>
    private dbName: string
    private tableName: string
    private cache: Map<string, ArrayBuffer>; // 内存缓存
    private failedUrls: Set<string>; // 跟踪失败的URL以避免重复请求
    private maxRetries: number = 2; // 最大重试次数
    private retryDelays: number[] = [1000, 3000]; // 重试延迟时间(毫秒)

    constructor(dbName: string = 'modelDB', tableName: string = 'myModelTab') {
        this.dbName = dbName
        this.tableName = tableName
        this.cache = new Map(); // 初始化内存缓存
        this.failedUrls = new Set(); // 初始化失败URL集合

        let db = new Dexie(dbName)
        db.version(1).stores({
            [tableName]: 'url, data'
        })
        this.dbTable = db.table(tableName)

        // 启动时加载所有缓存模型到内存
        this.getAllModel().then(res => {
            console.log(`已从IndexedDB加载${res.length}个模型到内存缓存`);
            res.forEach(model => {
                this.cache.set(model.url, model.data) // 更新内存缓存
            })            
        }).catch(err => {
            console.error('从IndexedDB加载模型到内存缓存失败:', err);
        })
    }

    // 重试逻辑包装器
    private async retryOperation(operation: () => Promise<any>, retryCount: number = 0): Promise<any> {
        try {
            return await operation();
        } catch (error) {
            if (retryCount < this.maxRetries) {
                const delay = this.retryDelays[retryCount] || 3000;
                console.log(`操作失败，${delay}ms后重试 (${retryCount + 1}/${this.maxRetries})`);
                
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve(this.retryOperation(operation, retryCount + 1));
                    }, delay);
                });
            }
            throw error; // 如果达到最大重试次数，继续抛出错误
        }
    }

    cacheOneModel = (modelUrl: string): Promise<ArrayBuffer | null> => {
        // 如果是虚拟模型或之前已失败，则跳过
        if (modelUrl.indexOf('insulator_virtual') > -1 || 
            modelUrl.indexOf('tower_virtual') > -1 ||
            this.failedUrls.has(modelUrl)) {
            return Promise.resolve(null);
        }

        const lastSlashIndex = modelUrl.lastIndexOf('/');
        const modelName = modelUrl.substring(lastSlashIndex + 1);
        
        return this.retryOperation(() => {
            console.log(`尝试加载模型: ${modelName} (${modelUrl})`);
            return fetch(modelUrl, {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache'
                },
                cache: 'no-store'
            })
            .then(async (response) => {
                if (!response.ok) {
                    console.log(`模型加载失败 ${modelName}: ${response.status} ${response.statusText}`);
                    this.failedUrls.add(modelUrl); // 记录失败URL
                    return null;
                }
                
                try {
                    const arrayBuffer = await response.arrayBuffer();
                    console.log(`模型加载成功: ${modelName} (${arrayBuffer.byteLength} bytes)`);
                    
                    // 保存到IndexedDB
                    return this.dbTable.put({ url: modelUrl, data: arrayBuffer })
                        .then(() => {
                            this.cache.set(modelUrl, arrayBuffer); // 同时更新内存缓存
                            return arrayBuffer;
                        });
                } catch (error) {
                    console.error(`处理模型数据失败: ${modelName}`, error);
                    this.failedUrls.add(modelUrl); // 记录失败URL
                    return null;
                }
            });
        }).catch(error => {
            console.error(`所有尝试加载模型都失败 ${modelName}:`, error);
            this.failedUrls.add(modelUrl); // 记录失败URL
            return null;
        });
    }

    getOneModel = (modelUrl: string): Promise<ArrayBuffer | null> => {
        if (modelUrl.indexOf('insulator_virtual') > -1 || 
            modelUrl.indexOf('tower_virtual') > -1) {
            return Promise.resolve(null);
        }
        
        // 检查是否是已知失败的URL
        if (this.failedUrls.has(modelUrl)) {
            console.log(`跳过已知失败的模型URL: ${modelUrl}`);
            return Promise.resolve(null);
        }

        const startTime = performance.now();
        const lastSlashIndex = modelUrl.lastIndexOf('/');
        const modelName = modelUrl.substring(lastSlashIndex + 1);

        // 首先尝试从内存缓存获取
        if (this.cache.has(modelUrl)) {
            console.log(`从内存缓存加载模型: ${modelName}`);
            return Promise.resolve(this.cache.get(modelUrl) || null);
        }

        // 然后从IndexedDB尝试获取
        return this.dbTable.get(modelUrl)
            .then((item) => {
                const endTime = performance.now();
                
                if (item) {
                    console.log(`从IndexedDB加载模型: ${modelName} (${endTime - startTime}ms)`);
                    this.cache.set(modelUrl, item.data); // 更新内存缓存
                    return item.data;
                } else {
                    console.log(`本地没有模型: ${modelName}，将从服务器获取`);
                    // 尝试从服务器加载
                    return this.cacheOneModel(modelUrl);
                }
            })
            .catch((error) => {
                console.warn(`从IndexedDB获取模型失败: ${modelName}`, error);
                // 数据库操作失败，尝试从服务器加载
                return this.cacheOneModel(modelUrl);
            });
    }

    // 重置失败URL，允许再次尝试之前失败的URL
    resetFailedUrls() {
        const count = this.failedUrls.size;
        this.failedUrls.clear();
        console.log(`已重置${count}个失败的模型URL`);
    }

    getAllModel = (): Promise<any[]> => {
        return this.dbTable.toArray();
    }

    getDBInfo = () => {
        return { 
            dbName: this.dbName, 
            tableName: this.tableName,
            cacheSize: this.cache.size,
            failedUrlsCount: this.failedUrls.size
        }
    }

    async cleanExpiredCache(maxAge: number): Promise<void> {
        // Dexie.js and browser cache policies handle expiration, so this is a placeholder.
        // Implement if specific app-level cache clearing logic is needed.
        console.log('cleanExpiredCache called, but not implemented as Dexie handles caching.');
    }

    dispose(): void {
        this.cache.clear();
        this.failedUrls.clear();
        console.log('ModelCache disposed.');
    }
}