import proj4 from 'proj4'

export function protectedAreaDataPreprocessing(jsonData: any) {
    console.log('protectedAreaDataPreprocessing')
    let newJsonData = {
        type: 'FeatureCollection',
        name: 'protectedArea',
        features: [] as any[]
    }

    if (jsonData.crs) {
        delete jsonData.crs // 删除crs 坐标系相关内容
    }

    for (let i = 0; i < jsonData.features.length; i++) {
        // console.log('protectedAreaDataPreprocessing: feature=',jsonData.features[i])
        let feature = jsonData.features[i]
        let coordinates = feature.geometry.coordinates
        for (let j = 0; j < coordinates.length; j++) {
            let newFeature = {
                type: 'Feature',
                properties: feature.properties,
                geometry: {
                    type: 'Polygon',
                    coordinates: [] as any[]
                }
            }
            let newCoordinates = []
            for (let k = 0; k < coordinates[j].length; k++) {
                newCoordinates.push(transformCoord(coordinates[j][k]))
            }
            newFeature.geometry.coordinates.push(newCoordinates)
            newJsonData.features.push(newFeature)
        }
    }
    console.log('protectedAreaDataPreprocessing: newJsonData=', newJsonData)
    return newJsonData
}

function transformCoord(cgcs2000Coord: number[]) {
    // 定义坐标系
    let cgcs2000 = '+proj=tmerc +lat_0=0 +lon_0=114 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs'
    let wgs84 = "+proj=longlat +datum=WGS84 +no_defs"
    var result = proj4(cgcs2000, wgs84, cgcs2000Coord) // 转换坐标
    return [result[0], result[1]]
}
