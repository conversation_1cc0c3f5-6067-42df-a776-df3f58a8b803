import require from "@/utils/requires"

/**
 * 根据时间获取杆塔工序-大屏施工回溯
 * @param data
 * @returns
 */
export function getPlanRecordByTime(data: any) {
    return require({
        url: "admin-api/manage-intranet/planRecord/getPlanRecordByTime",
        method: "get",
        params: data
    })
}

/**
 * 交叉跨越表格
 * @param data
 * @returns
 */
export function crossTable(data: any) {
    return require({
        url: "admin-api/manage-intranet/cross-plan/page",
        method: "get",
        params: data

    })
}
/**
 * 交叉跨越进度统计
 * @param data
 * @returns
 */
export function crossStatistics(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cross-plan/status-statistical",
        method: "get",
        params: data
    })
}
/**
 * 交叉跨越信息统计
 * @param data
 * @returns
 */
export function crossTypeStatistics(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cross-plan/crossing-type-statistical",
        method: "get",
        params: data
    })

}

export function crossChange(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cross-plan/change",
        method: "put",
        data: data,
        headers: {
            'Content-Type': 'application/json'
        }

    })
}
export function crossRecord(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cross-plan/change-record",
        method: "get",
        params: data,


    })
}
export function crossCode(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cross-plan/unconditional-list",
        method: "get",
        params: data
    })

}
/**
 * 线路解口表格
 * @param data
 * @returns
 */
export function disassemblyLine(data: any) {
    return require({
        url: "admin-api/manage-intranet/dismantle-plan/page",
        method: "get",
        params: data

    })
}
export function disassemblyLineStatistics(data: any) {
    return require({
        url: "admin-api/manage-intranet/dismantle-plan/status-statistical",
        method: "get",
        params: data
    })
}
export function disassemblyLineTypeStatistics(data: any) {
    return require({
        url: "admin-api/manage-intranet/dismantle-plan/dismantle-type-statistical",
        method: "get",
        params: data
    })
}
export function disassemblyLineChange(data: any) {
    return require({
        url: "admin-api/manage-intranet/dismantle-plan/change",
        method: "put",
        data: data,
        headers: {
            'Content-Type': 'application/json'
        }

    })
}
export function disassemblyLineRecord(data: any) {
    return require({
        url: "/admin-api/manage-intranet/dismantle-plan/change-record",
        method: "get",
        params: data,


    })
}



/**
 * 施工进度计划表格
 * @param data
 * @returns
 */
export function constructionSchedule(data: any) {
    return require({
        url: "admin-api/manage-intranet/stringing-section-plan/page",
        method: "get",
        params: data

    })
}
export function constructionEarning(data: any) {
    return require({
        url: "admin-api/manage-intranet/stringing-section-plan/warning-statistical",
        method: "get",
        params: data

    })
}
export function constructionEarningList(data: any) {
    return require({
        url: "admin-api/manage-intranet/stringing-section-plan/warning-list",
        method: "get",
        params: data

    })
}
export function constructionChange(data: any) {
    return require({
        url: "/admin-api/manage-intranet/stringing-section-plan/change",
        method: "put",
        data: data,
        headers: {
            'Content-Type': 'application/json'
        }

    })
}
export function constructionChangeRecord(data: any) {
    return require({
        url: "/admin-api/manage-intranet/stringing-section-plan/change-record",
        method: "get",
        params: data

    })
}
/**
 * 施工进度表格
 * @param data
 * @returns
 */

export function construction(data: any) {
    return require({
        url: "admin-api/manage-intranet/construction-progress/page",
        method: "get",
        params: data

    })
}

export function lineSegmentStatistics(data: any) {
    return require({
        url: "admin-api/manage-intranet/construction-progress/stringing-section-statistical",
        method: "get",
        params: data

    })
}

export function lineStatistics2and3(data: any) {
    return require({
        url: "admin-api/manage-intranet/construction-progress/tower-statistical",
        method: "get",
        params: data
    })
}


/**
 * 停电计划表格
 * @param data
 * @returns
 */
export function powerOutagePlan(data: any) {
    return require({
        url: "admin-api/manage-intranet/power-cut-plan/page",
        method: "get",
        params: data

    })
}
export function tingdianChange(data: any) {
    return require({
        url: "/admin-api/manage-intranet/power-cut-plan/change",
        method: "put",
        data: data,
        headers: {
            'Content-Type': 'application/json'
        }

    })
}
export function tingdianRecord(data: any) {
    return require({
        url: "/admin-api/manage-intranet/power-cut-plan/change-record",
        method: "get",
        params: data,
    })
}
/**
 * 照片管理表格
 * @param data
 * @returns
 */
export function photoManagement(data: any) {
    return require({
        url: "admin-api/manage-intranet/photo-manage/page",
        method: "get",
        params: data

    })
}

export function photoStatistical(data: any) {
    return require({
        url: "admin-api/manage-intranet/photo-manage/photo-statistical",
        method: "get",
        params: data

    })
}
export function photoList(data: any) {
    return require({
        url: "/admin-api/manage-intranet/photo-manage/look-over",
        method: "get",
        params: data

    })
}
/**
 * 物资管理表格
 * @param data
 * @returns
 */
export function materialManagement(data: any) {
    return require({
        url: "admin-api/manage-intranet/material-supply-plan/page",
        method: "get",
        params: data
    })
}
export function materialManagementWarning(data: any) {
    return require({
        url: "/admin-api/manage-intranet/material-supply-plan/warning-list",
        method: "get",
        params: data
    })
}
export function materialManagementDetail(data: any) {
    return require({
        url: "/admin-api/manage-intranet/material-supply-plan/detail",
        method: "get",
        params: data
    })
}
export function materialManagementStatisticsArrival(data: any) {
    return require({
        url: "/admin-api/manage-intranet/material-statistics/arrival",
        method: "get",
        params: data
    })
}
export function materialManagementStatisticsMaterial(data: any) {
    return require({
        url: "/admin-api/manage-intranet/material-statistics/material",
        method: "get",
        params: data
    })
}
export function materialManagementStatisticsDepot(data: any) {
    return require({
        url: "/admin-api/manage-intranet/material-statistics/depot/",
        method: "get",
        params: data
    })
}
/**
 * 施工方案交叉跨越表格
 * @param data
 * @returns
 */
export function schemeChange(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cross-scheme/change-list",
        method: "get",
        params: data
    })
}
export function crossScheme(data: any) {

    return require({
        url: "/admin-api/manage-intranet/cross-scheme/page",
        method: "get",
        params: data
    })
}
export function crossSchemeuploap(data: any) {
    return require({
        url: '/admin-api/manage-intranet/cross-scheme/create',
        method: 'post',
        data: data,
        headers: { 'Content-Type': 'multipart/form-data' }
    })
}
export function crossSchemechange(data: any) {
    return require({
        url: '/admin-api/manage-intranet/cross-scheme/change',
        method: 'put',
        data: data,
        headers: { 'Content-Type': 'multipart/form-data' }
    })
}
export function crossSchemeChangeRecord(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cross-scheme/change-record",
        method: "get",
        params: data

    })
}
export function crossSchemeDelete(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cross-scheme/delete",
        method: "delete",
        params: data

    })
}
export function crossSchemedetail(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cross-scheme/detail",
        method: "get",
        params: data

    })
}
/**
 * 施工方案线路解口表格
 * @param data
 * @returns
 */
export function disassemblyLineScheme(data: any) {
    return require({
        url: "/admin-api/manage-intranet/dismantle-scheme/page",
        method: "get",
        params: data

    })
}
export function disassemblyLineSchemeuploap(data: any) {
    return require({
        url: '/admin-api/manage-intranet/dismantle-scheme/create',
        method: 'post',
        data: data,
        headers: { 'Content-Type': 'multipart/form-data' }
    })
}
export function disassemblyLineSchemechange(data: any) {
    return require({
        url: '/admin-api/manage-intranet/dismantle-scheme/change',
        method: 'put',
        data: data,
        headers: { 'Content-Type': 'multipart/form-data' }
    })
}
export function disassemblyLineSchemeChangeRecord(data: any) {
    return require({
        url: "/admin-api/manage-intranet/dismantle-scheme/change-record",
        method: "get",
        params: data

    })
}
export function disassemblyLineSchemeDelete(data: any) {
    return require({
        url: "/admin-api/manage-intranet/dismantle-scheme/delete",
        method: "delete",
        params: data

    })
}
export function disassemblyLineSchemeDetail(data: any) {
    return require({
        url: "/admin-api/manage-intranet/dismantle-scheme/detail",
        method: "get",
        params: data

    })
}
/**
 * 搜索
 * @param data
 * @returns
 */
export function selectTableData(data: any) {
    let port = ""
    if (data.num == 0.1)
        port = "cross-scheme"
    else if (data.num == 0.2)
        port = "dismantle-scheme"
    else if (data.num == 1)
        port = "stringing-section-plan"
    else if (data.num == 2)
        port = "construction-progress"
    else if (data.num == 3)
        port = "cross-plan"
    else if (data.num == 4)
        port = "dismantle-plan"
    else if (data.num == 5)
        port = "power-cut-plan"
    else if (data.num == 6)
        port = "material-supply-plan"
    else if (data.num == 7)
        port = "photo-manage"

    return require({
        url: "/admin-api/manage-intranet/" + port + "/search",
        method: "get",
        params: data.code

    })
}


//工程数据
export function getproject(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cm-common/projectList",
        method: "get",
        params: data

    })
}
export function getStringing(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cm-common/stringingSectionList",
        method: "get",
        params: data

    })
}
export function getline(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cm-common/lineList",
        method: "get",
        params: data
    })
}
export function gettower(data: any) {
    return require({
        url: "/admin-api/manage-intranet/cm-common/towerList-pls",
        method: "get",
        params: data
    })

}

export function getprojectStructure(data: any) {
    return require({
        url: "/admin-api/manage-intranet/common/project-structure",
        method: "get",
        params: data
    })
}

