import * as Cesium from 'cesium'

export default class Roaming {
    private _viewer: any
    private _positions: number[][]
    private _speed: number
    private _loop: boolean
    private _callback: Function | undefined
    private _isRoaming: boolean
    private _preUpdateTime: number
    private _currentIndex: number
    private _currentPosition: Cesium.Cartesian3
    private _nextPosition: Cesium.Cartesian3
    private _direction: Cesium.Cartesian3
    private _up: Cesium.Cartesian3
    private _right: Cesium.Cartesian3

    constructor() {
        this._viewer = undefined
        this._positions = []
        this._speed = 1
        this._loop = false
        this._callback = undefined
        this._isRoaming = false
        this._preUpdateTime = 0
        this._currentIndex = 0
        this._currentPosition = new Cesium.Cartesian3()
        this._nextPosition = new Cesium.Cartesian3()
        this._direction = new Cesium.Cartesian3()
        this._up = new Cesium.Cartesian3()
        this._right = new Cesium.Cartesian3()
    }

    roaming_start(viewer: any, positions: number[][], speed: number = 1, loop: boolean = false, callback?: Function) {
        if (!viewer || !positions || positions.length < 2) {
            console.error('Invalid parameters for roaming')
            return
        }

        this._viewer = viewer
        this._positions = positions
        this._speed = speed
        this._loop = loop
        this._callback = callback
        this._isRoaming = true
        this._preUpdateTime = performance.now()
        this._currentIndex = 0

        // Convert first two positions to Cartesian3
        this._currentPosition = Cesium.Cartesian3.fromDegrees(
            positions[0][0],
            positions[0][1],
            positions[0][2]
        )
        this._nextPosition = Cesium.Cartesian3.fromDegrees(
            positions[1][0],
            positions[1][1],
            positions[1][2]
        )

        // Calculate initial direction
        Cesium.Cartesian3.subtract(this._nextPosition, this._currentPosition, this._direction)
        Cesium.Cartesian3.normalize(this._direction, this._direction)

        // Set up camera orientation
        this._updateCameraOrientation()

        // Start the roaming
        this._viewer.scene.preUpdate.addEventListener(this._onPreUpdate, this)
    }

    roaming_stop(viewer: any) {
        if (this._viewer && this._isRoaming) {
            this._viewer.scene.preUpdate.removeEventListener(this._onPreUpdate, this)
            this._isRoaming = false
            this._viewer = undefined
            
            if (this._callback) {
                this._callback()
            }
        }
    }

    private _updateCameraOrientation() {
        // Calculate up vector (using world up as reference)
        const worldUp = Cesium.Cartesian3.normalize(this._currentPosition, new Cesium.Cartesian3())
        Cesium.Cartesian3.cross(this._direction, worldUp, this._right)
        Cesium.Cartesian3.normalize(this._right, this._right)
        Cesium.Cartesian3.cross(this._right, this._direction, this._up)
        Cesium.Cartesian3.normalize(this._up, this._up)

        // Update camera
        this._viewer.camera.position = this._currentPosition
        this._viewer.camera.direction = this._direction
        this._viewer.camera.up = this._up
        this._viewer.camera.right = this._right
    }

    private _onPreUpdate() {
        if (!this._isRoaming) return

        const currentTime = performance.now()
        const deltaTime = (currentTime - this._preUpdateTime) / 1000 // Convert to seconds
        this._preUpdateTime = currentTime

        // Calculate distance to move
        const distance = this._speed * deltaTime * 50 // Adjust speed factor as needed

        // Move towards next position
        const toNext = Cesium.Cartesian3.subtract(this._nextPosition, this._currentPosition, new Cesium.Cartesian3())
        const distanceToNext = Cesium.Cartesian3.magnitude(toNext)

        if (distance >= distanceToNext) {
            // Reached next position
            this._currentPosition = Cesium.Cartesian3.clone(this._nextPosition)
            this._currentIndex++

            // Check if we've reached the end
            if (this._currentIndex >= this._positions.length - 1) {
                if (this._loop) {
                    this._currentIndex = 0
                } else {
                    this.roaming_stop(this._viewer)
                    return
                }
            }

            // Set next position
            this._nextPosition = Cesium.Cartesian3.fromDegrees(
                this._positions[this._currentIndex + 1][0],
                this._positions[this._currentIndex + 1][1],
                this._positions[this._currentIndex + 1][2]
            )

            // Update direction
            Cesium.Cartesian3.subtract(this._nextPosition, this._currentPosition, this._direction)
            Cesium.Cartesian3.normalize(this._direction, this._direction)
        } else {
            // Move along current segment
            const moveVector = Cesium.Cartesian3.multiplyByScalar(
                this._direction,
                distance,
                new Cesium.Cartesian3()
            )
            Cesium.Cartesian3.add(this._currentPosition, moveVector, this._currentPosition)
        }

        // Update camera orientation
        this._updateCameraOrientation()
    }
} 