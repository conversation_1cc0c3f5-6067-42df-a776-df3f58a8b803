import * as Cesium from 'cesium';

let RoamingPoint: any = null;
let onTickListener: any = null;

/**
 * 根据所给定的坐标数组，进行巡航漫游
 * @param {object} viewer cesium场景对象
 * @param {array} positions 位置坐标二维数组 [[lon,lat,height],[lon,lat,height]]
 * @param {number} stepSec 巡航速度 即每两个点之间移动的时间单位s
 * @param {bool} showPoint 是否显示视角跟踪点
 */
export class Roaming {
    private position2: any = null;
    private position3: any = null;

    constructor() {
        // Constructor implementation
    }

    roaming_start(viewer: any, positions: number[][], stepSec: number = 0, showPoint: boolean = false, callback?: () => void) {
        // viewer.scene.globe.show = false; // 关闭地球模型 用于调试
        if (positions.length < 2) {
            console.error('positions error', positions);
            return;
        }
        //Set bounds of our simulation time
        const start = Cesium.JulianDate.fromDate(new Date(2023, 8, 1, 12, 0, 0)); // 给定一个确定的中午时间，确保场景巡飞时的光照效果
        // const start = Cesium.JulianDate.fromDate(new Date());

        let roaming_time = (positions.length - 1) * stepSec;
        const stop = Cesium.JulianDate.addSeconds(start, roaming_time, new Cesium.JulianDate());

        //Make sure viewer is at the desired time.
        viewer.clock.startTime = start.clone();
        viewer.clock.stopTime = stop.clone();
        viewer.clock.currentTime = start.clone();
        viewer.clock.clockRange = Cesium.ClockRange.CLAMPED; //Loop at the end
        // viewer.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK; // 时钟设置为当前系统时间; 忽略所有其他设置。
        viewer.clock.multiplier = 1;
        //Set timeline to simulation bounds
        viewer.timeline.zoomTo(start, stop);
        // 转换坐标
        let coordinates: any[] = [];
        positions.forEach(_position => {
            _position[2] = _position[2] + 20

            try {
                coordinates.push(Cesium.Cartesian3.fromDegrees(_position[0], _position[1], _position[2]))
            } catch (e) {
                console.error("positions error", positions);
                return;
            }
        });

        // 创建一个点
        RoamingPoint = viewer.entities.add({
            position: coordinates[0],
            point: {
                pixelSize: (showPoint ? 5 : 0),
                color: Cesium.Color.TRANSPARENT,
                outlineColor: Cesium.Color.YELLOW,
                outlineWidth: (showPoint ? 1 : 0),
            },
        });

        // 创建移动路径
        var pathPositions = new Cesium.SampledPositionProperty();

        coordinates.forEach((coord: any, index: number) => {
            var time = Cesium.JulianDate.addSeconds(viewer.clock.startTime, index * stepSec, new Cesium.JulianDate());
            pathPositions.addSample(time, coord);
        });

        // 设置相机视角跟随圆点 通过修改 viewFrom 的值可以设定巡航相机相对跟踪点的初始视角
        RoamingPoint.viewFrom = new Cesium.Cartesian3(50, 50, 0)
        viewer.trackedEntity = RoamingPoint;

        let isStop = false

        // 更新圆点位置
        onTickListener = (clock: any) => {

            let isEnd = clock.stopTime.secondsOfDay - Cesium.JulianDate.addSeconds(clock.currentTime,3,new Cesium.JulianDate()).secondsOfDay;
            if(isEnd >= 0.5) {
                //根据时刻获取漫游位置
                let newPosition = pathPositions.getValue(clock.currentTime);
                let nextPosition = pathPositions.getValue(Cesium.JulianDate.addSeconds(clock.currentTime, 3, new Cesium.JulianDate()));//+3秒以后得位置

                this.setCameraPosition(newPosition,nextPosition, viewer);

            } else if (!isStop) {
                isStop = true
                // gbxlxf();
                this.roaming_stop(viewer)
                if (callback) callback()
            }
        }

        viewer.clock.onTick.addEventListener(onTickListener);

        // 开始动画
        viewer.clock.shouldAnimate = true;
    }

    /**
     * 设置相机位置
     * @param {cartesian3} position
     * @memberof Roaming
     */
    setCameraPosition(position: any, nextPosition: any, viewer: any) {
        if (position) {
            // 最新传进来的坐标（后一个位置）
            this.position2 = this.cartesian3ToWGS84(position);
            this.position3 = this.cartesian3ToWGS84(nextPosition);

            let heading = 0;
            // 前一个位置点位
            if (this.position3) {
                // 计算前一个点位与第二个点位的偏航角
                heading = this.bearing(this.position2.latitude, this.position2.longitude,
                    this.position3.latitude, this.position3.longitude);
            }
            // this.position1 = this.cartesian3ToWGS84(position);
            if (position) {
                // https://www.cnblogs.com/tiandi/p/16836292.html
                const dynamicHeading = Cesium.Math.toRadians(heading);      //航向角度（弧度），heading就是以z轴为中心绕着圈跑
                const pitch = Cesium.Math.toRadians(-20.0);     //俯仰角（弧度），pitch就是以y轴为中心，绕着y轴跑圈儿，顺时针是正
                const range = 120.0;    //距离中心的位置，以米为单位
                //console.log(55555555555555555,position, heading)
                //console.log(dynamicHeading + "," + pitch + "," + range + ",")
                viewer.camera.lookAt(position, new Cesium.HeadingPitchRange(dynamicHeading, pitch, range));
            }
        }
    }

    /**
     * @name bearing 计算两点的角度 heading
     * @param startLat 初始点的latitude
     * @param startLng 初始点的longitude
     * @param destLat 第二个点的latitude
     * @param destLng 第二个点的latitude
     * @return {number} heading值
     */
    bearing(startLat: number, startLng: number, destLat: number, destLng: number): number {
        startLat = Cesium.Math.toRadians(startLat);
        startLng = Cesium.Math.toRadians(startLng);
        destLat = Cesium.Math.toRadians(destLat);
        destLng = Cesium.Math.toRadians(destLng);
        const y = Math.sin(destLng - startLng) * Math.cos(destLat);
        const x = Math.cos(startLat) * Math.sin(destLat) - Math.sin(startLat) * Math.cos(destLat) * Math.cos(destLng - startLng);
        const brng = Math.atan2(y, x);
        const brngDgr = Cesium.Math.toDegrees(brng);
        return (brngDgr + 360) % 360;
    }

    /**
     * cartographic 转Degrees下地理坐标
     * @param point radius下的WGS84坐标
     * @return degrees下的WGS84坐标
     */
    cartesian3ToWGS84(point: any) {
        if (point) {
            const cartographic = Cesium.Cartographic.fromCartesian(point);
            const lat = Cesium.Math.toDegrees(cartographic.latitude);
            const lng = Cesium.Math.toDegrees(cartographic.longitude);
            const alt = cartographic.height;
            return {
                longitude: lng,
                latitude: lat,
                height: alt,
            };
        }
    }

    /**
     * 停止视角巡航
     * @param {object} viewer cesium场景对象
     */
    roaming_stop(viewer: any) {
        try {
            // 检查listener是否为函数，只有是函数时才尝试移除
            if (onTickListener && typeof onTickListener === 'function') {
                viewer.clock.onTick.removeEventListener(onTickListener);
            } else {
                console.warn('Roaming: onTickListener is not a function or is null');
                // 如果不是函数，可能需要重置整个事件监听器
                viewer.clock.onTick._listeners = [];
            }

            // 重置动画状态
            viewer.clock.shouldAnimate = false;

            // 安全删除漫游点
            if (RoamingPoint && viewer.entities.contains(RoamingPoint)) {
                viewer.entities.remove(RoamingPoint);
            }

            // 重置全局变量
            RoamingPoint = null;
            onTickListener = null;

            // 解除跟踪实体
            viewer.trackedEntity = undefined;
        } catch (ex) {
            console.error('Error during roaming stop:', ex);
        }
    }
}