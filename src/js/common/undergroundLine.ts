import * as Cesium from '@/Cesium'
// import * as Cesium from 'cesium'
import { getPositionHeight, createLineCollection } from '@/js/3dViewer.js'
import { xyz2Radians, distance_ang } from '@/js/line_tower.js'

type PropertiesType = {
    type: string,
    height: string,
    color: string,
    width: string
}
type GeometryType = {
    type: string,
    properties: PropertiesType,
    geometry: any
}

function calculateRadians(radius: number, angle: number, origin: [number, number], count: number = 10) {
    // 根据基本参数 生成圆弧的若干个相对坐标点数据
    let coordinateOffset = []
    let angleStep = Math.abs(angle) / count
    for (let i = 0; i < count * 2 + 1; i++) {
        // let _angle = -1 * angle + angleStep * i
        let _angle = angle > 0 ? -1 * angle + angleStep * i : -1 * (angle + Math.PI) - angleStep * i
        // let _angle = 0
        let x = origin[0] + radius * Math.cos(_angle)
        let y = origin[1] + radius * Math.sin(_angle)
        coordinateOffset.push([x, y])
    }
    return coordinateOffset
}


type LineOptionsType = {
    name?: string,
    width: number,
    color: string,
    offset: number,
    radius: number
}
function addUndergroundLine(viewer: Cesium.Viewer, positions: number[][], options: LineOptionsType[]) {
    let polylines = new Cesium.PolylineCollection()
    viewer.scene.primitives.add(polylines)
    let computeCircle = (radius: number, count: number = 10) => {
        const positions = [];
        for (let i = 0; i < count; i++) {
            const radians = Cesium.Math.toRadians(i * 360 / count);
            positions.push(
                new Cesium.Cartesian2(
                    radius * Math.cos(radians),
                    radius * Math.sin(radians)
                )
            );
        }
        return positions;
    }
    for (let i = 0; i < options.length; i++) {
        let xyz = [options[i].offset, 0, 0]
        let linePositions: number[] = []
        // 处理第一个点
        let point1 = positions[0]
        let point2 = positions[1]
        let disAndRes1 = distance_ang(point1, point2)
        let _offsetPosition = xyz2Radians(point1, xyz, disAndRes1[1] * 180 / Math.PI)
        linePositions.push(..._offsetPosition)
        // 循环处理中间部分的点
        let disAndRes2 = disAndRes1 // 用于循环的初始值
        for (let j = 1; j < positions.length - 1; j++) {
            point1 = point2
            point2 = positions[j + 1]
            disAndRes1 = disAndRes2
            disAndRes2 = distance_ang(point1, point2)
            _offsetPosition = xyz2Radians(point1, xyz, (disAndRes1[1] + disAndRes2[1]) / 2 * 180 / Math.PI)
            // linePositions.push(..._offsetPosition)

            // 处理该转角点的拐角圆弧
            let radius = options[i].radius
            let res = (disAndRes2[1] - disAndRes1[1]) / 2
            let origin: [number, number]
            if (res > 0) {
                origin = [xyz[0] - radius / Math.cos(res), 0]
            } else {
                origin = [xyz[0] - radius / Math.cos(res + Math.PI), 0]
            }
            let coordinateOffset = calculateRadians(radius, res, origin)
            for (let k = 0; k < coordinateOffset.length; k++) {
                let _position = xyz2Radians(point1, [...coordinateOffset[k], 0], (disAndRes1[1] + disAndRes2[1]) / 2 * 180 / Math.PI)
                linePositions.push(..._position)
            }
        }
        // 处理最后一个点
        _offsetPosition = xyz2Radians(point2, xyz, disAndRes2[1] * 180 / Math.PI)
        linePositions.push(..._offsetPosition)

        // 使用另一种电缆加载方式 by lkj
        let polylineVolumeInfo = {
            // id: `line@${lineData.ID}` || `line@${new Date().getTime()}线`,
            polylineVolume: {
                positions: Cesium.Cartesian3.fromDegreesArrayHeights(linePositions),
                shape: computeCircle(options[i].width),
                cornerType: Cesium.CornerType.MITERED,
                outline: true,
                outlineColor: Cesium.Color.fromCssColorString(options[i].color),
                outlineWidth: 0.001,
                material: Cesium.Color.fromCssColorString(options[i].color),
            }
        }
        let entity = viewer.entities.add(polylineVolumeInfo) // 电缆沟     
    }
    return polylines
}

export async function addPolylineVolume(data: any, viewer: Cesium.Viewer) {
    let entities = []
    for (let i = 0; i < data.length; i++) {
        let options = data[i];
        // 计算并添加深度参数
        let res = await getPositionHeight(viewer, options.positions)
        // 计算最低的海拔坐标
        let minHeight = Math.min(...res.map((item: any) => item.height))
        let newPositions = []
        let positionsWithHeight = []
        let linePositions = []
        let offsetHeight = minHeight + data[i].height
        for (let j = 0; j < res.length; j++) {
            newPositions.push(res[j].longitude, res[j].latitude, offsetHeight)
            positionsWithHeight.push([res[j].longitude * 180 / Math.PI, res[j].latitude * 180 / Math.PI, offsetHeight])
            linePositions.push([res[j].longitude * 180 / Math.PI, res[j].latitude * 180 / Math.PI, offsetHeight + 0.3])
        }
        let _positions_new = coordinateCornerTransformation(positionsWithHeight, options.radius)
        let shape = []
        for (let j = 0; j < options.shape.length; j++) {
            shape.push(new Cesium.Cartesian2(...options.shape[j]))
        }
        let polylineVolumeInfo1 = {
            name: `${options.name}`,
            polylineVolume: {
                positions: Cesium.Cartesian3.fromDegreesArrayHeights(_positions_new),
                // positions: Cesium.Cartesian3.fromRadiansArrayHeights(newPositions),
                cornerType: Cesium.CornerType.MITERED,
                shape: shape,
                outline: false,
                outlineColor: Cesium.Color.fromCssColorString('#000000'),
                outlineWidth: 0.001,
                material: Cesium.Color.fromCssColorString(options.trenchColor),
            }
        }
        let entity = viewer.entities.add(polylineVolumeInfo1) // 电缆沟                
        entities.push(entity)
        let polylines = addUndergroundLine(viewer, linePositions, options.lines)

    }
    return entities
}

// 坐标拐角转换处理
function coordinateCornerTransformation(positions: number[][], radius: number, count: number = 10) {
    let newPositions: number[] = []
    let point1 = positions[0]
    let point2 = positions[1]
    newPositions.push(...point1)
    let disAndRes1 = distance_ang(point1, point2)
    let disAndRes2 = disAndRes1 // 用于循环的初始值
    for (let i = 1; i < positions.length - 1; i++) {
        point1 = point2
        point2 = positions[i + 1]
        disAndRes1 = disAndRes2
        disAndRes2 = distance_ang(point1, point2)
        let res = (disAndRes2[1] - disAndRes1[1]) / 2
        let origin: [number, number]
        if (res > 0) {
            origin = [0 - radius / Math.cos(res), 0]
        } else {
            origin = [0 - radius / Math.cos(res + Math.PI), 0]
        }
        let coordinateOffset = calculateRadians(radius, res, origin)
        for (let k = 0; k < coordinateOffset.length; k++) {
            let _position = xyz2Radians(point1, [...coordinateOffset[k], 0], (disAndRes1[1] + disAndRes2[1]) / 2 * 180 / Math.PI)
            newPositions.push(..._position)
        }

    }
    newPositions.push(...point2)
    return newPositions
}