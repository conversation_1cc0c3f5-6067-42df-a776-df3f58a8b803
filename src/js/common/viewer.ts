import * as Cesium from 'cesium'
import { AmapImageryProviderOptions, useLocalMapDataFlag } from '@/config/viewerConfig'
import AmapImageryProvider from '@/js/cesium-map/AmapImageryProvider.js'
import { MODEL_STATIC_URL, LOCAL_MAP_URL, getMapServiceUrl } from '@/config/global.js';
import ModelCache from '@/js/common/modelCache'
import CommandManager from '@/js/commandManager/index';

export function addModel_glb(viewer: Cesium.Viewer, options: any) {
    const { position, angle, url, scale, id, name } = options;
    const position3 = Cesium.Cartesian3.fromDegrees(position[0], position[1], position[2] || 0);
    const heading = Cesium.Math.toRadians(angle || 0);
    const pitch = Cesium.Math.toRadians(0);
    const roll = Cesium.Math.toRadians(0);
    const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
    const orientation = Cesium.Transforms.headingPitchRollQuaternion(position3, hpr);

    const entity = viewer.entities.add({
        id: id,
        name: name,
        position: position3,
        orientation: orientation,
        model: {
            uri: url,
            scale: scale || 1.0,
            minimumPixelSize: 32,
        },
    });
    return entity;
}
// import { flattenTerrainForSubstation1 } from './flattenTerrain'
// import CesiumTerrainProviderEdit from './UserTerrainProviderEdit.js'

let localModelCache = new ModelCache();

export type FlyToOption = {
    longitude: number,
    latitude: number,
    height: number,
    heading?: number,
    pitch?: number,
    roll?: number,
    duration: number
}

/**
 * 初始化Cesium查看器
 * @param container DOM容器ID
 * @returns Cesium.Viewer实例
 */
export function initViewer(container: string) {
    console.time('地图初始化');
    console.log('📊 开始初始化地图...');
    
    // 创建高德地图图层提供者（作为备选）
    console.log('📶 准备底图服务...');
    let imageProvider = new AmapImageryProvider(AmapImageryProviderOptions)
    
    // 创建默认地形提供者（简单地形，快速加载）
    console.log('🏔️ 准备基础地形...');
    // 使用支持高程查询的地形提供者
    const terrainProvider_default = Cesium.Terrain.fromWorldTerrain({
        requestWaterMask: false,
        requestVertexNormals: false
    });

    // 获取地图服务基础URL
    const mapServiceUrl = getMapServiceUrl();
    console.log('🗺️ 地图服务基础URL:', mapServiceUrl);
    
    // 根据配置选择图层提供者
    let imageryProviderToUse;
    
    // 根据useLocalMapDataFlag选择图层
    if (useLocalMapDataFlag === 0) {
        // 使用在线地图 (Cesium默认或高德)
        console.log('📡 使用在线地图服务');
        imageryProviderToUse = imageProvider;
    } else {
        // 尝试使用GeoServer地图，如果失败则回退到高德
        console.log('💻 使用本地GeoServer地图服务');
        imageryProviderToUse = createOptimalImageryProvider(mapServiceUrl, imageProvider as any) as Cesium.ImageryProvider;
    }
    
    console.log('🚀 创建Cesium Viewer实例...');
    
    // 创建Cesium viewer
    const viewer = new Cesium.Viewer(container, {
        animation: false,                // 动画控制不显示
        timeline: true,                  // 时间线显示
        baseLayerPicker: false,          // 右上角图层选择控件
        geocoder: false,                 // 搜索框
        homeButton: false,               // 视角返回初始位置
        navigationHelpButton: false,     // 导航帮助
        sceneModePicker: false,            // 模式切换按钮
        fullscreenButton: false,         // 全屏按钮不显示
        infoBox: false,                  // 信息框不显示
        terrain: terrainProvider_default, // 使用支持高程查询的地形
        selectionIndicator: false,       // 选中实体出现绿色选择框
        shouldAnimate: true,             // 允许动画运行
    })
    viewer.imageryLayers.removeAll();
    viewer.imageryLayers.addImageryProvider(imageryProviderToUse as Cesium.ImageryProvider);
    terrainProvider_default.readyEvent.addEventListener((provider: any) => {
        viewer.scene.globe.enableLighting = true;
        // const terrainProvider = viewer.scene.terrainProvider
        // const terrainProviderEdit = new CesiumTerrainProviderEdit(terrainProvider)
        // console.log('terrainProviderEdit:', terrainProviderEdit)
        terrainProvider_default.provider.errorEvent.addEventListener((error: any) => {
          alert(`Encountered an error while loading terrain tiles! ${error}`);
        });
      });
    // 隐藏Cesium版权信息
    const creditContainer = viewer.cesiumWidget.creditContainer as HTMLElement;
    if (creditContainer) {
        creditContainer.style.display = 'none';
    }
    
    // 启用地形遮挡
    viewer.scene.globe.depthTestAgainstTerrain = true
    
    // 隐藏太阳和月亮
    viewer.scene.sun.show = false
    viewer.scene.moon.show = false
    
    // 显示FPS
    viewer.scene.debugShowFramesPerSecond = true
    
    // 设置太阳光源位置
    const start = Cesium.JulianDate.fromDate(new Date(2023, 8, 1, 12));
    viewer.clock.currentTime = start.clone();
    
    // 隐藏时间轴
    (viewer.timeline.container as HTMLElement).style.display = 'none'
    
    // 初始设置半透明效果为关闭
    viewer.scene.globe.translucency.enabled = false;
    
    // 禁止相机视角进入地下
    viewer.scene.screenSpaceCameraController.enableCollisionDetection = true;
    
    // 开启渲染优化
    viewer.scene.requestRenderMode = true;
    viewer.scene.maximumRenderTimeChange = Infinity;
    // viewer.scene.globe.show = false; // 关闭地球模型 用于调试
    
    console.log('✅ Cesium Viewer创建完成');
    
    // 设置合适的初始位置 - 减少初始地图请求
    console.log('🎯 设置初始视图位置...');
    viewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(114.44, 22.57, 5000),
        orientation: {
            heading: Cesium.Math.toRadians(0.0),
            pitch: Cesium.Math.toRadians(-45),
            roll: 0.0
        }
    });
    
    // 优化地图加载策略 - 限制同时请求的瓦片数量
    console.log('⚙️ 优化地图加载策略...');
    if (viewer.scene.globe.imageryLayers && 
        viewer.scene.globe.imageryLayers.length > 0 &&
        viewer.scene.globe.imageryLayers.get(0).imageryProvider) {
        
        try {
            // 限制同时加载的瓦片数
            viewer.scene.globe.maximumScreenSpaceError = 2; // 提高精度阈值，减少瓦片请求
            viewer.scene.globe.tileCacheSize = 1000; // 增加缓存大小
            
            // 添加默认错误处理，避免控制台大量日志
            const baseLayer = viewer.scene.globe.imageryLayers.get(0);
            if (baseLayer && baseLayer.imageryProvider) {
                baseLayer.imageryProvider.errorEvent.addEventListener(function() {
                    // 静默处理错误
                    return true;
                });
            }
        } catch (e) {
            console.warn('优化地图加载策略时发生错误:', e);
        }
    }

    console.log('🕒 计划资源延迟加载...');
    
    // 延迟加载详细地形 - 让界面先展示出来
        let terrainLoadTimeout: number | null = null;
    terrainLoadTimeout = setTimeout(async () => {
        try {
            console.log('🏔️ 加载详细地形数据...');
            if (useLocalMapDataFlag != 0) {
                let dem_url = MODEL_STATIC_URL+"/DEM/";
                
                // 创建支持高程查询的地形提供者
                const provider = await Cesium.createWorldTerrainAsync({
                    requestWaterMask: false,  // 关闭水面效果，提高性能
                    requestVertexNormals: true // 保留法线，提高地形渲染质量
                });
                
                // 当地形提供者准备好后再赋值给viewer
                console.log('本地地形数据准备就绪');
                viewer.scene.terrainProvider = provider;
                console.log('✅ 本地详细地形数据已应用');
                // 手动触发一次渲染更新
                viewer.scene.requestRender();
            }
        } catch (error) {
            console.error('加载详细地形失败:', error);
            console.log('保持使用默认全球地形');
        }
    }, 2000); // 延迟2秒加载详细地形

    // 延迟应用变电站地形压平 - 在地形加载完成后执行
        let flattenTimeout: number | null = null;
    flattenTimeout = setTimeout(async () => {
        try {
            console.log('🏗️ 应用变电站地形压平...');
            // const result = await flattenTerrainForSubstation1(viewer);
            // if (result) {
            //     console.log('✅ 变电站1地形压平应用成功');
            // } else {
            //     console.warn('⚠️ 变电站1地形压平应用失败');
            // }
        } catch (error) {
            console.error('❌ 变电站1地形压平过程发生错误:', error);
        }
    }, 4000); // 延迟4秒执行，确保地形加载完成
    
    // 添加超时清理
    viewer.scene.postRender.addEventListener(() => {
        // 注意: 这是一个事件监听器，会在每帧执行
        if (terrainLoadTimeout) {
            clearTimeout(terrainLoadTimeout);
            terrainLoadTimeout = null;
        }
        if (flattenTimeout) {
            clearTimeout(flattenTimeout);
            flattenTimeout = null;
        }
    });

    console.timeEnd('地图初始化');
    console.log('✅ 地图初始化完成!');
    
    return viewer
}

/**
 * 创建GeoServer图层提供者
 * @param mapServiceUrl 地图服务URL
 * @returns 图层提供者
 */
function createWmtsImageryProvider(mapServiceUrl: string) {
    console.log('🌐 构建GeoServer WMTS图层提供者...');
    console.time('WMTS图层创建');
    
    try {
        // 定义图层参数
        const layer = 'localMap1:map1_WGS84_L04';
        const style = '';
        const format = 'image/png';
        const tileMatrixSetID = 'EPSG:4326';
        
        // 优化级别限制 - 减少不必要的瓦片请求
        const minimumLevel = 4;  // 直接从服务器支持的最小级别开始
        const maximumLevel = 16; // 限制最大级别，避免请求过高分辨率瓦片
        
        // 图层名称中的L04表示服务器实际从级别4开始有数据
        const serverMinLevel = 4; // 根据L04推断的服务器实际最小级别
        
        console.log('📊 WMTS图层级别范围:', minimumLevel, '至', maximumLevel);
        
        // 构建优化版URL模板 - 使用KVP模式
        const urlTemplate = `${mapServiceUrl}/gwc/service/wmts?` +
            'SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0' +
            `&LAYER=${layer}&STYLE=${style}` +
            `&TILEMATRIXSET=${tileMatrixSetID}` +
            `&TILEMATRIX=${tileMatrixSetID}:{z}` +
            '&TILEROW={y}&TILECOL={x}' +
            `&FORMAT=${format}`;
        
        console.log('🔗 WMTS请求URL模板:', urlTemplate);
        
        // 创建图层提供者 - 使用优化配置
        const wmtsProvider = new Cesium.UrlTemplateImageryProvider({
            url: urlTemplate,
            minimumLevel: minimumLevel, // 直接从服务器支持的最小级别开始
            maximumLevel: maximumLevel, // 限制最大级别，避免请求过高分辨率瓦片
            // 精确设置覆盖区域 - 仅请求需要的地区瓦片
            rectangle: Cesium.Rectangle.fromDegrees(113.0, 21.0, 116.0, 24.0), // 缩小范围到广东区域
            tilingScheme: new Cesium.GeographicTilingScheme(),
            // 提高性能的附加设置
            tileWidth: 256,
            tileHeight: 256,
            enablePickFeatures: false, // 禁用拾取功能，提高性能
            subdomains: '0123', // 利用子域名并行加载
            credit: new Cesium.Credit('GeoServer本地地图')
        });
        
        console.log('⚙️ 配置WMTS瓦片请求处理...');
        
        // 重要优化：覆盖requestImage函数，增加缓存和错误处理
        const tileCache: { [key: string]: any } = {}; // 简单的内存缓存
        const originalRequestImage = wmtsProvider.requestImage;
        
        wmtsProvider.requestImage = function(this: Cesium.UrlTemplateImageryProvider, x: number, y: number, level: number) {
            // 缓存键
            const cacheKey = `${level}_${x}_${y}`;
            
            // 1. 级别过滤 - 跳过无效级别请求
            if (level < serverMinLevel) {
                return Promise.resolve(undefined);
            }
            
            // 2. 范围过滤 - 跳过明显超出范围的请求
            // 基于预设的范围计算瓦片边界
            if (level > 12) { // 高级别时才进行细粒度过滤
                const tilingScheme = this.tilingScheme;
                const tileRect = tilingScheme.tileXYToRectangle(x, y, level);
                const degrees = Cesium.Math.toDegrees;
                
                // 转换为经纬度检查是否在广东省范围内
                const west = degrees(tileRect.west);
                const east = degrees(tileRect.east);
                const south = degrees(tileRect.south);
                const north = degrees(tileRect.north);
                
                // 广东省大致范围
                if (east < 110 || west > 118 || north < 20 || south > 26) {
                    return Promise.resolve(undefined);
                }
            }
            
            // 3. 检查缓存 - 增加缓存大小到2000个瓦片
            if (tileCache[cacheKey]) {
                return Promise.resolve(tileCache[cacheKey]);
            }
            
            // 4. 发起实际请求
            const requestFunc = originalRequestImage as (x: number, y: number, level: number, request?: any) => Promise<any> | undefined;
            if (typeof requestFunc === 'function') {
                const promise = requestFunc.call(this, x, y, level);
                if (promise) {
                    return promise
                        .then((image: any) => {
                            // 缓存结果 - 最多存储2000个瓦片（增加缓存容量）
                            if (Object.keys(tileCache).length < 2000) {
                                tileCache[cacheKey] = image;
                            }
                            return image;
                        })
                        .catch((error: any) => {
                            // 静默处理特定错误，但在控制台记录
                            console.debug(`WMTS瓦片加载失败 [${level}/${x}/${y}]:`, error);
                            return undefined;
                        });
                }
            }
            return Promise.resolve(undefined);
        };
        
        // 添加错误处理 - 完全屏蔽错误事件
        wmtsProvider.errorEvent.addEventListener(function(error: any) {
            // 静默处理所有错误，返回true表示错误已处理
            return true;
        });
        
        console.timeEnd('WMTS图层创建');
        console.log('✅ GeoServer WMTS图层创建成功');
        return wmtsProvider;
   } catch (error) {
         console.timeEnd('WMTS图层创建');
         console.error('❌ 创建GeoServer WMTS图层失败:', error);
     throw error;
   }
}

/**
 * 创建最优的图层提供者
 * @param mapServiceUrl 地图服务URL
 * @param fallbackProvider 备选图层提供者
 * @returns 图层提供者
 */
function createOptimalImageryProvider(mapServiceUrl: string, fallbackProvider: Cesium.ImageryProvider) {
    console.log('正在创建图层提供者，地图服务URL:', mapServiceUrl);
    
    try {
        // 尝试创建WMTS图层
        console.log('正在尝试创建GeoServer WMTS图层...');
        return createWmtsImageryProvider(mapServiceUrl);
    } catch (error) {
        console.error('WMTS图层创建失败，使用高德地图作为备选:', error);
        return fallbackProvider;
    }
}

export function flyTo(viewer: any, options: FlyToOption, callback = function () { }) {
    const camera = viewer.scene.camera
    return new Promise((resolve, reject) => {
        camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(options.longitude, options.latitude, options.height),
            duration: options.duration,
            complete: () => {
                callback()
                resolve(true)
            },
            orientation: {
                heading: options.heading,
                pitch: options.pitch,
                roll: options.roll,
            }
        })
    })
}

export function clickGetPoint(viewer: any,myCommandManager:CommandManager) {
    // 点击地图 输出具体经纬度
    const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)
    handler.setInputAction((movement: Cesium.ScreenSpaceEventHandler.MotionEvent) => {
        // 点击地图获取经纬度
        let position = viewer.scene.pickPosition(movement.endPosition)
        position = Cesium.Cartographic.fromCartesian(position)
        console.log('click point:', [(position.longitude / Math.PI) * 180, (position.latitude / Math.PI) * 180, position.height])
        // console.log('click point2:', position)
        // 点击模型获取id
        const pick = viewer.scene.pick(movement.endPosition)
        if (Cesium.defined(pick)) {
           if(pick.id.billboard){
             // 用户点击vr广告牌 进行页面跳转
             let userMsg = pick.id.userMsg // 创建广告牌对象时传递的用户自定义信息
             let userName = pick.id.userName
             console.log('用户点击vr广告牌，进行页面跳转：', userMsg, userName)
             window.open(userMsg, userName)
           }else{
             const modelID = pick.id.id;
             console.log('pick: ', modelID)
             myCommandManager.sendMessageToUE({ id: "FFFFFFFF", event: "click", objectType: "TOWER",objectId:modelID })
           }
         }
        // 点击输出当前相机的各个参数
        const camera = viewer.camera
        const cameraPosition = Cesium.Cartographic.fromCartesian(camera.position)
        // console.log('camera-position:', camera.position)
        console.log('camera-position:', [(cameraPosition.longitude / Math.PI) * 180, (cameraPosition.latitude / Math.PI) * 180, cameraPosition.height])
        console.log('camera-pitch-heading :', camera.pitch, camera.heading)
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
}

export function setTimeline(viewer: any, start: Date, stop: Date) {
    const startTime = Cesium.JulianDate.fromDate(start)
    const stopTime = Cesium.JulianDate.fromDate(stop)
    viewer.clock.startTime = startTime.clone()
    viewer.clock.stopTime = stopTime.clone()
    viewer.clock.currentTime = startTime.clone()
    viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP
    viewer.timeline.zoomTo(startTime, stopTime)
}

/**
 * 异步创建模型实体
 * @param modelInfo 模型信息
 * @param color 颜色
 * @returns Promise<Cesium.Entity>
 */
export interface ModelInfo {
    url: string;
    position: number[] | { longitude: number; latitude: number; height: number };
    scale?: number;
    heading?: number;
    pitch?: number;
    roll?: number;
    id: string;
    name?: string;
    description?: string;
    properties?: any;
    angle?: number;
    userData?: any;
    procedureStatus?: any;
    show?: boolean;
}

export async function createEntity_glb(modelInfo: ModelInfo, color = '#dfe0e2') {
    return new Promise((resolve, reject) => {
        // 获取模型数据
        localModelCache.getOneModel(modelInfo.url)
            .then((modelData) => {
                if (!modelData) {
                    console.warn(`模型数据加载失败，使用占位符: ${modelInfo.url}`);
                    
                    // 创建占位符实体
                    const placeholder = createPlaceholderEntity(modelInfo);
                    resolve(placeholder);
                    return;
                }

                try {
                    // 生成内存 URL
                    const modelUrl = URL.createObjectURL(new Blob([modelData]));

                    // 获取位置和旋转信息
                    let modelPosition;
                    if (Array.isArray(modelInfo.position)) {
                        modelPosition = Cesium.Cartesian3.fromDegrees(modelInfo.position[0], modelInfo.position[1], modelInfo.position[2]);
                    } else {
                        modelPosition = Cesium.Cartesian3.fromDegrees(modelInfo.position.longitude, modelInfo.position.latitude, modelInfo.position.height);
                    }
                    const hpr = new Cesium.HeadingPitchRoll(modelInfo.angle || 0, 0.0, 0.0); // 旋转角 俯仰角 翻滚角
                    const orientation = Cesium.Transforms.headingPitchRollQuaternion(modelPosition, hpr);

                    // 创建实体对象属性
                    const glbModel = {
                        id: modelInfo.id,
                        name: modelInfo.name,
                        position: modelPosition,
                        orientation: orientation,
                        model: {
                            uri: modelUrl,
                            scale: modelInfo.scale,
                            color: Cesium.Color.fromCssColorString(color),
                            colorBlendMode: Cesium.ColorBlendMode.MIX,
                        },
                        properties: modelInfo.properties,
                        userData: {
                            position: modelPosition,
                            angle: modelInfo.angle,
                            procedureStatus: modelInfo.procedureStatus,
                        },
                    };

                    const entity = new Cesium.Entity(glbModel);

                    // 返回实体
                    resolve(entity);
                } catch (error) {
                    console.error('创建实体过程中发生错误:', error);
                    
                    // 创建占位符实体作为备选
                    const placeholder = createPlaceholderEntity(modelInfo);
                    resolve(placeholder);
                }
            })
            .catch((error) => {
                console.error('模型加载过程中发生错误:', error);
                
                // 创建占位符实体作为备选
                const placeholder = createPlaceholderEntity(modelInfo);
                resolve(placeholder);
            });
    });
}

// 重写模型加载方法 适配cesium 117版本 使用Cesium.Model.fromGltfAsync 创建模型
export async function createEntity_glb_v2(modelInfo: ModelInfo, color = '#dfe0e2') {
    console.log('createEntity_glb_v2', modelInfo)   
    return new Promise((resolve, reject) => {
        // 获取模型数据
        localModelCache.getOneModel(modelInfo.url)
            .then(async (modelData) => {
                if (!modelData) {
                    console.warn(`模型数据加载失败，使用占位符: ${modelInfo.url}`);

                    // 创建占位符实体
                    const placeholder = createPlaceholderEntity(modelInfo);
                    resolve(placeholder);
                    return;
                }

                try {
                    // 生成内存 URL
                    const modelUrl = URL.createObjectURL(new Blob([modelData]));

                    // 获取位置和旋转信息
                    let modelPosition;
                    if (Array.isArray(modelInfo.position)) {
                        modelPosition = Cesium.Cartesian3.fromDegrees(modelInfo.position[0], modelInfo.position[1], modelInfo.position[2]);
                    } else {
                        modelPosition = Cesium.Cartesian3.fromDegrees(modelInfo.position.longitude, modelInfo.position.latitude, modelInfo.position.height);
                    }
                    const hpr = new Cesium.HeadingPitchRoll(modelInfo.angle || 0, 0.0, 0.0); // 旋转角 俯仰角 翻滚角
                    const orientation = Cesium.Transforms.headingPitchRollQuaternion(modelPosition, hpr);
                    const fixedFrameTransform = Cesium.Transforms.localFrameToFixedFrameGenerator(
                        "north",
                        "west"
                    );
                    const model = await Cesium.Model.fromGltfAsync({
                        id: modelInfo.id,
                        url: modelUrl,
                        modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
                            modelPosition,
                            hpr,
                            Cesium.Ellipsoid.WGS84,
                            // fixedFrameTransform
                        ),
                        show: modelInfo.show || true,
                        scale: modelInfo.scale || 1.0,
                        // minimumPixelSize: config.minimumPixelSize === undefined ? 1 : config.minimumPixelSize, // 默认最小像素大小
                        // maximumScale: config.maximumScale || 20000, // 默认最大缩放比例
                        // incrementallyLoadTextures: config.incrementallyLoadTextures === undefined ? true : config.incrementallyLoadTextures,
                        // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, VISIBILITY_RANGES.FAR * 1.2), // 比最远可见范围稍大一点
                        // silhouetteColor: Cesium.Color.RED,
                        // silhouetteSize: 2.0,
                        color: Cesium.Color.fromCssColorString(color),
                        colorBlendMode: Cesium.ColorBlendMode.MIX, // Cesium.ColorBlendMode.HIGHLIGHT, // 
                        // clampAnimations: config.clampAnimations === undefined ? true : config.clampAnimations, // 动画播放完毕后停在最后一帧
                        // debugShowBoundingVolume: config.debugShowBoundingVolume || false,
                        // // shadows: Cesium.ShadowMode.ENABLED, // 开启阴影
                        // ...(config.cesiumModelOptions || {}), // 允许传入更多Cesium.Model构造参数
                    });
            
                    resolve(model);
                } catch (error) {
                    console.error('创建实体过程中发生错误:', error);

                    // 创建占位符实体作为备选
                    const placeholder = createPlaceholderEntity(modelInfo);
                    resolve(placeholder);
                }
            })
            .catch((error) => {
                console.error('模型加载过程中发生错误:', error);

                // 创建占位符实体作为备选
                const placeholder = createPlaceholderEntity(modelInfo);
                resolve(placeholder);
            });
    });
}
/**
 * 为加载失败的模型创建占位符实体
 * @param modelInfo 模型信息
 * @returns Cesium.Entity
 */
function createPlaceholderEntity(modelInfo: ModelInfo) {
    try {
        let position;
        if (Array.isArray(modelInfo.position)) {
            position = Cesium.Cartesian3.fromDegrees(modelInfo.position[0], modelInfo.position[1], modelInfo.position[2]);
        } else {
            position = Cesium.Cartesian3.fromDegrees(modelInfo.position.longitude, modelInfo.position.latitude, modelInfo.position.height);
        }
        const hpr = new Cesium.HeadingPitchRoll(modelInfo.angle || 0, 0.0, 0.0);
        const orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);
        
        // 根据模型ID判断类型
        const entityType = modelInfo.id.includes('tower') ? 'tower' : 
                         modelInfo.id.includes('insulator') ? 'insulator' : 'unknown';
        
        // 创建不同类型的占位符
        let placeholder;
        
        switch (entityType) {
            case 'tower':
                // 为杆塔创建柱体占位符
                // placeholder = new Cesium.Entity({
                //     id: modelInfo.id,
                //     name: modelInfo.name + ' (占位符)',
                //     position: position,
                //     cylinder: {
                //         length: 20,
                //         topRadius: 0.5,
                //         bottomRadius: 1.5,
                //         material: Cesium.Color.GRAY.withAlpha(0.7),
                //         outline: true,
                //         outlineColor: Cesium.Color.BLACK
                //     },
                //     properties: modelInfo.properties,
                //     userData: modelInfo.userData || {}
                // });
                placeholder = null
                break;
                
            case 'insulator':
                // 为绝缘子创建小球占位符
                // placeholder = new Cesium.Entity({
                //     id: modelInfo.id,
                //     name: modelInfo.name + ' (占位符)',
                //     position: position,
                //     ellipsoid: {
                //         radii: new Cesium.Cartesian3(1.0, 1.0, 2.0),
                //         material: Cesium.Color.LIGHTSTEELBLUE.withAlpha(0.7),
                //         outline: true,
                //         outlineColor: Cesium.Color.DARKBLUE
                //     },
                //     properties: modelInfo.properties,
                //     userData: modelInfo.userData || {}
                // });
                placeholder = null
                break;
                
            default:
                // 通用占位符 - 使用小方框
                placeholder = new Cesium.Entity({
                    id: modelInfo.id,
                    name: modelInfo.name + ' (占位符)',
                    position: position,
                    box: {
                        dimensions: new Cesium.Cartesian3(2.0, 2.0, 2.0),
                        material: Cesium.Color.YELLOW.withAlpha(0.7),
                        outline: true,
                        outlineColor: Cesium.Color.BLACK
                    },
                    properties: { ...modelInfo.properties, userData: modelInfo.userData || {} },
                });
        }
        
        return placeholder;
    } catch (error) {
        console.error('创建占位符实体时发生错误:', error);
        
        // 创建最简单的点实体作为最终备选
        return new Cesium.Entity({
            id: modelInfo.id,
            name: modelInfo.name + ' (点)',
            position: Array.isArray(modelInfo.position) ? Cesium.Cartesian3.fromDegrees(modelInfo.position[0], modelInfo.position[1], modelInfo.position[2]) : Cesium.Cartesian3.fromDegrees(modelInfo.position.longitude, modelInfo.position.latitude, modelInfo.position.height),
            point: {
                pixelSize: 10,
                color: Cesium.Color.RED
            }
        });
    }
}

/**
 * 获取单个点的地形高程 - 针对少量点的高效查询
 * @param viewer Cesium查看器
 * @param cartographic 要查询的点的笛卡尔坐标
 * @returns 高程值
 */
export async function getTerrainHeight(viewer: any, cartographic: any): Promise<number> {
    try {
        // 检查viewer和地形提供者是否可用
        if (!viewer || !viewer.scene || !viewer.scene.terrainProvider) {
            console.warn('地形提供者不可用，返回默认高度0');
            return 0;
        }
        
        // 首先尝试使用globe.getHeight - 最快的方法，但不是所有地形都支持
        if (viewer.scene.globe && typeof viewer.scene.globe.getHeight === 'function') {
            const height = viewer.scene.globe.getHeight(cartographic);
            if (height !== undefined && height !== null) {
                return height;
            }
        }
        
        // 然后尝试使用sampleHeight - 次快的方法，但精度可能较低
        if (viewer.scene.sampleHeight) {
            try {
                const height = viewer.scene.sampleHeight(cartographic);
                if (height !== undefined && height !== null) {
                    return height;
                }
            } catch (e) {
                // sampleHeight可能会失败，继续尝试其他方法
            }
        }
        
        // 最后使用sampleTerrain - 最准确但也是最慢的方法
        const terrainProvider = viewer.scene.terrainProvider;
        
        // 使用Promise包装异步操作
        return new Promise((resolve, reject) => {
            const level = 12; // 使用级别12提供合理的精度和性能平衡
            
            try {
                Cesium.sampleTerrainMostDetailed(terrainProvider, [cartographic])
                    .then((updatedCartographics: any) => {
                        if (updatedCartographics[0].height !== undefined) {
                            resolve(updatedCartographics[0].height);
                        } else {
                            console.warn('无法获取地形高度，使用默认值0');
                            resolve(0);
                        }
                    })
                    .catch((error: any) => {
                        console.error('查询地形高度时出错:', error);
                        resolve(0); // 失败时返回0而不是拒绝promise
                    });
            } catch (error) {
                console.error('初始化地形查询时出错:', error);
                resolve(0);
            }
        });
    } catch (error) {
        console.error('getTerrainHeight发生未处理错误:', error);
        return 0;
    }
}

// 优化批量高程查询函数
export async function getPositionHeight(viewer: any, positions: number[][], maxConcurrent = 100): Promise<any[]> {
    try {
        // 创建高程查询缓存
        const heightCache = new Map();
        const results: any[] = [];
        
        // 将位置数组转换为Cartographic对象数组
        const cartographics = positions.map((position, index) => {
            const cartographic = Cesium.Cartographic.fromDegrees(position[0], position[1]);
            // 存储原始索引，以便之后能够重建正确顺序
            (cartographic as any).originalIndex = index;
            return cartographic;
        });
        
        // 检查是否有已缓存的位置
        const uncachedCartographics = [];
        for (const cartographic of cartographics) {
            const posKey = `${cartographic.longitude.toFixed(6)},${cartographic.latitude.toFixed(6)}`;
            if (heightCache.has(posKey)) {
                results[(cartographic as any).originalIndex] = { height: heightCache.get(posKey) };
            } else {
                uncachedCartographics.push(cartographic);
            }
        }
        
        // 如果所有点都已缓存，立即返回结果
        if (uncachedCartographics.length === 0) {
            return results;
        }
        
        // 处理未缓存的点
        console.log(`批量处理 ${uncachedCartographics.length} 个未缓存高程点...`);
        
        // 批量查询地形高度 - 限制批次大小以避免内存问题
        const batchSize = Math.min(uncachedCartographics.length, maxConcurrent);
        const batches = [];
        
        for (let i = 0; i < uncachedCartographics.length; i += batchSize) {
            const batch = uncachedCartographics.slice(i, i + batchSize);
            batches.push(batch);
        }
        
        console.log(`将 ${uncachedCartographics.length} 个点分为 ${batches.length} 批`);
        
        // 依次处理每个批次
        for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];
            
            try {
                // 使用地形采样API来获取高度
                await Cesium.sampleTerrainMostDetailed(viewer.scene.terrainProvider, batch);
                
                // 处理结果
                for (const updatedCartographic of batch) {
                    const index = (updatedCartographic as any).originalIndex;
                    const height = updatedCartographic.height !== undefined ? updatedCartographic.height : 0;
                    
                    // 更新缓存
                    const posKey = `${updatedCartographic.longitude.toFixed(6)},${updatedCartographic.latitude.toFixed(6)}`;
                    heightCache.set(posKey, height);
                    
                    // 更新结果数组
                    results[index] = { height };
                }
                
                // 进度日志
                if (batches.length > 1) {
                    console.log(`完成批次 ${i+1}/${batches.length}`);
                }
            } catch (error) {
                console.error(`批次 ${i+1} 查询地形高度失败:`, error);
                
                // 为失败的批次填充默认高度
                for (const cartographic of batch) {
                    const index = (cartographic as any).originalIndex;
                    results[index] = { height: 0 };
                }
            }
        }
        
        // 确保所有结果都已填充
        for (let i = 0; i < positions.length; i++) {
            if (!results[i]) {
                results[i] = { height: 0 };
            }
        }
        
        // 返回与输入位置数组对应的高度数组
        return results;
    } catch (error) {
        console.error('批量获取地形高度时发生错误:', error);
        
        // 返回全部为0高度的数组
        return positions.map(() => ({ height: 0 }));
    }
}

/**
 * 根据地形设置飞行到杆塔的高度
 */
export async function flyToTower(viewer: any, options: FlyToOption, callback = function () { }) {
    const camera = viewer.scene.camera
    const position = [options.longitude, options.latitude]
    let height = options.height || 300 // 使用传入的高度或默认300米
    
    try {
        const positions_new = await getPositionHeight(viewer, [position])
        if (positions_new && positions_new[0] && typeof positions_new[0].height === 'number') {
            // 计算相对高度 - 地形高度 + 相对高度
            height = positions_new[0].height + 300
            console.log('计算得到的高度:', height, '(地形高度:', positions_new[0].height, '+300)');
        } else {
            console.warn('无法获取地形高度，使用默认高度:', height);
        }
    } catch (error) {
        console.error('获取地形高度失败，使用默认高度:', height, error);
    }
    
    return new Promise((resolve, reject) => {
        camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(options.longitude, options.latitude, height),
            duration: options.duration,
            complete: () => {
                callback()
                resolve(true)
            },
            orientation: {
                heading: options.heading,
                pitch: options.pitch,
                roll: options.roll,
            }
        })
    })
}

/**
 * 创建线集合
 * @param viewer Cesium查看器
 * @returns Cesium.PolylineCollection
 */
export function createLineCollection (viewer: Cesium.Viewer): Cesium.PolylineCollection {
    let polylineCollection = new Cesium.PolylineCollection()
    

    viewer.scene.primitives.add(polylineCollection);
    return polylineCollection;
}

// 新增销毁线段集合的方法
export function destroyLineCollection(viewer: Cesium.Viewer, polylineCollection: Cesium.PolylineCollection) {
  if (!polylineCollection || polylineCollection.isDestroyed()) {
      console.warn('线段集合不存在或已被销毁');
      return;
  }

  try {
      // 从场景中移除并销毁
      viewer.scene.primitives.remove(polylineCollection);
      console.log('线段集合已成功销毁');
  } catch (error) {
      console.error('销毁线段集合时发生错误:', error);
  }
}