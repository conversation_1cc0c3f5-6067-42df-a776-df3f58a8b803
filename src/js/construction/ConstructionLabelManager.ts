import * as Cesium from 'cesium';

export interface LabelConfig {
    id: string;
    name: string;
    coordinates: number[];
    description?: string;
    style?: {
        font?: string;
        scale?: number;
        color?: string;
        backgroundColor?: string;
    };
}

export class ConstructionLabelManager {
    private viewer: any;
    private labels: Map<string, any> = new Map();
    private readonly DEFAULT_STYLE = {
        font: '18px sans-serif',
        scale: 1.0,
        color: 'rgba(255, 255, 255, 1)',
        backgroundColor: 'rgba(64, 158, 255, 0.9)'
    };
    private readonly MIN_LABEL_SPACING = 100;
    private readonly LINE_HEIGHT = 24;
    // 遮挡检测相关变量
    private occlusionCheckEnabled: boolean = true;
    private occlusionFrameSkip: number = 10; // 每隔多少帧检查一次遮挡
    private currentFrame: number = 0;

    constructor(viewer: any) {
        this.viewer = viewer;
        // Add post-render event listener to check label overlaps
        this.viewer.scene.postRender.addEventListener(this.checkLabelOverlap.bind(this));
        // 添加遮挡检测的事件监听
        this.viewer.scene.postRender.addEventListener(this.checkLabelOcclusion.bind(this));
    }

    public loadLabels(configs: LabelConfig[]): void {
        // Clear existing labels first
        this.removeAllLabels();

        if (!configs || configs.length === 0) {
            console.warn('No label configurations provided');
            return;
        }

        // Sort configs by coordinates to maintain consistent ordering
        const sortedConfigs = [...configs].sort((a, b) => {
            if (a.coordinates[1] !== b.coordinates[1]) {
                return b.coordinates[1] - a.coordinates[1]; // Sort by latitude
            }
            return a.coordinates[0] - b.coordinates[0]; // Then by longitude
        });

        // Create entities for each config
        sortedConfigs.forEach((config, index) => {
            try {
                const entity = this.createLabelEntity(config, index);
                if (entity) {
                    this.viewer.entities.add(entity);
                }
            } catch (error) {
                console.error(`Failed to create label entity for config:`, config, error);
            }
        });
    }

    private createLabelEntity(config: LabelConfig, index: number): any {
        const style = { ...this.DEFAULT_STYLE, ...config.style };
        
        // Create position with height offset if no height provided
        const position = Cesium.Cartesian3.fromDegrees(
            config.coordinates[0],
            config.coordinates[1],
            config.coordinates[2] || (130 + (index * 20)) // Use height offset if no height provided
        );

        const groundPosition = Cesium.Cartesian3.fromDegrees(
            config.coordinates[0],
            config.coordinates[1],
            0
        );

        // Calculate canvas size based on content
        const { width, height } = this.calculateCanvasSize(config.name);
        
        // Create canvas with label content
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        if (!ctx) return null;

        // Draw background
        ctx.fillStyle = style.backgroundColor;
        this.drawRoundedRect(ctx, 10, 10, canvas.width - 20, canvas.height - 20, 12);
        ctx.fill();

        // Draw border
        ctx.beginPath();
        ctx.lineWidth = 2;
        ctx.strokeStyle = 'rgba(0, 255, 255, 0.8)';
        this.drawRoundedRect(ctx, 8, 8, canvas.width - 16, canvas.height - 16, 12);
        ctx.stroke();

        // Draw text
        ctx.fillStyle = style.color;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        this.wrapText(ctx, config.name, canvas.width / 2, canvas.height / 2, canvas.width - 60, this.LINE_HEIGHT);

        // Create entity with unique ID
        const entity = new Cesium.Entity({
            id: config.id,
            name: 'construction_label',
            position: position,
            billboard: {
                image: canvas,
                scaleByDistance: new Cesium.NearFarScalar(0, 1.0, 65000, 0.3),
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                // 移除 disableDepthTestDistance，允许深度测试
                disableDepthTestDistance: undefined,
                pixelOffset: new Cesium.Cartesian2(0, 0),
                show: true,
                // 添加透明度控制
                translucencyByDistance: new Cesium.NearFarScalar(0, 1.0, 10000, 1.0)
            },
            polyline: {
                positions: [position, groundPosition],
                width: 1,
                material: new Cesium.PolylineDashMaterialProperty({
                    color: Cesium.Color.AQUA,
                    dashLength: 8.0
                }),
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 15000.0)
            },
            // 添加属性用于遮挡检测
            properties: {
                isOccluded: false,
                occlusionAlpha: 1.0
            }
        });

        this.labels.set(config.id, entity);
        return entity;
    }

    private calculateCanvasSize(text: string): { width: number, height: number } {
        const parts = text.split('：');
        const baseWidth = 400;
        const minHeight = 100;
        const charPerLine = 24;
        
        let totalLines = 1;
        if (parts.length === 2) {
            totalLines = 1 + Math.ceil(parts[1].length / charPerLine);
        } else {
            totalLines = Math.ceil(text.length / charPerLine);
        }

        const height = Math.max(minHeight, totalLines * this.LINE_HEIGHT + 40);
        const width = text.length < 10 ? baseWidth * 0.75 : baseWidth;

        return { width, height };
    }

    private drawRoundedRect(ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, radius: number): void {
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
    }

    private wrapText(ctx: CanvasRenderingContext2D, text: string, x: number, y: number, maxWidth: number, lineHeight: number): void {
        const parts = text.split('：');
        
        if (parts.length === 2) {
            const title = parts[0] + '：';
            const content = parts[1];
            
            // 绘制标题
            ctx.font = '18px sans-serif';
            ctx.fillText(title, x, y - lineHeight);
            
            // 内容使用较小字体
            ctx.font = '16px sans-serif';
            const contentWords = content.split('');
            let line = '';
            let currentY = y;
            
            for (let i = 0; i < contentWords.length; i++) {
                const testLine = line + contentWords[i];
                const metrics = ctx.measureText(testLine);
                
                if (metrics.width > maxWidth - 60 && i > 0) {
                    ctx.fillText(line, x, currentY);
                    line = contentWords[i];
                    currentY += lineHeight;
                } else {
                    line = testLine;
                }
            }
            
            if (line.length > 0) {
                ctx.fillText(line, x, currentY);
            }
        } else {
            ctx.font = '16px sans-serif';
            const words = text.split('');
            let line = '';
            let currentY = y - lineHeight / 2;
            
            for (let i = 0; i < words.length; i++) {
                const testLine = line + words[i];
                const metrics = ctx.measureText(testLine);
                
                if (metrics.width > maxWidth - 60 && i > 0) {
                    ctx.fillText(line, x, currentY);
                    line = words[i];
                    currentY += lineHeight;
                } else {
                    line = testLine;
                }
            }
            
            if (line.length > 0) {
                ctx.fillText(line, x, currentY);
            }
        }
    }

    private checkLabelOverlap(): void {
        if (!this.viewer || !this.viewer.scene || !this.viewer.camera) {
            return;
        }

        const scene = this.viewer.scene;
        const camera = this.viewer.camera;
        const labelEntities = Array.from(this.labels.values());
        const currentTime = this.viewer.clock.currentTime;
        
        if (labelEntities.length === 0) {
            return;
        }

        // 获取所有标签的屏幕位置
        const labelPositions = labelEntities.map(entity => {
            if (!entity || !entity.position) {
                return null;
            }

            try {
                const position = entity.position.getValue(currentTime);
                if (!position) return null;

                // 使用正确的方法转换坐标
                const screenPos = scene.cartesianToCanvasCoordinates(position);
                if (!screenPos) return null;

                return {
                    entity: entity,
                    screenPos: screenPos
                };
            } catch (e) {
                console.warn('Failed to get screen position for label:', e);
                return null;
            }
        }).filter(pos => pos !== null);

        // 检查重叠并调整显示
        const canvasHeight = this.viewer.canvas.height;
        const canvasWidth = this.viewer.canvas.width;
        
        labelPositions.forEach(posInfo => {
            const pos = posInfo?.screenPos;
            const entity = posInfo?.entity;
            
            if (!pos || !entity) return;
            
            // 检查是否超出视口
            if (pos.x < 0 || pos.y < 0 || pos.x > canvasWidth || pos.y > canvasHeight) {
                // 超出视口范围，隐藏标签
                if (entity.billboard) {
                    entity.billboard.show = false;
                }
                if (entity.polyline) {
                    entity.polyline.show = false;
                }
                return;
            }
            
            // 在视口内，恢复显示（除非被遮挡）
            const isOccluded = entity.properties?.isOccluded?.getValue(currentTime);
            if (!isOccluded) {
                if (entity.billboard) {
                    entity.billboard.show = true;
                }
                if (entity.polyline) {
                    entity.polyline.show = true;
                }
            }
        });
    }

    // 新增遮挡检测方法
    private checkLabelOcclusion(): void {
        // 帧率控制，不是每一帧都执行以提高性能
        this.currentFrame++;
        if (!this.occlusionCheckEnabled || this.currentFrame % this.occlusionFrameSkip !== 0) {
            return;
        }

        if (!this.viewer || !this.viewer.scene || !this.viewer.camera) {
            return;
        }

        // 检查场景是否支持pickFromRay功能
        if (!this.viewer.scene.pickFromRay) {
            console.warn('pickFromRay not supported in this Cesium version. Disabling occlusion check.');
            this.occlusionCheckEnabled = false;
            return;
        }

        const scene = this.viewer.scene;
        const camera = this.viewer.camera;
        const labelEntities = Array.from(this.labels.values());
        const currentTime = this.viewer.clock.currentTime;
        
        if (labelEntities.length === 0) {
            return;
        }

        // 遍历所有标签进行遮挡检测
        labelEntities.forEach(entity => {
            if (!entity || !entity.position) {
                return;
            }

            try {
                const position = entity.position.getValue(currentTime);
                if (!position) return;

                // 创建从相机到标签位置的射线
                const direction = Cesium.Cartesian3.normalize(
                    Cesium.Cartesian3.subtract(position, camera.position, new Cesium.Cartesian3()),
                    new Cesium.Cartesian3()
                );
                
                // 计算相机到标签的距离
                const distance = Cesium.Cartesian3.distance(camera.position, position);
                
                // 射线拾取，检查是否有地形或3D物体阻挡
                const ray = new Cesium.Ray(camera.position, direction);
                
                // 排除当前实体本身
                const excludedObjects = [entity];
                
                // 执行射线检测
                let result;
                try {
                    result = scene.pickFromRay(ray, excludedObjects);
                } catch (error) {
                    console.warn('Error in pickFromRay:', error);
                    return; // 跳过此次检测
                }
                
                let isOccluded = false;
                let occlusionAlpha = 1.0;
                
                if (result && result.position) {
                    // 射线与物体相交点的距离
                    const hitDistance = Cesium.Cartesian3.distance(camera.position, result.position);
                    
                    // 如果射线与物体相交点的距离小于相机到标签的距离，表示标签被遮挡
                    if (hitDistance < distance * 0.95) { // 增加一点容差
                        isOccluded = true;
                        
                        // 计算遮挡透明度 - 数值越大越透明
                        const occlusionRatio = hitDistance / distance;
                        occlusionAlpha = Math.max(0.0, Math.min(0.4, occlusionRatio * 0.5));
                    }
                }
                
                // 更新实体属性
                if (entity.properties) { // 添加属性检查
                    entity.properties.isOccluded = isOccluded;
                    entity.properties.occlusionAlpha = occlusionAlpha;
                }
                
                // 更新标签显示状态
                if (entity.billboard) {
                    if (isOccluded) {
                        // 被遮挡时，可以选择完全隐藏
                        entity.billboard.show = false;
                    } else {
                        entity.billboard.show = true;
                        entity.billboard.color = Cesium.Color.WHITE;
                    }
                }
                
                if (entity.polyline) {
                    entity.polyline.show = !isOccluded;
                }
            } catch (e) {
                console.warn('Failed to check occlusion for label:', e);
            }
        });
    }

    // 启用或禁用遮挡检测
    public setOcclusionCheckEnabled(enabled: boolean): void {
        this.occlusionCheckEnabled = enabled;
    }

    // 控制遮挡检测的帧率
    public setOcclusionFrameSkip(skip: number): void {
        this.occlusionFrameSkip = Math.max(1, skip);
    }

    // 移除所有标签
    public removeAllLabels(): void {
        this.labels.forEach(entity => {
            if (this.viewer.entities.contains(entity)) {
                this.viewer.entities.remove(entity);
            }
        });
        this.labels.clear();
    }

    // 更新标签显示状态
    public updateLabelVisibility(show: boolean): void {
        this.labels.forEach(entity => {
            if (entity.billboard) {
                entity.billboard.show = show;
            }
            if (entity.polyline) {
                entity.polyline.show = show;
            }
        });
    }
}