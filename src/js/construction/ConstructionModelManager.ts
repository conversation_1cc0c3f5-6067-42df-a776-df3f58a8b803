// 移除 Cesium 导入，使用全局变量
declare const Cesium: any;

import { ModelResourceManager } from './ModelResourceManager'
import type { ModelConfig } from './ModelResourceManager'
import { getPositionHeight } from '@/js/common/viewer'
import { ModelConfigLoader } from './ModelConfigLoader'

/**
 * 施工模型管理器，负责施工模型的加载、显示和管理
 */
export class ConstructionModelManager {
    // 可见性范围 (单位：米)
    private static readonly VISIBILITY_RANGES = {
        NEAR: 500,    // 0.5km - 用于非常近距离的交互或细节显示
        MEDIUM: 1000, // 1km   - 中等距离范围
        FAR: 2000     // 2km   - 主要的可见性判断范围，模型在此距离内会尝试加载和显示
    };
    
    // private static readonly DISTANCE_THRESHOLD = 150; // 旧的相机移动阈值，不再使用

    private viewer: any;
    private resourceManager: ModelResourceManager;
    private configLoader: ModelConfigLoader;
    
    // 当前活跃的模型实例映射表, key是模型ID
    private activeModels: Map<string, any> = new Map();
    
    // 所有已加载的模型配置, key是配置源, value是配置数组
    private loadedConfigs: Map<string, ModelConfig[]> = new Map();
    
    // 跟踪正在加载的模型, 避免重复加载
    private loadingModels: Set<string> = new Set();
    
    // 所有模型配置
    private modelConfigs: ModelConfig[] = [];
    
    // 配置源映射
    private configSources: Map<string, string> = new Map();
    
    // 跟踪模型的可见性状态
    private modelVisibility: Map<string, boolean> = new Map();
    
    // 上次检查可见性的时间
    private lastVisibilityCheckTime: number = 0;

    // 用于存储事件取消订阅函数的数组
    private eventDisposers: Array<() => void> = [];
    
    // 加载队列
    private loadQueue: Array<{config: ModelConfig, priority: number}> = [];
    
    // 是否正在处理队列
    private isProcessingQueue: boolean = false;
    
    // 预加载的资源状态
    private resourcePreloadStatus: Map<string, boolean> = new Map();
    
    // 配置源统计
    private configSourceStats: Map<string, {total: number, loaded: number, visible: number}> = new Map();

    // 定时器引用
    private visibilityUpdateTimer: number | null = null;
    
    // 是否激活
    private isActive: boolean = false;

    // URL转换器，可由外部设置
    private modelUrlTransformer: ((url: string) => string) | null = null;

    // 新增: 解决 TS 错误
    private modelStatsBySourceFile: Map<string, { total: number, loaded: number }> = new Map();

    // 用于缓存 getTerrainHeightAsync 的结果，键为 "lon,lat"，值为 Promise<number>
    private asyncTerrainHeightCache: Map<string, Promise<number>> = new Map();
    // 用于缓存同步获取的地形高度，或异步获取成功后的高度值
    private terrainHeightCache: Map<string, number> = new Map();

    private terrainPointCache: Array<{lon: number, lat: number, height: number}> = [];
    private terrainPointCacheTimestamp: number = 0;
    private readonly TERRAIN_POINT_CACHE_TTL = 60000; // 地形点缓存1分钟 (ms)
    private readonly ASYNC_TERRAIN_CACHE_MAX_SIZE = 200; // 异步地形高度Promise缓存的最大数量
    private readonly TERRAIN_CACHE_MAX_SIZE = 500;       // 同步地形高度值缓存的最大数量

    // 相机事件处理节流定时器
    private cameraEventThrottleTimer: number | null = null;
    private readonly CAMERA_EVENT_THROTTLE_MS = 250; // 相机移动时，节流处理model visibility更新的毫秒数

    /**
     * 构造函数
     * @param viewer Cesium Viewer实例
     */
    constructor(viewer: any) {
        this.viewer = viewer;
        this.resourceManager = new ModelResourceManager(viewer);
        this.configLoader = new ModelConfigLoader();

        // 注册相机移动事件，用于更新模型可见性（使用节流处理）
        this.viewer.camera.changed.addEventListener(() => {
            if (!this.isActive) return;

            if (this.cameraEventThrottleTimer === null) { // 如果没有激活的定时器
                this.cameraEventThrottleTimer = window.setTimeout(() => {
                    // console.log('Camera changed (throttled), calling updateModelVisibility.');
                    this.updateModelVisibility(); // updateModelVisibility 内部有自己的500ms节流
                    this.cameraEventThrottleTimer = null; // 允许设置新的定时器
                }, this.CAMERA_EVENT_THROTTLE_MS);
            }
        });
        
        console.log('施工模型管理器初始化完成');
    }

    /**
     * 清理地形高度缓存中超出大小限制的条目
     */
    private trimTerrainCache(): void {
        // 清理 asyncTerrainHeightCache (基于插入顺序，简单LRU-like)
        if (this.asyncTerrainHeightCache.size > this.ASYNC_TERRAIN_CACHE_MAX_SIZE) {
            const keysToDelete = Array.from(this.asyncTerrainHeightCache.keys()).slice(0, this.asyncTerrainHeightCache.size - this.ASYNC_TERRAIN_CACHE_MAX_SIZE);
            keysToDelete.forEach(key => this.asyncTerrainHeightCache.delete(key));
            // console.debug(`Trimmed asyncTerrainHeightCache to ${this.asyncTerrainHeightCache.size}`);
        }

        // 清理 terrainHeightCache (基于插入顺序，简单LRU-like)
        if (this.terrainHeightCache.size > this.TERRAIN_CACHE_MAX_SIZE) {
            const keysToDelete = Array.from(this.terrainHeightCache.keys()).slice(0, this.terrainHeightCache.size - this.TERRAIN_CACHE_MAX_SIZE);
            keysToDelete.forEach(key => this.terrainHeightCache.delete(key));
            // console.debug(`Trimmed terrainHeightCache to ${this.terrainHeightCache.size}`);
        }
    }
    
    /**
     * 启动模型管理器
     * 激活定时检查和模型加载
     */
    public activate(): void {
        if (this.isActive) return;
        
        this.isActive = true;
        console.log('激活施工模型管理器');
        
        // 启动定期更新可见性的定时器
        this.startVisibilityTimer();
        
        // 立即执行一次可见性更新
        this.updateModelVisibility(true);
    }
    
    /**
     * 停用模型管理器
     * 停止定时检查
     */
    public deactivate(): void {
        if (!this.isActive) {
            // console.log('[ConstructionModelManager] Manager is already inactive.');
            return;
        }
        
        this.isActive = false;
        this.isProcessingQueue = false; // 重置队列处理状态
        this.loadQueue = []; // 清空加载队列
        this.resourcePreloadStatus.clear(); // 清空预加载状态，确保下次激活时能重新预加载
        console.log('[ConstructionModelManager] Deactivated. Load queue, processing state, and preload status reset.');
        
        // 停止定时器
        this.stopVisibilityTimer();
        
        // 移除通过 eventAggregator 订阅的事件监听器 (如果适用)
        if (this.eventDisposers.length > 0) { // Removed 'this.eventDisposers &&' check as it's now initialized

            this.eventDisposers.forEach(dispose => dispose());
            this.eventDisposers = [];
            console.log('[ConstructionModelManager] Disposed event listeners.');
        }
    }
    
    /**
     * 启动定期更新可见性的定时器
     * @private
     */
    private startVisibilityTimer(): void {
        // 确保之前的定时器已停止
        this.stopVisibilityTimer();
        
        // 启动新的定时器
        this.visibilityUpdateTimer = window.setInterval(() => {
            if (this.isActive) {
                this.updateModelVisibility(true);
            }
        }, 2000); // 每2秒检查一次
        
        console.log('已启动模型可见性定期更新定时器');
    }
    
    /**
     * 停止定期更新可见性的定时器
     * @private
     */
    private stopVisibilityTimer(): void {
        if (this.visibilityUpdateTimer !== null) {
            window.clearInterval(this.visibilityUpdateTimer);
            this.visibilityUpdateTimer = null;
            console.log('已停止模型可见性定期更新定时器');
        }
    }
    
    // calculateDistance 方法不再需要，因为 checkCameraMovement 被移除

    /**
     * 加载模型配置
     * @param configInput 可以是配置文件路径数组或直接的模型配置数组
     * @param sourceIdentifier 可选的配置来源标识符
     */
    async loadModels(configInput: string[] | ModelConfig[], sourceIdentifier?: string): Promise<boolean> {
        try {
            let configs: ModelConfig[] = [];
            
            // 处理不同类型的输入
            if (Array.isArray(configInput) && configInput.length > 0) {
                if (typeof configInput[0] === 'string') {
                    // 输入是文件路径数组
                    const configFiles = configInput as string[];
                    console.log(`🔄 开始加载模型配置文件: ${configFiles.join(', ')}`);
                    configs = await ModelConfigLoader.loadFromFiles(configFiles);
                } else {
                    // 输入是直接的模型配置数组
                    configs = configInput as ModelConfig[];
                    console.log(`🔄 使用直接提供的 ${configs.length} 个模型配置`);
                    
                    // 如果提供了来源标识符，将其添加到配置中
                    if (sourceIdentifier) {
                        configs.forEach(config => {
                            if (!config.sourceFile) {
                                config.sourceFile = sourceIdentifier;
                            }
                        });
                    }
                }
            } else {
                console.warn('❌ 无效的配置输入');
                return false;
            }
            
            // 确保每个配置都有唯一ID，特别是相同URL但位置不同的模型
            this.ensureUniqueModelIds(configs);
            
            // 保存配置
            this.modelConfigs = configs;
            
            if (!this.modelConfigs || this.modelConfigs.length === 0) {
                console.warn('❌ 没有找到有效的模型配置');
                return false;
            }
            
            console.log(`📋 总共读取了 ${this.modelConfigs.length} 个模型配置`);
            
            // 记录每个配置的来源
            this.modelConfigs.forEach(config => {
                if (config.sourceFile) {
                    // 从路径中提取文件名
                    const filename = config.sourceFile.split('/').pop() || config.sourceFile;
                    this.configSources.set(config.id, filename);
                    
                    // 初始化统计信息
                    const sourceStats = this.configSourceStats.get(filename) || { total: 0, loaded: 0, visible: 0 };
                    sourceStats.total++;
                    this.configSourceStats.set(filename, sourceStats);
                }
            });
            
            // 打印每个来源文件的模型数量
            console.log('📊 各配置文件中的模型数量:');
            this.configSourceStats.forEach((stats, source) => {
                console.log(`  - ${source}: ${stats.total} 个模型`);
            });
            
            // 记录每种模型URL的数量（用于识别重复模型）
            const modelUrlCount: Record<string, number> = {};
            this.modelConfigs.forEach(config => {
                if (config.url) {
                    modelUrlCount[config.url] = (modelUrlCount[config.url] || 0) + 1;
                }
            });
            
            // 打印每个模型URL的实例数量
            console.log("📊 模型URL实例数量:");
            Object.entries(modelUrlCount)
                .filter(([_, count]) => count > 1) // 只显示有多个实例的模型
                .forEach(([url, count]) => {
                    const shortUrl = url.split('/').pop() || url;
                    console.log(`  - ${shortUrl}: ${count} 个实例`);
                });
                
            // 预加载所有模型资源
            await this.preloadAllModelResources();
            
            // 立即检查哪些模型应该可见
            this.updateModelVisibility(true);
            
            return true;
        } catch (error) {
            console.error('❌ 加载模型失败:', error);
            return false;
        }
    }
    
    /**
     * 确保每个模型配置都有唯一的ID
     * 特别是相同URL但不同位置的模型
     */
    private ensureUniqueModelIds(configs: ModelConfig[]): void {
        const urlInstanceCount: Record<string, number> = {};
        
        for (const config of configs) {
            if (!config.id) {
                // 如果没有ID，根据URL和位置创建一个
                const urlBase = config.url.split('/').pop() || config.url;
                let positionStr = '';
                
                if (Array.isArray(config.position)) {
                    positionStr = `_${config.position[0]}_${config.position[1]}`;
                } else if (config.position) {
                    positionStr = `_${config.position.x}_${config.position.y}`;
                }
                
                // 生成ID
                config.id = `model_${urlBase}${positionStr}`;
            } else {
                // 检查相同URL的模型
                if (config.url in urlInstanceCount) {
                    urlInstanceCount[config.url]++;
                    
                    // 如果同一个URL有多个实例，且ID不包含位置信息，则添加位置
                    if (!config.id.includes('_pos_')) {
                        let positionStr = '';
                        
                        if (Array.isArray(config.position)) {
                            positionStr = `_pos_${config.position[0]}_${config.position[1]}`;
                        } else if (config.position) {
                            positionStr = `_pos_${config.position.x}_${config.position.y}`;
                        }
                        
                        // 添加位置信息和实例编号
                        config.id = `${config.id}${positionStr}_inst${urlInstanceCount[config.url]}`;
                    }
                } else {
                    urlInstanceCount[config.url] = 1;
                }
            }
        }
        
        // 检查ID冲突
        const idSet = new Set<string>();
        const idDuplicates = new Set<string>();
        
        // 找出所有重复的ID
        for (const config of configs) {
            if (idSet.has(config.id)) {
                idDuplicates.add(config.id);
            } else {
                idSet.add(config.id);
            }
        }
        
        // 处理重复的ID
        if (idDuplicates.size > 0) {
            console.warn(`⚠️ 发现 ${idDuplicates.size} 个重复的模型ID，正在修复...`);
            
            const idInstanceCount: Record<string, number> = {};
            
            for (const config of configs) {
                if (idDuplicates.has(config.id)) {
                    // 这是一个重复的ID，添加唯一后缀
                    const baseId = config.id;
                    idInstanceCount[baseId] = (idInstanceCount[baseId] || 0) + 1;
                    
                    // 添加位置信息作为唯一标识
                    let uniqueSuffix = '';
                    if (Array.isArray(config.position)) {
                        uniqueSuffix = `_${config.position[0]}_${config.position[1]}`;
                    } else if (config.position) {
                        uniqueSuffix = `_${config.position.x}_${config.position.y}`;
                    }
                    
                    // 如果位置相同，添加实例计数
                    config.id = `${baseId}${uniqueSuffix}_${idInstanceCount[baseId]}`;
                    console.log(`  - 重复ID "${baseId}" 已重命名为 "${config.id}"`);
                }
            }
        }
    }
    
    /**
     * 设置模型URL转换器
     * @param transformer URL转换函数
     */
    public setModelUrlTransformer(transformer: (url: string) => string): void {
        this.modelUrlTransformer = transformer;
        console.log('已设置自定义模型URL转换器');
    }
    
    /**
     * 转换模型URL
     * @param originalUrl 原始URL
     * @returns 转换后的URL
     */
    private getModelUrl(originalUrl: string): string {
        // 如果设置了自定义转换器，优先使用
        if (this.modelUrlTransformer) {
            return this.modelUrlTransformer(originalUrl);
        }
        
        // 默认转换逻辑
        if (originalUrl.startsWith('/model/')) {
            return originalUrl.replace('/model/', '/modelStatic/TEST/model/');
        }
        return originalUrl;
    }
    
    /**
     * 预加载所有模型资源但不创建实例
     */
    private async preloadAllModelResources(): Promise<void> {
        try {
            console.log(`🔄 开始预加载 ${this.modelConfigs.length} 个模型资源...`);
            
            // 记录预加载开始时间
            const startTime = performance.now();
            
            // 为每个唯一的URL创建预加载任务
            const uniqueUrls = new Set<string>();
            this.modelConfigs.forEach(config => {
                uniqueUrls.add(this.getModelUrl(config.url));
            });
            
            console.log(`🔍 识别到 ${uniqueUrls.size} 个唯一模型URL需要预加载`);
            
            // 执行预加载
            const preloadTasks = Array.from(uniqueUrls).map(url => {
                this.resourcePreloadStatus.set(url, false);
                return this.resourceManager.preloadModel({
                    id: `preload_${url}`,
                    url: url,
                    position: [0, 0, 0] // 预加载时位置不重要
                }).then(() => {
                    this.resourcePreloadStatus.set(url, true);
                }).catch(error => {
                    console.error(`❌ 预加载模型失败 ${url}:`, error);
                });
            });
            
            // 等待所有预加载任务完成
            await Promise.allSettled(preloadTasks);
            
            // 计算预加载耗时
            const duration = ((performance.now() - startTime) / 1000).toFixed(2);
            
            // 统计预加载结果
            let successCount = 0;
            this.resourcePreloadStatus.forEach((status) => {
                if (status) successCount++;
            });
            
            console.log(`✅ 预加载完成: ${successCount}/${uniqueUrls.size} 个模型, 耗时 ${duration}s`);
        } catch (error) {
            console.error('❌ 预加载模型时发生错误:', error);
        }
    }
    
    /**
     * 更新模型可见性
     * @param forceUpdate 是否强制更新
     */
    private updateModelVisibility(forceUpdate: boolean = false): void {
        // 获取当前时间
        const now = Date.now();
        
        // 只在距离上次检查超过阈值或强制更新时执行
        if (forceUpdate || !this.lastVisibilityCheckTime || (now - this.lastVisibilityCheckTime) > 500) {
            this.lastVisibilityCheckTime = now;
            
            // 检查相机位置是否可用
            if (!this.viewer.camera) {
                console.warn('⚠️ 无法更新模型可见性: 相机不可用');
                return;
            }
            
            // 获取相机位置
            const cameraPosition = this.viewer.camera.position;
            const ellipsoid = this.viewer.scene.globe.ellipsoid;
            const cartographic = ellipsoid.cartesianToCartographic(cameraPosition);
            
            // 转换为经纬度和高度
            const cameraLon = cartographic ? cartographic.longitude * 180 / Math.PI : 0;
            const cameraLat = cartographic ? cartographic.latitude * 180 / Math.PI : 0;
            const cameraHeight = cartographic ? cartographic.height : 0;
            
            if (cameraLon === 0 && cameraLat === 0) {
                console.warn('⚠️ 无法更新模型可见性: 相机位置无效');
            return;
            }
            
            console.log(`🔍 检查模型可见性, 相机位置: ${cameraLon.toFixed(6)}, ${cameraLat.toFixed(6)}, 高度: ${cameraHeight.toFixed(0)}m`);
            
            // 处理所有模型的可见性
            this.processAllModels(cameraLon, cameraLat, cameraHeight);
            
            // 更新统计信息
            let visibleCount = 0;
            this.modelVisibility.forEach((visible) => {
                if (visible) visibleCount++;
            });
            
            // 清理统计信息
            this.configSourceStats.forEach((stats) => {
                stats.visible = 0;
            });
            
            // 更新每个来源的可见模型数量
            this.modelVisibility.forEach((visible, modelId) => {
                if (visible) {
                    const sourceFile = this.configSources.get(modelId);
                    if (sourceFile) {
                        const stats = this.configSourceStats.get(sourceFile);
                        if (stats) {
                            stats.visible++;
                        }
                    }
                }
            });
            
            // 打印可见性统计
            console.log(`👁️ 可见性统计: ${visibleCount}/${this.modelConfigs.length} 个模型可见`);
            this.configSourceStats.forEach((stats, source) => {
                console.log(`  - ${source}: ${stats.visible}/${stats.total} 个模型可见, ${stats.loaded} 个已加载`);
            });
        }
    }
    
    /**
     * 处理所有模型
     */
    private processAllModels(cameraLon: number, cameraLat: number, cameraHeight: number): void {
        this.trimTerrainCache(); // 在处理模型前，清理一下缓存
        // 清空加载队列
        this.loadQueue = [];
        
        // 为每个模型检查距离并确定其优先级
        for (const config of this.modelConfigs) {
            try {
                let modelPosition: number[];
                
                // 提取位置信息
                if (Array.isArray(config.position)) {
                    modelPosition = config.position as number[];
                } else if (config.position && typeof config.position === 'object') {
                    modelPosition = [config.position.x, config.position.y, config.position.z || 0];
                } else {
                    console.warn(`⚠️ 模型 ${config.id} 缺少有效位置信息`);
                    continue;
                }
                
                // 如果位置数组不包含两个以上的元素，跳过
                if (modelPosition.length < 2) {
                    console.warn(`⚠️ 模型 ${config.id} 位置格式错误: [${modelPosition}]`);
                    continue;
                }
                
                // 计算模型到相机的距离
                const modelLon = modelPosition[0];
                const modelLat = modelPosition[1];
                const distance = this.calculateSurfaceDistance(cameraLon, cameraLat, modelLon, modelLat);
                
                // 模型在包围球内可见
                let isInRange = distance <= ConstructionModelManager.VISIBILITY_RANGES.FAR;
                
                // 优先级计算 - 距离越近优先级越高
                let priority = 1000 - Math.min(distance, 1000);
                
                // 模型类型优先级增强
                const sourceFile = this.configSources.get(config.id) || '';
                const modelUrl = config.url;
                const modelName = config.name || '';
                
                // 特别处理"高效索道运输装置"模型
                if (modelUrl.includes('高效索道运输装置') || 
                    modelName.includes('索道') || 
                    modelName.includes('运输装置') ||
                    sourceFile.includes('cableway')) {
                    priority += 400; // 给予高优先级但小于基础工程
                    // 注意，不强制可见性，让它按照通常的距离规则可见
                    console.log(`🔍 特别识别索道运输装置模型: ${config.id}, 距离=${distance.toFixed(0)}m, 提高优先级`);
                }
                
                // 特别提高"基础和接地机械化施工"模型的优先级
                if (modelUrl.includes('基础和接地机械化施工') || 
                    sourceFile.includes('foundationGrounding') ||
                    modelName.includes('基础') ||
                    modelName.includes('接地')) {
                    priority += 500;
                    isInRange = true; // 保留强制可见
                    console.log(`🔍 特别处理基础工程模型: ${config.id}, 距离=${distance.toFixed(0)}m, 提高优先级`);
                }
                
                // 对于所有可以识别的特殊建设类型的模型增加可见性
                if (isInRange || 
                    sourceFile.includes('erectionIronTower') ||
                    sourceFile.includes('MehanizedConstruction') ||
                    sourceFile.includes('materialTransport')) {
                    priority += 200; // 给予额外优先级
                    // 注意：原先在此处有一个 if (distance <= MEDIUM * 1.5) { isInRange = true; } 的逻辑。
                    // 这个逻辑在当前 isInRange 主要由 FAR (2000m) 决定的情况下，
                    // 对于 MEDIUM * 1.5 (现在是 1000m * 1.5 = 1500m) 的判断是多余的，
                    // 因为如果 distance <= 1500m，那么它已经满足 distance <= 2000m (FAR)，isInRange 已为 true。
                    // 因此移除该行以简化逻辑，这些特殊模型的可见范围与普通模型一致（由FAR决定），但优先级更高。
                }
                
                // 记录可见性
                this.modelVisibility.set(config.id, isInRange);
                
                if (isInRange) {
                    // 如果模型在范围内，添加到加载队列
                    this.loadQueue.push({
                        config,
                        priority
                    });
                    
                    // 调试输出
                    console.log(`📏 模型: ${config.id}, 距离: ${distance.toFixed(0)}m, 来源: ${sourceFile}, 在可见范围内`);
                } else {
                    // 如果模型已加载但不在范围内，卸载它
                    // 增加缓冲区防止模型在边界处闪烁
                    if (this.activeModels.has(config.id) && distance > ConstructionModelManager.VISIBILITY_RANGES.FAR * 1.2) {
                        console.log(`🚫 模型超出范围，但根据新策略不卸载: ${config.id}, 距离: ${distance.toFixed(0)}m`);
                        // this.unloadModel(config.id); // 用户要求不卸载模型
                    }
                }
            } catch (error) {
                console.error(`❌ 处理模型 ${config.id} 时出错:`, error);
            }
        }
        
        // 按优先级排序加载队列
        this.loadQueue.sort((a, b) => b.priority - a.priority);
        
        // 统计需要加载的模型数量
        const sourceCount = new Map<string, number>();
        this.loadQueue.forEach(item => {
            const source = this.configSources.get(item.config.id) || '未知';
            sourceCount.set(source, (sourceCount.get(source) || 0) + 1);
        });
        
        // 输出加载统计
        console.log(`🔄 需要加载 ${this.loadQueue.length} 个模型:`);
        sourceCount.forEach((count, source) => {
            console.log(`  - ${source}: ${count} 个模型`);
        });
        
        // 开始处理加载队列
        if (this.loadQueue.length > 0 && !this.isProcessingQueue) {
            this.processLoadQueue();
        }
    }
    
    /**
     * 处理加载队列
     */
    private async processLoadQueue(): Promise<void> {
        if (this.isProcessingQueue || this.loadQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        // 按优先级排序
        this.loadQueue.sort((a, b) => b.priority - a.priority);
        
        // 输出加载队列以调试
        console.log('模型加载队列 (按优先级排序):');
        this.loadQueue.slice(0, 10).forEach((item, index) => {
            console.log(`${index + 1}. ID=${item.config.id}, NAME=${item.config.name || 'unnamed'}, 优先级=${item.priority}`);
        });
        
        const batchSize = 5; // 减小批量大小，确保高优先级模型能更快加载
        const highPriorityItems = this.loadQueue.filter(item => item.priority >= 3);
        
        // 优先处理高优先级的项目
        if (highPriorityItems.length > 0) {
            console.log(`处理${highPriorityItems.length}个高优先级模型...`);
            for (const item of highPriorityItems) {
                await this.createModelInstance(item.config);
                
                // 从加载队列中移除已处理的项目
                const index = this.loadQueue.findIndex(queueItem => queueItem.config.id === item.config.id);
                if (index !== -1) {
                    this.loadQueue.splice(index, 1);
                }
            }
        }
        
        // 处理剩余的队列项
        let processed = 0;
        while (this.loadQueue.length > 0 && processed < batchSize) {
            const item = this.loadQueue.shift();
            if (item) {
                await this.createModelInstance(item.config);
                processed++;
            }
        }
        
        this.isProcessingQueue = false;
        
        // 如果还有剩余项目，稍后继续处理
        if (this.loadQueue.length > 0) {
            setTimeout(() => {
                this.processLoadQueue();
            }, 100);
        } else {
            console.log('所有排队模型已处理完毕');
        }
    }
    
    /**
     * 创建模型实例
     * @param config 模型配置
     * @param terrainHeight 可选的地形高度
     */
    private async createModelInstance(config: ModelConfig, terrainHeight?: number): Promise<any | null> {
        const modelId = config.id;
        if (!this.viewer || !this.viewer.scene) {
            console.warn(`[ConstructionModelManager] Viewer or scene not available for creating model instance for ${modelId}.`);
            return null;
        }

        // Check if model is already active
        const existingModel = this.activeModels.get(modelId);
        if (existingModel) {
            // console.debug(`[ConstructionModelManager] Model ${modelId} is already active. Ensuring it's in scene and visible.`);
            if (!this.viewer.scene.primitives.contains(existingModel)) {
                console.warn(`[ConstructionModelManager] Model ${modelId} was in activeModels but not in scene. Re-adding.`);
                this.viewer.scene.primitives.add(existingModel);
            }
            const isVisible = this.modelVisibility.get(modelId);
            if (isVisible !== undefined) {
                existingModel.show = isVisible;
            } else {
                existingModel.show = true; // Default to visible if no specific info
            }
            // existingModel.distanceDisplayCondition = undefined; // Or new Cesium.DistanceDisplayCondition(near, far);
            return existingModel;
        }

        if (this.loadingModels.has(modelId)) {
            console.debug(`[ConstructionModelManager] Model ${modelId} is currently being loaded. Skipping duplicate creation attempt.`);
            return null; // Another process is already loading this model.
        }

        this.loadingModels.add(modelId);
        const modelNameForLog = config.name || config.url.split('/').pop() || modelId;
        console.log(`[ConstructionModelManager] Attempting to create model instance for: ${modelNameForLog} (ID: ${modelId}), URL: ${config.url}`);

        try {
            const transformedUrl = this.getModelUrl(config.url);
            if (!transformedUrl) {
                console.error(`[ConstructionModelManager] Failed to get transformed model URL for config:`, config);
                this.loadingModels.delete(modelId);
                return null;
            }

            let longitude: number, latitude: number;
            if (Array.isArray(config.position)) {
                longitude = config.position[0];
                latitude = config.position[1];
            } else if (config.position && typeof config.position.x === 'number' && typeof config.position.y === 'number') {
                longitude = config.position.x;
                latitude = config.position.y;
            } else {
                console.error(`[ConstructionModelManager] Invalid position for model ${modelId}:`, config.position);
                this.loadingModels.delete(modelId);
                return null;
            }

            // 获取地形高度，如果失败则使用配置中的高度或0
            const terrainHeight = await this.getTerrainHeightAsync(longitude, latitude);
            const modelMatrix = this.computeModelMatrix(config, longitude, latitude, terrainHeight);

            if (!modelMatrix) {
                console.error(`[ConstructionModelManager] Failed to compute model matrix for ${modelId}.`);
                this.loadingModels.delete(modelId);
                return null;
            }

            const initialShow = this.modelVisibility.get(modelId) ?? config.show ?? true;

            const model = await Cesium.Model.fromGltfAsync({
                url: transformedUrl,
                modelMatrix: modelMatrix,
                show: initialShow,
                scale: config.scale || 1.0,
                minimumPixelSize: config.minimumPixelSize === undefined ? 1 : config.minimumPixelSize, // 默认最小像素大小
                maximumScale: config.maximumScale || 20000, // 默认最大缩放比例
                incrementallyLoadTextures: config.incrementallyLoadTextures === undefined ? true : config.incrementallyLoadTextures,
                // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, VISIBILITY_RANGES.FAR * 1.2), // 比最远可见范围稍大一点
                // silhouetteColor: Cesium.Color.RED,
                // silhouetteSize: 2.0,
                // color: Cesium.Color.WHITE,
                // colorBlendMode: Cesium.ColorBlendMode.HIGHLIGHT,
                // colorBlendAmount: 0.5,
                clampAnimations: config.clampAnimations === undefined ? true : config.clampAnimations, // 动画播放完毕后停在最后一帧
                debugShowBoundingVolume: config.debugShowBoundingVolume || false,
                // shadows: Cesium.ShadowMode.ENABLED, // 开启阴影
                ...(config.cesiumModelOptions || {}), // 允许传入更多Cesium.Model构造参数
            });

            if (model) {
                model.id = modelId; // 确保模型ID被设置
                // @ts-ignore
                model.config = config; // 将原始配置附加到模型上，方便后续操作

                this.viewer.scene.primitives.add(model);
                this.activeModels.set(modelId, model);
                console.log(`[ConstructionModelManager] Successfully created model instance: ${modelId}`);

                const sourceFile = config.sourceFile || 'unknown';
                const stats = this.modelStatsBySourceFile.get(sourceFile) || { total: 0, loaded: 0 };
                stats.loaded += 1;
                this.modelStatsBySourceFile.set(sourceFile, stats);

                return model;
            } else {
                console.error(`[ConstructionModelManager] Failed to create model instance for ${modelId} (fromGltfAsync returned null/undefined). URL: ${transformedUrl}`);
                return null;
            }
        } catch (error) {
            console.error(`[ConstructionModelManager] Error creating model instance for ${modelId} (URL: ${config.url}):`, error);
            if (this.activeModels.has(modelId)) {
                const problematicModel = this.activeModels.get(modelId);
                if (problematicModel && this.viewer && this.viewer.scene && this.viewer.scene.primitives.contains(problematicModel)) {
                    this.viewer.scene.primitives.remove(problematicModel);
                }
                // @ts-ignore
                if (problematicModel && typeof problematicModel.destroy === 'function') {
                    // @ts-ignore
                    problematicModel.destroy();
                }
                this.activeModels.delete(modelId);
                console.warn(`[ConstructionModelManager] Cleaned up model ${modelId} from activeModels and scene due to creation error.`);
            }
            return null;
        } finally {
            if (this.loadingModels.has(modelId)) {
                this.loadingModels.delete(modelId);
                // console.log(`[ConstructionModelManager] Cleared loading flag for model: ${modelId}`);
            } else {
                 console.warn(`[ConstructionModelManager] Attempted to clear loading flag for ${modelId}, but it was not in loadingModels.`);
            }
        }
    }
    
    /**
     * 根据经纬度计算模型矩阵，确保模型正确置于地形表面
     */
    private computeModelMatrix(config: ModelConfig, longitude: number, latitude: number, terrainHeightInput?:number): any {
        // 获取地形高度，如果无法获取则使用默认高度
        const calculatedTerrainHeight = terrainHeightInput ?? this.getTerrainHeight(longitude, latitude, config);
        let modelHeight = calculatedTerrainHeight;
        // 如果 terrainHeightInput 未提供，则 terrainHeight 与 calculatedTerrainHeight 相同
        // const terrainHeight = calculatedTerrainHeight; // This line is redundant if logic is correct
        // let modelHeight = terrainHeight; // This line is also redundant
        
        // 如果配置中明确指定了高度，则使用配置中的高度
        if (Array.isArray(config.position) && config.position.length > 2) {
            // 使用相对高度 (地形高度 + 模型相对地形高度)
            const relativeHeight = config.position[2];
            if (relativeHeight !== undefined && relativeHeight > 0) {
                modelHeight = calculatedTerrainHeight + relativeHeight;
                console.log(`使用配置中的相对高度: 地形(${calculatedTerrainHeight}m) + 相对高度(${relativeHeight}m) = ${modelHeight}m`);
            } else {
                console.log(`使用地形高度: ${calculatedTerrainHeight}m`);
            }
        } else if (!Array.isArray(config.position) && config.position && 'z' in config.position && config.position.z !== undefined) {
            // 使用相对高度 (地形高度 + 模型相对地形高度)
            const relativeHeight = config.position.z;
            if (relativeHeight > 0) {
                modelHeight = calculatedTerrainHeight + relativeHeight;
                console.log(`使用配置中的相对高度(对象): 地形(${calculatedTerrainHeight}m) + 相对高度(${relativeHeight}m) = ${modelHeight}m`);
            } else {
                console.log(`使用地形高度(对象配置): ${calculatedTerrainHeight}m`);
            }
        } else {
            console.log(`使用地形高度: ${calculatedTerrainHeight}m`);
        }
        
        // 确保模型不会显示在地表以下
        if (modelHeight < calculatedTerrainHeight) {
            modelHeight = calculatedTerrainHeight + 1; // 至少高出地表1米
            console.log(`调整模型高度防止陷入地表，新高度: ${modelHeight}m`);
        }

        // 解析缩放参数
        const scale = config.scale || 1.0;
        
        // 创建模型矩阵
        const position = Cesium.Cartesian3.fromDegrees(longitude, latitude, modelHeight);
        
        // 使用配置中的rotation参数（如果有的话）
        let heading = 0, pitch = 0, roll = 0;
        
        if (config.rotation && Array.isArray(config.rotation) && config.rotation.length >= 3) {
            // 使用rotation参数 [heading, pitch, roll]
            heading = Cesium.Math.toRadians(config.rotation[0] || 0);
            pitch = Cesium.Math.toRadians(config.rotation[1] || 0);
            roll = Cesium.Math.toRadians(config.rotation[2] || 0);
            console.log(config.url, `使用rotation参数进行旋转: [${config.rotation[0]}, ${config.rotation[1]}, ${config.rotation[2]}]度`);
        } else {
            // 回退使用heading, pitch, roll参数
            heading = Cesium.Math.toRadians(config.heading || 0);
            pitch = Cesium.Math.toRadians(config.pitch || 0);
            roll = Cesium.Math.toRadians(config.roll || 0);
        }
        
        const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
        const modelMatrix = Cesium.Transforms.headingPitchRollToFixedFrame(position, hpr);
        
        // 应用缩放
        if (scale !== 1.0) {
            const scalingMatrix = Cesium.Matrix4.fromScale(new Cesium.Cartesian3(scale, scale, scale));
            Cesium.Matrix4.multiply(modelMatrix, scalingMatrix, modelMatrix);
        }
        
        return modelMatrix;
    }
    
    /**
     * 获取模型位置信息
     * @param model Cesium模型对象
     * @returns 包含经度、纬度和高度的位置信息
     */
    private getModelPosition(model: any): { longitude: number; latitude: number; height: number } {
        try {
            if (!model || !model.modelMatrix) {
                return { longitude: 0, latitude: 0, height: 0 };
            }
            
            // 从模型矩阵中提取位置
            const modelMatrix = model.modelMatrix;
            const position = new Cesium.Cartesian3();
            Cesium.Matrix4.getTranslation(modelMatrix, position);
            
            // 转换为经纬度和高度
            const cartographic = Cesium.Cartographic.fromCartesian(position);
            const longitude = Cesium.Math.toDegrees(cartographic.longitude);
            const latitude = Cesium.Math.toDegrees(cartographic.latitude);
            const height = cartographic.height;
            
            return { longitude, latitude, height };
        } catch (error) {
            console.error('获取模型位置失败:', error);
            return { longitude: 0, latitude: 0, height: 0 };
        }
    }
    
    /**
     * 获取指定经纬度的地形高度
     * @param longitude 经度（度）
     * @param latitude 纬度（度）
     * @returns 地形高度（米），如果无法获取则返回默认高度
     */
    private getTerrainHeight(longitude: number, latitude: number, config?: ModelConfig): number {
        const cacheKey = `${longitude.toFixed(6)},${latitude.toFixed(6)}`;
        if (this.terrainHeightCache.has(cacheKey)) {
            return this.terrainHeightCache.get(cacheKey)!;
        }

        // 旧的 _cachedTerrainHeight 逻辑可以移除或保留作为过渡，但新缓存优先
        // if (config && (config as any)._cachedTerrainHeight !== undefined) {
        //     const cachedHeight = (config as any)._cachedTerrainHeight;
        //     this.terrainHeightCache.set(cacheKey, cachedHeight); // Populate new cache
        //     return cachedHeight;
        // }

        try {
            // 无效坐标检查
            if (!isFinite(longitude) || !isFinite(latitude) || 
                (longitude === 0 && latitude === 0)) {
                console.warn(`⚠️ 无效坐标: [${longitude}, ${latitude}]，使用默认高度50m`);
                return 50;
            }
            
            const cartographic = Cesium.Cartographic.fromDegrees(longitude, latitude);
            
            // 方法1: 使用globe.getHeight - 最快但精度可能较低
            if (this.viewer.scene.globe && typeof this.viewer.scene.globe.getHeight === 'function') {
                const globeHeight = this.viewer.scene.globe.getHeight(cartographic);
                if (globeHeight !== undefined && globeHeight !== null && isFinite(globeHeight) && globeHeight > 0) {
                    console.log(`✅ 使用globe.getHeight获得高度: ${globeHeight}m`);
                    this.terrainHeightCache.set(cacheKey, globeHeight);
                    this.trimTerrainCache();
                    return globeHeight;
                }
            }
            
            // 方法2: 使用scene.sampleHeight
            if (this.viewer.scene.sampleHeight) {
                try {
                    const sampleHeight = this.viewer.scene.sampleHeight(cartographic);
                    if (sampleHeight !== undefined && sampleHeight !== null && isFinite(sampleHeight) && sampleHeight > 0) {
                        console.log(`✅ 使用sampleHeight获得高度: ${sampleHeight}m`);
                        this.terrainHeightCache.set(cacheKey, sampleHeight);
                        this.trimTerrainCache();
                        return sampleHeight;
                    }
                } catch (e) {
                    // sampleHeight可能会失败，继续尝试其他方法
                    console.log(`⚠️ sampleHeight方法失败，尝试下一个方法`);
                }
            }
            
            // 方法3: 同步方式尝试获取地形高度（只在某些情况下有效）
            try {
                const terrainProvider = this.viewer.terrainProvider;
                if (!terrainProvider) {
                    console.warn('⚠️ 未找到地形提供者，使用默认高度50m');
                    return 50;
                }
                
                // 获取当前地形的最大级别
                const maxLevel = terrainProvider.availability?.maximumLevel ?? 15;
                const terrainSamplePositions = [cartographic];
                
                // 尝试同步获取高度 (注意：在大多数情况下返回Promise)
                const heights = Cesium.sampleTerrainMostDetailed(terrainProvider, terrainSamplePositions);
                
                // 如果返回的不是Promise，说明有同步结果
                if (heights && typeof heights.then !== 'function' && 
                    Array.isArray(heights) && heights.length > 0 && 
                    heights[0].height !== undefined && heights[0].height > 0) {
                    console.log(`✅ 地形高度同步获取成功: ${heights[0].height}m`);
                    this.terrainHeightCache.set(cacheKey, heights[0].height);
                    this.trimTerrainCache();
                    return heights[0].height;
                }
            } catch (error) {
                console.warn('⚠️ 同步地形采样失败:', error);
            }
            
            // 如果以上方法都失败，尝试从已知点位内插高度
            const knownPoints = this.getCachedTerrainPoints();
            if (knownPoints.length > 0) {
                const interpolatedHeight = this.interpolateHeight(longitude, latitude, knownPoints);
                if (interpolatedHeight !== null && interpolatedHeight > 0 && isFinite(interpolatedHeight)) {
                    console.log(`✅ 使用插值法估算高度: ${interpolatedHeight}m`);
                    // 插值结果通常不如直接采样精确，但作为备选可以缓存
                    this.terrainHeightCache.set(cacheKey, interpolatedHeight);
                    this.trimTerrainCache();
                    return interpolatedHeight;
                }
            }
            
            // 所有方法失败，使用默认地形高度值
            const defaultHeight = 50;
            console.warn(`⚠️ 所有高度获取方法失败，对 [${longitude}, ${latitude}] 使用默认高度${defaultHeight}m`);
            this.terrainHeightCache.set(cacheKey, defaultHeight); // 缓存默认值以避免重复尝试
            this.trimTerrainCache();
            return defaultHeight;
        } catch (error) {
            console.error('❌ 获取地形高度出错:', error);
            const defaultHeightOnError = 50;
            this.terrainHeightCache.set(cacheKey, defaultHeightOnError);
            this.trimTerrainCache();
            return defaultHeightOnError; // 错误时返回默认高度并缓存
        }
    }
    
    /**
     * 获取已缓存的地形高度点
     * @private
     */
    private getCachedTerrainPoints(): Array<{lon: number, lat: number, height: number}> {
        const now = Date.now();
        // 如果缓存为空或已过期，则重新填充
        if (this.terrainPointCache.length === 0 || (now - this.terrainPointCacheTimestamp > this.TERRAIN_POINT_CACHE_TTL)) {
            this.terrainPointCache = []; // 清空旧缓存
            this.activeModels.forEach((modelEntry) => {
                // modelEntry could be the Cesium.Model itself or an object containing it
                const model = modelEntry.cesiumModel || modelEntry; 
                // Ensure model is a Cesium.Model and has modelMatrix
                if (model && typeof model.getModelMatrix === 'function' && model.modelMatrix) { 
                    try {
                        const position = this.getModelPosition(model);
                        if (position && position.height > 0 && isFinite(position.longitude) && isFinite(position.latitude)) {
                            this.terrainPointCache.push({
                                lon: position.longitude,
                                lat: position.latitude,
                                height: position.height
                            });
                        }
                    } catch (e) {
                        // console.warn(`Error getting position for model ${model.id || 'unknown'} for terrain point cache:`, e);
                    }
                }
            });
            this.terrainPointCacheTimestamp = now;
            // console.debug(`Refreshed terrainPointCache with ${this.terrainPointCache.length} points.`);
        }
        return this.terrainPointCache;
    }

    /**
     * 根据附近点插值计算高度 (IDW - Inverse Distance Weighting)
     * @private
     */
    private interpolateHeight(targetLon: number, targetLat: number, knownPoints: Array<{lon: number, lat: number, height: number}>): number | null {
        if (knownPoints.length === 0) return null;

        const distances = knownPoints.map(p => ({
            point: p,
            // 使用平方距离避免开方，直到最后需要
            distSq: Math.pow(p.lon - targetLon, 2) + Math.pow(p.lat - targetLat, 2) 
        }));

        // 按距离排序，取最近的N个点
        distances.sort((a, b) => a.distSq - b.distSq);

        const N = Math.min(3, knownPoints.length); // 使用最近的2或3个点进行插值
        if (N === 0) return null;

        // 如果目标点非常接近某个已知点，直接返回该点的高度
        if (distances[0].distSq < 1e-12) { // 1e-12 约等于经纬度0.000001度的平方
            return distances[0].point.height;
        }

        let totalWeight = 0;
        let weightedHeightSum = 0;
        const p = 2; // IDW的幂参数，通常为2

        for (let i = 0; i < N; i++) {
            const dist = Math.sqrt(distances[i].distSq);
            if (dist < 1e-6) { // 如果距离非常小（几乎重合），直接用这个点
                return distances[i].point.height;
            }
            const weight = 1.0 / Math.pow(dist, p);
            weightedHeightSum += distances[i].point.height * weight;
            totalWeight += weight;
        }

        return totalWeight > 0 ? weightedHeightSum / totalWeight : null;
    }

    /**
     * 异步获取地形高度 - 用于需要更精确高度的场合, 带缓存
     * @private
     */
    // This is the primary getTerrainHeightAsync, the other one (getTerrainHeightAsync_Old) and getTerrainHeightAsync_Renamed will be removed.
    public async getTerrainHeightAsync(longitude: number, latitude: number): Promise<number> {
        const cacheKey = `${longitude.toFixed(6)},${latitude.toFixed(6)}`;

        // 1. 检查异步Promise缓存
        if (this.asyncTerrainHeightCache.has(cacheKey)) {
            return this.asyncTerrainHeightCache.get(cacheKey)!;
        }

        // 2. 检查同步值缓存 (如果Promise不存在但值已解析)
        if (this.terrainHeightCache.has(cacheKey)) {
            return Promise.resolve(this.terrainHeightCache.get(cacheKey)!);
        }

        // 3. 无效坐标检查
        if (!isFinite(longitude) || !isFinite(latitude) || (longitude === 0 && latitude === 0)) {
            const invalidCoordHeight = 0; // Or a more suitable default for invalid coords
            this.terrainHeightCache.set(cacheKey, invalidCoordHeight);
            this.trimTerrainCache();
            return Promise.resolve(invalidCoordHeight);
        }

        const terrainProvider = this.viewer.terrainProvider;
        if (!terrainProvider || !terrainProvider.ready) {
            console.warn(`Terrain provider not ready for async height check at ${cacheKey}. Falling back to sync.`);
            const fallbackHeight = this.getTerrainHeight(longitude, latitude); // Sync method handles its own caching
            const promise = Promise.resolve(fallbackHeight);
            this.asyncTerrainHeightCache.set(cacheKey, promise);
            this.trimTerrainCache();
            return promise;
        }

        const cartographic = Cesium.Cartographic.fromDegrees(longitude, latitude);
        
        const promise = new Promise<number>(async (resolve) => {
            try {
                const sampledPositions = await Cesium.sampleTerrainMostDetailed(terrainProvider, [cartographic]);
                if (sampledPositions && sampledPositions[0] && 
                    sampledPositions[0].height !== undefined && 
                    isFinite(sampledPositions[0].height)) {
                    let height = sampledPositions[0].height;
                    height = Math.max(height, -5000); 
                    this.terrainHeightCache.set(cacheKey, height); 
                    resolve(height);
                } else {
                    console.warn(`Async terrain sampling returned invalid data for ${cacheKey}. Falling back to sync.`);
                    const fallbackHeight = this.getTerrainHeight(longitude, latitude);
                    resolve(fallbackHeight); 
                }
            } catch (e) {
                console.warn(`Async terrain sampling failed for ${cacheKey}:`, e, `. Falling back to sync.`);
                const fallbackHeight = this.getTerrainHeight(longitude, latitude);
                resolve(fallbackHeight); 
            }
        });

        this.asyncTerrainHeightCache.set(cacheKey, promise);
        this.trimTerrainCache(); 
        return promise;
    }


    
    /**
     * 卸载模型
     * @param modelId 模型ID
     */
    private unloadModel(modelId: string): void {
        const model = this.activeModels.get(modelId);
        if (!model) {
            // console.warn(`尝试卸载一个不存在于 activeModels 的模型: ${modelId}`);
            return;
        }

        try {
            // 检查模型是否已经被销毁，或者 viewer 或 scene 是否无效
            if (!this.viewer || !this.viewer.scene || !this.viewer.scene.primitives || this.viewer.isDestroyed()) {
                console.warn(`Viewer 或 Scene 已销毁，无法卸载模型 ${modelId}`);
                this.activeModels.delete(modelId); // 从记录中移除
                return;
            }

            if (model.isDestroyed && model.isDestroyed()) {
                console.warn(`模型 ${modelId} 已经被销毁，从 activeModels 中移除`);
                this.activeModels.delete(modelId);
                return;
            }

            // 从场景中移除
            if (this.viewer.scene.primitives.contains(model)) {
                this.viewer.scene.primitives.remove(model);
            }

            // 调用销毁方法（如果存在且模型未被销毁）
            if (typeof model.destroy === 'function' && !(model.isDestroyed && model.isDestroyed())) {
                model.destroy();
                console.log(`模型 ${modelId} 已成功销毁`);
            } else {
                console.warn(`模型 ${modelId} 没有 destroy 方法或已被销毁，仅从场景移除`);
            }

            // 从 activeModels 中移除
            this.activeModels.delete(modelId);

        } catch (error) {
            console.warn(`卸载模型 ${modelId} 时发生错误:`, error);
            // 即使出错也要从activeModels中移除引用
            this.activeModels.delete(modelId);
        }
    }
    
    /**
     * 计算两个经纬度点之间的地表距离(米)
     */
    private calculateSurfaceDistance(lon1: number, lat1: number, lon2: number, lat2: number): number {
        // 地球半径(米)
        const R = 6371000;
        
        // 转换为弧度
        const φ1 = Cesium.Math.toRadians(lat1);
        const φ2 = Cesium.Math.toRadians(lat2);
        const Δφ = Cesium.Math.toRadians(lat2 - lat1);
        const Δλ = Cesium.Math.toRadians(lon2 - lon1);
        
        // 半正矢公式计算
        const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                  Math.cos(φ1) * Math.cos(φ2) * 
                  Math.sin(Δλ/2) * Math.sin(Δλ/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        
        // 距离
        return R * c;
    }
    
    /**
     * 移除所有模型
     */
    public removeAllModels(): void {
        // 停用管理器，这将处理 isProcessingQueue 和 loadQueue
        this.deactivate();
        
        // 卸载所有活跃模型
        console.log(`[ConstructionModelManager] Unloading ${this.activeModels.size} active models.`);
        for (const modelId of this.activeModels.keys()) {
            this.unloadModel(modelId);
        }
        
        // 清空与场景和运行时状态相关的集合
        this.activeModels.clear();
        this.loadedConfigs.clear(); // 清除已加载配置的记录
        this.modelVisibility.clear(); // 清除可见性状态
        this.loadingModels.clear(); // 清除正在加载的模型记录
        // this.loadQueue is cleared in deactivate()
        this.configSourceStats.clear(); // 清除统计信息
        
        // 注意: this.modelConfigs 和 this.configSources 不在此处清空。
        // 这些是核心配置数据，通常由外部加载和管理。
        // removeAllModels 的职责是清理场景实体和内部运行时状态，
        // 而不是卸载或丢弃原始配置数据。
        // 如果需要重新加载配置，应该由调用者负责。
        
        console.log('[ConstructionModelManager] All scene entities and runtime states related to models have been removed. Core model configurations are preserved.');
    }
    
    /**
     * 强制重新加载所有模型
     */
    public reloadVisibleModels(): void {
        console.log('正在重新加载可见范围内的所有模型...');
        
        // 清空加载队列
        this.loadQueue = [];
        
        // 触发可见性更新
        this.updateModelVisibility(true);
    }
    
    /**
     * 强制加载所有模型，不考虑可见性
     */
    public forceLoadAllModels(): Promise<void> {
        return new Promise(async (resolve) => {
            console.log('正在强制加载所有模型，无视可见性限制...');
            
            // 收集所有模型配置
            const allConfigs = [...this.modelConfigs];
            
            console.log(`共需加载 ${allConfigs.length} 个模型...`);
            
            // 批量加载模型
            const batchSize = 20;
            for (let i = 0; i < allConfigs.length; i += batchSize) {
                const batch = allConfigs.slice(i, i + batchSize);
                
                // 并行加载当前批次
                await Promise.all(batch.map(config => this.createModelInstance(config)));
                
                console.log(`已加载 ${Math.min(i + batchSize, allConfigs.length)}/${allConfigs.length} 个模型...`);
            }
            
            console.log('所有模型强制加载完成');
            resolve();
        });
    }
    
    /**
     * 显示当前模型可见性状态信息
     * 这是一个调试方法，可以帮助了解模型加载和可见性状态
     */
    public reportModelStatus(): void {
        console.log("=== 模型状态报告 ===");
        console.log(`共计配置: ${this.modelConfigs.length} 个模型`);
        console.log(`当前活跃: ${this.activeModels.size} 个模型`);
        console.log(`正在加载: ${this.loadingModels.size} 个模型`);
        console.log(`加载队列: ${this.loadQueue.length} 个模型`);
        
        // 按源文件统计
        console.log("\n按源文件统计:");
        this.configSourceStats.forEach((stats, source) => {
            console.log(`- ${source}: 总共${stats.total}个, 已加载${stats.loaded}个, 可见${stats.visible}个`);
        });
        
        // 列出所有可见但未加载的模型
        console.log("\n可见但未加载的模型:");
        let visibleButNotLoaded = 0;
        this.modelVisibility.forEach((visible, id) => {
            if (visible && !this.activeModels.has(id)) {
                visibleButNotLoaded++;
                const sourceFile = this.configSources.get(id) || '未知';
                console.log(`- ID: ${id}, 来源: ${sourceFile}`);
            }
        });
        console.log(`共 ${visibleButNotLoaded} 个模型可见但未加载`);
        
        // 列出加载但不可见的模型
        console.log("\n已加载但不可见的模型:");
        let loadedButNotVisible = 0;
        this.activeModels.forEach((_, id) => {
            if (!this.modelVisibility.get(id)) {
                loadedButNotVisible++;
                const sourceFile = this.configSources.get(id) || '未知';
                console.log(`- ID: ${id}, 来源: ${sourceFile}`);
            }
        });
        console.log(`共 ${loadedButNotVisible} 个模型已加载但不可见`);
        
        // 获取相机位置用于参考
        if (this.viewer.camera) {
            const cameraPosition = this.viewer.camera.position;
            const ellipsoid = this.viewer.scene.globe.ellipsoid;
            const cartographic = ellipsoid.cartesianToCartographic(cameraPosition);
            
            if (cartographic) {
                const lon = cartographic.longitude * 180 / Math.PI;
                const lat = cartographic.latitude * 180 / Math.PI;
                const height = cartographic.height;
                
                console.log(`\n当前相机位置: ${lon.toFixed(6)}, ${lat.toFixed(6)}, 高度: ${height.toFixed(0)}m`);
            }
        }
        
        console.log("=== 报告结束 ===");
    }
}
