import type { ModelConfig } from './ModelResourceManager';

/**
 * 模型配置加载器，用于从配置文件加载模型配置
 */
export class ModelConfigLoader {
    /**
     * 从多个配置文件加载模型配置
     * @param filePaths 配置文件路径数组
     * @returns 所有模型配置的数组
     */
    public static async loadFromFiles(filePaths: string[]): Promise<ModelConfig[]> {
        console.log(`[ModelConfigLoader] Loading configurations from ${filePaths.length} files:`, filePaths);
        
        const loadPromises = filePaths.map(filePath => {
            return this.loadSingleFile(filePath).catch(error => {
                console.error(`[ModelConfigLoader] Failed to load configurations from ${filePath}:`, error);
                return null;
            });
        });
        
        const results = await Promise.all(loadPromises);
        let allConfigs: ModelConfig[] = [];
        let successCount = 0;
        let failureCount = 0;
        
        results.forEach((configs, index) => {
            const filePath = filePaths[index];
            if (configs && configs.length > 0) {
                console.log(`[ModelConfigLoader] Successfully loaded ${configs.length} model configurations from ${filePath}`);
                
                // Log a sample of loaded models (up to 3)
                const sampleSize = Math.min(3, configs.length);
                const samples = configs.slice(0, sampleSize);
                console.log(`[ModelConfigLoader] Sample models from ${filePath}:`, samples.map(c => c.url || 'No URL').join(', '));
                
                allConfigs = allConfigs.concat(configs);
                successCount++;
            } else {
                console.warn(`[ModelConfigLoader] File ${filePath} contained no valid model configurations`);
                failureCount++;
            }
        });
        
        console.log(`[ModelConfigLoader] Loading summary: ${successCount} files succeeded, ${failureCount} files failed, total ${allConfigs.length} model configurations loaded`);
        
        // Validate all configs to ensure they have required properties
        const validConfigs = allConfigs.filter(config => {
            if (!config.url) {
                console.warn('[ModelConfigLoader] Skipping model configuration with missing URL:', config);
                return false;
            }
            return true;
        });
        
        if (validConfigs.length !== allConfigs.length) {
            console.warn(`[ModelConfigLoader] Filtered out ${allConfigs.length - validConfigs.length} invalid configurations`);
        }
        
        return validConfigs;
    }

    /**
     * 从单个配置文件加载模型配置
     * @param filePath 配置文件路径
     * @returns 模型配置数组
     * @private
     */
    private static async loadSingleFile(filePath: string): Promise<ModelConfig[]> {
        try {
            console.log(`[ModelConfigLoader] Loading from ${filePath}...`);
            
            const response = await fetch(filePath);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            // 验证数据格式，支持不同的配置格式
            if (Array.isArray(data)) {
                // 直接是模型配置数组
                return data as ModelConfig[];
            } else if (data && typeof data === 'object' && Array.isArray(data.models)) {
                // 包含models数组的对象
                return data.models as ModelConfig[];
            } else if (data && typeof data === 'object' && Array.isArray(data.constructionModelLoading)) {
                // 使用constructionModelLoading数组作为模型ID列表，以ID为键的模型配置
                console.log(`[ModelConfigLoader] Found constructionModelLoading format in ${filePath}`);
                
                const modelIds = data.constructionModelLoading;
                const configs: ModelConfig[] = [];
                
                for (const modelId of modelIds) {
                    if (data[modelId] && typeof data[modelId] === 'object') {
                        // 将模型ID添加到配置中，以便后续引用
                        const config = { 
                            ...data[modelId],
                            id: modelId  // 添加ID字段
                        };
                        configs.push(config);
                    }
                }
                
                if (configs.length > 0) {
                    console.log(`[ModelConfigLoader] Extracted ${configs.length} model configurations from constructionModelLoading list`);
                    return configs;
                }
                
                // 如果没有找到有效的配置，返回空数组
                return [];
            } else {
                // 尝试从对象中提取所有可能的模型配置
                console.warn(`[ModelConfigLoader] No standard format found in ${filePath}, attempting to extract model configurations directly...`);
                
                const configs: ModelConfig[] = [];
                
                // 遍历所有属性，寻找具有url字段的对象
                for (const key in data) {
                    const value = data[key];
                    if (value && typeof value === 'object' && value.url) {
                        // 将键添加为ID
                        configs.push({
                            ...value,
                            id: key
                        });
                    }
                }
                
                if (configs.length > 0) {
                    console.log(`[ModelConfigLoader] Extracted ${configs.length} model configurations directly from object properties`);
                    return configs;
                }
                
                console.warn(`[ModelConfigLoader] Invalid data format in ${filePath}. Expected array or object with 'models' array or 'constructionModelLoading' array.`);
                
                // 最后的尝试：查找任何可能是模型配置的属性
                const possibleConfigArrays = Object.values(data).filter(value => 
                    Array.isArray(value) && value.length > 0 && 
                    typeof value[0] === 'object' && 'url' in value[0]
                );
                
                if (possibleConfigArrays.length > 0) {
                    const extractedConfigs = possibleConfigArrays[0] as ModelConfig[];
                    console.log(`[ModelConfigLoader] Found potential model configurations in unexpected property in ${filePath}`);
                    return extractedConfigs;
                }
                
                return [];
            }
        } catch (error: any) {
            console.error(`[ModelConfigLoader] Error loading model configurations from ${filePath}:`, error);
            throw new Error(`配置文件格式无效: ${filePath} ${error.message}`);
        }
    }
}