import * as Cesium from 'cesium';

// GLSL相关错误类型
interface CesiumError {
    name: string;
    message: string;
    stack?: string;
}

// 错误处理函数类型
type ErrorHandler = (error: CesiumError) => void;

export interface ModelConfig {
    id: string;
    position: number[] | { x: number; y: number; z?: number };
    scale?: number;
    url: string;
    rotation?: number[];
    distanceDisplayCondition?: {
        near: number;
        far: number;
    };
    show?: boolean;
    [key: string]: any;
}

// 定义加载队列中元素的接口
interface QueuedModelLoad extends ModelConfig {
    _resolve: (value: void | PromiseLike<void>) => void;
    _reject: (reason?: any) => void;
}

export class ModelResourceManager {
    private viewer: Cesium.Viewer; // 更具体的类型
    private modelCache: Map<string, Cesium.Model | Cesium.Cesium3DTileset> = new Map(); // 更具体的类型
    private rawModelDataCache: Map<string, any> = new Map(); // 存储模型的原始数据, 如果有特定结构，也应定义类型
    private loadQueue: QueuedModelLoad[] = []; // 使用定义的接口
    private maxConcurrentLoads: number = 3;
    private activeLoads: number = 0;
    private readonly CLEANUP_INTERVAL: number = 60000; // 60 seconds, 使用 readonly
    private readonly MONITOR_INTERVAL: number = 30000; // 30 seconds, 定义为常量
    private lastCleanup: number = Date.now();
    private isProcessingQueue: boolean = false;
    private loadingPromises: Map<string, Promise<void>> = new Map();
    private modelRefs: Map<string, number> = new Map();
    private _currentModelId: string | undefined;
    private _lastVisibilityCheck: number | undefined;
    private modelInstances: Map<string, (Cesium.Model | Cesium.Cesium3DTileset)[]> = new Map(); // 更具体的类型
    private modelLoadPromises: Map<string, Promise<Cesium.Model | Cesium.Cesium3DTileset>> = new Map(); // 更具体的类型
    private lastAccessTime: Map<string, number> = new Map();
    private originalConsoleError: Console['error'] | null = null; // 更具体的类型

    constructor(viewer: Cesium.Viewer) { // 更具体的类型
        this.viewer = viewer;
        // 每30秒尝试修复因为被剔除算法错误处理的模型
        setInterval(() => {
            this.monitorAndFixModels();
        }, this.MONITOR_INTERVAL);

        // 定期清理未使用的资源
        setInterval(() => {
            this.cleanUnusedCache();
        }, this.CLEANUP_INTERVAL);
    }

    /**
     * 设置最大并发加载数量
     * @param maxLoads 最大并发加载数量，范围1-10
     */
    public setMaxConcurrentLoads(maxLoads: number): void {
        if (maxLoads < 1) {
            console.warn('最大并发加载数不能小于1，已设置为1');
            maxLoads = 1;
        }

        if (maxLoads > 10) {
            console.warn('最大并发加载数不能超过10，已设置为10');
            maxLoads = 10;
        }

        console.log(`设置模型资源管理器最大并发加载数: ${maxLoads}`);
        this.maxConcurrentLoads = maxLoads;
    }

    /**
     * 预加载模型，确保它在后续使用时可用
     * 修改为返回Promise以便等待加载完成
     */
    public preloadModel(modelConfig: ModelConfig): Promise<void> {
        // 增加引用计数
        const refCount = this.modelRefs.get(modelConfig.url) || 0;
        this.modelRefs.set(modelConfig.url, refCount + 1);

        // 如果已经加载或正在加载，直接返回对应的Promise
        if (this.modelCache.has(modelConfig.url)) {
            return Promise.resolve();
        }

        if (this.loadingPromises.has(modelConfig.url)) {
            return this.loadingPromises.get(modelConfig.url)!;
        }

        // 创建新的加载Promise
        const loadPromise = new Promise<void>((resolve, reject) => {
            // 添加到加载队列
            this.loadQueue.push({
                ...modelConfig,
                _resolve: resolve,
                _reject: reject
            } as QueuedModelLoad); // 使用定义的接口类型

            // 处理队列
            if (!this.isProcessingQueue) {
                this.processLoadQueue();
            }
        });

        // 缓存Promise以便复用
        this.loadingPromises.set(modelConfig.url, loadPromise);

        return loadPromise;
    }

    private async processLoadQueue(): Promise<void> {
        if (this.loadQueue.length === 0 || this.activeLoads >= this.maxConcurrentLoads) {
            this.isProcessingQueue = false;
            return;
        }

        this.isProcessingQueue = true;

        while (this.loadQueue.length > 0 && this.activeLoads < this.maxConcurrentLoads) {
            const queuedLoad = this.loadQueue.shift(); // 类型已为 QueuedModelLoad | undefined
            if (!queuedLoad) break;

            const { _resolve: resolve, _reject: reject, ...modelConfig } = queuedLoad;
            // modelConfig 现在是纯粹的 ModelConfig 类型，无需 delete

            // 开始加载模型
            this.activeLoads++;

            try {
                await this.loadSingleModel(modelConfig);
                if (resolve) resolve();
            } catch (error) {
                console.error(`Error loading model ${modelConfig.id}:`, error);
                if (reject) reject(error);
            } finally {
                this.activeLoads--;
                this.loadingPromises.delete(modelConfig.url);
                this.processLoadQueue(); // 处理下一个模型
            }
        }

        this.isProcessingQueue = this.loadQueue.length > 0;
    }

    private async loadSingleModel(modelConfig: ModelConfig): Promise<Cesium.Model | Cesium.Cesium3DTileset> {
        // 如果模型已经在缓存中，获取它并使用当前的 modelConfig 更新其状态
        if (this.modelCache.has(modelConfig.url)) {
            const cachedModel = this.modelCache.get(modelConfig.url)!;
            // 确保模型是 Cesium.Model 的实例才调用 processReadyModel
            // 对于 Cesium.Model, 更新其状态
            if (cachedModel instanceof Cesium.Model) {
                console.log(`[ModelResourceManager] Updating cached Cesium.Model ${modelConfig.id} with current config.`);
                this.processReadyModel(cachedModel, modelConfig, modelConfig.url);
            } else if (cachedModel instanceof Cesium.Cesium3DTileset) {
                // 对于 Cesium3DTileset, 我们可能需要更新其 modelMatrix if a new position is provided
                // For now, we assume tilesets are less frequently repositioned dynamically this way
                // or their position is inherent to the tileset data.
                // If dynamic repositioning of tilesets is needed, specific logic would go here.
                console.log(`[ModelResourceManager] Cached Cesium3DTileset ${modelConfig.id} found. Applying show state.`);
                cachedModel.show = modelConfig.show !== undefined ? modelConfig.show : true;
                // Potentially update modelMatrix if applicable and different
                // const newMatrix = await this.computeModelMatrix(modelConfig);
                // if (newMatrix && !Cesium.Matrix4.equals(cachedModel.modelMatrix, newMatrix)) {
                //     cachedModel.modelMatrix = newMatrix;
                // }
            }
            return cachedModel;
            return cachedModel;
        }

        // 创建加载指示器
        const loadingIndicator = await this.createLoadingIndicatorForModel(modelConfig);

        // 构建完整的URL
        let modelUrl = modelConfig.url;
        if (!modelUrl.startsWith('http') && !modelUrl.startsWith('/')) {
            modelUrl = `/${modelUrl}`;
        }

        // 确保URL格式正确 (重要：不要直接修改原始URL以保持引用完整性)
        const displayUrl = modelUrl; // 用于显示的URL

        // 处理代理URL
        // 注意：我们不修改原始modelUrl以确保缓存键保持一致
        // Cesium实际加载时，浏览器会通过vite代理处理这个URL
        // 只记录日志，显示预期的代理路径，便于调试
        let expectedProxyPath = '';
        if (modelUrl.startsWith('/model/')) {
            // 这个路径会被vite代理规则重写为 /modelStatic/TEST/model/...
            expectedProxyPath = `/modelStatic/TEST${modelUrl}`;
            console.log(`🔄 模型URL: ${modelUrl} 将被代理到: ${expectedProxyPath}`);
        }

        console.log(`🔄 Loading model: ${modelConfig.id} from ${displayUrl}`);

        // 验证文件是否存在 - 使用fetch时不要干预URL，让浏览器和vite处理代理
        try {
            // 使用HEAD请求检查文件是否存在
            const response = await fetch(modelUrl, { method: 'HEAD' });
            if (!response.ok) {
                throw new Error(`File not found: ${displayUrl} (${response.status})`);
            }
        } catch (error) {
            console.error(`❌ 模型文件不存在或无法访问: ${displayUrl}`, error);

            // 移除加载指示器
            if (loadingIndicator && this.viewer.entities.contains(loadingIndicator)) {
                this.viewer.entities.remove(loadingIndicator);
            }

            // 创建占位符
            await this.createPlaceholderForFailedModel(modelConfig);

            throw error;
        }

        // 计算模型矩阵，在创建模型时直接传入
        let modelMatrix: Cesium.Matrix4 | undefined = undefined;
        let longitude: number, latitude: number, height: number = 0;

        // Extract longitude, latitude, height regardless of position type
        if (modelConfig.position) {
            if (Array.isArray(modelConfig.position)) {
                if (modelConfig.position.length < 2) {
                    const errorMessage = "modelConfig.position array must have at least 2 elements (longitude, latitude)";
                    console.error(errorMessage);
                    await this.createPlaceholderForFailedModel(modelConfig);
                    throw new Error(errorMessage); // Reject the promise
                }
                longitude = modelConfig.position[0];
                latitude = modelConfig.position[1];
                height = modelConfig.position.length > 2 ? modelConfig.position[2] : 0;
            } else { // It's an object { x, y, z? }
                longitude = modelConfig.position.x;
                latitude = modelConfig.position.y;
                height = modelConfig.position.z || 0;
            }
        } else {
            const errorMessage = "modelConfig.position is undefined, cannot calculate model matrix.";
            console.error(errorMessage);
            await this.createPlaceholderForFailedModel(modelConfig);
            throw new Error(errorMessage); // Reject the promise
        }

        // Get terrain height if needed
        height = await this.getTerrainHeight(longitude, latitude) || height;
        const position = Cesium.Cartesian3.fromDegrees(longitude, latitude, height);

        // If modelConfig provides rotation, calculate modelMatrix with HeadingPitchRoll
        if (modelConfig.rotation && modelConfig.rotation.length >= 3) {
            const heading = Cesium.Math.toRadians(modelConfig.rotation[0] || 0);
            const pitch = Cesium.Math.toRadians(modelConfig.rotation[1] || 0);
            const roll = Cesium.Math.toRadians(modelConfig.rotation[2] || 0);
            const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
            modelMatrix = Cesium.Transforms.headingPitchRollToFixedFrame(position, hpr);
            console.log('Model matrix calculated from modelConfig.position and modelConfig.rotation.');
        } else {
            // Otherwise, use default eastNorthUpToFixedFrame
            modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(position);
            console.log('Model matrix calculated using default eastNorthUpToFixedFrame.');
        }

        // 创建模型加载选项 - 关闭增量纹理加载以避免着色器重编译问题
        const modelOptions: any = { // Use any for modelOptions for flexibility
            url: modelUrl, // 使用原始URL，让浏览器和Cesium的资源加载器处理代理
            asynchronous: true,
            incrementallyLoadTextures: false, // 关闭增量纹理加载以避免着色器重编译问题
            shadows: Cesium.ShadowMode.DISABLED,
            releaseGltfJson: true,
            upAxis: Cesium.Axis.Y,
            minimumPixelSize: 48,
            cull: false,
            // 添加着色器稳定性选项
            allowPicking: false, // 禁用拾取以减少着色器变体
            // Removed highPerformanceMode as it's not a valid option for Model.fromGltfAsync
        };

        // For Cesium3DTileset, add maximumScreenSpaceError
        if (modelConfig.modelType === '3Dtiles') {
            modelOptions.maximumScreenSpaceError = modelConfig.maximumScreenSpaceError || 16;
        }

        // If modelMatrix is calculated, add it to options
        if (modelMatrix) {
            modelOptions.modelMatrix = modelMatrix;
        }

        console.log(`[ModelResourceManager] Loading model with URL: ${modelOptions.url}`);
        console.log('[ModelResourceManager] Model options:', JSON.stringify(modelOptions, null, 2));

        // 跟踪纹理错误，但不重写console.error以避免着色器编译问题
        let textureErrors = 0;

        // 使用更安全的方式监听纹理错误，避免干扰着色器编译
        const textureErrorHandler = (message: string) => {
            if (message && message.includes('Failed to load texture')) {
                textureErrors++;
                if (textureErrors > 3) {
                    console.warn(`⚠️ Multiple texture load errors for model ${modelConfig.id}, this may affect rendering quality`);
                }
            }
        };

        // 临时监听错误，但不替换console.error
        const errorListener = (event: ErrorEvent) => {
            if (event.message && event.message.includes('texture')) {
                textureErrorHandler(event.message);
            }
        };

        // 添加临时错误监听器
        window.addEventListener('error', errorListener);

        // 设置清理函数
        const cleanupErrorListener = () => {
            window.removeEventListener('error', errorListener);
        };

        try {
            let modelInstance: Cesium.Model | Cesium.Cesium3DTileset; // Explicitly type modelInstance

            switch (modelConfig.modelType || 'gltf') {
                case '3Dtiles':
                    // Cesium3DTileset.fromUrl options are different from Model.fromGltfAsync
                    // Only pass relevant options for Cesium3DTileset.fromUrl
                    const tilesetOptions: any = {
                        url: modelOptions.url,
                        modelMatrix: modelOptions.modelMatrix,
                        maximumScreenSpaceError: modelOptions.maximumScreenSpaceError,
                    };
                    modelInstance = await Cesium.Cesium3DTileset.fromUrl(tilesetOptions.url, tilesetOptions);
                    console.log(`[DEBUG] Cesium3DTileset.fromUrl returned:`, modelInstance);
                    if (!modelInstance) {
                        throw new Error(`Cesium.Cesium3DTileset.fromUrl returned undefined for ${modelConfig.id}`);
                    }
                    break;
                case 'gltf':
                default:
                    // Only pass valid options for Model.fromGltfAsync
                    const gltfOptions: any = {
                        url: modelOptions.url,
                        asynchronous: modelOptions.asynchronous,
                        incrementallyLoadTextures: modelOptions.incrementallyLoadTextures,
                        shadows: modelOptions.shadows,
                        releaseGltfJson: modelOptions.releaseGltfJson,
                        upAxis: Cesium.Axis.Y,
                        minimumPixelSize: 48,
                        cull: modelOptions.cull,
                        modelMatrix: modelOptions.modelMatrix, // Pass modelMatrix directly
                    };
                    modelInstance = await Cesium.Model.fromGltfAsync(gltfOptions);
                    console.log(`[DEBUG] Cesium.Model.fromGltfAsync returned:`, modelInstance);
                    if (!modelInstance) {
                        throw new Error(`Cesium.Model.fromGltfAsync returned undefined for ${modelConfig.id}`);
                    }
                    break;
            }

            // 清理纹理错误监听器
            cleanupErrorListener();

            // Ensure modelInstance is a valid object before proceeding
            if (!modelInstance) {
                const errorMessage = `Failed to create model instance for ${modelConfig.id}. It is null or undefined.`;
                console.error(errorMessage, modelInstance);
                await this.createPlaceholderForFailedModel(modelConfig);
                throw new Error(errorMessage);
            }

            // 将模型添加到场景
            this.viewer.scene.primitives.add(modelInstance);

            // Wait for the model to be fully ready using its 'ready' property
            await new Promise<void>((resolve, reject) => {
                const checkReady = () => {
                    // For both Model and Cesium3DTileset, the 'ready' property indicates loading completion.
                    // For Cesium3DTileset, use 'tilesLoaded'
                    const isModelReady = (modelInstance instanceof Cesium.Model) ? modelInstance.ready :
                                         (modelInstance instanceof Cesium.Cesium3DTileset) ? modelInstance.tilesLoaded : false;

                    if (isModelReady) {
                        resolve();
                    } else if (modelInstance.isDestroyed && modelInstance.isDestroyed()) { // Check if destroyed during loading
                        reject(new Error(`Model ${modelConfig.id} was destroyed during loading.`));
                    } else {
                        // Poll for readiness
                        setTimeout(checkReady, 50); // Check every 50ms
                    }
                };
                checkReady();
            });

            // Now that the model is ready, proceed with processing
            if (modelInstance instanceof Cesium.Model) { // Type guard for Cesium.Model
                this.processReadyModel(modelInstance, modelConfig, modelUrl);
                // 纹理错误过多时添加警告标签
                if (textureErrors > 3) {
                    this.addWarningLabelToModel(modelInstance, modelConfig, "纹理加载不完整");
                }
            } else if (modelInstance instanceof Cesium.Cesium3DTileset) { // Type guard for Cesium.Cesium3DTileset
                console.log(`3D Tileset ${modelConfig.id} loaded.`);
            }
            // 移除加载指示器
            if (loadingIndicator && this.viewer.entities.contains(loadingIndicator)) {
                this.viewer.entities.remove(loadingIndicator);
            }

            // 将模型添加到缓存中
            this.modelCache.set(modelConfig.url, modelInstance);

            return modelInstance; // Return the model instance itself, as it is wrapped in a Promise upstream
        } catch (error: any) {
            // 清理纹理错误监听器
            cleanupErrorListener();

            console.error(`❌ Failed to load model ${modelConfig.id}:`, error);

            // 移除加载指示器
            if (loadingIndicator && this.viewer.entities.contains(loadingIndicator)) {
                this.viewer.entities.remove(loadingIndicator);
            }

            // 创建占位符
            await this.createPlaceholderForFailedModel(modelConfig);

            throw error;
        }
    }

    private processReadyModel(model: Cesium.Model, modelConfig: ModelConfig, modelUrl: string): void {
        // 给模型添加标识，用于事件处理等
        (model as any).modelId = this.getModelIdFromUrl(modelUrl);
        (model as any).configId = modelConfig.id;

        // 尝试修复可能的纹理问题
        this.fixMissingTextures(model);

        // 初始化模型矩阵
        const position = this.convertPositionToCartesian(modelConfig.position);

        // 设置旋转角度
        const rotation = modelConfig.rotation || [0, 0, 0];
        const heading = Cesium.Math.toRadians(rotation[0] || 0);
        const pitch = Cesium.Math.toRadians(rotation[1] || 0);
        const roll = Cesium.Math.toRadians(rotation[2] || 0);

        // 创建模型矩阵
        const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
        const matrix = Cesium.Transforms.headingPitchRollToFixedFrame(position, hpr);

        // 应用模型矩阵
        model.modelMatrix = matrix;

        // 设置缩放比例
        if (modelConfig.scale !== undefined && modelConfig.scale !== 1.0) {
            model.scale = modelConfig.scale;
        }

        // 确保显示条件设置合理
        if (modelConfig.distanceDisplayCondition) {
            model.distanceDisplayCondition = new Cesium.DistanceDisplayCondition(
                modelConfig.distanceDisplayCondition.near,
                modelConfig.distanceDisplayCondition.far
            );
        } else {
            // 如果没有设置显示条件，设置一个默认的较大范围，确保可见性
            model.distanceDisplayCondition = new Cesium.DistanceDisplayCondition(0, 10000000);
        }

        // 增加模型可见性
        model.minimumPixelSize = 128; // 确保模型即使在远处也保持可见
        model.maximumScale = 20000;   // 允许模型放大到合理大小

        // 禁用模型裁剪，防止错误裁剪
        // 注意：cull 属性可能是只读的，所以不要尝试修改它
        // 如果需要控制裁剪，请使用Cesium提供的其他方法

        // 提高渲染优先级
        // Removed direct assignment of renderOrder, as it's not a direct property of Cesium.Model

        // 显式设置模型可见
        model.show = true;

        // 获取模型当前位置并校验高度
        const modelPos = this.getModelPosition(model);
        if (modelPos.height === 0 || modelPos.height < 1) {
            console.log(`⚠️ 检测到模型高度过低或为零 (${modelPos.height}m)，正在调整...`);

            // 首先尝试获取有效地形高度
            this.getTerrainHeight(modelPos.longitude, modelPos.latitude).then(height => {
                if (height !== undefined && height > 0) {
                    // 创建新的带有正确高度的矩阵
                    const newPosition = Cesium.Cartesian3.fromDegrees(
                        modelPos.longitude,
                        modelPos.latitude,
                        height + 1 // 添加1米偏移避免陷入地形
                    );
                    const matrix = Cesium.Transforms.headingPitchRollToFixedFrame(
                        newPosition,
                        new Cesium.HeadingPitchRoll(heading, pitch, roll)
                    );

                    // 应用新矩阵
                    model.modelMatrix = matrix;
                    console.log(`✅ 已将模型高度从 ${modelPos.height}m 调整到 ${height + 1}m`);
                } else {
                    // 如果无法获取有效高度，直接提升到默认高度
                    const newPosition = Cesium.Cartesian3.fromDegrees(
                        modelPos.longitude,
                        modelPos.latitude,
                        50 // 使用固定的50米高度
                    );
                    const matrix = Cesium.Transforms.headingPitchRollToFixedFrame(
                        newPosition,
                        new Cesium.HeadingPitchRoll(heading, pitch, roll)
                    );

                    // 应用新矩阵
                    model.modelMatrix = matrix;
                    console.log(`✅ 由于无法获取有效地形高度，已将模型提升到默认50米高度`);
                }
            }).catch((error: any) => {
                console.error(`❌ 获取地形高度失败:`, error);
                // 即使获取地形高度失败，也尝试将模型提升到默认高度
                const newPosition = Cesium.Cartesian3.fromDegrees(
                    modelPos.longitude,
                    modelPos.latitude,
                    50 // 使用固定的50米高度
                );
                const matrix = Cesium.Transforms.headingPitchRollToFixedFrame(
                    newPosition,
                    new Cesium.HeadingPitchRoll(heading, pitch, roll)
                );
                model.modelMatrix = matrix;
                console.log(`✅ 由于获取地形高度失败，已将模型提升到默认50米高度`);
            });
        }

        // 记录模型加载时间，用于检查是否需要恢复可见性
        (model as any).loadTime = Date.now();

        // 为加载成功的模型添加持久性指示标签
        this.addModelPositionLabel(model, modelConfig);

        console.log(`✅ 模型 ${modelConfig.id} 已加载并准备就绪。`);

        // 强制设置渲染顺序，避免被其他图层遮挡
        // Removed direct assignment of renderOrder, as it's not a direct property of Cesium.Model

        // 保存模型的原始位置和方向
        // ... existing code ...
    }

    /**
     * 为模型添加持久性位置指示标签
     * @private
     */
    private addModelPositionLabel(model: Cesium.Model, modelConfig: ModelConfig): void {
        try {
            // 获取模型位置
            const modelPosition = this.getModelPosition(model);
            const position = Cesium.Cartesian3.fromDegrees(
                modelPosition.longitude,
                modelPosition.latitude,
                modelPosition.height + 20 // 标签位于模型上方20米
            );

            // 提取模型名称
            const modelName = modelConfig.name || this.getModelIdFromUrl(modelConfig.url);
            const modelType = modelConfig.type || '未指定类型';

            // 创建标签实体
            this.viewer.entities.add({
                id: `label_${modelConfig.id}`,
                position: position,
                billboard: {
                    image: this.createModelLabelBillboard(modelName, modelType),
                    width: 220,
                    height: 60,
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    depthTestAgainstTerrain: false // Added here
                }
            });

            // 添加连接线
            this.viewer.entities.add({
                id: `line_${modelConfig.id}`,
                polyline: {
                    positions: [
                        Cesium.Cartesian3.fromDegrees(
                            modelPosition.longitude,
                            modelPosition.latitude,
                            modelPosition.height
                        ),
                        position
                    ],
                    width: 2,
                    material: new Cesium.PolylineDashMaterialProperty({
                        color: Cesium.Color.CYAN,
                        dashLength: 8
                    }),
                    depthFailMaterial: new Cesium.PolylineDashMaterialProperty({
                        color: Cesium.Color.fromCssColorString('#00ffff80'), // Cyan with alpha
                        dashLength: 8
                    }),
                    // disableDepthTestDistance: Number.POSITIVE_INFINITY // This is for the entity itself, not the polyline material
                    depthTestAgainstTerrain: false // Added here for the entity
                }
            });
        } catch (error: any) {
            console.error('Error adding model position label:', error);
        }
    }

    /**
     * 创建模型标签的Billboard图像
     * @private
     */
    private createModelLabelBillboard(modelName: string, modelType: string): HTMLCanvasElement {
        const canvas = document.createElement('canvas');
        canvas.width = 220;
        canvas.height = 60;
        const context = canvas.getContext('2d');

        if (context) {
            // 绘制背景
            context.fillStyle = 'rgba(40, 44, 52, 0.85)';
            context.strokeStyle = 'rgba(0, 229, 255, 0.9)';
            context.lineWidth = 2;
            context.beginPath();
            context.roundRect(0, 0, canvas.width, canvas.height, 6);
            context.fill();
            context.stroke();

            // 绘制标题
            context.fillStyle = 'rgba(0, 229, 255, 1.0)';
            context.font = 'bold 14px Arial, sans-serif';
            context.textAlign = 'center';
            context.fillText(modelName.length > 16 ? modelName.substring(0, 15) + '...' : modelName, canvas.width / 2, 20);

            // 绘制类型
            context.fillStyle = 'rgba(255, 255, 255, 0.9)';
            context.font = '12px Arial, sans-serif';
            context.fillText(modelType, canvas.width / 2, 40);

            // 绘制下边框装饰
            context.fillStyle = 'rgba(0, 229, 255, 0.9)';
            context.fillRect(canvas.width / 4, canvas.height - 4, canvas.width / 2, 2);
        }

        return canvas;
    }

    private async createLoadingIndicatorForModel(modelConfig: ModelConfig): Promise<Cesium.Entity | undefined> {
        try {
            // 获取模型位置
            const position = this.convertPositionToCartesian(modelConfig.position);

            // 创建加载指示器实体
            const loadingEntity = this.viewer.entities.add({
                id: `loading_${modelConfig.id}`,
                position: position,
                name: `Loading ${modelConfig.id}`,
                billboard: {
                    image: this.createLoadingIndicatorBillboard(modelConfig.name || this.getModelIdFromUrl(modelConfig.url), 0),
                    width: 220,
                    height: 60,
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM
                }
            });

            // 设置进度更新循环
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress = (progress + 5) % 101;
                if (loadingEntity && this.viewer.entities.contains(loadingEntity)) {
                    (loadingEntity.billboard as any).image = this.createLoadingIndicatorBillboard(
                        modelConfig.name || this.getModelIdFromUrl(modelConfig.url),
                        progress
                    );
                } else {
                    clearInterval(progressInterval);
                }
            }, 200);

            // 存储计时器ID以便稍后清除
            (loadingEntity as any).progressInterval = progressInterval;

            return loadingEntity;
        } catch (error: any) {
            console.error('Error creating loading indicator:', error);
            return undefined;
        }
    }

    /**
     * 创建加载指示器的Billboard图像
     * @private
     */
    private createLoadingIndicatorBillboard(modelName: string, progress: number): HTMLCanvasElement {
        const canvas = document.createElement('canvas');
        canvas.width = 220;
        canvas.height = 60;
        const context = canvas.getContext('2d');

        if (context) {
            // 绘制背景
            context.fillStyle = 'rgba(40, 44, 52, 0.85)';
            context.strokeStyle = 'rgba(255, 165, 0, 0.9)';
            context.lineWidth = 2;
            context.beginPath();
            context.roundRect(0, 0, canvas.width, canvas.height, 6);
            context.fill();
            context.stroke();

            // 绘制模型名称
            context.fillStyle = 'rgba(255, 255, 255, 0.9)';
            context.font = 'bold 14px Arial, sans-serif';
            context.textAlign = 'center';
            context.fillText(modelName.length > 16 ? modelName.substring(0, 15) + '...' : modelName, canvas.width / 2, 20);

            // 绘制"加载中"文本
            context.fillStyle = 'rgba(255, 165, 0, 0.9)';
            context.font = '12px Arial, sans-serif';
            context.fillText(`正在加载... ${progress}%`, canvas.width / 2, 40);

            // 绘制进度条背景
            context.fillStyle = 'rgba(80, 80, 80, 0.5)';
            context.fillRect(20, 45, 180, 6);

            // 绘制进度条
            context.fillStyle = 'rgba(255, 165, 0, 0.9)';
            context.fillRect(20, 45, 180 * (progress / 100), 6);
        }

        return canvas;
    }

    /**
     * 清理与模型相关的所有实体
     */
    private cleanupModelLabels(modelId: string): void {
        // 查找并移除与此模型关联的所有标签和线条
        const modelEntities = this.viewer.entities.values.filter((entity: any) =>
            entity.id && (
                entity.id.includes(`label_${modelId}`) ||
                entity.id.includes(`line_${modelId}`) ||
                entity.id.includes(`loading_${modelId}`) ||
                entity.id.includes(`warning_${modelId}`)
            )
        );

        for (const entity of modelEntities) {
            // 清除进度条定时器
            if ((entity as any).progressInterval) {
                clearInterval((entity as any).progressInterval);
            }

            // 移除实体
            this.viewer.entities.remove(entity);
        }
    }

    private addWarningLabelToModel(model: Cesium.Model, modelConfig: ModelConfig, warningText: string): void {
        try {
            const position = this.convertPositionToCartesian(modelConfig.position);

            // 创建警告标签
            this.viewer.entities.add({
                position: position,
                id: `warning_${modelConfig.id}`,
                label: {
                    text: `⚠️ ${warningText}`,
                    font: '14px sans-serif',
                    fillColor: Cesium.Color.YELLOW,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    outlineWidth: 2,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0, -30),
                    backgroundColor: Cesium.Color.fromCssColorString('rgba(42, 42, 42, 0.7)'),
                    // disableDepthTestDistance: Number.POSITIVE_INFINITY // Removed from here
                    depthTestAgainstTerrain: false // Added here
                }
            });
            } catch (error) {
            console.error('Error adding warning label:', error);
        }
    }

    private getModelLoadingStatus(model: Cesium.Model | Cesium.Cesium3DTileset): boolean {
        if (!model) return false;

        try {
            if (model instanceof Cesium.Model) {
                return model.ready;
            } else if (model instanceof Cesium.Cesium3DTileset) {
                return model.tilesLoaded; // Use tilesLoaded for Cesium3DTileset
            }
            return false;
        } catch (error) {
            console.error('Error checking model loading status:', error);
            return false;
        }
    }

    private getModelPixelSize(model: Cesium.Model | Cesium.Cesium3DTileset): number {
        if (!model || !this.viewer || !this.viewer.scene || !this.viewer.scene.camera) {
            return 0;
        }

        try {
            // 获取模型的包围球
            // Bounding sphere access differs for Model and Cesium3DTileset
            let boundingSphere: Cesium.BoundingSphere | undefined;
            if (model instanceof Cesium.Model) {
                boundingSphere = model.boundingSphere;
            } else if (model instanceof Cesium.Cesium3DTileset) {
                // For Cesium3DTileset, readyPromise must resolve before accessing boundingSphere
                // and it might not be immediately available or could be dynamic.
                // A common approach is to use the tileset's root tile's bounding volume or a pre-defined one.
                // For simplicity, if not ready, we might return 0 or a default.
                if (model.ready) {
                    boundingSphere = model.boundingSphere;
                }
            }

            if (!boundingSphere) return 0;

            // 计算包围球在屏幕上的像素大小
            const size = this.viewer.scene.camera.getPixelSize(
                boundingSphere,
                this.viewer.scene.drawingBufferWidth,
                this.viewer.scene.drawingBufferHeight
            );

            return size;
        } catch (error) {
            console.error('Error calculating model pixel size:', error);
            return 0;
        }
    }

    public getCachedModel(url: string): Cesium.Model | undefined {
        return this.modelCache.get(url);
    }

    public cleanUnusedCache(): void {
        const now = Date.now();
        const expireTime = 10 * 60 * 1000; // 10分钟过期时间

        // 检查每个缓存的资源
        for (const [url, lastTime] of this.lastAccessTime.entries()) {
            // 如果资源超过过期时间未被访问，且没有活跃实例，则清理
            if (now - lastTime > expireTime && (!this.modelInstances.has(url) || this.modelInstances.get(url)!.length === 0)) {
                this.modelCache.delete(url);
                this.lastAccessTime.delete(url);
                console.log(`清理未使用的模型资源缓存: ${url}`);
            }
        }
    }

    public releaseModel(url: string): void {
        // 减少引用计数
        const refCount = this.modelRefs.get(url) || 0;
        if (refCount > 0) {
            this.modelRefs.set(url, refCount - 1);
        }

        // 自动清理
            this.cleanUnusedCache();
    }

    public destroyAllModels(): void {
        // 清理所有模型和相关资源
        for (const [url, model] of this.modelCache.entries()) {
            try {
                // 清理相关标签
                this.cleanupModelLabels((model as any).modelId || url);

                // 确保模型存在且没有被销毁
                if (model && !model.isDestroyed()) {
                    // 先隐藏模型
                    model.show = false;
                    // 从场景中移除模型
                this.viewer.scene.primitives.remove(model);
                }
            } catch (error) {
                console.error(`Error destroying model ${url}:`, error);
            }
        }

        // 清空缓存和引用计数
        this.modelCache.clear();
        this.modelRefs.clear();
        this.loadQueue = [];

        // 恢复原始的console.error方法
        if (this.originalConsoleError) {
            console.error = this.originalConsoleError;
            this.originalConsoleError = null;
        }

        console.log('🧹 All models destroyed and resources released');
    }

    private async getTerrainHeight(longitude: number, latitude: number): Promise<number | undefined> {
        try {
            // Skip terrain query for invalid or zero coordinates
            if (!isFinite(longitude) || !isFinite(latitude) ||
                (longitude === 0 && latitude === 0)) {
                console.warn(`⚠️ 跳过无效坐标的地形高度查询: [${longitude}, ${latitude}]`);
                return 50; // 返回一个合理的默认高度
            }

            const cartographic = Cesium.Cartographic.fromDegrees(longitude, latitude);

            // 使用sampleTerrain获取地形高度
            const terrainProvider = this.viewer.terrainProvider;
            if (!terrainProvider) {
                console.warn('未找到地形提供者，使用默认高度');
                return 50;
            }

            // 获取当前地形的最大级别
            let maxLevel = 15; // Default value
            if (terrainProvider.availability) {
                maxLevel = terrainProvider.availability.maximumLevel;
            }

            // 采样地形高度
            try {
                console.log(`🔍 正在获取地形高度 [${longitude}, ${latitude}]...`);
                const updatedPositions = await Cesium.sampleTerrainMostDetailed(terrainProvider, [cartographic]);
                const height = updatedPositions[0].height;

                // 检查高度是否有效
                if (height === undefined || height === null || !isFinite(height)) {
                    console.warn(`⚠️ 获取到无效地形高度: ${height}，使用默认高度`);
                    return 50; // 使用默认高度
                }

                // 确保高度不为零，除非确实在海平面
                if (height === 0) {
                    // 检查坐标是否在海洋/平坦区域
                    const isWaterOrFlat = await this.checkIfWaterOrFlatRegion(longitude, latitude);
                    if (!isWaterOrFlat) {
                        console.log(`⚠️ 地形高度为0但不是水域，使用默认高度50米`);
                        return 50;
                    }
                }

                console.log(`✅ 地形高度获取成功: ${height}m`);
                return height;
            } catch (error) {
                console.warn('Error sampling terrain, falling back to globe.getHeight:', error);
                try {
                    // 回退到simpler的方法
                    const height = this.viewer.scene.globe.getHeight(cartographic);

                    // 验证高度有效性
                    if (height === undefined || height === null || !isFinite(height) || height === 0) {
                        console.log(`⚠️ 备选方法获取地形高度失败，使用默认高度50米`);
                        return 50;
                    }

                    console.log(`⚠️ 使用备选方法获取地形高度: ${height}m`);
                    return height;
                } catch(e) {
                    console.error('Fallback terrain height calculation failed:', e);
                    return 50;
                }
            }
        } catch (error) {
            console.error('Failed to get terrain height:', error);
            // 返回一个合理的默认高度，而不是undefined
            return 50; // 50米作为合理默认高度
        }
    }

    /**
     * 检查坐标是否位于水域或平坦区域
     * @private
     */
    private async checkIfWaterOrFlatRegion(longitude: number, latitude: number): Promise<boolean> {
        try {
            // 简单检查周围几个点，看高度是否都为0
            const offsets = [
                [0.001, 0], [-0.001, 0], [0, 0.001], [0, -0.001]
            ];

            const heights = [];
            for (const [lonOffset, latOffset] of offsets) {
                const cart = Cesium.Cartographic.fromDegrees(longitude + lonOffset, latitude + latOffset);
                const height = this.viewer.scene.globe.getHeight(cart);
                heights.push(height);
            }

            // 如果周围点高度都为0或接近0，可能是水域或平坦区域
            const allFlat = heights.every(h => h === undefined || h === null || Math.abs(h) < 5);
            return allFlat;
        } catch (error) {
            console.error('Error checking for water/flat region:', error);
            return false;
        }
    }

    private isPositionObject(position: number[] | { x: number; y: number; z?: number }): position is { x: number; y: number; z?: number } {
        return position && typeof position === 'object' && 'x' in position && 'y' in position;
    }

    private async computeModelMatrixWithTerrainHeight(modelPosition: number[] | { x: number; y: number; z?: number }): Promise<Cesium.Matrix4> {
        // 提取经纬度
        let longitude: number;
        let latitude: number;
        let height: number | undefined;

        if (Array.isArray(modelPosition)) {
            if (modelPosition.length < 2) {
                console.error('Invalid model position array:', modelPosition);
                return Cesium.Matrix4.IDENTITY;
            }
            longitude = modelPosition[0];
            latitude = modelPosition[1];
            height = modelPosition.length > 2 ? modelPosition[2] : undefined;
        } else if (this.isPositionObject(modelPosition)) {
            longitude = modelPosition.x;
            latitude = modelPosition.y;
            height = modelPosition.z;
        } else {
            console.error('Invalid model position format:', modelPosition);
            return Cesium.Matrix4.IDENTITY;
        }

        // 如果没有指定高度，获取地形高度
        if (height === undefined) {
            height = await this.getTerrainHeight(longitude, latitude);

            // 如果地形高度获取失败，使用默认高度0
            if (height === undefined) {
                console.warn(`Failed to get terrain height for position [${longitude}, ${latitude}], using 0 instead.`);
                height = 0;
            }

            // 添加1米的偏移量，避免模型陷入地形
            height += 1.0;
        }

        // 创建Cartesian3位置
        const position = Cesium.Cartesian3.fromDegrees(longitude, latitude, height);

        // 创建默认旋转矩阵（无旋转）
        const modelMatrix = Cesium.Matrix4.fromTranslation(position);

        return modelMatrix;
    }

    private convertPositionToCartesian(position: number[] | { x: number; y: number; z?: number }): Cesium.Cartesian3 {
        if (Array.isArray(position)) {
            // 处理数组格式 [longitude, latitude, (optional)height]
            if (position.length < 2) {
                console.error('Invalid position array:', position);
                return new Cesium.Cartesian3(0, 0, 0);
            }

            const longitude = position[0];
            const latitude = position[1];
            const height = position.length > 2 ? position[2] : 0;

            return Cesium.Cartesian3.fromDegrees(longitude, latitude, height);
        } else if (this.isPositionObject(position)) {
            // 处理对象格式 {x: longitude, y: latitude, z?: height}
            return Cesium.Cartesian3.fromDegrees(position.x, position.y, position.z || 0);
        } else {
            console.error('Invalid position format:', position);
            return new Cesium.Cartesian3(0, 0, 0);
        }
    }

    private getCurrentModelId(): string {
        // 如果没有当前ID或当前ID计数超过10000，重置为1
        if (!this._currentModelId || parseInt(this._currentModelId.split('_')[1]) >= 10000) {
            this._currentModelId = 'model_1';
                return this._currentModelId;
        }

        // 否则，递增ID
        const parts = this._currentModelId.split('_');
        const number = parseInt(parts[1]) + 1;
        this._currentModelId = `model_${number}`;

        return this._currentModelId;
    }

    public debugModelPosition(modelUrl: string): void {
        const model = this.modelCache.get(modelUrl);
        if (!model) {
            console.error(`Model not found for debugging: ${modelUrl}`);
            return;
        }

        try {
            const modelPosition = this.getModelPosition(model);
            console.log(`Model position for ${modelUrl}:`, modelPosition);

            // 创建一个调试实体，以便在3D场景中可视化
            this.viewer.entities.add({
                name: `Debug Point for ${modelUrl}`,
                position: Cesium.Cartesian3.fromDegrees(
                    modelPosition.longitude,
                    modelPosition.latitude,
                    modelPosition.height
                ),
                point: {
                    pixelSize: 15,
                    color: Cesium.Color.RED,
                    outlineColor: Cesium.Color.WHITE,
                    outlineWidth: 2,
                    // disableDepthTestDistance: Number.POSITIVE_INFINITY // Removed from here
                    depthTestAgainstTerrain: false // Added here
                },
                label: {
                    text: `Model Position: ${modelPosition.longitude.toFixed(6)}, ${modelPosition.latitude.toFixed(6)}, ${modelPosition.height.toFixed(2)}m`,
                    font: '14px sans-serif',
                    fillColor: Cesium.Color.WHITE,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    outlineWidth: 2,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0, -20),
                    // disableDepthTestDistance: Number.POSITIVE_INFINITY // Removed from here
                    depthTestAgainstTerrain: false // Added here
                }
            });

            // 飞行到模型位置
            this.flyToModel(modelUrl);
        } catch (error) {
            console.error(`Error debugging model position for ${modelUrl}:`, error);
        }
    }

    public flyToModel(modelUrl: string): void {
        const model = this.modelCache.get(modelUrl);
        if (!model) {
            console.error(`Model not found for flying to: ${modelUrl}`);
            return;
        }

        try {
            // Ensure getModelPosition can handle Cesium3DTileset if necessary, or adjust here
            const modelPosition = this.getModelPosition(model as Cesium.Model); // Assuming getModelPosition expects Cesium.Model for now

            // 计算合适的视角距离
            let boundingSphere: Cesium.BoundingSphere | undefined;
            if (model instanceof Cesium.Model) {
                boundingSphere = model.boundingSphere;
            } else if (model instanceof Cesium.Cesium3DTileset && model.ready) {
                boundingSphere = model.boundingSphere;
            }
            const radius = boundingSphere ? boundingSphere.radius : 100;
            const viewDistance = radius * 3;

            // 飞行到模型位置
            this.viewer.camera.flyTo({
                destination: Cesium.Cartesian3.fromDegrees(
                    modelPosition.longitude,
                    modelPosition.latitude,
                    modelPosition.height + viewDistance
                ),
                orientation: {
                    heading: 0.0,
                    pitch: -Cesium.Math.PI_OVER_FOUR,
                    roll: 0.0
                },
                duration: 1.5
            });
        } catch (error) {
            console.error(`Error flying to model ${modelUrl}:`, error);
        }
    }

    private getModelPosition(model: Cesium.Model | Cesium.Cesium3DTileset): { longitude: number; latitude: number; height: number } {
        if (!model || !model.modelMatrix) {
            throw new Error('Invalid model or model matrix');
        }

        // 从模型矩阵中提取位置
        const position = new Cesium.Cartesian3();
        Cesium.Matrix4.getTranslation(model.modelMatrix, position);

        // 转换为经纬度和高度
        const cartographic = Cesium.Cartographic.fromCartesian(position);
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);
        const height = cartographic.height;

        return { longitude, latitude, height };
    }

    public getAllModelsStatus(): any[] {
        const result = [];

        for (const [url, model] of this.modelCache.entries()) {
            try {
                const isReady = this.getModelLoadingStatus(model);
                const pixelSize = this.getModelPixelSize(model);
                const modelId = (model as any).modelId || this.getModelIdFromUrl(url);
                const configId = (model as any).configId || 'unknown';

                // 获取模型位置
                let position;
                try {
                    position = this.getModelPosition(model); // getModelPosition now accepts Model | Cesium3DTileset
                } catch (error) {
                    console.warn(`Could not get position for ${url}:`, error);
                    position = { longitude: 0, latitude: 0, height: 0 };
                }

                result.push({
                    url,
                    modelId,
                    configId,
                    isReady,
                    isVisible: model.show,
                    pixelSize,
                    position,
                    refCount: this.modelRefs.get(url) || 0
                });
            } catch (error) {
                console.error(`Error getting status for model ${url}:`, error);
            }
        }

        return result;
    }

    private async createPlaceholderForFailedModel(modelConfig: ModelConfig): Promise<void> {
        try {
            // 检查是否已经存在相同ID的占位符，如果存在则不重复创建
            const placeholderId = `placeholder_${modelConfig.id}`;
            const existingEntity = this.viewer.entities.getById(placeholderId);
            if (existingEntity) {
                console.log(`占位符 ${placeholderId} 已存在，不重复创建`);
                return;
            }

            // 转换模型位置
            let longitude: number;
            let latitude: number;
            let height = 0;

            if (Array.isArray(modelConfig.position)) {
                longitude = modelConfig.position[0];
                latitude = modelConfig.position[1];
                height = modelConfig.position.length > 2 ? modelConfig.position[2] : 0;
            } else if (this.isPositionObject(modelConfig.position)) {
                longitude = modelConfig.position.x;
                latitude = modelConfig.position.y;
                height = modelConfig.position.z || 0;
            } else {
                console.error('Invalid model position for placeholder:', modelConfig.position);
                return;
            }

            // 如果没有高度，尝试获取地形高度
            if (height === 0) {
                const terrainHeight = await this.getTerrainHeight(longitude, latitude);
                if (terrainHeight !== undefined) {
                    height = terrainHeight + 1; // 添加1米偏移量
                }
            }

            // 创建点位标记作为占位符
            this.viewer.entities.add({
                id: placeholderId,
                name: `Failed to load: ${modelConfig.id}`,
                position: Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
                ellipsoid: {
                    radii: new Cesium.Cartesian3(10.0, 10.0, 10.0),
                    material: Cesium.Color.RED.withAlpha(0.7),
                    outline: true,
                    outlineColor: Cesium.Color.WHITE
                },
                label: {
                    text: `模型加载失败: ${this.getModelIdFromUrl(modelConfig.url)}`,
                    font: '14px sans-serif',
                    fillColor: Cesium.Color.WHITE,
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    outlineWidth: 2,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(0, -20),
                    // disableDepthTestDistance: Number.POSITIVE_INFINITY, // Removed from here
                    depthTestAgainstTerrain: false, // Added here
                    backgroundColor: Cesium.Color.fromCssColorString('rgba(255, 0, 0, 0.7)')
                }
            });

            console.log(`Created placeholder for failed model: ${modelConfig.id}`);
        } catch (error) {
            console.error(`Error creating placeholder for failed model ${modelConfig.id}:`, error);
        }
    }

    private monitorAndFixModels(): void {
        // 检查所有模型，修复可能的渲染问题
        for (const [url, model] of this.modelCache.entries()) {
            if (model && model.show && this.getModelLoadingStatus(model)) {
                try {
                    const pixelSize = this.getModelPixelSize(model);
                    const modelId = (model as any).configId || 'unknown';

                    // 如果模型应该可见但像素大小为0，可能被错误剔除
                    if (pixelSize === 0 && this.isModelInFrustum(model)) {
                        console.log(`⚠️ 检测到模型 ${modelId} 可能被错误剔除，尝试恢复`);
                        // 调用可见性恢复函数
                        this.ensureModelVisibility(url);
                    }
                } catch (e) {
                    // 忽略错误
                }
            }
        }
    }

    /**
     * 从URL中推断模型ID
     * @private
     */
    private getModelIdFromUrl(url: string): string {
        // 尝试从URL中提取模型ID
        const parts = url.split('/');
        const filename = parts[parts.length - 1];

        // 移除扩展名
        const nameWithoutExt = filename.replace(/\.[^/.]+$/, "");

        return nameWithoutExt || '未知模型';
    }

    /**
     * 检查模型是否在视锥体内
     * @private
     */
    private isModelInFrustum(model: Cesium.Model | Cesium.Cesium3DTileset): boolean {
        try {
            const boundingSphere = model.boundingSphere;
            if (!boundingSphere) return true; // 无法判断，默认认为在内

            // 获取场景的视锥体
            const frustum = this.viewer.camera.frustum;

            // 简单检查：模型中心到相机距离是否在可见范围内
            const cameraPosition = this.viewer.camera.position;
            const distance = Cesium.Cartesian3.distance(cameraPosition, boundingSphere.center);

            return distance < 10000; // 在10公里范围内认为应该可见
        } catch (e) {
            return true; // 出错时默认认为在视锥体内
        }
    }

    // 添加新方法：确保模型可见性的恢复函数
    private ensureModelVisibility(modelUrl: string): void {
        const model = this.modelCache.get(modelUrl);
        if (!model) return;

        try {
            // 检查模型可见性
            const isVisible = model.show;
            const pixelSize = this.getModelPixelSize(model);
            const isInFrustum = this.isModelInFrustum(model);

            // 如果模型已经加载但不可见或像素大小为0，则尝试恢复显示
            if (isVisible && pixelSize === 0 && isInFrustum) {
                console.log(`🔄 尝试恢复模型可见性: ${modelUrl}`);

                // 强制重新计算模型矩阵，触发重绘
                const originalMatrix = model.modelMatrix.clone();

                // 获取模型位置
                const modelPosition = this.getModelPosition(model);

                // 创建新的模型矩阵，稍微偏移高度以确保可见
                const position = Cesium.Cartesian3.fromDegrees(
                    modelPosition.longitude,
                    modelPosition.latitude,
                    modelPosition.height + 2.0 // 增加2米高度偏移，避免陷入地形
                );

                // 从原始矩阵提取旋转部分
                const rotation = Cesium.Matrix4.getMatrix3(originalMatrix, new Cesium.Matrix3());

                // 创建新矩阵
                const matrix = Cesium.Matrix4.fromRotationTranslation(rotation, position);

                // 应用新矩阵
                model.modelMatrix = matrix;

                // 确保所有设置都被应用
            model.show = true;
            if (model instanceof Cesium.Model) {
                model.cull = false;
                model.minimumPixelSize = 128;
            } else if (model instanceof Cesium.Cesium3DTileset) {
                // For Cesium3DTileset, there's no direct 'cull' or 'minimumPixelSize' like Cesium.Model
                // Visibility is primarily controlled by 'show'
                // Additional culling might be managed by tileset's internal LOD and culling strategies
            }

                // 显示调试标记，帮助确认模型位置
                this.viewer.entities.add({
                    position: position,
                    point: {
                        pixelSize: 10,
                        color: Cesium.Color.YELLOW,
                        outlineColor: Cesium.Color.BLACK,
                        outlineWidth: 2,
                        disableDepthTestDistance: Number.POSITIVE_INFINITY
                    },
                    label: {
                        text: `Model: ${(model as any).configId || 'unknown'}`,
                        font: '12px sans-serif',
                        fillColor: Cesium.Color.WHITE,
                        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                        outlineWidth: 2,
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                        pixelOffset: new Cesium.Cartesian2(0, -10),
                        disableDepthTestDistance: Number.POSITIVE_INFINITY,
                        backgroundColor: Cesium.Color.fromCssColorString('rgba(0, 0, 0, 0.7)')
                    }
                });

                console.log(`✅ 模型可见性恢复完成: ${modelUrl}`);
            }
        } catch (error) {
            console.error(`❌ 恢复模型可见性失败: ${modelUrl}`, error);
        }
    }

    /**
     * 创建一个新的模型实例，即使是从同一个URL
     * 这允许在不同位置放置同一个模型文件的多个实例
     * @param url 模型的URL
     * @returns 返回一个新的Cesium.Model实例
     */
    public async createModelInstance(url: string): Promise<Cesium.Model | undefined> {
        if (!this.modelCache.has(url)) {
            await this.preloadModelByUrl(url); // Ensure model is loaded into cache
        }
        const cachedModel = this.modelCache.get(url);
        if (cachedModel) {
            // For models that are already loaded, we just return a reference to them.
            // If a true new instance is needed, the glTF would need to be reloaded.
            // This function is primarily for internal use where a cached model is sufficient.
            return cachedModel as Cesium.Model; // Ensure correct type return
        }
        return undefined;
    }

    /**
     * 通过URL预加载模型，不需要完整的ModelConfig
     */
    private async preloadModelByUrl(url: string): Promise<void> {
        // If model is already loading or loaded, return the existing promise
        if (this.loadingPromises.has(url)) {
            return this.loadingPromises.get(url)!;
        }

        if (this.modelCache.has(url)) {
            return Promise.resolve();
        }

        const modelConfig: ModelConfig = {
            id: this.getModelIdFromUrl(url),
            url: url,
            position: [0, 0, 0] // Default position, will be updated if model has one
        };

        const loadPromise = new Promise<void>(async (resolve, reject) => {
            try {
                // Temporarily create a loading indicator to avoid conflicts with loadSingleModel
                const loadingIndicator = await this.createLoadingIndicatorForModel(modelConfig);

                let modelInstance: Cesium.Model | Cesium.Cesium3DTileset | undefined; // Explicitly type modelInstance
                // Simplified loading for preload - assuming gltf for now
                // More complex logic for 3D Tiles would go here if needed during preload
                modelInstance = await Cesium.Model.fromGltfAsync({
                    url: url,
                    asynchronous: true,
                    incrementallyLoadTextures: false, // 关闭增量纹理加载以避免着色器重编译问题
                    shadows: Cesium.ShadowMode.DISABLED,
                    releaseGltfJson: true,
                    upAxis: Cesium.Axis.Y,
                    minimumPixelSize: 48,
                    cull: false,
                    allowPicking: false, // 禁用拾取以减少着色器变体
                    // Removed highPerformanceMode as it's not a valid option for Model.fromGltfAsync
                });

                if (!modelInstance) {
                    const errorMessage = `Failed to create model instance for preload ${url}`;
                    console.error(errorMessage);
                    if (loadingIndicator && this.viewer.entities.contains(loadingIndicator)) {
                        this.viewer.entities.remove(loadingIndicator);
                    }
                    throw new Error(errorMessage);
                }

                // Add to scene temporarily, then remove after processing
                this.viewer.scene.primitives.add(modelInstance);

                (modelInstance as any).readyPromise.then(() => {
                    this.modelCache.set(url, modelInstance);
                    this.modelRefs.set(url, (this.modelRefs.get(url) || 0) + 1);
                    // Optionally remove from scene after preload if it's only for caching
                    this.viewer.scene.primitives.remove(modelInstance);
                    if (loadingIndicator && this.viewer.entities.contains(loadingIndicator)) {
                        this.viewer.entities.remove(loadingIndicator);
                    }
                    resolve();
                }).catch((err: any) => {
                    console.error(`Error processing preloaded model ${url}:`, err);
                    if (loadingIndicator && this.viewer.entities.contains(loadingIndicator)) {
                        this.viewer.entities.remove(loadingIndicator);
                    }
                    reject(err);
                });

            } catch (error: any) {
                console.error(`Error preloading model ${url}:`, error);
                reject(error);
            }
        });
        this.loadingPromises.set(url, loadPromise);
        return loadPromise;
    }

    /**
     * 创建一个纯色贴图作为纹理加载失败时的备用选项
     * @param color 颜色（CSS颜色字符串）
     * @private
     */
    private createFallbackTexture(color: string = '#CCCCCC'): HTMLCanvasElement {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        if (ctx) {
            // 填充主背景
            ctx.fillStyle = color;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 添加网格线，便于识别是备用纹理
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.lineWidth = 1;

            // 绘制水平线
            for (let y = 32; y < canvas.height; y += 32) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }

            // 绘制垂直线
            for (let x = 32; x < canvas.width; x += 32) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }

            // 添加警告标记
            ctx.fillStyle = 'rgba(255, 0, 0, 0.7)';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('纹理丢失', canvas.width / 2, canvas.height / 2);
        }

        return canvas;
    }

    /**
     * 尝试获取和修复丢失的纹理
     * 这个方法会在模型加载后调用
     * @private
     */
    private fixMissingTextures(model: Cesium.Model): void {
        // 注意：这是一个实验性功能，可能并不适用于所有Cesium版本
        try {
            if (!model.ready) return;

            // 保存原始方法
            const originalLoadTexture = Cesium.Resource.prototype.fetchImage;

            // 创建一个更健壮的包装函数，确保返回格式良好的Promise
            function safeTextureWrapper(this: any, options: any) {
                try {
                    // 处理输入可能为undefined的情况
                    if (!options) options = {};

                    // 调用原始方法
                    const result = originalLoadTexture.call(this, options);

                    // 确保返回值是Promise
                    if (!result || typeof result.then !== 'function') {
                        console.warn('fetchImage返回非Promise值，创建替代Promise');
                        return Promise.resolve(this.url ?
                            new Image() :
                            document.createElement('canvas').toDataURL());
                    }

                    // 添加统一的错误处理
                    return result.then(
                        (image: any) => image,
                        (error: any) => {
                            console.warn(`尝试修复丢失的纹理: ${this.url}`, error);

                            // 创建一个备用纹理
                            const fallbackTexture = new Image();
                            fallbackTexture.src = document.createElement('canvas').toDataURL();

                            return fallbackTexture;
                        }
                    );
                } catch (e) {
                    console.error('纹理加载安全包装器错误:', e);
                    // 始终返回有效的Promise
                    return Promise.resolve(document.createElement('canvas'));
                }
            }

            // 替换为更安全的版本
            Cesium.Resource.prototype.fetchImage = safeTextureWrapper;

            // 在模型加载完成后恢复原始方法
            if (model.readyPromise && typeof model.readyPromise.then === 'function') {
                model.readyPromise.then(() => {
                    Cesium.Resource.prototype.fetchImage = originalLoadTexture;
                }).catch(() => {
                    Cesium.Resource.prototype.fetchImage = originalLoadTexture;
                });

                // 添加额外的安全措施，无论如何5秒后恢复原始方法
                setTimeout(() => {
                    Cesium.Resource.prototype.fetchImage = originalLoadTexture;
                }, 5000);
            }
        } catch (error) {
            console.error('修复丢失纹理失败:', error);
        }
    }
}