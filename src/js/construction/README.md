# 工程施工模型管理系统

这个系统用于管理 3D 工程施工模型的加载、显示和销毁。它具有以下特点：

1. **距离感知模型加载** - 只加载相机可见范围内的模型，动态响应相机移动
2. **多配置文件支持** - 可以从多个配置文件中加载和管理模型
3. **相同模型多实例** - 同一模型文件可以在不同位置创建多个实例
4. **智能可见性管理** - 根据相机距离自动管理模型可见性
5. **批量加载优化** - 优先加载近处模型，后台加载远处模型
6. **资源自动清理** - 自动清理不再使用的模型资源

## 主要组件

- `ConstructionModelManager` - 负责管理模型的生命周期、可见性和层次细节
- `ModelResourceManager` - 负责模型资源的加载、缓存和实例化
- `ModelConfigLoader` - 负责从配置文件加载和处理模型配置

## 使用示例

### 基本使用

```typescript
import { ConstructionModelManager } from './construction/ConstructionModelManager';
import { ModelConfigLoader } from './construction/ModelConfigLoader';

// 初始化
const constructionModelManager = new ConstructionModelManager(viewer);

// 加载多个配置文件中的模型
async function loadAllConstructionModels() {
  const configFiles = [
    'static/jsdata/TEST/erectionIronTower.json',
    'static/jsdata/TEST/foundationGrounding.json', 
    'static/jsdata/TEST/MehanizedConstructionOfWireStringing.json',
    'static/jsdata/TEST/materialTransport.json',
    'static/jsdata/TEST/cableway.json'
  ];
  
  // 加载所有配置文件中的模型配置
  const allConfigs = await ModelConfigLoader.loadFromFiles(configFiles);
  
  // 加载所有模型 - 只有可见范围内的模型会被实际创建和显示
  await constructionModelManager.loadModels(allConfigs);
  
  console.log('所有工程施工模型配置加载完成');
}

// 清理所有模型
function cleanupAllModels() {
  constructionModelManager.removeAllModels();
}
```

### 高级使用

如果需要更细粒度的控制，可以直接使用 `ModelResourceManager`：

```typescript
import { ModelResourceManager } from './construction/ModelResourceManager';

const resourceManager = new ModelResourceManager(viewer);

// 预加载单个模型
await resourceManager.preloadModel({
  id: 'customModel',
  url: '/model/custom.glb',
  position: [116.123, 39.456, 10],
  scale: 0.5,
  rotation: [0, 0, 45]
});

// 创建同一个模型的多个实例
const model1 = await resourceManager.createModelInstance('/model/custom.glb');
const model2 = await resourceManager.createModelInstance('/model/custom.glb');

// 分别设置位置
if (model1 && model2) {
  model1.modelMatrix = Cesium.Transforms.headingPitchRollToFixedFrame(
    Cesium.Cartesian3.fromDegrees(116.123, 39.456, 10),
    new Cesium.HeadingPitchRoll(0, 0, 0)
  );
  
  model2.modelMatrix = Cesium.Transforms.headingPitchRollToFixedFrame(
    Cesium.Cartesian3.fromDegrees(116.223, 39.556, 10),
    new Cesium.HeadingPitchRoll(0, 0, 0)
  );
}
```

## 距离感知加载逻辑

系统使用智能的距离感知加载逻辑：

1. **配置的预加载**: 所有配置文件中的模型配置会被加载并存储
2. **距离筛选**: 只有在 `VISIBILITY_RANGES.FAR` 范围内的模型才会被实际创建实例和显示
3. **动态加载/卸载**: 当相机移动时，系统会自动计算所有模型与相机的距离
   - 进入可见范围的模型会被自动加载
   - 超出可见范围的模型会被自动卸载
4. **批量加载**: 近处的模型优先加载，远处的模型后台加载，避免一次性加载太多模型
5. **周期性检查**: 系统会定期检查所有模型的可见性，确保状态正确

## 注意事项

1. 模型配置文件必须包含 `constructionModelLoading` 数组，列出要加载的模型 ID
2. 每个模型 ID 必须在配置文件中有对应的详细配置对象
3. 模型的 `position` 可以是 `[经度, 纬度, 高度]` 数组或 `{x, y, z}` 对象
4. 如果提供了高度值，将优先使用配置中的高度；否则使用地形高度
5. 相机距离超出 `VISIBILITY_RANGES.FAR`（默认 5000）时，模型将被卸载
6. 相同URL的模型可以在不同位置创建多个独立实例，不会相互覆盖或消失

## 性能优化

系统包含以下性能优化措施：

1. 只加载视距范围内的模型，减少内存和渲染压力
2. 根据距离调整模型 LOD（细节层次）
3. 批量加载模型避免阻塞主线程
4. 定期清理未使用的资源
5. 不同距离的模型使用不同的 minimumPixelSize 以保持可见性
6. 主动卸载超出视距的模型，释放内存和渲染资源 