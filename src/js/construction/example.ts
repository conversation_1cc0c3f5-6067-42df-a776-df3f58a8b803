import * as Cesium from 'cesium';
import { ConstructionModelManager } from './ConstructionModelManager';


/**
 * 工程施工模型加载示例
 * 这个示例演示如何使用改进后的模型加载系统
 */
export class ConstructionModelExample {
    private constructionModelManager: ConstructionModelManager;
    
    // 添加URL转换方法
    public getModelUrl(originalUrl: string): string {
        // 默认实现，可以由外部覆盖
        if (originalUrl.startsWith('/model/')) {
            return originalUrl.replace('/model/', '/modelStatic/TEST/model/');
        }
        return originalUrl;
    }
    
    /**
     * 构造函数
     * @param viewer Cesium Viewer实例
     */
    constructor(viewer: any) {
        // 创建施工模型管理器
        this.constructionModelManager = new ConstructionModelManager(viewer);
        
        // 应用URL重写方法到模型管理器
        this.constructionModelManager.setModelUrlTransformer(this.getModelUrl.bind(this));
        
        console.log('工程施工模型示例初始化完成');
    }
    
    /**
     * 加载所有工程施工模型配置
     * 注意：模型仅在可见范围内创建和显示
     */
    async loadAllModels(): Promise<void> {
        try {
            const configFiles = [
                'static/jsdata/TEST/erectionIronTower.json',
                'static/jsdata/TEST/foundationGrounding.json', 
                'static/jsdata/TEST/MehanizedConstructionOfWireStringing.json',
                'static/jsdata/TEST/materialTransport.json',
                'static/jsdata/TEST/cableway.json',
                'static/jsdata/TEST/temporaryRoads.json',
            ];
            
            console.log('正在加载所有工程施工模型配置...');
            
            // 激活模型管理器
            this.constructionModelManager.activate();
            
            // 直接使用配置文件路径数组加载模型
            const success = await this.constructionModelManager.loadModels(configFiles);
            
            if (success) {
                console.log('所有工程施工模型配置加载完成，可见范围内的模型已创建');
                
                // 报告加载状态
                this.reportModelStatus();
            } else {
                console.warn('部分工程施工模型配置加载失败');
            }
        } catch (error) {
            console.error('加载工程施工模型失败:', error);
        }
    }
    
    /**
     * 显示当前模型状态报告
     * 可帮助调试和了解模型加载情况
     */
    reportModelStatus(): void {
        this.constructionModelManager.reportModelStatus();
    }
    
    /**
     * 强制显示所有高效索道运输装置模型
     * 用于确保特定类型模型的可见性
     */
    forceShowCablewayModels(): Promise<void> {
        return new Promise(async (resolve) => {
            try {
                console.log('正在强制显示所有高效索道运输装置模型...');
                
                // 先加载cableway配置，确保所有索道模型都已加载
                await this.loadSpecificModels(['static/jsdata/TEST/cableway.json']);
                
                // 延迟一些时间以确保模型处理完成
                setTimeout(() => {
                    // 报告所有模型状态
                    this.reportModelStatus();
                    resolve();
                }, 1000);
            } catch (error) {
                console.error('强制显示索道模型失败:', error);
                resolve();
            }
        });
    }
    
    /**
     * 加载特定配置文件中的工程施工模型
     * @param configFiles 配置文件路径数组
     */
    async loadSpecificModels(configFiles: string[]): Promise<void> {
        try {
            console.log(`正在加载指定的工程施工模型配置: ${configFiles.join(', ')}`);
            
            // 激活模型管理器
            this.constructionModelManager.activate();
            
            // 直接使用配置文件路径数组加载模型
            const success = await this.constructionModelManager.loadModels(configFiles);
            
            if (success) {
                console.log(`指定工程施工模型配置加载完成，可见范围内的模型已创建`);
            } else {
                console.warn('部分工程施工模型配置加载失败');
            }
        } catch (error) {
            console.error('加载工程施工模型失败:', error);
        }
    }
    
    /**
     * 强制加载所有配置的模型，不考虑相机位置和距离
     * 适用于需要显示所有模型而不受距离限制的场景
     */
    async forceLoadAllModels(): Promise<void> {
        try {
            // 激活模型管理器
            this.constructionModelManager.activate();
            
            // 如果还没有加载配置，则先加载配置
            if (this.constructionModelManager['modelConfigs'].length === 0) {
                await this.loadAllModels();
            }
            
            // 强制加载所有配置的模型
            await this.constructionModelManager.forceLoadAllModels();
            
            console.log('已强制加载所有配置的模型');
        } catch (error) {
            console.error('强制加载模型失败:', error);
        }
    }
    
    /**
     * 重新检查并加载视距范围内的模型
     * 当相机移动到新位置后，可以调用此方法刷新显示
     */
    async refreshVisibleModels(): Promise<void> {
        // 确保模型管理器已激活
        this.constructionModelManager.activate();
        
        await this.constructionModelManager.reloadVisibleModels();
    }
    
    /**
     * 移除所有工程施工模型
     */
    private _originalFetchImage: any = null;
    
    removeAllModels(): void {
        try {
            // 停用模型管理器
            this.constructionModelManager.deactivate();
            
            // 移除所有模型
            this.constructionModelManager.removeAllModels();
            
            // 还原所有Cesium的原型方法，防止残留的修改
            if (typeof Cesium !== 'undefined' && Cesium.Resource && Cesium.Resource.prototype) {
                // 备份当前的fetchImage方法（如果尚未备份）
                if (!this._originalFetchImage && Cesium.Resource.prototype.fetchImage) {
                    this._originalFetchImage = Cesium.Resource.prototype.fetchImage;
                }
                
                // 如果有备份的原始方法，则恢复它
                if (this._originalFetchImage) {
                    Cesium.Resource.prototype.fetchImage = this._originalFetchImage;
                    console.log('已恢复Cesium.Resource.prototype.fetchImage原始方法');
                }
            }
            
            console.log('已移除所有工程施工模型');
        } catch (error) {
            console.error('移除工程施工模型时发生错误:', error);
        }
    }
}

/**
 * 使用示例:
 * 
 * // 初始化
 * const viewer = new Cesium.Viewer('cesiumContainer');
 * const modelExample = new ConstructionModelExample(viewer);
 * 
 * // 加载所有模型
 * modelExample.loadAllModels();
 * 
 * // 加载特定配置文件中的模型
 * modelExample.loadSpecificModels(['static/jsdata/TEST/cableway.json']);
 * 
 * // 移除所有模型
 * modelExample.removeAllModels();
 */
