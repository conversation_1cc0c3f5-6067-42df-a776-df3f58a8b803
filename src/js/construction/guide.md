# 施工模型模块开发指南

本文档旨在为施工模型模块的开发和维护提供指导。

## 1. 模块概述

施工模型模块负责在三维场景中加载、显示和管理施工过程中的各种模型，例如塔吊、挖掘机、建筑物等。它旨在提供一个灵活且高效的方式来可视化施工进度和现场情况。

## 2. 核心组件

模块主要由以下几个核心组件构成：

-   **`ConstructionModelManager.ts`**: 施工模型管理器，是模块的核心，负责模型的加载、卸载、显隐控制、状态管理以及与视图的交互。
-   **`ModelResourceManager.ts`**: 模型资源管理器，负责模型资源的预加载、缓存管理和加载队列控制，以优化性能和资源利用率。
-   **`ModelConfigLoader.ts`** (设想): 模型配置加载器，用于从外部源（如JSON文件或API）加载模型的配置信息，包括模型URL、初始位置、缩放、旋转等。
-   **`ConstructionModelExample.ts`** (设想): 施工模型实例，代表场景中的一个具体模型对象，封装了模型的属性、状态和操作方法。

## 3. 主要功能

-   **模型加载与卸载**: 支持动态加载和卸载gltf/glb格式的模型。
-   **显隐控制**: 能够根据需求控制单个或批量模型的显示和隐藏。
-   **状态管理**: 跟踪每个模型的加载状态（如：未加载、加载中、已加载、加载失败）。
-   **性能优化**: 通过资源缓存和加载队列管理，提高模型加载效率和场景渲染性能。
-   **事件通知**: (可选) 提供事件机制，以便在模型加载完成、状态改变等关键时刻通知其他模块。

## 4. `ConstructionModelManager.ts` 详解

`ConstructionModelManager` 是施工模型管理的核心类。

### 4.1. 初始化与激活

-   构造函数中接收 `Cesium.Viewer` 实例和可选的 `ModelResourceManager` 实例。
-   `activate()` 方法：激活模型管理器，开始处理模型加载队列和更新。
-   `deactivate()` 方法：停用模型管理器，清理资源，停止模型更新。

### 4.2. 模型加载与创建

-   `addModel(config)`: 添加模型配置到加载队列。配置对象应包含模型URL、ID、位置等信息。
-   `createModelInstance(config)`: 根据配置异步创建模型实例。主要步骤包括：
    1.  检查模型是否已存在或正在加载。
    2.  (可选) 从 `ModelResourceManager` 获取缓存资源或预加载资源。
    3.  调用 `Cesium.Model.fromGltfAsync` 加载模型。
    4.  计算模型矩阵，设置模型的位置、朝向、缩放。
    5.  设置模型的显示属性，如 `show`, `distanceDisplayCondition`。
    6.  将创建的模型实例添加到 `viewer.scene.primitives`。
    7.  更新模型统计信息。

### 4.3. 模型管理

-   `getModelById(id)`: 根据ID获取模型实例。
-   `removeModel(id)`: 移除并销毁指定ID的模型。
-   `removeAllModels()`: 移除并销毁所有模型。
-   `setModelVisibility(id, visible)`: 设置指定模型的可见性。
-   `updateModelPosition(id, position, orientation)`: 更新模型的位置和朝向。

### 4.4. 内部状态与队列

-   `activeModels`: 存储当前场景中激活的模型实例。
-   `loadQueue`: 待加载的模型配置队列。
-   `modelStatsBySourceFile`: (已添加) 用于跟踪每个模型源文件的统计信息。
-   `processLoadQueue()`: 处理加载队列中的模型配置，创建模型实例。

### 4.5. 修复历史

在开发过程中，`ConstructionModelManager.ts` 经历了多次修复，主要包括：

-   **语法错误修复**: 移除了 `createModelInstance` 方法中 `finally` 块之后以及方法外的孤立代码块。
-   **运行时错误修复 (`TypeError: this.resourceManager.getCachedResource is not a function`)**: 删除了对 `ModelResourceManager` 中不存在的 `getCachedResource` 和 `preloadResource` 方法的错误调用。现在直接使用模型URL调用 `Cesium.Model.fromGltfAsync`。
-   **TypeScript编译错误修复**: 
    -   将 `createModelInstance` 的返回类型从 `Promise<Cesium.Model | null>` 改为 `Promise<any | null>` 以临时解决全局 `Cesium` 类型查找问题 (建议后续完善类型定义)。
    -   修改了对 `visibilityInfo` (布尔类型) 的属性访问，直接用于设置 `model.show`。
    -   注释掉了尝试从布尔值设置 `distanceDisplayCondition` 的代码。
    -   增加了逻辑以正确访问 `config.position` 中的经纬度属性 (例如，从数组或对象中提取)。
    -   在类中声明了 `modelStatsBySourceFile` 属性。
    -   调整了 `computeModelMatrix` 的调用参数，确保与函数定义匹配 (具体实现需参考其定义)。

## 5. `ModelResourceManager.ts` 详解

`ModelResourceManager` 负责管理模型资源的加载和缓存。

### 5.1. 初始化

-   构造函数中接收 `Cesium.Viewer` 实例。

### 5.2. 资源预加载与缓存

-   `preloadModel(url)`: (原有逻辑) 预加载模型资源。实际实现中可能直接使用 `Cesium.Model.fromGltfAsync` 并管理其Promise。
-   `getCachedResource(url)`: (原有设想，现已移除相关调用) 获取缓存的模型资源。实际缓存机制可能基于URL管理已加载的 `Cesium.Model` 对象或其原始数据。
-   `processLoadQueue()`: (原有逻辑) 处理内部的资源加载队列。

### 5.3. 职责调整

在近期的修复中，`ConstructionModelManager` 中对 `getCachedResource` 和 `preloadResource` 的直接调用已被移除。这意味着 `ConstructionModelManager` 当前直接负责调用 `Cesium.Model.fromGltfAsync`。`ModelResourceManager` 的角色可能需要重新评估，可以专注于更底层的资源管理策略，如：

-   **统一的资源请求接口**: 提供一个统一的接口来请求模型，内部处理缓存检查和实际加载。
-   **缓存策略**: 实现LRU（Least Recently Used）等缓存淘汰策略。
-   **加载优先级管理**: 如果有大量模型需要加载，可以实现优先级队列。

## 6. 与 `PowerLineView.vue` 的集成

`PowerLineView.vue` 作为视图层，会实例化并使用 `ConstructionModelManager` 来在三维场景中展示施工模型。

-   在 `PowerLineView.vue` 的 `mounted` 或相关生命周期钩子中，创建 `ConstructionModelManager` 实例。
-   通过调用 `constructionModelManager.addModel()` 方法，将需要显示的施工模型配置传递给管理器。
-   视图层可以通过 `ConstructionModelManager` 提供的接口控制模型的显隐、查询状态等。
-   在组件销毁时 (`beforeUnmount`)，调用 `constructionModelManager.deactivate()` 来释放资源。

## 7. 开发最佳实践

-   **明确职责分离**: `ConstructionModelManager` 关注模型的生命周期和场景交互，`ModelResourceManager` (如果使用) 关注资源的获取和缓存。
-   **异步操作处理**: 模型加载是异步的，确保正确使用 `async/await` 和 `Promise` 处理异步流程和错误。
-   **错误处理与日志记录**: 在关键操作（如模型加载、API调用）中添加健壮的错误处理逻辑，并记录必要的日志信息，便于调试。
-   **性能监控**: 使用浏览器的开发者工具或自定义的性能监控手段，关注模型加载时间、内存占用和渲染帧率。
-   **类型安全**: 充分利用 TypeScript 的类型系统，为接口、配置对象和函数签名提供明确的类型定义。
-   **代码注释与文档**: 为复杂的逻辑和公共接口编写清晰的注释和文档。
-   **模块化设计**: 将功能分解为更小的、可复用的模块或函数。
-   **资源清理**: 确保在模型不再需要时，从场景中正确移除并释放相关资源，防止内存泄漏。

## 8. `README.md` 文件回顾

项目的 `README.md` 文件应包含施工模型模块的简要介绍、如何集成和使用的基本示例，以及关键配置项的说明。这有助于新成员快速了解和上手该模块。

## 9. 后续工作与建议

-   **完善 `Cesium` 类型定义**: 解决全局 `Cesium` 类型查找问题，避免使用 `any` 类型。\可以考虑引入 `@types/cesium` 或项目内自定义更完善的 `*.d.ts` 文件。
-   **`computeModelMatrix` 函数审查**: 仔细检查 `computeModelMatrix` 函数的实现和参数，确保其能正确处理各种位置和朝向配置。
-   **`ModelResourceManager` 角色明确**: 如果需要高级的资源管理功能（如缓存、预加载），重新设计并实现 `ModelResourceManager`，并调整 `ConstructionModelManager` 与其协作的方式。
-   **单元测试与集成测试**: 为核心功能编写单元测试和集成测试，确保代码质量和稳定性。
-   **用户交互**: 根据需求，增加与施工模型的用户交互功能，如点击查询信息、高亮显示等。

通过遵循本指南，希望能帮助开发团队更有效地进行施工模型模块的开发与维护。