import * as Cesium from '@/Cesium'
// import * as Cesium from 'cesium';
import {getCurrentInstance} from "vue";
/**
 * @descripion: 广告牌封装js
 * @param {Viewer} viewer
 * @param {Cartesian2} position
 * @return {*}
*/

var labelNum = []
var labelZndNum = []
export class DivLabel {
    constructor(val) {
    }
    addDynamicLabel(viewer, data, isxf=null, num=null) {
        let div = document.createElement("div");
        div.id = data.id;
        div.style.position = "absolute";
        // div.style.width = "100px";
        // div.style.height = "30px";
        let divHTML = data.divHTML;
        div.innerHTML = divHTML;
        // div.innerHTML = HTMLTable;
        viewer.cesiumWidget.container.appendChild(div);
        let gisPosition = Cesium.Cartesian3.fromDegrees(
            data.position[0],
            data.position[1],
            data.position[2]
        );
        //实时更新位置
        viewer.scene.postRender.addEventListener(() => {
            const canvasHeight = viewer.scene.canvas.height;
            const windowPosition = new Cesium.Cartesian2();
            Cesium.SceneTransforms.wgs84ToWindowCoordinates(
                viewer.scene,
                gisPosition,
                windowPosition
            );
            div.style.bottom = canvasHeight - windowPosition.y - 15 + "px";
            const elWidth = div.offsetWidth;
            div.style.left = windowPosition.x - elWidth / 2 + "px";

            //解决滚动不隐藏问题
            const camerPosition = viewer.camera.position;
           
            let height = viewer.scene.globe.ellipsoid.cartesianToCartographic(camerPosition).height;
            
            var position = viewer.scene.camera.positionCartographic; //经纬度单位为弧度，高程单位为米.
            // 弧度转经纬度
            var longitude = Cesium.Math.toDegrees(position.longitude).toFixed(6);
            height += viewer.scene.globe.ellipsoid.maximumRadius;
            // 此处因为开启巡航时候，广告牌会隐藏，所以删除了第一个判断
            // if ((!(Cesium.Cartesian3.distance(camerPosition, gisPosition) > height)) && viewer.camera.positionCartographic.height < 50000000) {
            if (viewer.camera.positionCartographic.height < 50000000) {
                let maxHeight = 6379523  // 广告牌可视的最大高度
                if (height > 0 && height < maxHeight) {
                    div.style.display = "block"
                }else {
                    div.style.display = "none"
                }
                if (isxf && longitude - data.position[0] > 0.006)  {
                    div.style.display = "none"
                }
            } else {
                div.style.display = "none"
            }
        });
        addLabelNum(div)
    }

    // 重难点场景label
    //暂时不用这个方法
    addDynamicLabelZnd(viewer, data) {
        let div = document.createElement("div");
        div.id = data.id;
        div.style.position = "absolute";
        // div.style.width = "100px";
        // div.style.height = "100px";
        let divHTML = data.divHTML;
        div.innerHTML = divHTML;
        // div.innerHTML = HTMLTable;
        viewer.cesiumWidget.container.appendChild(div);
        let gisPosition = Cesium.Cartesian3.fromDegrees(
            data.position[0],
            data.position[1],
            data.position[2]
        );
        //实时更新位置
        viewer.scene.postRender.addEventListener(() => {
            const canvasHeight = viewer.scene.canvas.height;
            const windowPosition = new Cesium.Cartesian2();
            Cesium.SceneTransforms.wgs84ToWindowCoordinates(
                viewer.scene,
                gisPosition,
                windowPosition
            );
            div.style.bottom = canvasHeight - windowPosition.y - 15 + "px";
            const elWidth = div.offsetWidth;
            div.style.left = windowPosition.x - elWidth / 2 + "px";

            //解决滚动不隐藏问题
            const camerPosition = viewer.camera.position;
            let height = viewer.scene.globe.ellipsoid.cartesianToCartographic(camerPosition).height;
            height += viewer.scene.globe.ellipsoid.maximumRadius;
            // 此处因为开启巡航时候，广告牌会隐藏，所以删除了第一个判断
            // if ((!(Cesium.Cartesian3.distance(camerPosition, gisPosition) > height)) && viewer.camera.positionCartographic.height < 50000000) {
            if (viewer.camera.positionCartographic.height < 50000000) {
                let maxHeight = 6379523  // 广告牌可视的最大高度
                if (height > 0 && height < maxHeight) {
                    div.style.display = "block"
                } else {
                    div.style.display = "none"
                }
            } else {
                div.style.display = "none"
            }
        });
        addLabelZndNum(div)
    }

    // 基于cesium引擎添加label by lkj
    addDynamicLabel_cesium(viewer, data,isZND=false){
        let label_id = data.id
        if(viewer.entities.getById (label_id) != undefined){
            // 待添加的标签已经存在 不重复添加
            return;
        }

        // 如果data中有给定颜色则按给定颜色 否则使用默认 给定颜色的格式为十六进制格式的rgba  eg: 0xaabbccdd
        let fillColor = data.fillColor?new Cesium.Color.fromCssColorString  (data.fillColor):Cesium.Color.YELLOW;
        let bgColor = data.bgColor?new Cesium.Color.fromCssColorString  (data.bgColor):new Cesium.Color(0.165, 0.165, 0.165, 0.3);
        let font = data.font?data.font:'24px Helvetica';

        let options = {
            position: Cesium.Cartesian3.fromDegrees(...data.position),
            id: label_id,
            label: {
                text: data.text,
                font: font,
                fillColor: fillColor,
                showBackground: true,
                backgroundColor: bgColor,
                backgroundPadding : new Cesium.Cartesian2(7, 5),
                // fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                // horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                // verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                // pixelOffset: new Cesium.Cartesian2(0, 15),
                scaleByDistance : new Cesium.NearFarScalar(0, 2.5, 2000, 0.0),
                // eyeOffset: new Cesium.Cartesian3(0, 0, 0),  // 固定标签位置
                // disableDepthTestDistance : Number.POSITIVE_INFINITY
            }
        }

        if(data.img){
            options.label.horizontalOrigin = Cesium.HorizontalOrigin.RIGHT
            options.billboard = {
                image: data.img,
                // height: 23,
                // width: 32,
                scale: 0.04,
                // eyeOffset: new Cesium.Cartesian3(0, 0, 0),  // 固定标签位置
                // pixelOffset: new Cesium.Cartesian2(0, -15),
                // showBackground: true,
                // backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.3),
                // backgroundPadding : new Cesium.Cartesian2(7, 5),
                scaleByDistance : new Cesium.NearFarScalar(0, 2.5, 2000, 0.0),
                horizontalOrigin :Cesium.HorizontalOrigin.LEFT,
                // disableDepthTestDistance : Number.POSITIVE_INFINITY
            }
            // options.description = '<h2>HTML内容</h2><p>这是一段HTML格式的内容。</p>'

            options.userMsg = data.url; // 传递一个自定义字段信息，用于点击时促发链接跳转用
            let billbaord_id = 'billboard_'+data.id;;
            options.userName = billbaord_id;
        }
        let label_entity = viewer.entities.add(options);
        // 设置标签的深度测试
        // label_entity.label.disableDepthTestDistance = 100000;

        // if(data.img){
        //     let billboard_position = data.position; 
        //     billboard_position[2] += 5;// 图片比文字标签高
        //     let billbaord_id = 'billboard_'+data.id;;
        //     let billboard = viewer.entities.add({
        //         position: Cesium.Cartesian3.fromDegrees(...billboard_position),
        //         id: billbaord_id,
        //         billboard: {
        //             image: data.img,
        //             height: 23,
        //             width: 32,
        //             showBackground: true,
        //             backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.3),
        //             backgroundPadding : new Cesium.Cartesian2(7, 5),
        //             scaleByDistance : new Cesium.NearFarScalar(0, 3.5, 2000, 0.0)
        //         },
        //         userMsg: data.url, // 传递一个自定义字段信息，用于点击时促发链接跳转用
        //         userName: billbaord_id,
        //     });
        //     isZND ? addLabelZndNum(billbaord_id):addLabelNum(billbaord_id)
        // }
        isZND ? addLabelZndNum(label_id):addLabelNum(label_id)
        return label_entity;
    }

}
//统计广告牌数量
export function addLabelNum(div) {
    labelNum.push(div)
    // console.log("labelNum",labelNum)
}

//添加重难点场景广告牌数量
export function addLabelZndNum(div) {
    labelZndNum.push(div)
    // console.log("labelNum",labelNum)
}

export function deleteZndLabel(viewer) {
    console.log("deleteZndLabel=",labelZndNum)
    for (let i = 0; i < labelZndNum.length; i++) {
        // if(document.getElementById(labelZndNum[i].id) && document.getElementById(labelZndNum[i].id) !== undefined) {
        //     document.getElementById(labelZndNum[i].id).remove()
        // }
        viewer.entities.removeById(labelZndNum[i])
    }
    labelZndNum = []
}

//隐匿或删除广告牌
export function deleteLabel() {
    for (let i = 0; i < labelNum.length; i++) {
        // document.getElementById(labelNum[i].id).outerHTML = ''
        if(document.getElementById(labelNum[i].id) && document.getElementById(labelNum[i].id) !== undefined) {
            document.getElementById(labelNum[i].id).remove()
        }
    }
    labelNum = []
}


/**
 * @descripion: 工序展示
 * @param {viewer} viewer 地图视图
 * @param {array<T>} names 创建的entity实例名字数组
 * @param {array<T>} IDS 创建的entity实例ID数组
 * @param {array<T>} model 模型数组
 * @param {array<T>} nodeNames 节点名称数组
 * @param {boolean} isShow 展示选择 false为不显示，true为显示
 * @param {obj} attribute 画圆的属性(画圆速度较慢) color 颜色 radius 半径 position 位置
 * @return {*}
*/
export class gltfShow {
    constructor(val) {
    }

    selectModelNodesbyNames(viewer, names) {
        let model = viewer.scene.primitives._primitives
        let newModel = []
        if (names && names.length >= 1)
            names.forEach(element => {
                const model2 = model.filter(c => c._id._name == element)
                newModel.push(model2)
            });
        else
            return model
        return newModel
    }

    selectModelNodesbyIDS(viewer, IDS) {
        let model = viewer.scene.primitives._primitives
        let newModel = []
        if (IDS && IDS.length >= 1)
            IDS.forEach(element => {
                const model2 = model.filter(c =>
                    c._id && c._id._id == element
                )
                newModel.push(...model2)
            });
        else
            return model

        return newModel
    }


    isShowModelNodes(model, nodeNames, isShow) {
        if (!model && model.length < 1)
            return
        model.forEach(element => {
            console.log("element", element)
            let node = element._nodesByName
            console.log("node", node)
            if (node)
                for (let val in node) {
                    if (nodeNames && nodeNames.length >= 1)
                        nodeNames.forEach(nodeName => {
                            if (val == nodeName)
                                node[val].show = isShow
                        })
                    else
                        node[val].show = isShow
                }

        })


        return model
    }

    getModelNodesArray(model) {
        if (!model)
            return
        let nodesArray = []
        let node = model._nodesByName
        for (let nodeName in node) {
            nodesArray.push(nodeName)
        }
        return nodesArray

    }

    isShowModel(model, isShow) {
        if (!model && model.length < 1)
            return model

        model.forEach(element => {
            console.log("element", element)
            let node = element._nodesByName
            console.log("node", node)
            if (node)
                for (let val in node) {
                    node[val].show = isShow
                }
        })
        return model
    }

    addcmzlTest(viewer, attribute = { color: [0, 0, 0, 1], radius: 10 }) {
        var czml = [{
            "id": "document",
            "name": "box",
            "version": "1.0"
        }, {
            "id": "shape2",
            "name": "Red box with black outline",
            "position": {
                "cartographicDegrees": attribute.position
            },
            "ellipse": {
                "height": attribute.position[2],
                "HeightReference": 0,
                "semiMajorAxis": attribute.radius,
                "semiMinorAxis": attribute.radius,
                "material": {
                    "solidColor": {
                        "color": {
                            "rgba": attribute.color
                        }
                    }
                },
                "outline": true,
                "outlineColor": {
                    "rgba": [0, 0, 0, 1]
                }
            }
        }];
        var dataSourcePromise = Cesium.CzmlDataSource.load(czml);
        viewer.dataSources.add(dataSourcePromise);
        // viewer.zoomTo(dataSourcePromise);
        return dataSourcePromise
    }

}




