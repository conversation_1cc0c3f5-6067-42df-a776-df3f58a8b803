import * as Cesium from '@/Cesium'
// import * as Cesium from 'cesium';

export default class Map3DTool_common {
    constructor(viewer) {
        if (!(viewer instanceof Cesium.Viewer)) {
            throw new Error('Invalid viewer: must be an instance of Cesium.Viewer');
        }
        this.viewer = viewer;
    }
    drawPoint = (position, size = 10, CssColorString = undefined) => {
        // 创建一个点
        let color = CssColorString ? new Cesium.Color.fromCssColorString(CssColorString) : Cesium.Color.YELLOW;
        position = Cesium.Cartesian3.fromDegrees(position[0], position[1], position[2]);
        let point = this.viewer.entities.add({
            position: position, // 点的经纬度坐标
            point: {
                pixelSize: size, // 点的大小
                color: color, // 点的颜色
                outlineColor: Cesium.Color.WHITE,
                outlineWidth: 2
            }
        });
        console.log('add point : ', point)
        return point;
    }
}