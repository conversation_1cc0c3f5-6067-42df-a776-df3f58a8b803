import { Constants } from './constants.js';
import {
    MODEL_STATIC_URL,
    FixTowerHeight,
    LINE_POINTS_NUM,
    MODEL_SHOW_DISTANCE,
    CONTEXT
} from '@/config/global.js';
import { createEntity_glb, getPositionHeight } from "../common/viewer.ts";
import { addLabelNum, gltfShow, deleteLabel, deleteAllLabels } from "../common/divLabel.ts";
import { catenaryLineArr } from "../common/catenary.js"
import * as Cesium from '@/Cesium'
// import * as Cesium from 'cesium';
import {showModelByStatus} from "../common/line_tower";
import {useLineProjStore} from '@/store/lineProj.js'
export default class Map3DTool{
    
    constructor(projectCode,towerModels,insulatorModels,towerProfiles,showDesign, viewer, flag, planRecordMap) {
        this.projectCode = projectCode
        this.line =  null
        this.lineDetails = null //回路详情集合
        this.towerModels = towerModels //塔模型信息集合
        this.insulatorModels = insulatorModels //绝缘子信息集合
        this.towerProfiles = towerProfiles ||[]
        this.showDesign = showDesign //是否显示设计,true 显示全部线路
        this.towerEntities = []//塔集合
        this.towerLabels = []//塔标牌集合
        this.insulatorEntities = []//绝缘子集合
        this.polylines = []
        this.viewer = viewer // 地图对象
        this.flag = flag
        this.planRecordMap = planRecordMap
        // 用来在当前底线基础上，保存地线point1的点，后面连起来 { 'A50': { 'start': [], 'end': [] }}
        this.dixian = {}
        // for (let i = 0; i <)
    }
    fixTowerAng = (towers)=>{
        let lastTowerPosition = [towers[0].longitude, towers[0].latitude, towers[0].height]; // 第一个杆塔的上一个塔没有 所以取它本身的值
        for(let ind=0; ind<towers.length; ind++){
            let nowTowerPosition = [towers[ind].longitude, towers[ind].latitude, towers[ind].height];
            let baseAngle = distance_ang(lastTowerPosition, nowTowerPosition)[1]*180/ Math.PI + 90;
            towers[ind].angle = baseAngle+towers[ind].angle;
            lastTowerPosition = nowTowerPosition;
            // console.log('fixtowerang:',towers[ind].towerId, baseAngle, towers[ind].angle);
        }
    }
    draw3DLine= (line,lineDetails,isxf=null)=>{
        console.log("开始画线",line)
        this.dixian = {}
        let lineDatas = this.generateLineDatas(line,lineDetails,isxf)
        console.log("生成完画线数据")
        lineDatas.forEach(c=>{
            let polyline = this.overheadLine(c,LINE_POINTS_NUM);
            this.polylines.push(polyline);
        })
        console.log("生成完画线结束")
        // 添加线路监听器
        // this.viewer.scene.postRender.removeEventListener(this.lineRefresh);
        // this.viewer.scene.postRender.addEventListener(this.lineRefresh);

        // 添加绝缘子监听器
        // this.viewer.scene.postRender.removeEventListener(this.insulatorRefresh);
        // this.viewer.scene.postRender.addEventListener(this.insulatorRefresh);

    }

    /**
     * 改变lien宽度
     */
    updateWidth= (widthValue)=> {
        for (let ind = 0; ind < this.polylines.length; ind++) {
            const entity = this.polylines[ind]
            entity.polyline.width.setValue(widthValue)
        };
    }

    /**
     * 添加监听器
     *
     *
     */
    lineRefresh= ()=>{
        if (this.polylines.length > 0) {
            const entity = this.polylines[0];
            const camerPosition = this.viewer.camera.position;
            let height = this.viewer.scene.globe.ellipsoid.cartesianToCartographic(camerPosition).height;
            height += this.viewer.scene.globe.ellipsoid.maximumRadius;
            if (this.viewer.camera.positionCartographic.height < 50000000) {
                let maxHeight = 6379523  // 广告牌可视的最大高度
                if (height > 0 && height < maxHeight) {
                    if (height > 6378631.484850452) {
                        this.updateWidth(0.3)
                    } else  if (height > 6378332.18703183) {
                        this.updateWidth(0.5)
                        // entity.polyline.width.setValue(2)
                    } else {
                        this.updateWidth(1.5)
                    }
                } else {
                    this.updateWidth(0.1)
                    // entity.polyline.width = 0.5
                }
            } else {
                entity.polyline.width.setValue(0.1)
            }
        }
    }

    insulatorRefresh= ()=>{
        for (let ind = 0; ind < this.insulatorEntities.length; ind++) {
            const entity = this.insulatorEntities[ind];
            // 获取当前时间
            let currentTime = Cesium.JulianDate.now();
            const position = entity.position.getValue(currentTime);
            var distance = Cesium.Cartesian3.distance(this.viewer.camera.position, position);
            entity.show = (distance < MODEL_SHOW_DISTANCE.insulator);
        };
    }

    /**
 * 计算两点之间的水平距离和方位角
 * @param {array} origin [lon, lat, height]
 * @param {array} target [lon, lat, height]
 * @returns 水平距离和方位角
 */
    distance_ang =(origin, target)=> {
        let geodesic = new Cesium.EllipsoidGeodesic(Cesium.Cartographic.fromDegrees(...origin), Cesium.Cartographic.fromDegrees(...target));
        return [geodesic.surfaceDistance, geodesic.endHeading];
    }

    overheadLine=(lineData,pointNum)=> {
        if(lineData.start == null || lineData.end === null) {
            return
        }
        let len_ang = this.distance_ang(lineData.start, lineData.end); // 计算两点距离和方向角
        let sagHeight = (lineData.start[2] < lineData.end[2] ? lineData.start[2] : lineData.end[2]) - 5; // 设置弧垂最低点的高度
        let StartPoint = { x: 0, y: lineData.start[2] };
        let EndPoint = { x: len_ang[0], y: lineData.end[2] };
        let res = catenaryLineArr(StartPoint, EndPoint, sagHeight, pointNum);
        // console.log('res:', res); // 计算得到x y数组

        // let res = catenaryLine(StartPoint, EndPoint, sagHeight, pointNum);
        // console.log("generateCatenaryPoints总耗时：", new Date().getTime() - start_time);
        // console.log('generateCatenaryPoints res:', res); // 计算得到x y数组

        let pointPos = [];
        res.forEach(el => {
            let pos = this.getMountPoint3(lineData.start[0] * Math.PI / 180, lineData.start[1] * Math.PI / 180, len_ang[1], el[0])
            pointPos.push([pos.lon, pos.lat, el[1]]);
        });
        // console.log('pointPos', pointPos);

        // let line = addline(polylines, { positions: pointPos, color:color, width:width });
        let _pos = [];
        let _pos2 = [];
        pointPos.forEach(p => {
            _pos.push(new Cesium.Cartesian3.fromRadians(...p));
            _pos2.push(p[0]/Math.PI*180, p[1]/Math.PI*180, p[2]);
        });
        try{
        let entity = {
            id:  `line@${lineData.ID}` || `line@${new Date().getTime()}线`,
            name:  `${new Date().getTime()}线`,
            show:true,
            polyline: {
                show:true,
                positions: _pos,//Cesium.Cartesian3.fromDegreesArrayHeights(p),
                width: 1,
                material: new Cesium.Color.fromCssColorString(Constants.PHASE_COLOR[lineData.phase]),
                // disableDepthTestDistance : 0
            },
        }

        let computeCircle = (radius)=> {
            const positions = [];
            for (let i = 0; i < 6; i++) {
              const radians = Cesium.Math.toRadians(i*60);
              positions.push(
                new Cesium.Cartesian2(
                  radius * Math.cos(radians),
                  radius * Math.sin(radians)
                )
              );
            }
            return positions;
          }

        // 使用另一种电缆加载方式 by lkj
        let polylineVolumeInfo = {
            id: `line@${lineData.ID}` || `line@${new Date().getTime()}线`,
            polylineVolume: {
                positions: Cesium.Cartesian3.fromDegreesArrayHeights(_pos2),
                shape: computeCircle(0.040),
                outline: true,
                outlineColor: new Cesium.Color.fromCssColorString(Constants.PHASE_COLOR[lineData.phase]),
                outlineWidth: 0.001,
                material: new Cesium.Color.fromCssColorString(Constants.PHASE_COLOR[lineData.phase]),
            }
        }

        var polyline_entity = this.viewer.entities.add(entity);
        // polyline_entity.polyline.disableDepthTestDistance = Number.POSITIVE_INFINITY;
        // var polyline_entity = this.viewer.entities.add(polylineVolumeInfo);
        return polyline_entity;
    }catch(e){
        // console.log(`phase is ${lineData.phase}`,e.message)
    }
    }
    /**
*
* @param {*} lon 经度
* @param {*} lat 纬度
* @param {*} brng 方位角 0~360度
* @param {*} dist 距离(米)
*
*/
 getMountPoint3=(lon, lat, brng, dist)=> {
    var a = 6378137;
    var b = 6356752.3142;
    var f = 1 / 298.257223563;

    var lon1 = lon * 180 / Math.PI;
    var lat1 = lat * 180 / Math.PI;
    var s = dist;
    var alpha1 = brng //  * (Math.PI / 180)

    var sinAlpha1 = Math.sin(alpha1);
    var cosAlpha1 = Math.cos(alpha1);
    var tanU1 = (1 - f) * Math.tan(lat1 * (Math.PI / 180));
    var cosU1 = 1 / Math.sqrt((1 + tanU1 * tanU1)), sinU1 = tanU1 * cosU1;
    var sigma1 = Math.atan2(tanU1, cosAlpha1);
    var sinAlpha = cosU1 * sinAlpha1;
    var cosSqAlpha = 1 - sinAlpha * sinAlpha;
    var uSq = cosSqAlpha * (a * a - b * b) / (b * b);
    var A = 1 + uSq / 16384 * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));
    var B = uSq / 1024 * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));
    var sigma = s / (b * A), sigmaP = 2 * Math.PI;
    while (Math.abs(sigma - sigmaP) > 1e-12) {
        var cos2SigmaM = Math.cos(2 * sigma1 + sigma);
        var sinSigma = Math.sin(sigma);
        var cosSigma = Math.cos(sigma);
        var deltaSigma = B * sinSigma * (cos2SigmaM + B / 4 * (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM) -
            B / 6 * cos2SigmaM * (-3 + 4 * sinSigma * sinSigma) * (-3 + 4 * cos2SigmaM * cos2SigmaM)));
        sigmaP = sigma;
        sigma = s / (b * A) + deltaSigma;
    }

    var tmp = sinU1 * sinSigma - cosU1 * cosSigma * cosAlpha1;
    var lat2 = Math.atan2(sinU1 * cosSigma + cosU1 * sinSigma * cosAlpha1,
        (1 - f) * Math.sqrt(sinAlpha * sinAlpha + tmp * tmp));
    var lambda = Math.atan2(sinSigma * sinAlpha1, cosU1 * cosSigma - sinU1 * sinSigma * cosAlpha1);
    var C = f / 16 * cosSqAlpha * (4 + f * (4 - 3 * cosSqAlpha));
    var L = lambda - (1 - C) * f * sinAlpha *
        (sigma + C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));

    var revAz = Math.atan2(sinAlpha, -tmp); // final bearing

    var lngLatObj = { lon: lon + L, lat: lat2 }
    return lngLatObj;
}
    /**生成一回路的线路数据
     * @param{Object} line 回路信息
     * @param{Array} lineDetails 回路详情列表信息
     */
    generateLineDatas = (line,lineDetails,isxf)=>{
        this.line=line
        this.lineDetails = lineDetails
        var lineDatas = []
        for(var i=0;i<lineDetails.length;i++){
            var curTower = lineDetails[i]
            var nextTower = (lineDetails.length== i+1) ? curTower : lineDetails[i+1]
            var phaseSequence = line.phaseSequence || 'ABC'
            // var lineId = line.lineId
            // 中期检查，先固定好相序，后面调整
            // if (lineId === 101) {
            //     if (nextTower === 'A57+1') {
            //         phaseSequence = 'CBA'
            //     } else {
            //         phaseSequence = 'BCA'
            //     }
            // } else if (lineId === 102) {
            //     phaseSequence = 'CBA'
            // } else if (lineId === 104) {
            //     if (nextTower === 'B17' || nextTower === 'B18') {
            //         phaseSequence = 'BAC'
            //     } else if (nextTower === 'B15' || nextTower === 'B16') {
            //         phaseSequence = 'ABC'
            //     } else {
            //         phaseSequence = 'BAC'
            //     }
            // }
            var lds = this.generateLineData(curTower,nextTower, phaseSequence,isxf, i)
            if(lds){
                lineDatas.push(...lds)
            }
        }
        // for(let key in this.dixian) {
        //     for (let key1 in this.dixian[key]) {
        //         var houdi_flag = false
        //         var qiandi_flag = false
        //         for (let key2 in this.dixian[key][key1]) {
        //             if (key2 === 'houdi') houdi_flag= true
        //             if (key2 === 'qiandi') qiandi_flag= true
        //         }
        //         if(houdi_flag && qiandi_flag) {
        //             var ld = this.tem(this.dixian[key][key1]['houdi'], this.dixian[key][key1]['qiandi'], this.dixian[key][key1]['phase'], `${key}${key1}test`)
        //             lineDatas.push(...ld)
        //         }
        //     }
        // }
        return lineDatas
    }
    /**
     *
     * @param {Object} curTower 当
     * @param {Object} nextTower
     * @param {string} phaseSequence  回路中的相序信息，如ABC
     * @returns
     */
    generateLineData = (curTower,nextTower,phaseSequence, isxf=null, num=null)=>{
        var curTowerModel = this.towerModels.find(c=>c.towerModel == curTower.towerModel)
        var nextTowerModel =nextTower? this.towerModels.find(c=>c.towerModel == nextTower.towerModel) : curTowerModel

        if(!(curTowerModel && nextTowerModel)){
            console.log(`杆塔 ${curTower.towerModel} 或 ${nextTower.towerModel} TowerModel不正确，缺少对应的TowerModels数据`, curTower, nextTower)
            return null
        }
        //显示塔
        this.addTowerToViewer(curTower,curTowerModel)
        //显示塔标牌
        this.addTowerLabelToViewer({tower:curTower})
        // this.addTowerLabelToViewer(curTower, curTowerModel,isxf, num)
        
        //前后两塔中有一塔状态为非完成状态时，不加载绝缘子及线路信息
        let curTowerProcedureStatus = curTower.procedureStatus
        let nextTowerProcedureStatus = nextTower.procedureStatus
        if(this.flag) {
            if(this.planRecordMap.hasOwnProperty(curTower.towerId)) {
                curTowerProcedureStatus = this.planRecordMap[curTower.towerId].procedureStatus
            } else {
                curTowerProcedureStatus = 0
            }
            if(this.planRecordMap.hasOwnProperty(nextTower.towerId)) {
                nextTowerProcedureStatus = this.planRecordMap[nextTower.towerId].procedureStatus
            } else {
                nextTowerProcedureStatus = 0
            }
        }
        if((curTowerProcedureStatus != Constants.PROGRESS_5 || nextTowerProcedureStatus != Constants.PROGRESS_5) && !this.showDesign){
            return []
        }
        // 最后一个杆塔不连
        if (curTower.towerId === nextTower.towerId ) {
            return []
        }
        //>104线路的是额外处理羊角塔（酒杯塔）地线
        // if (curTower.lineId == 107||curTower.lineId==108) {
        //     // if (curTower.lineId > 104) {
        //     if (curTower.towerId === 'A57') {
        //         // 手动修正下A57杆塔的 角度
        //         curTower.angle = -0.6285808742583683
        //     }
        //     if (curTower.towerId === 'B14') {
        //         // 手动修正下A57杆塔的 角度
        //         curTower.angle = -12.042823449538893
        //     }
        //     var lds = []
        //     // - 原点坐标
        //     if(curTower.origin == undefined){
        //         const origin = Cesium.Cartographic.fromDegrees(curTower.longitude, curTower.latitude, curTower.height);
        //         curTower.originCartesian = this.viewer.scene.globe.ellipsoid.cartographicToCartesian(origin);
        //     }
        //     if(nextTower.origin == undefined){
        //         const nextOrigin = Cesium.Cartographic.fromDegrees(nextTower.longitude, nextTower.latitude, nextTower.height);
        //         nextTower.originCartesian = this.viewer.scene.globe.ellipsoid.cartographicToCartesian(nextOrigin);
        //     }
        //     var ld_g = this.generateLineDataByNumber(curTower,curTowerModel,nextTower,nextTowerModel,'G',['houdi'],['qiandi'],curTower.lineLocation%2 ==1 ?1:2,nextTower.lineLocation%2 ==1 ?1:2)
        //     lds.push(...ld_g)
        //     return lds
        // }
        // 塔状态为完成状态时，加载绝缘子及线路
        return this.addInsulatorToViewer(curTower,curTowerModel,nextTower,nextTowerModel,phaseSequence)
    }
    /**
     * 增加绝缘子到Viewer,同时返回可以显示的线路集合
     * 前一个塔不处理前导，后一个塔不处理后导
     * @param {*} tower
     * @param {*} towerModel
     * @param {*} nextTower
     * @param {*} nextTowerModel
     */
    addInsulatorToViewer = (tower,towerModel,nextTower,nextTowerModel,linePhase)=>{
        var lds = []
        var numbers = this.takePointNums(tower.lineLocation)
        var nextNumbers = this.takePointNums(nextTower.lineLocation)

        // - 原点坐标
        if(tower.origin == undefined){
            const origin = Cesium.Cartographic.fromDegrees(tower.longitude, tower.latitude, tower.height);
            tower.originCartesian = this.viewer.scene.globe.ellipsoid.cartographicToCartesian(origin);
        }
        if(nextTower.origin == undefined){
            const nextOrigin = Cesium.Cartographic.fromDegrees(nextTower.longitude, nextTower.latitude, nextTower.height);
            nextTower.originCartesian = this.viewer.scene.globe.ellipsoid.cartographicToCartesian(nextOrigin);
        }
        //处理地线
        // 额外处理地线颜色 需要通过数据库后端获取
        let G_phase = 'G';
        if (tower.lineId >= 104) {
            if (tower.lineId > 104
                || tower.towerId === 'B5'
                || tower.towerId === 'B6'
                || tower.towerId === 'B14'
                || tower.towerId === 'B15'
                || tower.towerId === 'B16'
                || tower.towerId === 'B17') {
                    G_phase = 'G1'
            }
        }
        
        var ld_g = this.generateLineDataByNumber(tower,towerModel,nextTower,nextTowerModel,G_phase,['houdi'],['qiandi'],tower.lineLocation%2 ==1 ?1:2,nextTower.lineLocation%2 ==1 ?1:2)
        lds.push(...ld_g)

        //changed 2023/09/05
        // if(!tower.isEnd){
        //     ld_g = this.generateLineDataByNumber(tower,towerModel,tower,towerModel,'G',['qiandi'],['houdi'],tower.lineLocation%2 ==1 ?1:2,tower.lineLocation%2 ==1 ?1:2)
        //     lds.push(...ld_g)
        // }


        numbers.forEach((number,index)=>{
            const phase = linePhase.charAt(index)
            if(towerModel.towerStyle == '耐张塔' && nextTowerModel.towerStyle == '耐张塔' ){
                var ld = this.generateLineDataByNumber(tower, towerModel, nextTower, nextTowerModel, phase, ['zhongdao', 'houdao'], ['qiandao', 'zhongdao'], number, nextNumbers[index])
                lds.push(...ld)
            }
            else if(towerModel.towerStyle == '悬垂塔' && nextTowerModel.towerStyle == '悬垂塔'){
                var ld = this.generateLineDataByNumber(tower,towerModel,nextTower,nextTowerModel,phase,['zhongdao'],['zhongdao'],number,nextNumbers[index])
                lds.push(...ld)

            }else if(towerModel.towerStyle == '耐张塔' && nextTowerModel.towerStyle == '悬垂塔'){
                var ld = this.generateLineDataByNumber(tower,towerModel,nextTower,nextTowerModel,phase,['zhongdao','houdao'],['zhongdao'],number,nextNumbers[index])
                lds.push(...ld)
            }else if(towerModel.towerStyle == '悬垂塔' && nextTowerModel.towerStyle == '耐张塔'){
                var ld = this.generateLineDataByNumber(tower,towerModel,nextTower,nextTowerModel,phase,['zhongdao'],['qiandao','zhongdao'],number,nextNumbers[index])
                lds.push(...ld)

            }

        })
        return lds
    }
    generateLineDataByNumber=(tower,towerModel,nextTower,nextTowerModel,phase,nowMountNames,nextMountNames,nowNumber,nextNumber)=>{
        let result = [] // 保存每条线的信息 本函数最终返回的结果

        // ---------- 第一步 先处理两个杆塔连接部分的线路 ----------
        // insulator1 
        let point1Info = this.takePointInfo(tower,towerModel,nowMountNames[nowMountNames.length-1],nowNumber)
        if(!point1Info || point1Info.insulatorNo === '/' || point1Info.insulatorNo === '' || point1Info.position.length === 0){
            console.log(`线路${tower.lineId}中${tower.towerId}缺少${nowMountNames[nowMountNames.length-1]}${nowNumber}信息或绝缘子配串为'/'`)
            return result
        }
        let insulator1 = this.takeInsulatorInfo(tower, point1Info) // 获取挂载的绝缘子信息
        if(insulator1==undefined){
            console.log(`${tower.towerId} ${point1Info.insulatorNo} 绝缘子信息缺失,检查InsulatorModels表中的是否存在相应的数据`, point1Info)
            return result
        }
        let insulator1Position = this.getMountPosition(point1Info.position,tower.originCartesian,tower.angle) // 获取绝缘子挂载的实际位置 经纬度
        // insulator2
        let point2Info = this.takePointInfo(nextTower,nextTowerModel,nextMountNames[0],nextNumber)
        if(!point2Info || point2Info.insulatorNo === '/' || point2Info.insulatorNo === ''  || point2Info.position.length === 0){
            console.log(`线路${nextTower.lineId}中${nextTower.towerId}缺少${nextMountNames[0]}${nextNumber}信息或绝缘子配串为'/'`)
            return result
        }
        let insulator2 = this.takeInsulatorInfo(nextTower, point2Info) // 获取挂载的绝缘子信息
        if(insulator2==undefined){
            console.log(`${nextTower.towerId} ${point2Info.insulatorNo} 绝缘子信息缺失,检查InsulatorModels表中的是否存在相应的数据`)
            return result
        }
        let insulator2Position = this.getMountPosition(point2Info.position, nextTower.originCartesian, nextTower.angle) // 获取绝缘子挂载的实际位置 经纬度
        if((!insulator2Position) || insulator2Position.length == 0){
            // debugger
            let dd= this.getMountPosition(point2Info.position, nextTower.originCartesian, nextTower.angle)
        }
        // 计算夹角 加载模型 连接电缆
        let angle = this.calAzimuth(insulator1Position,insulator2Position); // 两个绝缘子的水平夹角
        let insulator1_angRaid = (nowMountNames[nowMountNames.length-1]=='zhongdao')?(tower.angle * Math.PI / 180):angle + Math.PI/2
        let insulator2_angRaid = (nextMountNames[0]=='zhongdao')?(nextTower.angle * Math.PI / 180):angle - Math.PI/2
        this.addOneInsulator({
            id: `${tower.towerId}@${point1Info.name}@${point1Info.number}@${insulator1.modelName}`,
            insulatorModel: insulator1,
            angRaid: insulator1_angRaid,
            mounts: insulator1Position
        })
        this.insulator_Internal_line({
            lineID_prefix: `${tower.lineId}-${tower.towerId}-${tower.towerId}-${phase}-${point1Info.name}`,
            insulatorInfo: insulator1,
            mountPoint: insulator1Position,
            heading: insulator1_angRaid,
            phase: phase,
        });
        this.addOneInsulator({
            id: `${nextTower.towerId}@${point2Info.name}@${point2Info.number}@${insulator2.modelName}`,
            insulatorModel: insulator2,
            angRaid: insulator2_angRaid,
            mounts: insulator2Position
        })
        this.insulator_Internal_line({
            lineID_prefix: `${nextTower.lineId}-${nextTower.towerId}-${nextTower.towerId}-${phase}-${point1Info.name}`,
            insulatorInfo: insulator2,
            mountPoint: insulator2Position,
            heading: insulator2_angRaid,
            phase: phase,
        });
        let insulator1Mounts = this.takeInsulatorMount(insulator1) // 根据绝缘子类型 获取绝缘子的挂点信息
        let insulator1_mountAng = (insulator1.modelType == '耐张串'&&insulator2.lineType!='地线')?insulator1_angRaid+Math.PI/2:insulator1_angRaid-Math.PI/2 // 由于绝缘子模型挂点位置不一致 需要处理计算的角度
        let insulator1GisMounts = this.convertInsulatorMount2GIS(insulator1Position, insulator1Mounts,insulator1_mountAng) // 根据挂点信息 计算得到绝对位置
        let insulator2Mounts = this.takeInsulatorMount(insulator2) // 根据绝缘子类型 获取绝缘子的挂点信息
        let insulator2_mountAng = (insulator2.modelType == '耐张串'&&insulator2.lineType!='地线')?insulator2_angRaid+Math.PI/2:insulator2_angRaid-Math.PI/2 // 由于绝缘子模型挂点位置不一致 需要处理计算的角度
        let insulator2GisMounts = this.convertInsulatorMount2GIS(insulator2Position, insulator2Mounts,insulator2_mountAng) // 更具挂点信息 计算得到绝对位置
        let ld = this.generateLineDataByMounts(insulator1GisMounts,insulator2GisMounts,insulator1,insulator2,tower,nextTower,phase,`${nowMountNames[nowMountNames.length - 1]}${nowNumber}TO${nextMountNames[0]}${nextNumber}`)
        result.push(...ld)

        // ---------- 第二步 处理单个杆塔内部连接部分的线路 ----------
        if(nowMountNames.length>1){
            //insulator3 如果是内部连接 这里就是中导位置的绝缘子
            let point3Info = this.takePointInfo(tower,towerModel,nowMountNames[0],nowNumber)
            if(!point3Info || point3Info.insulatorNo === '/' || point2Info.insulatorNo === ''){
                // console.log(`${tower.towerId}缺少${nowMountNames[0]}${nowNumber}信息或绝缘子配串为'/'`)
                // ** 当没有中导的绝缘子配串时 尝试寻找该杆塔是否存在已经加载的前导 如果存在则连接前导-后导电缆
                // B5@后导5@5@SA15611S-A0201-23-2
                point3Info = this.takePointInfo(tower, towerModel, 'qiandao', nowNumber)
                if (!point3Info || point3Info.insulatorNo === '/' || point3Info.position.length === 0) {
                    console.log(`${tower.towerId}缺少${nowMountNames[0]}${nowNumber}信息或绝缘子配串为'/'  ${JSON.stringify(point3Info)}`)
                } else {
                    let insulator3 = this.takeInsulatorInfo(tower, point3Info) // 获取挂载的绝缘子信息
                    if(insulator3==undefined){
                        console.log(`${tower.towerId} ${point3Info.insulatorNo} 绝缘子信息缺失,检查InsulatorModels表中的是否存在相应的数据`)
                    } else {
                        let _entityID = `insulator@${tower.towerId}@${point3Info.name}@${point3Info.number}@${insulator3.modelName}`
                        let _entity = this.viewer.entities.getById(_entityID)
                        if (_entity) {
                            let insulator3Position = _entity.userData.position
                            let insulator3Mounts = this.takeInsulatorMount(insulator3) // 根据绝缘子类型 获取绝缘子的挂点信息
                            let insulator3_mountAng = _entity.userData.angle + Math.PI / 2 // 由于绝缘子模型挂点位置不一致 需要处理计算的角度
                            let insulator3GisMounts = this.convertInsulatorMount2GIS(insulator3Position, insulator3Mounts, insulator3_mountAng) // 根据挂点信息 计算得到绝对位置
                            let ld = this.generateLineDataByMounts(insulator3GisMounts, insulator1GisMounts, insulator3, insulator1, tower, tower, phase, `qiandao${nowNumber}TO${nowMountNames[1]}${nowNumber}`)
                            result.push(...ld)
                        }
                    }
                }
            }else if(point3Info.position.length!=0){
                let insulator3 = this.takeInsulatorInfo(tower, point3Info) // 获取挂载的绝缘子信息
                if (insulator3 == undefined) {
                    console.log(`${tower.towerId} ${point3Info.insulatorNo} 绝缘子信息缺失,检查InsulatorModels表中的是否存在相应的数据`)
                } else {
                    let insulator3Position = this.getMountPosition(point3Info.position, tower.originCartesian, tower.angle) // 获取绝缘子挂载的实际位置 经纬度
                    let insulator3_angRaid = (tower.angle * Math.PI / 180)
                    let entity = this.addOneInsulator({
                        id: `${tower.towerId}@${point3Info.name}@${point3Info.number}@${insulator3.modelName}`,
                        insulatorModel: insulator3,
                        angRaid: insulator3_angRaid,
                        mounts: insulator3Position
                    })
                    this.insulator_Internal_line({
                        lineID_prefix: `${tower.lineId}-${tower.towerId}-${tower.towerId}-${phase}-${point3Info.name}`,
                        insulatorInfo: insulator3,
                        mountPoint: insulator3Position,
                        heading: insulator3_angRaid,
                        phase: phase,
                    });
                    let insulator3Mounts = this.takeInsulatorMount(insulator3) // 根据绝缘子类型 获取绝缘子的挂点信息
                    let insulator3_mountAng = insulator3_angRaid - Math.PI / 2 // 由于绝缘子模型挂点位置不一致 需要处理计算的角度
                    let insulator3GisMounts = this.convertInsulatorMount2GIS(insulator3Position, insulator3Mounts, insulator3_mountAng) // 根据挂点信息 计算得到绝对位置
                    let ld = this.generateLineDataByMounts(insulator3GisMounts, insulator1GisMounts, insulator3, insulator1, tower, tower, phase, `${nowMountNames[0]}${nowNumber}TO${nowMountNames[1]}${nowNumber}`)
                    result.push(...ld)
                }
            }
        }
        if(nextMountNames.length>1){
            // insulator4 杆塔内部两个绝缘子连线 
            let point4Info = this.takePointInfo(nextTower,nextTowerModel,nextMountNames[1],nextNumber)
            if(!point4Info || point4Info.insulatorNo === '/' || point4Info.position.length === 0){
                console.log(`${nextTower.towerId}缺少${nextMountNames[1]}${nextNumber}信息或绝缘子配串为'/' ${JSON.stringify(point4Info)}`)
            }else{
                let insulator4 = this.takeInsulatorInfo(nextTower, point4Info) // 获取挂载的绝缘子信息
                if (insulator4 == undefined) {
                    console.log(`${nextTower.towerId} ${point4Info.insulatorNo} 绝缘子信息缺失,检查InsulatorModels表中的是否存在相应的数据`, point4Info)
                } else {
                    let insulator4Position = this.getMountPosition(point4Info.position, nextTower.originCartesian, nextTower.angle) // 获取绝缘子挂载的实际位置 经纬度
                    let insulator4_angRaid = (nextTower.angle * Math.PI / 180)
                    let entity = this.addOneInsulator({
                        id: `${nextTower.towerId}@${point4Info.name}@${point4Info.number}@${insulator4.modelName}`,
                        insulatorModel: insulator4,
                        angRaid: insulator4_angRaid,
                        mounts: insulator4Position
                    })
                    this.insulator_Internal_line({
                        lineID_prefix: `${nextTower.lineId}-${nextTower.towerId}-${nextTower.towerId}-${phase}-${point4Info.name}`,
                        insulatorInfo: insulator4,
                        mountPoint: insulator4Position,
                        heading: insulator4_angRaid,
                        phase: phase,
                    });
                    let insulator4Mounts = this.takeInsulatorMount(insulator4) // 根据绝缘子类型 获取绝缘子的挂点信息
                    let insulator4_mountAng = insulator4_angRaid - Math.PI / 2 // 由于绝缘子模型挂点位置不一致 需要处理计算的角度
                    let insulator4GisMounts = this.convertInsulatorMount2GIS(insulator4Position, insulator4Mounts, insulator4_mountAng) // 根据挂点信息 计算得到绝对位置
                    let ld = this.generateLineDataByMounts(insulator2GisMounts, insulator4GisMounts, insulator2, insulator4, nextTower, nextTower, phase, `${nextMountNames[0]}${nextNumber}TO${nextMountNames[1]}${nextNumber}`)
                    result.push(...ld)
                }
            }
        }
        // ---------- 第三步 处理地线线路 连接前后地线串电缆 ----------
        if (phase == 'G' || phase == 'G1') {
            let point3Info = this.takePointInfo(tower,towerModel,'qiandi',nowNumber)
            if(!point3Info || point3Info.insulatorNo === '/' || point3Info.position.length === 0){
                console.log(`${nextTower.towerId}缺少${nextMountNames[1]}${nextNumber}信息或绝缘子配串为'/' ${JSON.stringify(point4Info)}`)
            }
            let insulator3 = this.takeInsulatorInfo(tower, point3Info) // 获取挂载的绝缘子信息
            if (insulator3 == undefined) {
                console.log(`${tower.towerId} ${point3Info.insulatorNo} 绝缘子信息缺失,检查InsulatorModels表中的是否存在相应的数据`, point3Info)
            } else {
                let _entityID = `insulator@${tower.towerId}@${point3Info.name}@${point3Info.number}@${insulator3.modelName}`
                let _entity = this.viewer.entities.getById(_entityID)
                if (_entity) {
                    let insulator3Position = _entity.userData.position
                    let insulator3Mounts = this.takeInsulatorMount(insulator3) // 根据绝缘子类型 获取绝缘子的挂点信息
                    let insulator3_mountAng = _entity.userData.angle - Math.PI / 2 // 由于绝缘子模型挂点位置不一致 需要处理计算的角度
                    let insulator3GisMounts = this.convertInsulatorMount2GIS(insulator3Position, insulator3Mounts, insulator3_mountAng) // 根据挂点信息 计算得到绝对位置
                    let ld = this.generateLineDataByMounts(insulator3GisMounts, insulator1GisMounts, insulator3, insulator1, tower, tower, phase, `qiandi${nowNumber}TOhoudi${nowNumber}`)
                    result.push(...ld)
                }
            }
        }        
        return result
        // if(nowMountNames.length>0){
        //     // 处理同杆塔内部连接
        //     for(let ind=1;ind<nowMountNames.length;ind++){
        //         let point2Info = this.takePointInfo(tower,towerModel,nowMountNames[ind],nowNumber)
        //         if(!point2Info || point2Info.insulatorNo === '/'){
        //             console.log(`${tower.towerId}缺少${nowMountNames[ind]}${nowNumber}信息或绝缘子配串为'/'`)
        //             return result
        //         }
        //         let insulator2 = this.takeInsulatorInfo(tower, point2Info) // 获取挂载的绝缘子信息
        //         let insulator1Position = this.getMountPosition(point2Info.position, tower.originCartesian, tower.angle) // 获取绝缘子挂载的实际位置 经纬度
        //     }
        // }





        var lastNow = this.takePointInfo(tower,towerModel,nowMountNames[nowMountNames.length - 1],nowNumber)
        if(!lastNow){
            console.log(`${tower.towerId}缺少${nowMountNames[nowMountNames.length - 1]}${nowNumber}信息`)
            return []
        }
        var lastNowPoint =  this.getMountPosition(lastNow.position,tower.originCartesian,tower.angle)
        if(lastNow.insulatorNo === '/'){
            // console.log('lastNow')
            return []
        }
        var lastInsulator = this.takeInsulatorInfo(tower,lastNow)
        var lastNowMounts = this.takeInsulatorMount(lastInsulator,phase)


        var nextFirst = this.takePointInfo(nextTower,nextTowerModel,nextMountNames[0],nextNumber)
        if(!nextFirst){
            console.log(`${nextTower.towerId}缺少${nextMountNames[0]}${nextNumber}信息`)
            return []
        }
        var nextFirstPoint =  this.getMountPosition(nextFirst.position,nextTower.originCartesian,nextTower.angle)
        if (nextFirst.insulatorNo === '/') {
            // console.log('nextFirst')
            return []
        }
        var nextFirstInsulator = this.takeInsulatorInfo(nextTower,nextFirst)
        var nextFirstMounts = this.takeInsulatorMount(nextFirstInsulator,phase)


        let jiajiao = this.calAzimuth(lastNowPoint,nextFirstPoint);
        // var lastNowGisMounts = this.convertInsulatorMount2GIS(lastNowPoint, lastNowMounts,nowMountNames.length ==1?(jiajiao+ Math.PI):jiajiao)
        var lastNowGisMounts = this.convertInsulatorMount2GIS(lastNowPoint, lastNowMounts,nowMountNames.length ==1?jiajiao-Math.PI/2:jiajiao+Math.PI/2)
        var lastNowGisMounts1 = this.convertInsulatorMount2GIS(lastNowPoint, [[lastInsulator.point1]], nowMountNames.length ==1?jiajiao-Math.PI/2:jiajiao+Math.PI/2)
        // var lastNowGisMounts1 = this.convertInsulatorMount2GIS(lastNowPoint, [[lastInsulator.point1]],nowMountNames.length ==1?(jiajiao+ Math.PI):jiajiao)
        // var nextFirstGisMounts = this.convertInsulatorMount2GIS(nextFirstPoint, nextFirstMounts,(nextMountNames.length ==3 &&phase != 'G')?0:(jiajiao))
        var nextFirstGisMounts = this.convertInsulatorMount2GIS(nextFirstPoint, nextFirstMounts,(nextMountNames.length ==2 &&phase != 'G')?(jiajiao- Math.PI/2):jiajiao-Math.PI/2)
        // var nextFirstGisMounts = this.convertInsulatorMount2GIS(nextFirstPoint, nextFirstMounts,(nextMountNames.length ==3 &&phase != 'G')?0:(jiajiao+ Math.PI))
        var nextFirstGisMounts1
        if(phase == 'G'){
            nextFirstGisMounts = this.convertInsulatorMount2GIS(nextFirstPoint, nextFirstMounts,jiajiao+Math.PI/2)
            // nextFirstGisMounts = this.convertInsulatorMount2GIS(nextFirstPoint, nextFirstMounts,(nextMountNames.length ==3 &&phase != 'G')?0:(jiajiao))
            nextFirstGisMounts1 = this.convertInsulatorMount2GIS(nextFirstPoint, [[nextFirstInsulator.point1]],jiajiao+Math.PI/2)
            // nextFirstGisMounts1 = this.convertInsulatorMount2GIS(nextFirstPoint, [[nextFirstInsulator.point1]],(nextMountNames.length ==3 &&phase != 'G')?0:(jiajiao))
        }
        if (lastNow.insulatorNo === '/') {
            // console.log(lastNow.insulatorNo)
        } else {
            // if(nowMountNames)
            this.addOneInsulator(lastNow,lastNowPoint,this.takeInsulatorInfo(tower,lastNow),tower,nowMountNames.length ==3?0:jiajiao+ Math.PI/2)
            // this.addOneInsulator({
            //     insulatorModel: lastInsulator,
            //     id: `${tower.towerId}@${lastNow.name}@${}`
            // })
            let options = {
                lineID_prefix: `${tower.lineId}-${tower.towerId}-${tower.towerId}-${phase}-${lastNow.name}`,
                insulatorInfo: this.takeInsulatorInfo(tower, lastNow),
                mountPoint: lastNowPoint,
                heading: nowMountNames.length ==3?0:jiajiao+ Math.PI/2,
                phase: phase,
            }
            this.insulator_Internal_line(options);
        }
        if (nextFirst.insulatorNo === '/') {
            // console.log(nextFirst.insulatorNo)
        } else {
            let _jiajiao = null;
            if(nextMountNames.length ==2 &&phase != 'G'){
                // 耐张串
                _jiajiao = jiajiao- Math.PI/2
            }else if(phase == 'G'){
                // 地线串
                _jiajiao = jiajiao- Math.PI/2
            }else{
                // V型串
                _jiajiao = jiajiao+ Math.PI/2
            
            }
            // this.addOneInsulator(nextFirst,nextFirstPoint,this.takeInsulatorInfo(nextTower,nextFirst),nextTower,(nextMountNames.length ==3 &&phase != 'G')?jiajiao+ Math.PI:(jiajiao))
            this.addOneInsulator(nextFirst,nextFirstPoint,this.takeInsulatorInfo(nextTower,nextFirst),nextTower,_jiajiao)
            let options = {
                lineID_prefix: `${nextTower.lineId}-${nextTower.towerId}-${nextTower.towerId}-${phase}-${nextFirst.name}`,
                insulatorInfo: this.takeInsulatorInfo(nextTower, nextFirst),
                mountPoint: nextFirstPoint,
                heading: (nextMountNames.length ==2 &&phase != 'G')?(jiajiao- Math.PI/2):jiajiao+ Math.PI/2,
                phase: phase,
            }
            this.insulator_Internal_line(options);
        }
        // 额外处理地线颜色
        if (phase === 'G' && tower.lineId >= 104) {
            if (tower.lineId > 104
                || tower.towerId === 'B5'
                || tower.towerId === 'B6'
                || tower.towerId === 'B14'
                || tower.towerId === 'B15'
                || tower.towerId === 'B16'
                || tower.towerId === 'B17') {
                phase = 'G1'
            }
        }
        // 为了中期汇报，先手动添加地线额外处理
        // 将杆塔下的前地point1点  跟后地point1 点连起来
        if (phase == 'G' || phase === 'G1') {
            var lineId = tower.lineId
            if (this.dixian.hasOwnProperty(tower.towerId)) {
                var locationmap  = this.dixian[tower.towerId]
                if (locationmap.hasOwnProperty(lineId)) {
                    var qiandihoudi = locationmap[lineId]
                    if (!qiandihoudi.hasOwnProperty('houdi')) {
                        qiandihoudi['houdi'] = lastNowGisMounts1
                    }
                } else {
                    var qiandihoudi = {}
                    qiandihoudi['houdi'] = lastNowGisMounts1
                    qiandihoudi['phase'] = phase
                    locationmap[lineId] = qiandihoudi
                    // this.dixian[tower.towerId] = locationmap
                }
            } else {
                var locationmap = {}
                var qiandihoudi = {}
                qiandihoudi['houdi'] = lastNowGisMounts1
                qiandihoudi['phase'] = phase
                locationmap[lineId] = qiandihoudi
                this.dixian[tower.towerId] = locationmap
            }

            if (this.dixian.hasOwnProperty(nextTower.towerId)) {
                var locationmap  = this.dixian[nextTower.towerId]
                if (locationmap.hasOwnProperty(lineId)) {
                    var qiandihoudi = locationmap[lineId]
                    if (!qiandihoudi.hasOwnProperty('qiandi')) {
                        qiandihoudi['qiandi'] = nextFirstGisMounts1
                    }
                } else {
                    var qiandihoudi = {}
                    qiandihoudi['qiandi'] = nextFirstGisMounts1
                    qiandihoudi['phase'] = phase
                    locationmap[lineId] = qiandihoudi
                    // this.dixian[tower.towerId] = locationmap
                }
            } else {
                var locationmap = {}
                var qiandihoudi = {}
                qiandihoudi['qiandi'] = nextFirstGisMounts1
                qiandihoudi['phase'] = phase
                locationmap[lineId] = qiandihoudi
                this.dixian[nextTower.towerId] = locationmap
            }
        }
        let lds = []
        // var ld = this.generateLineDataByMounts(lastNowGisMounts,nextFirstGisMounts,lastInsulator,nextFirstInsulator,tower,nextTower,phase,`${nowMountNames[nowMountNames.length - 1]}${nowNumber}TO${nextMountNames[0]}${nextNumber}`)
        // lds.push(...ld)
        if(nowMountNames.length ==1){
        }else{
            for(var i=0;i<nowMountNames.length-1;i++){
                var now1 = this.takePointInfo(tower,towerModel,nowMountNames[i],nowNumber)
                var now1Point =  this.getMountPosition(now1.position,tower.originCartesian,tower.angle)
                if (now1.insulatorNo === '/') {
                    continue
                }
                var now1Insulator = this.takeInsulatorInfo(tower,now1)
                var now1Mounts = this.takeInsulatorMount(now1Insulator,phase)
                var now1GisMounts = this.convertInsulatorMount2GIS(now1Point, now1Mounts,(tower.angle* Math.PI / 180-Math.PI) )
                if (now1.insulatorNo === '/') {
                    continue
                }
                this.addOneInsulator(now1,now1Point,this.takeInsulatorInfo(tower,now1),tower,(tower.angle * Math.PI / 180))
                let options = {
                    lineID_prefix: `${tower.lineId}-${tower.towerId}-${tower.towerId}-${phase}-${now1.name}`,
                    insulatorInfo: this.takeInsulatorInfo(tower, now1),
                    mountPoint: now1Point,
                    heading: (tower.angle * Math.PI / 180),
                    phase: phase,
                }
                this.insulator_Internal_line(options);
    
                if(tower.towerId == 'A50'){
                    // console.log(`BBBBBBBB ${tower.towerId}=${nowMountNames[i]}${nowNumber}`)
                    // console.log(now1Point,now1Mounts,now1GisMounts)
                }

                var now2 = (i+2 == nowMountNames.length)? lastNow : this.takePointInfo(tower,towerModel,nowMountNames[i+1],nowNumber)
                var now2Point = (i+2 == nowMountNames.length)? lastNowPoint : this.getMountPosition(now2.position,tower.originCartesian,tower.angle)
                if (now2.insulatorNo === '/') {
                    continue
                }
                var now2Insulator = (i+2 == nowMountNames.length)? lastInsulator : this.takeInsulatorInfo(tower,now2)
                var now2Mounts = (i+2 == nowMountNames.length)? lastNowMounts : this.takeInsulatorMount(now2Insulator,phase)
                var now2GisMounts = (i+2 == nowMountNames.length)? lastNowGisMounts : this.convertInsulatorMount2GIS(now2Point, now2Mounts,(tower.angle * Math.PI / 180))
                if(((i+2) != nowMountNames.length)){
                    if (now2.insulatorNo === '/') {
                        continue
                    }
                    this.addOneInsulator(now2,now2Point,this.takeInsulatorInfo(tower,now2),tower,(tower.angle * Math.PI / 180))
                    let options = {
                        lineID_prefix: `${tower.lineId}-${tower.towerId}-${tower.towerId}-${phase}-${now2.name}`,
                        insulatorInfo: this.takeInsulatorInfo(tower, now2),
                        mountPoint: now1Point,
                        heading: (tower.angle * Math.PI / 180),
                        phase: phase,
                    }
                    this.insulator_Internal_line(options);
                    
                }

                if(tower.towerId == 'A50'){
                    // console.log('CCCCCCCCCC',(i+2 == nowMountNames.length)?1:2)
                    // console.log(`BBBBBBBB ${tower.towerId}=${nowMountNames[i+1]}${nowNumber}`)
                    // console.log(now2Point,now2Mounts,now2GisMounts)
                }
                ld = this.generateLineDataByMounts(now1GisMounts,now2GisMounts,now1Insulator,now2Insulator,tower,tower,phase,`${nowMountNames[i]}${nowNumber}TO${nowMountNames[i+1]}${nowNumber}`)
                lds.push(...ld)
            }
        }
        if(nextMountNames.length ==1){
        }else{
            for(var i=1;i<nextMountNames.length;i++){
                var now1 = i==1 ? nextFirst : this.takePointInfo(nextTower,nextTowerModel,nextMountNames[i-1],nextNumber)
                var now1Point = i==1 ? nextFirstPoint : this.getMountPosition(now1.position,nextTower.originCartesian,nextTower.angle)
                if (now1.insulatorNo === '/') {
                    continue
                }
                var now1Insulator = i==1 ? nextFirstInsulator : this.takeInsulatorInfo(nextTower,now1)
                var now1Mounts = i==1 ? nextFirstMounts : this.takeInsulatorMount(now1Insulator,phase)
                var now1GisMounts = i==1 ? nextFirstGisMounts : this.convertInsulatorMount2GIS(now1Point, now1Mounts,(nextTower.angle * Math.PI / 180))
                if((i> 1)){
                    if (now1.insulatorNo === '/') {
                        continue
                    }
                    this.addOneInsulator(now1,now1Point,this.takeInsulatorInfo(nextTower,now1),nextTower,(nextTower.angle * Math.PI / 180))
                    let options = {
                        lineID_prefix: `${nextTower.lineId}-${nextTower.towerId}-${nextTower.towerId}-${phase}-${now1.name}`,
                        insulatorInfo: this.takeInsulatorInfo(nextTower, now1),
                        mountPoint: now1Point,
                        heading: (nextTower.angle * Math.PI / 180),
                        phase: phase,
                    }
                    this.insulator_Internal_line(options);
        
                }


                var now2 =  this.takePointInfo(nextTower,nextTowerModel,nextMountNames[i],nextNumber)
                var now2Point = this.getMountPosition(now2.position,nextTower.originCartesian,nextTower.angle)
                if (now2.insulatorNo === '/') {
                    // 专门处理中导悬垂串没有 直接前后耐张串相连的情况
                    const insulatorEntityID = `${tower.towerId}@前导${nowNumber}@${nowNumber}@${now1Insulator.modelName}`
                    let houdao_insulator_model_msg = this.viewer.entities.getById(insulatorEntityID);
                    if(nowMountNames[1] === 'houdao' && houdao_insulator_model_msg!=undefined){
                        // 这里需要拿到当前杆塔的后导绝缘子信息 得到绝缘子的角度 KYA2@前导5@5@D0302-23-1
                        let last_houdao_angle = houdao_insulator_model_msg.userData.angle;
                        let last_insulator_pointInfo = this.takePointInfo(tower,towerModel,'qiandao',nowNumber);
                        let last_insulator_point = this.getMountPosition(last_insulator_pointInfo.position,tower.originCartesian,tower.angle)
                        var last_insulator =  this.takeInsulatorInfo(tower,last_insulator_pointInfo)
                        var last_insulator_Mounts =  this.takeInsulatorMount(last_insulator,phase)
                        var last_insulator_GisMounts =  this.convertInsulatorMount2GIS(last_insulator_point, last_insulator_Mounts,last_houdao_angle)

                        // 
                        let _jiajiao1 = jiajiao; // qiandao绝缘子的角度
                        var now1 =  this.takePointInfo(tower,towerModel,'houdao',nowNumber)
                        var now1Point = this.getMountPosition(now1.position,tower.originCartesian,tower.angle)
                        var now1Insulator =  this.takeInsulatorInfo(tower,now1)
                        var now1Mounts =  this.takeInsulatorMount(now1Insulator,phase)
                        var now1GisMounts =  this.convertInsulatorMount2GIS(now1Point, now1Mounts,_jiajiao1+Math.PI/2)
                        ld = this.generateLineDataByMounts(last_insulator_GisMounts,now1GisMounts,last_insulator,now1Insulator,tower,tower,phase,`qiandao${nextNumber}TOhoudao${nextNumber}`)
                        lds.push(...ld)
                    }
                    continue
                }
                var now2Insulator =  this.takeInsulatorInfo(nextTower,now2)
                var now2Mounts =  this.takeInsulatorMount(now2Insulator,phase)
                var now2GisMounts =  this.convertInsulatorMount2GIS(now2Point, now2Mounts,(nextTower.angle * Math.PI / 180)-Math.PI)
                if (now2.insulatorNo === '/') {
                    continue
                }
                this.addOneInsulator(now2,now2Point,this.takeInsulatorInfo(nextTower,now2),nextTower,(nextTower.angle * Math.PI / 180))
                let options = {
                    lineID_prefix: `${nextTower.lineId}-${nextTower.towerId}-${nextTower.towerId}-${phase}-${now2.name}`,
                    insulatorInfo: this.takeInsulatorInfo(nextTower, now2),
                    mountPoint: now2Point,
                    heading: (nextTower.angle * Math.PI / 180),
                    phase: phase,
                }
                this.insulator_Internal_line(options);

                ld = this.generateLineDataByMounts(now1GisMounts,now2GisMounts,now1Insulator,now2Insulator,nextTower,nextTower,phase,`${nextMountNames[i-1]}${nextNumber}TO${nextMountNames[i]}${nextNumber}`)
                lds.push(...ld)
            }
        }
        return lds
    }
    generateLineDataByMounts=(gisMounts,nextGisMounts,insulatorModel,nextInsulatorModel,tower,nextTower,phase,fromto)=>{
        var lds = []
        for(var i = 0; i< gisMounts.length; i++){
            var mount1 = gisMounts[i]
            var mount2 =  nextGisMounts[i]
            if(mount2.length==0){
                // 绝缘子分裂数不一致
                console.log(`绝缘子${insulatorModel.modelName}和${nextInsulatorModel.modelName}分裂数不一致`);
                return;
            }
            if (insulatorModel.lineType == '导线' && gisMounts.length > 1) {

                // 这里需要处理前后绝缘子挂点连接顺序的问题 不同杆塔耐张串连接 同杆塔前导-中导连接 
                // if ((tower.towerId == nextTower.towerId && insulatorModel.modelType == '耐张串')) {
                //     mount2 = nextGisMounts[i ^ 1]
                // }
                if((tower.towerId != nextTower.towerId && insulatorModel.modelType == '耐张串')){
                    mount1 = gisMounts[i ^ 1]
                }
                if((tower.towerId == nextTower.towerId && insulatorModel.modelType == '悬垂串')){
                    mount2 = nextGisMounts[i ^ 1]
                }
                if(tower.towerId == nextTower.towerId && /^qiandao\d+TOhoudao\d+$/.test(fromto)){
                    mount2 = nextGisMounts[i ^ 1]
                }
            }
            if(insulatorModel.isConnected){
                for(var j =1;j< mount1.length;j++){
                    if(mount1[j-1][0]==mount1[j][0] && mount1[j-1][1]==mount1[j][1]){
                        continue
                    }
                    var lineId = `${tower.lineId}-${tower.towerId}-${tower.towerId}-${phase}-${i}-${j}`
                    var ld = {}
                    Object.assign(ld,{ID:lineId,smallTowerID:tower.towerId,largeTowerID:tower.towerId,phase,start:mount1[j-1],end:mount1[j]})
                    lds.push(ld)
                }
            }
            if(nextInsulatorModel.isConnected){
                for(var j =1;j< mount2.length;j++){
                    if(mount2[j-1][0]==mount2[j][0] && mount2[j-1][1]==mount2[j][1]){
                        continue
                    }
                    var lineId = `${nextTower.lineId}-${nextTower.towerId}-${nextTower.towerId}-${phase}-${i}-${j}`
                    var ld = {}
                    Object.assign(ld,{ID:lineId,smallTowerID:nextTower.towerId,largeTowerID:nextTower.towerId,phase,start:mount2[j],end:mount2[j-1]})
                    lds.push(ld)
                }
            }
            var lineId = `${tower.lineId}-${tower.towerId}-${nextTower.towerId}-${phase}-${i}-${fromto}`
            //debugger
            var ld = {}
            //changed at 2023/09/05
            if(tower.towerId == nextTower.towerId){//同塔
                if(/^zhongdao\d+TOhoudao\d+$/.test(fromto)){ //中导到后导的连接线
                    Object.assign(ld,{ID:lineId,smallTowerID:tower.towerId,largeTowerID:nextTower.towerId,phase,start:mount1[mount1.length-1],end:mount2[mount2.length-1]})
                    // console.log('debug:ld=', ld);
                }else if(/^qiandao\d+TOhoudao\d+$/.test(fromto) || /^qiandi\d+TOhoudi\d+$/.test(fromto)){
                    // 处理中导没有绝缘子 直接前导连接后导的情况
                    Object.assign(ld,{ID:lineId,smallTowerID:tower.towerId,largeTowerID:nextTower.towerId,phase,start:mount1[mount1.length-1],end:mount2[mount2.length-1]})
                }else{
                    Object.assign(ld,{ID:lineId,smallTowerID:tower.towerId,largeTowerID:nextTower.towerId,phase,start:mount1[mount1.length-1],end:mount2[0]})
                }

            }else{
                if(/^zhongdao\d+.+\d+$/.test(fromto)){ //悬垂串出发的到下一塔的连接线
                    Object.assign(ld,{ID:lineId,smallTowerID:tower.towerId,largeTowerID:nextTower.towerId,phase,start:mount1[mount1.length-1],end:mount2[0]})
                }else{
                    Object.assign(ld,{ID:lineId,smallTowerID:tower.towerId,largeTowerID:nextTower.towerId,phase,start:mount1[0],end:mount2[0]})
                }
            }
            lds.push(ld)
        }
        return lds
    }

    tem=(gisMounts,nextGisMounts,phase,fromto)=>{
        var lds = []
        for(var i = 0; i< gisMounts.length; i++){
            var mount1 = gisMounts[i]
            var mount2 =  nextGisMounts[i]
            var lineId = `${fromto}`
            //debugger
            var ld = {}
            Object.assign(ld,{ID:lineId,phase,start:mount1[mount1.length-1],end:mount2[0]})
            lds.push(ld)
        }
        return lds
    }

    convertInsulatorMount2GIS = (towerMount,insulatorMounts,insulatorAngleRad) => {
        const originModel = {
            longitude: towerMount[0],
            latitude: towerMount[1],
            height: towerMount[2],
            angle:insulatorAngleRad * 180 / Math.PI
        }
        const origin = Cesium.Cartographic.fromDegrees(originModel.longitude, originModel.latitude, originModel.height);
        const originCartesian = this.viewer.scene.globe.ellipsoid.cartographicToCartesian(origin);
        let result = []
        for(var i =0;i<insulatorMounts.length;i++){
            let ele = []
            for(var j =0;j<insulatorMounts[i].length;j++){
                if(insulatorMounts[i][j].length==0){
                    console.log('绝缘子挂点信息错误:',insulatorMounts, i, j)
                }
                let mountPos =  this.getMountPosition(insulatorMounts[i][j],originCartesian,originModel.angle)
                ele.push(mountPos)
            }
            result.push(ele)
        }
        return result
    }
    convertInsulatorPoint = (insulator)=>{
        if (insulator === undefined) {
            return
        }
        for(let i=0;i<8;i++){
            if(insulator['point'+i] && typeof(insulator['point'+i]) == 'string'){
                insulator['point'+i] = JSON.parse(insulator['point'+i])
            }
        }
    }
    /**
     * 
     * @param {object} insulator - 绝缘子模型信息的对象
     * @returns 
     */
    takeInsulatorMount = (insulator,phase=undefined)=>{
        if (insulator === undefined) {
            return
        }
        if(!(insulator.splitNum ==1 || insulator.splitNum ==2 || insulator.splitNum == 4)){
            console.log(`分裂数设置错误:${insulator.modelName}.${insulator.splitNum}`);
            return
        }
        // if(phase == 'G') return [[insulator.point0]];
        // if(insulator.lineType == '地线') return [[insulator.point0]];
        let _mount = null;
        // 根据分裂数和连接点信息 整理挂点位置信息
        if (insulator.connectNum == 1 && insulator.modelType == '双悬垂串') {
            switch (insulator.splitNum) {
                case 1:
                    _mount = [[insulator.point0.slice(0,3),insulator.point0.slice(3)]];
                    break;
                case 2:
                    _mount = [[insulator.point0.slice(0,3),insulator.point0.slice(3)], [insulator.point1.slice(0,3),insulator.point1.slice(3)]];
                    break;
                case 4:
                    _mount = [[insulator.point0.slice(0,3),insulator.point0.slice(3)], [insulator.point1.slice(0,3),insulator.point1.slice(3)],[insulator.point2.slice(0,3),insulator.point2.slice(3)], [insulator.point3.slice(0,3),insulator.point3.slice(3)]]
                    break;
            }
        } else if (insulator.connectNum == 1 && insulator.modelType != '双悬垂串') {
            switch (insulator.splitNum) {
                case 1:
                    _mount = [[insulator.point0, insulator.point0]];
                    break;
                case 2:
                    _mount = [[insulator.point0], [insulator.point1]];
                    break;
                case 4:
                    // _mount = [[insulator.point0], [insulator.point3], [insulator.point2], [insulator.point1]]
                    _mount = [[insulator.point0], [insulator.point1], [insulator.point2], [insulator.point3]]
                    break;
            }
        }else if (insulator.connectNum == 2 && insulator.modelType == '双悬垂串') {
            switch (insulator.splitNum) {
                case 1:
                    _mount = [[insulator.point0.slice(0,3),insulator.point0.slice(3),insulator.point1.slice(0,3),insulator.point1.slice(3)]];
                    break;
                case 2:
                    _mount = [[insulator.point0.slice(0,3),insulator.point0.slice(3),insulator.point1.slice(0,3),insulator.point1.slice(3)],[insulator.point2.slice(0,3),insulator.point2.slice(3),insulator.point3.slice(0,3),insulator.point3.slice(3)]];
                    break;
                case 4:
                    // _mount = [[insulator.point0.slice(0,3),insulator.point0.slice(3),insulator.point1.slice(0,3),insulator.point1.slice(3)],[insulator.point2.slice(0,3),insulator.point2.slice(3),insulator.point3.slice(0,3),insulator.point3.slice(3)],
                    //[insulator.point4.slice(0,3),insulator.point4.slice(3),insulator.point5.slice(0,3),insulator.point5.slice(3)],[insulator.point6.slice(0,3),insulator.point6.slice(3),insulator.point7.slice(0,3),insulator.point7.slice(3)]];
                    _mount = [[insulator.point0.slice(0,3),insulator.point0.slice(3),insulator.point1.slice(0,3),insulator.point1.slice(3)],[insulator.point2.slice(0,3),insulator.point2.slice(3),insulator.point3.slice(0,3),insulator.point3.slice(3)],
                        [insulator.point4.slice(0,3),insulator.point4.slice(3),insulator.point5.slice(0,3),insulator.point5.slice(3)],[insulator.point6.slice(0,3),insulator.point6.slice(3),insulator.point7.slice(0,3),insulator.point7.slice(3)]];
                    break;
            }
        }else if (insulator.connectNum == 2 && insulator.modelType != '双悬垂串') {
            switch (insulator.splitNum) {
                case 1:
                    _mount = [[insulator.point0, insulator.point1]];
                    break;
                case 2:
                    _mount = [[insulator.point0, insulator.point1], [insulator.point2, insulator.point3]];
                    break;
                case 4:
                    // _mount = [[insulator.point4, insulator.point5],[insulator.point0, insulator.point1], [insulator.point2, insulator.point3],  [insulator.point6, insulator.point7]]
                    _mount = [[insulator.point0, insulator.point1], [insulator.point2, insulator.point3], [insulator.point4, insulator.point5], [insulator.point6, insulator.point7]]
                    break;
            }
        }
        return _mount;
    }
    /**
     * 获取杆塔对应挂点的绝缘子信息
     * @param {*} tower
     * @param {*} pointInfo
     * @returns
     */
    takeInsulatorInfo = (tower,pointInfo)=>{
        let insulator = this.insulatorModels.find(c=>c.modelName == pointInfo.insulatorNo)
        if(!insulator){
            console.log(`${tower.towerId} ${pointInfo.insulatorNo} 绝缘子信息缺失,检查InsulatorModels表中的是否存在相应的数据`, pointInfo)
        }
        return insulator
    }
    takePointNums=(lineLocation)=>{
        var numbers = []
        switch (lineLocation) {
            case 1:
                numbers.push(1,3,5)
                break;
            case 2:
                numbers.push(2,4,6)
                break;
            case 3:
                numbers.push(7,9,11)
                break;
            case 4:
                numbers.push(8,10,12)
                break;
            default:
                console.log(`${lineLocation} 无效的LineLocation，检查LineDetail表`);
                break;
        }
        return numbers
    }
    /**获取挂点信息 */
    takePointInfo=(tower,towerModel,pointName,pointNumber)=>{
        let daoObj ,daoObj1
        var towerProfile = this.towerProfiles.find(c=>c.towerID == tower.towerId)
        if( (!towerModel) || !towerModel.PointDetail || !towerModel.PointDetail[pointName]){
            console.log(`${tower.towerId}.${tower.towerModel}没有对应的TowerModel信息或没有PointDetail信息或没有 ${pointName} 信息`)
            return null
        }
        daoObj =  towerModel.PointDetail[pointName]?.find(c=>c.number ==pointNumber)
        daoObj1 =  (towerProfile? towerProfile.PointDetail[pointName]?.find(c=>c.number ==pointNumber):null)

        if(daoObj1){
            console.log(`${tower.towerId}存在TowerProile信息`)
        }
        if(daoObj && daoObj.position.length==3){
            let result = {}
            Object.assign(result,daoObj,daoObj1 || {})
            result.position = result.position?.concat()
            this.fixBaseHeight(towerModel,result)
            if(result.insulatorNo == undefined || result.insulatorNo == '' || result.insulatorNo == '/')
            {
                console.log(`${tower.lineId}线路 ${tower.towerId}杆塔 中 配串表没有分配对应绝缘子模型`, result)
                return null
            }
            return result
        }
        else{
            console.log(`${tower.towerId} 没有对应的 ${pointName}${pointNumber}`)
        }
        return null
    }
    fixBaseHeight=(towerModel,pointInfo, result=0)=>{
        if (result == 0) {
            // 修复数据中杆塔挂点没有加入杆塔呼高的偏差
            pointInfo.position[2] += towerModel.baseHeight;
            // 修正x和y轴相反的问题
            let temp = pointInfo.position[0];
            pointInfo.position[0] = pointInfo.position[1];
            pointInfo.position[1] = temp;
            if (pointInfo.position.length == 6) {
                pointInfo.position[5] += towerModel.baseHeight;
                // 修正x和y轴相反的问题
                let temp = pointInfo.position[3];
                pointInfo.position[3] = pointInfo.position[4];
                pointInfo.position[4] = temp;
            }
        } else {
            result = []
            // 修复数据中杆塔挂点没有加入杆塔呼高的偏差
            result.push(pointInfo.position[1]);
            result.push(pointInfo.position[0]);
            result.push(pointInfo.position[2] + towerModel.baseHeight);
            if (pointInfo.position.length == 6) {
                result.push(pointInfo.position[4]);
                result.push(pointInfo.position[3]);
                result.push(pointInfo.position[5] + towerModel.baseHeight);
            }
            return result;
        }

    }
    /**
     *
     * @param {object} options - 加载绝缘子到场景的参数对象
     * @param {string} options.id - 绝缘子实体的id
     * @param {object} options.insulatorModel - 绝缘子模型信息
     * @param {number} options.pitchRaid - 俯仰夹角
     * @param {number} options.angRaid - 水平夹角
     * @param {array} options.mounts - 挂载位置 经纬度高度
     * @returns 绝缘子实体 entity
     */
    // addOneInsulator=(pointInfo,mounts,insulatorModel,tower,angRaid, pitchRaid=0)=>{
    addOneInsulator = (options)=>{
        // if(pointInfo == null) return
        let insulatorModel = options.insulatorModel
        if(!insulatorModel) return
        // const insulatorEntityID = `${tower.towerId}@${pointInfo.name}@${pointInfo.number}@${insulatorModel.modelName}`
        let insulatorEntityID = `insulator@${options.id}`
        let _entity = this.viewer.entities.getById(insulatorEntityID)
        if(_entity!=undefined){
            // console.log(`绝缘子模型${insulatorEntityID}已经加载`, _entity);
            return undefined;
        }
        if(this.insulatorEntities.findIndex(c=>c.id== insulatorEntityID)>=0) return
        let pitchRaid = options.pitchRaid || 0
        let angRaid = options.angRaid
        let mounts = options.mounts
        const hpr = new Cesium.HeadingPitchRoll(angRaid, pitchRaid, 0.0); // 旋转角 俯仰角 翻滚角
        const entityPosition = Cesium.Cartesian3.fromDegrees(...mounts);
        const orientation = Cesium.Transforms.headingPitchRollQuaternion(entityPosition, hpr);
        const entityInfo = {
            id: `${insulatorEntityID}`,
            position: entityPosition,
            orientation: orientation,
            model: {
                uri: `${MODEL_STATIC_URL}/${this.projectCode}` + insulatorModel.url,
            },
            userData:{
                // 额外记录用户自定义数据 用于后续的计算
                position: mounts,
                angle: angRaid,
            }
        }
        // if(tower.towerId == 'A47'){
        //     // console.log(`BBBBBBBB `,mounts,insulatorEntityID,entityPosition,angRaid)
        // }
        let entity = new Cesium.Entity(entityInfo);
        this.insulatorEntities.push(entity)
        entity.show = true;
        this.viewer.entities.add(entity);
        return entity;
    }
    /**
     *
     * @param {array} point1 基准点坐标[lon, lat, height]
     * @param {array} point2 目标点坐标[lon, lat, height]
     * @returns 基准点到目标点的连线与正北方向的方位角 弧度
     */
    calAzimuth = (point1, point2) => {
        const x1 = point1[0] * Math.PI / 180;
        const y1 = point1[1] * Math.PI / 180;
        const z1 = point1[2];
        const x2 = point2[0] * Math.PI / 180;
        const y2 = point2[1] * Math.PI / 180;
        const z2 = point2[2];
        let geodesic = new Cesium.EllipsoidGeodesic(Cesium.Cartographic.fromDegrees(x1, y1, z1), Cesium.Cartographic.fromDegrees(x2, y2, z2));
        return geodesic.endHeading;
    }
    /**
     * 计算两个点的俯仰角度（高度角）
     */
    getPitchAngle = (point1, point2) => {        
        // 计算两个点在水平方向上的距离
        let p1 = new Cesium.Cartesian3.fromDegrees(...point1);
        let p2 = new Cesium.Cartesian3.fromDegrees(...point2);
        let horizontalDistance = Cesium.Cartesian3.distance(new Cesium.Cartesian3(p1.x, p1.y, 0), new Cesium.Cartesian3(p2.x, p2.y, 0));

        let h = point2[2] - point1[2];
        // 将弧度转换为度数
        let pitchAngle = Math.asin(h/horizontalDistance);
        var degree = Cesium.Math.toDegrees(pitchAngle);
        // console.log(`debug:h=${h}, horizontalDis=${horizontalDistance}`);
        // console.log(`debug:degree=${degree}`);

        // 计算俯仰角度
        return pitchAngle;
    }
    /**
     * 将相对的挂点坐标转换为地图的坐标
     * @param {*} targetPoint
     * @param {*} originCartesian
     * @param {*} angRaid
     * @returns
     */
    getMountPosition=(targetPoint,originCartesian,entityAngRaid, pitchAngRaid = 0)=> {
        let angRaid = -1 * entityAngRaid * Math.PI / 180; // 杆塔旋转坐标于cesium中方向相反 所以取负
        let pitRaid = -1*pitchAngRaid
        // - 相对坐标
        let x = targetPoint[0] * Math.cos(angRaid) - targetPoint[1] * Math.sin(angRaid);
        let y = targetPoint[0] * Math.sin(angRaid) + targetPoint[1] * Math.cos(angRaid);
        let z = targetPoint[2];
        // 增加基于俯仰角（高度角）的计算
        let len = Math.sqrt(x * x + y * y);
        z -= len * Math.sin(pitRaid);
        x = x * Math.cos(pitRaid);
        y = y * Math.cos(pitRaid);

        const relativePosition = new Cesium.Cartesian3(x, y, z);

        // 计算相对位置的绝对位置坐标
        const target = Cesium.Matrix4.multiplyByPoint(
            Cesium.Transforms.eastNorthUpToFixedFrame(originCartesian),
            relativePosition,
            new Cesium.Cartesian3()
        );
        // 将绝对位置坐标转换为经纬度高度坐标
        try{
        const absoluteCartographic = this.viewer.scene.globe.ellipsoid.cartesianToCartographic(target);

        // 转换经纬度高度坐标的度数表示
        //debugger
        const absoluteDegrees = [absoluteCartographic.longitude * 180 / Math.PI, absoluteCartographic.latitude * 180 / Math.PI, absoluteCartographic.height];
        return absoluteDegrees
    }catch(ex){
            console.log('AAAAAAAA',targetPoint, originCartesian,    entityAngRaid, ex)
            return null
    }
    }
    addTowerToViewer= async (tower,towerModel)=>{
        //前面两种状态不加载塔
        let procedureStatus = this.line.lineType == "L" ? 5: tower.procedureStatus
        let towerID = `tower@${tower.towerId}`
        if (this.flag && this.line.lineType != "L" ) {
            if(this.planRecordMap.hasOwnProperty(tower.towerId)) {
                procedureStatus = this.planRecordMap[tower.towerId].procedureStatus
            } else {
                procedureStatus = 0
            }
        }
        if((procedureStatus === Constants.PROGRESS_0 || procedureStatus === Constants.PROGRESS_1) && !this.showDesign) {
            showModelByStatus(procedureStatus, [tower.longitude, tower.latitude, tower.height], [procedureStatus], this.viewer)
            return
        }
        let res = this.towerEntities.find(t => { return (t.id === towerID) })
        if (res) {
            return
        }
        // 判断其他工序，根据工序展示杆塔模型
        if((procedureStatus === Constants.PROGRESS_2 || procedureStatus === Constants.PROGRESS_3) && !this.showDesign) {
            // A48 A56两个杆塔要单独额外处理，先写死
            let modelUrl = '';
            // if (tower.towerId === 'A48' || tower.towerId === 'A56' ) {
            //     modelUrl = PROCEDURE_`${MODEL_STATIC_URL}/${this.projectCode}` + tower.towerModel + '_' + tower.towerId + '/' + tower.towerModel + '_' + tower.towerId + '_' + (procedureStatus-1) + '.glb'
            // } else {
            //      modelUrl = PROCEDURE_`${MODEL_STATIC_URL}/${this.projectCode}` + tower.towerModel + '/' + tower.towerModel + '_' + (procedureStatus-1) + '.glb'
            // }
            modelUrl = `${MODEL_STATIC_URL}/${this.projectCode}/gltf/procedure/` + tower.towerModel + '/' + tower.towerModel + '_' + (procedureStatus-1) + '.glb'
            let modelInfo = {
                id: towerID,
                name: tower.towerModel,
                position: [tower.longitude, tower.latitude, tower.height],
                // angle: 90*Math.PI/180,
                angle: tower.angle * Math.PI / 180,
                scale: 1,
                url: modelUrl,
                procedureStatus: procedureStatus
            }
            let entity = await createEntity_glb(modelInfo);
            this.viewer.entities.add(entity);
            this.towerEntities.push(entity);
            return
        }
        if(this.viewer.entities.getById(towerID)){
            return
        }
        let modelInfo = {
            id: towerID,
            name: tower.towerModel,
            position: [tower.longitude, tower.latitude, tower.height],
            // angle: 90*Math.PI/180,
            angle: tower.angle * Math.PI / 180,
            scale: 1,
            url: `${MODEL_STATIC_URL}/${this.projectCode}` + towerModel.url,
            procedureStatus: procedureStatus
        }
        let entity = await createEntity_glb(modelInfo);
        this.viewer.entities.add(entity);

        this.towerEntities.push(entity);
        if(tower.towerId == 'B14'){
            // console.log(`CCCCCC ${tower.towerId}=${modelInfo.angle}`)
        }
    }
    /**
     * 实现添加塔号标签（或对应信息）
     * @param {object} options - 包含塔号标签信息的参数对象
     * @param {object} options.tower - 杆塔信息 线路详情表中的数据 
     * @param {string} options.msg - 需要额外添加的标签信息（可以为空） 
     * @returns 
     */
    addTowerLabelToViewer = (options) =>{
        let tower = options.tower
        let label_id = `label_${options.tower.towerId}`
        let res = this.towerLabels.find(t => { return (t.id === label_id) })
        if (res) {//已显示的就不再处理了
            console.log(`label_${options.tower.towerId} 已经显示了`)
            return
        }
        const divHtmlStyle = '<div style="width:auto;height:auto;background:transparent;color: yellow;font-size: 22px">'
        let curTowerModel = this.towerModels.find(t => { return (t.towerModel === tower.towerModel) })
        if(curTowerModel == undefined){
            console.log(`没有找到${tower.towerModel}对应的模型`)
            return
        }
        let divHtml
        let height = tower.height + curTowerModel.twoerHeight + 3 // 标签高度根据杆塔模型的高度自适应
        let _label_show_msg = tower.towerId
        if(this.showDesign || this.line.lineType == "L"){
            // 设计模式
            // 根据是否显示vr标识 拼接完整的标签html
            if ( tower.vrUrl !== undefined && tower.vrUrl !== null) {
                divHtml = `<div style="width:auto;height:auto;background:transparent;color: yellow;font-size: 22px">`
                +`${tower.towerId}<a  href="${import.meta.env.VITE_CONTEXT}Threejs3D?tower=${tower.towerId}&projectCode=${this.projectCode}" target="_blank">`
                +`<img src="${import.meta.env.VITE_CONTEXT}static/img/Threejs3D/vr.png" style="width: 32px;height: 23px;"></a>`
                +`</div>`
            }else{
                divHtml = `<div style="width:auto;height:auto;background:transparent;color: yellow;font-size: 22px">${tower.towerId}</div>`
            }
        }else{
            // 施工模式
            let procedureStatus = this.line.lineType == "L" ?5 : tower.procedureStatus
            let procedureStatusName = tower.procedureStatusName
            if (this.flag) {
                if(this.planRecordMap.hasOwnProperty(tower.towerId)) {
                    procedureStatus = this.planRecordMap[tower.towerId].procedureStatus
                    procedureStatusName = this.planRecordMap[tower.towerId].procedureStatusName
                } else {
                    procedureStatus = 0
                    procedureStatusName = '未开始'
                }
                if (procedureStatus === Constants.PROGRESS_0 
                    || procedureStatus === Constants.PROGRESS_1 
                    || procedureStatus === Constants.PROGRESS_2 
                    || procedureStatus === Constants.PROGRESS_3) {
                    // 在杆塔这几种施工状态下 需要将标签高度下降处理
                    height = tower.height + 50 + 3    
                }    
            }
            // 根据是否显示vr标识 拼接完整的标签html
            if ( tower.vrUrl !== undefined && tower.vrUrl !== null) {
                divHtml = `<div style="width:auto;height:auto;background:transparent;color: yellow;font-size: 22px">`
                +`${tower.towerId}(${procedureStatusName})<a  href="${import.meta.env.VITE_CONTEXT}Threejs3D?tower=${tower.towerId}&projectCode=${this.projectCode}" target="_blank">`
                +`<img src="${import.meta.env.VITE_CONTEXT}static/img/Threejs3D/vr.png" style="width: 32px;height: 23px;"></a>`
                +`</div>`
            }else{
                divHtml = `<div style="width:auto;height:auto;background:transparent;color: yellow;font-size: 22px">
                ${tower.towerId}(${procedureStatusName})
                </div>`
            }
            _label_show_msg = _label_show_msg+`(` + procedureStatusName+ `)`
        }
        let val = {
            id: label_id,
            text: _label_show_msg,
            viewer: this.viewer,
            position: [tower.longitude, tower.latitude, height],
            // title: '广告牌'
            divHTML: divHtml,
            img: ( tower.vrUrl !== undefined && tower.vrUrl !== null)?import.meta.env.VITE_CONTEXT+'static/img/Threejs3D/vr.png':undefined,
            url: ( tower.vrUrl !== undefined && tower.vrUrl !== null)?import.meta.env.VITE_CONTEXT+`Threejs3D?tower=${tower.towerId}&projectCode=${this.projectCode}`:undefined
        }

        let label_entity = addLabelNum(this.viewer, val) // 调用新的标签加载函数 by lkj
        this.towerLabels.push(label_entity)
            // console.log("--------")
    }
    substation_line_load(towerInfo, lineId, substationInfo, connectInfo){
        let procedureStatus = towerInfo.procedureStatus
        if(this.planRecordMap.hasOwnProperty(towerInfo.towerId)) {
            procedureStatus = this.planRecordMap[towerInfo.towerId].procedureStatus
        }

        if (procedureStatus != Constants.PROGRESS_5  && !this.showDesign) {
            return []
        }
        // 加载变电站和对应杆塔的连接绝缘子模型
        let result_entities = []
        let connectType = connectInfo.connectType;
        let towerModel = this.towerModels.find(item => item.towerModel === towerInfo.towerModel);
        if(towerModel==undefined){
            console.log(`模型${towerInfo.towerId}不存在`);
            return;
        }
        let numbers = this.takePointNums(towerInfo.lineLocation);
        let mountPoints;
        if(connectType == 0){
            mountPoints = towerModel.towerStyle=="耐张塔"?towerModel.PointDetail.houdao:towerModel.PointDetail.zhongdao;
        }else if(connectType == 1){
            mountPoints = towerModel.towerStyle=="耐张塔"?towerModel.PointDetail.qiandao:towerModel.PointDetail.zhongdao;
        }else{
            console.log(`连接类型错误, connectType=${connectType}`);
            return;
        }
        let substation_mounts = substationInfo.substation_mounts.filter(item => item.lineLocation === connectInfo.substation_linelocation);
        if(substation_mounts.length==0){
            console.log(`变电站${substationInfo.id}名称${substationInfo.name}的挂点${substationInfo.lineLocation}信息错误`);
            return;
        }
        for(let i=0; i<3; i++){
            let targetPointInfo = mountPoints.find(item => item.number === numbers[i]);
            let _position = this.fixBaseHeight(towerModel, targetPointInfo, 1);
            // console.log(`debug:_position = ${_position}`)
            let position1 = this.getMountPosition(_position, towerInfo.originCartesian, towerInfo.angle);
            if(position1==null){
                console.log(`杆塔号${towerInfo.towerId}的模型${towerInfo.towerModel},挂点${targetPointInfo.name}信息不存在`);
                continue;
            }
            let substationMount = substation_mounts.find(item => item.number === i);
            if(substationMount==undefined){
                console.log(`变电站${substationInfo.id}名称${substationInfo.name}的挂点${connectInfo.lineLocation}-${i}信息错误`);
                continue;
            }
            let position2 = substationMount.position;
            let ang = this.calAzimuth(position1, position2);
            let pitchRaid = this.getPitchAngle(position1, position2);
            // 挂载杆塔和变电站上的绝缘子模型
            let insulator1 = this.insulatorModels.find(item => item.modelName === targetPointInfo.insulatorNo);
            let insulator1_entity = this.addOneInsulator({
                id: `${towerInfo.towerId}@${targetPointInfo.name}@${targetPointInfo.number}@${insulator1.modelName}`,
                insulatorModel: insulator1,
                angRaid: ang+ Math.PI / 2,
                pitchRaid: -1*pitchRaid,
                mounts: position1
            });
            result_entities.push(insulator1_entity)
            // this.addOneInsulator(targetPointInfo, position1, insulator1, towerInfo, ang+ Math.PI / 2, -1*pitchRaid);
            let insulator2 = this.insulatorModels.find(item => item.modelName === substationMount.insulator);
            let insulator2_entity = this.addOneInsulator({
                id: `${substationInfo.name}-${connectInfo.substation_linelocation}}@${substationMount.name}@${substationMount.number}@${insulator1.modelName}`,
                insulatorModel: insulator2,
                angRaid: ang-Math.PI/2,
                pitchRaid: substationMount.pitchAngle*Math.PI/180,
                mounts: position2
            });
            result_entities.push(insulator2_entity)
            // this.addOneInsulator(substationMount, position2, insulator2, {towerId:`${substationInfo.name}-${connectInfo.substation_linelocation}`}, ang-Math.PI/2, substationMount.pitchAngle*Math.PI/180);

            // 连接杆塔和变电站之间的电缆
            let start_insulator_mount = this.takeInsulatorMount(insulator1, null);
            let end_insulator_mount = this.takeInsulatorMount(insulator2, null);
            let OverhangInsulator_mount = null;
            let zhongdao_position = null;
            let zhongdao_ang = null;
            let zhongdao_flag = false;
            if(towerModel.towerStyle=="耐张塔"){
                // 如果杆塔是耐张塔 则还需要额外连接杆塔的中导-后导的电缆 这里通过id拿到已加载的绝缘子模型信息
                let OverhangInsulator_name = towerModel.PointDetail.zhongdao.find(item => item.number === numbers[i]).insulatorNo;
                let overhangInsulator = this.insulatorModels.find(item => item.modelName === OverhangInsulator_name);
                let _id = `insulator@${towerInfo.towerId}@中导${numbers[i]}@${numbers[i]}@${overhangInsulator.modelName}`;  
                let _entity = this.viewer.entities.getById(_id);
                if(_entity==undefined){
                    console.log(`绝缘子${_id}，没有加载`);
                    continue;
                }else{
                    zhongdao_position = _entity.userData.position;
                    zhongdao_ang = _entity.userData.angle;
                    OverhangInsulator_mount = this.takeInsulatorMount(overhangInsulator, null);
                    zhongdao_flag = true;
                }
            }
            for(let j=0; j<start_insulator_mount.length; j++){
                let lineData = {};
                lineData.ID = `${lineId}-${towerInfo.towerId}-${substationInfo.name}-${connectInfo.phaseSequence[i]}-${i}-${j}`;
                lineData.phase = connectInfo.phaseSequence[i]
                lineData.start = this.getMountPosition(start_insulator_mount[j][0], Cesium.Cartesian3.fromDegrees(...position1), (ang-Math.PI) *180/Math.PI, pitchRaid);
                lineData.end = this.getMountPosition(end_insulator_mount[j^0x01][0], Cesium.Cartesian3.fromDegrees(...position2), ang*180/Math.PI, -1*substationMount.pitchAngle*Math.PI/180);
                let polyline = this.overheadLine(lineData, LINE_POINTS_NUM);
                this.polylines.push(polyline);
                result_entities.push(polyline);
                    // console.log(`debug:lineData=${JSON.stringify(lineData)}`);
                if(towerModel.towerStyle=="耐张塔" && zhongdao_flag==true){
                    // 如果是耐张塔 则还需要额外连接杆塔的中导-后导的电缆
                    lineData.ID = `${lineId}-${towerInfo.towerId}-${towerInfo.towerId}-${connectInfo.phaseSequence[i]}-${i}-${j}`;
                    let _mount = (OverhangInsulator_mount[j].length == 1 || connectType == 1) ? OverhangInsulator_mount[j][0] : OverhangInsulator_mount[j][1]; // 根据连接点是否为2 取挂点坐标
                    lineData.start = this.getMountPosition(_mount, Cesium.Cartesian3.fromDegrees(...zhongdao_position), (zhongdao_ang-Math.PI/2) *180/Math.PI);
                    lineData.end = this.getMountPosition(start_insulator_mount[j^0x01][1], Cesium.Cartesian3.fromDegrees(...position1), (ang - Math.PI) * 180 / Math.PI, pitchRaid);
                    let polyline = this.overheadLine(lineData, LINE_POINTS_NUM);
                    this.polylines.push(polyline);
                    result_entities.push(polyline);
                    // console.log(`debug:lineData=${JSON.stringify(lineData)}`);
                }
            }
        }
        console.log(`成功连接变电站${substationInfo.name}和杆塔${towerInfo.towerId}回路${lineId}的电缆！`)
        return result_entities
    }
    /**
    * 该函数用于处理绝缘子内部连线
    * @param {Object} options - 传入的参数对象，包括以下属性：
    *  - lineID_prefix: 线ID前缀
    *  - insulatorInfo: 绝缘子信息
    *  - mountPoint: 绝缘子挂载点
    *  - heading: 绝缘子挂载角度 航向角
    *  - pitch: 绝缘子挂载角度 俯仰角
    *  - phase: 线路相位
    * @returns void
     */
    insulator_Internal_line = (options)=>{
        // console.log('debug insulator_Internal_line:', options);
        if(!options.lineID_prefix){
            console.log('lineID_prefix不能为空', options);
            return;
        }
        if(!options.insulatorInfo){
            console.log('insulatorInfo不能为空', options);
            return;
        }
        if(!options.mountPoint){
            console.log('mountPoint不能为空', options);
            return;
        }
        if(!options.heading){
            console.log('heading不能为空', options);
            return;
        }
        if(!options.pitch){
            options.pitch = 0;
        }
        if(!options.phase){
            console.log('phase不能为空', options);
            return;
        }
        if(options.insulatorInfo.connectNum!=2 || options.insulatorInfo.lineType=="地线" || options.insulatorInfo.modelType=="耐张串"){
            // 不处理连接点为1、地线串和耐张串等绝缘子
            return;
        }

        let computeCircle = (radius)=> {
            const positions = [];
            for (let i = 0; i < 6; i++) {
              const radians = Cesium.Math.toRadians(i*60);
              positions.push(
                new Cesium.Cartesian2(
                  radius * Math.cos(radians),
                  radius * Math.sin(radians)
                )
              );
            }
            return positions;
          }


        let insulator_mount = this.takeInsulatorMount(options.insulatorInfo, null);
        // console.log('debug insulator_mount:', insulator_mount, options.insulatorInfo);
        for(let i=0; i<options.insulatorInfo.splitNum; i++){
            let lineId = `line@${options.lineID_prefix}-内部-${i}-${i}`;
            if(this.viewer.entities.getById(lineId)!=undefined){
                return;
            }
            // console.log('debug lineId:', lineId);
            let lineStart = this.getMountPosition(insulator_mount[i][0], Cesium.Cartesian3.fromDegrees(...options.mountPoint), (options.heading-Math.PI/2) *180/Math.PI, options.pitch*180/Math.PI);
            let lineEnd = this.getMountPosition(insulator_mount[i][1], Cesium.Cartesian3.fromDegrees(...options.mountPoint), (options.heading-Math.PI/2) *180/Math.PI, options.pitch*180/Math.PI);
            // console.log('debug lineStart, lineEnd:', [...lineStart, ...lineEnd]);
            let entity = {
                id:  `${lineId}`,
                polyline: {
                    positions: [new Cesium.Cartesian3.fromDegrees(...lineStart), new Cesium.Cartesian3.fromDegrees(...lineEnd)],
                    width: 1,
                    material: new Cesium.Color.fromCssColorString(Constants.PHASE_COLOR[options.phase])
                }
            }    
            let polylineVolumeInfo = {
                id: `${lineId}`,
                polylineVolume: {
                    positions: Cesium.Cartesian3.fromDegreesArrayHeights([...lineStart, ...lineEnd]),
                    shape: computeCircle(0.040),
                    outline: true,
                    outlineColor: new Cesium.Color.fromCssColorString(Constants.PHASE_COLOR[options.phase]),
                    outlineWidth: 0.001,
                    material: new Cesium.Color.fromCssColorString(Constants.PHASE_COLOR[options.phase]),
                }
            }
            // 两种不同的绘制电缆方式 不同效果 占用资源不同
            var polyline_entity = this.viewer.entities.add(entity);
            // var polyline_entity = this.viewer.entities.add(polylineVolumeInfo);
            this.polylines.push(polyline_entity);
        }
        
    }
    // 处理加载额外的地线连接
    addGroundLine = async (options)=>{
        //统计并从后端请求地线串的绝缘子信息 确保绝缘子串信息存在
        let insulatorAllName = []
        for(let i=0; i<options.length; i++){
            insulatorAllName.push(options[i].groundInsulator)
        }
        const store_lineProj = useLineProjStore();
        await store_lineProj.getInsulators_ext(insulatorAllName);
        this.insulatorModels = store_lineProj.insulators
        console.log('更新后的绝缘子模型表：', this.insulatorModels)
        
        for(let i=0; i<options.length-1; i++){
            // 1- 获取当前塔和下一塔的地线绝缘子挂点信息
            let LineLocation1 = options[i].lineLocation
            let TowerID1 = options[i].towerId
            let _entity_tower1 = this.viewer.entities.getById(`tower@${TowerID1}`)
            if(_entity_tower1==undefined){
                console.log(`挂载地线${options[i].lineId}时 杆塔${TowerID1}没有加载`)
                continue;
            }else{
                if(!this.showDesign && _entity_tower1.userData.procedureStatus != Constants.PROGRESS_5){
                    console.log(`杆塔${TowerID1}是施工模式下未完成状态 ${_entity_tower1.userData.procedureStatus}`)
                    continue;
                }
            }
            let towerModel1 = this.towerModels.find(item => item.towerModel === _entity_tower1.name)
            let _position1 = towerModel1.PointDetail.houdi[LineLocation1-1]
            if(_position1==undefined || _position1.position.length!=3){
                console.log(`${TowerID1}杆塔模型${towerModel1.towerModel}没有 houdi${LineLocation1}数据信息`, towerModel1)
                continue;
            }
           _position1 = this.fixBaseHeight(towerModel1, _position1, 1)
            let position1 = this.getMountPosition(_position1, _entity_tower1.userData.position, _entity_tower1.userData.angle*180/Math.PI);

            let LineLocation2 = options[i+1].lineLocation
            let TowerID2 = options[i+1].towerId
            let _entity_tower2 = this.viewer.entities.getById(`tower@${TowerID2}`)
            if(_entity_tower2==undefined){
                console.log(`杆塔${TowerID2}没有加载`)
                continue;
            }
            let towerModel2 = this.towerModels.find(item => item.towerModel === _entity_tower2.name)
            let _position2 = towerModel2.PointDetail.qiandi[LineLocation2-1]
            if(_position2==undefined || _position2.position.length!=3){
                console.log(`${TowerID2}杆塔模型${towerModel2.towerModel}没有 qiandi${LineLocation2}数据信息`, towerModel2)
                continue;
            }
            _position2 = this.fixBaseHeight(towerModel2, _position2, 1)
            let position2 = this.getMountPosition(_position2, _entity_tower2.userData.position, _entity_tower2.userData.angle*180/Math.PI);

            // 2- 根据两个挂点位置 计算夹角 加载地线串绝缘子
            let ang = this.calAzimuth(position1, position2);
            let insulatorName1 = options[i].groundInsulator
            let insulatorModel1 = this.insulatorModels.find(item => item.modelName === insulatorName1)
            this.convertInsulatorPoint(insulatorModel1)
            let insulator1_entity = this.addOneInsulator({
                id: `${TowerID1}@后地${LineLocation1}@${LineLocation1}@${insulatorName1}`,
                insulatorModel: insulatorModel1,
                angRaid: ang+ Math.PI / 2,
                mounts: position1
            });
            let insulatorName2 = options[i+1].groundInsulator
            let insulatorModel2 = this.insulatorModels.find(item => item.modelName === insulatorName2)
            this.convertInsulatorPoint(insulatorModel2)
            let insulator12_entity = this.addOneInsulator({
                id: `${TowerID2}@前地${LineLocation2}@${LineLocation2}@${insulatorName2}`,
                insulatorModel: insulatorModel2,
                angRaid: ang- Math.PI / 2,
                mounts: position2
            });

            // 3- 连接前后杆塔的地线绝缘子电缆
            let insulator1Mounts = this.takeInsulatorMount(insulatorModel1) // 根据绝缘子类型 获取绝缘子的挂点信息
            let insulator2Mounts = this.takeInsulatorMount(insulatorModel2) // 根据绝缘子类型 获取绝缘子的挂点信息
            let lineData = {};
            lineData.phase = options[i].phase
            lineData.ID = `${options[i].lineId}-${TowerID1}-${TowerID2}-${lineData.phase}-houdi${LineLocation1-1}TOqiandi${LineLocation2}`;
            lineData.start = this.getMountPosition(insulator1Mounts[0][0], Cesium.Cartesian3.fromDegrees(...position1), ang *180/Math.PI);
            lineData.end = this.getMountPosition(insulator2Mounts[0][0], Cesium.Cartesian3.fromDegrees(...position2), (ang-Math.PI)*180/Math.PI);
            let polyline = this.overheadLine(lineData, LINE_POINTS_NUM);
            this.polylines.push(polyline);    

            // 4- 判断当前杆塔的前地绝缘子是否已经加载 如果加载则连线
            let _entityID_insulator0 = `insulator@${TowerID1}@前地${LineLocation1}@${LineLocation1}@${insulatorName1}`
            let _entityInsulator0 = this.viewer.entities.getById(_entityID_insulator0)
            if(_entityInsulator0==undefined){
                console.log(`杆塔${TowerID1}的前地地线串前地${LineLocation1}-${insulatorName1}没有加载`)
            }else{
                lineData.phase = options[i].phase
                lineData.ID = `${options[i].lineId}-${TowerID1}-${TowerID1}-${lineData.phase}-qiandi${LineLocation1}TOhoudi${LineLocation1}`;
                //console.log('insulator1mount:', insulator1Mounts[0][1], insulator1Mounts)
                lineData.start = this.getMountPosition(insulator1Mounts[0][1], Cesium.Cartesian3.fromDegrees(..._entityInsulator0.userData.position), (_entityInsulator0.userData.angle-Math.PI/2) *180/Math.PI);
                lineData.end = this.getMountPosition(insulator1Mounts[0][1], Cesium.Cartesian3.fromDegrees(...position1), ang *180/Math.PI);
                let polyline = this.overheadLine(lineData, LINE_POINTS_NUM);
                this.polylines.push(polyline);    
            }

            // 5- 判断下一杆塔的后地绝缘子是否已经加载 如果加载则连线
            let _entityID_insulator3 = `insulator@${TowerID2}@后地${LineLocation2}@${LineLocation2}@${insulatorName2}`
            let _entityInsulator3 = this.viewer.entities.getById(_entityID_insulator3)
            if(_entityInsulator3!=undefined){
                console.log(`杆塔${TowerID2}的后地地线串${LineLocation2}-${insulatorName2}已经加载`)                
                lineData.phase = options[i].phase
                lineData.ID = `${options[i].lineId}-${TowerID2}-${TowerID2}-${lineData.phase}-qiandi${LineLocation2}TOhoudi${LineLocation2}`;
                lineData.start = this.getMountPosition(insulator2Mounts[0][1], Cesium.Cartesian3.fromDegrees(..._entityInsulator3.userData.position), (_entityInsulator3.userData.angle-Math.PI/2) *180/Math.PI);
                lineData.end = this.getMountPosition(insulator2Mounts[0][1], Cesium.Cartesian3.fromDegrees(...position2), (ang-Math.PI) *180/Math.PI);
                let polyline = this.overheadLine(lineData, LINE_POINTS_NUM);
                this.polylines.push(polyline);    
            }

        }
    }
}
