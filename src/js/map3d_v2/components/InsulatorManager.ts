// src/js/map3d_v2/components/InsulatorManager.ts
import * as Cesium from 'cesium';
import { ModelCache } from '@/js/common/modelCache';
import { InstanceRenderer } from '../core/InstanceRenderer';

// 绝缘子数据接口
export interface IInsulator {
  id: string;
  url: string;
  position: number[];
  scale?: number;
  rotation?: number[];
  properties?: Record<string, any>;
}

// 绝缘子管理器
export class InsulatorManager {
  private viewer: Cesium.Viewer;
  private modelCache: ModelCache;
  private instanceRenderer: InstanceRenderer;
  private insulators: Map<string, any> = new Map();
  private useInstancing: boolean = true;
  
  // LOD模型映射
  private lodModels: Map<string, {high: string, medium: string, low: string}> = new Map();
  
  constructor(viewer: Cesium.Viewer, modelCache: ModelCache, instanceRenderer: InstanceRenderer) {
    this.viewer = viewer;
    this.modelCache = modelCache;
    this.instanceRenderer = instanceRenderer;
    
    // 注册默认LOD模型
    this.registerDefaultLodModels();
  }

  // 注册默认LOD模型
  private registerDefaultLodModels(): void {
    // 这里可以根据实际项目中的模型路径进行配置
    this.lodModels.set('insulator_type1', {
      high: '/models/insulators/insulator_type1_high.glb',
      medium: '/models/insulators/insulator_type1_medium.glb',
      low: '/models/insulators/insulator_type1_low.glb'
    });
    
    this.lodModels.set('insulator_type2', {
      high: '/models/insulators/insulator_type2_high.glb',
      medium: '/models/insulators/insulator_type2_medium.glb',
      low: '/models/insulators/insulator_type2_low.glb'
    });
  }

  // 加载绝缘子
  async loadInsulators(insulators: IInsulator[]): Promise<void> {
    console.log(`加载${insulators.length}个绝缘子...`);
    
    // 按URL分组
    const insulatorsByUrl = this.groupByUrl(insulators);
    
    // 加载每种类型的绝缘子
    for (const [url, instances] of Object.entries(insulatorsByUrl)) {
      if (this.useInstancing && instances.length > 1) {
        // 使用实例化渲染
        await this.instanceRenderer.createInstances(url, instances);
      } else {
        // 单独加载每个绝缘子
        for (const insulator of instances) {
          const model = await this.modelCache.getModel(
            insulator.id,
            url,
            {
              scale: insulator.scale,
              rotation: insulator.rotation,
              metadata: { type: 'insulator', id: insulator.id }
            }
          );
          
          // 设置模型位置
          const position = Cesium.Cartesian3.fromDegrees(
            insulator.position[0],
            insulator.position[1],
            insulator.position[2] || 0
          );
          
          const modelMatrix = Cesium.Matrix4.fromTranslation(position);
          model.modelMatrix = modelMatrix;
          
          // 添加到场景
          this.viewer.scene.primitives.add(model);
          
          // 保存引用
          this.insulators.set(insulator.id, model);
        }
      }
    }
    
    console.log(`绝缘子加载完成`);
  }

  // 按URL分组
  private groupByUrl(insulators: IInsulator[]): Record<string, IInsulator[]> {
    const result: Record<string, IInsulator[]> = {};
    
    for (const insulator of insulators) {
      if (!result[insulator.url]) {
        result[insulator.url] = [];
      }
      result[insulator.url].push(insulator);
    }
    
    return result;
  }

  // 更新绝缘子位置
  updateInsulatorPosition(id: string, newPosition: number[]): void {
    // 如果使用实例化渲染
    this.instanceRenderer.updateInstancePosition(id, newPosition);
    
    // 如果是单独加载的模型
    const insulator = this.insulators.get(id);
    if (insulator) {
      const position = Cesium.Cartesian3.fromDegrees(
        newPosition[0],
        newPosition[1],
        newPosition[2] || 0
      );
      
      const modelMatrix = Cesium.Matrix4.fromTranslation(position);
      insulator.modelMatrix = modelMatrix;
    }
  }

  // 设置是否使用实例化渲染
  setUseInstancing(enabled: boolean): void {
    this.useInstancing = enabled;
  }

  // 释放资源
  dispose(): void {
    // 清理单独加载的模型
    for (const model of this.insulators.values()) {
      this.viewer.scene.primitives.remove(model);
    }
    this.insulators.clear();
  }
}