// src/js/map3d_v2/components/LineManager.ts
import * as Cesium from 'cesium';

// 线路数据接口
export interface ILine {
  id: string;
  positions: number[][];
  width?: number;
  color?: string;
  properties?: Record<string, any>;
}

// 线路管理器
export class LineManager {
  private viewer: Cesium.Viewer;
  private lines: Map<string, Cesium.Entity> = new Map();
  
  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer;
  }

  // 加载线路
  loadLines(lines: ILine[]): void {
    console.log(`加载${lines.length}条线路...`);
    
    for (const line of lines) {
      // 创建线路实体
      const entity = this.viewer.entities.add({
        id: line.id,
        polyline: {
          positions: line.positions.map(pos => 
            Cesium.Cartesian3.fromDegrees(pos[0], pos[1], pos[2] || 0)
          ),
          width: line.width || 2,
          material: new Cesium.PolylineOutlineMaterialProperty({
            color: Cesium.Color.fromCssColorString(line.color || '#FFFFFF'),
            outlineWidth: 1,
            outlineColor: Cesium.Color.BLACK
          }),
          clampToGround: false
        }
      });
      
      // 保存引用
      this.lines.set(line.id, entity);
    }
    
    console.log(`线路加载完成`);
  }

  // 更新线路位置
  updateLinePositions(id: string, newPositions: number[][]): void {
    const line = this.lines.get(id);
    if (!line || !line.polyline) return;
    
    line.polyline.positions = new Cesium.CallbackProperty(() => {
      return newPositions.map(pos => 
        Cesium.Cartesian3.fromDegrees(pos[0], pos[1], pos[2] || 0)
      );
    }, false);
  }

  // 更新线路样式
  updateLineStyle(id: string, options: {width?: number, color?: string}): void {
    const line = this.lines.get(id);
    if (!line || !line.polyline) return;
    
    if (options.width !== undefined) {
      line.polyline.width = options.width;
    }
    
    if (options.color !== undefined) {
      line.polyline.material = new Cesium.PolylineOutlineMaterialProperty({
        color: Cesium.Color.fromCssColorString(options.color),
        outlineWidth: 1,
        outlineColor: Cesium.Color.BLACK
      });
    }
  }

  // 释放资源
  dispose(): void {
    for (const entity of this.lines.values()) {
      this.viewer.entities.remove(entity);
    }
    this.lines.clear();
  }
}