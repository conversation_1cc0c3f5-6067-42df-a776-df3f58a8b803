
// src/js/map3d_v2/components/LineManager.ts

import * as Cesium from 'cesium';

export class LineManager {
  private viewer: Cesium.Viewer;
  private lines: Map<string, Cesium.Entity> = new Map();

  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer;
  }

  // 创建电线
  createLine(id: string, positions: Cesium.Cartesian3[], color: Cesium.Color, width: number): void {
    if (this.lines.has(id)) {
      this.removeLine(id);
    }

    const polyline = new Cesium.Entity({
      id: id,
      polyline: {
        positions: positions,
        width: width,
        material: color,
      },
    });

    this.viewer.entities.add(polyline);
    this.lines.set(id, polyline);
  }

  // 移除电线
  removeLine(id: string): void {
    const line = this.lines.get(id);
    if (line) {
      this.viewer.entities.remove(line);
      this.lines.delete(id);
    }
  }

  // 移除所有电线
  removeAllLines(): void {
    for (const id of this.lines.keys()) {
      this.removeLine(id);
    }
  }

  // 释放资源
  dispose(): void {
    this.removeAllLines();
  }
}
