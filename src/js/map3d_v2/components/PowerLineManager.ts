// src/js/map3d_v2/components/PowerLineManager.ts
import * as Cesium from 'cesium';
import  ModelCache  from '@/js/common/modelCache';
import { InstanceRenderer } from '../core/InstanceRenderer';
import { PerformanceMonitor, DynamicOptimizer } from '../core/PerformanceMonitor';
import type { InsulatorManager, IInsulator } from './InsulatorManager';
import type { TowerManager, ITower } from './TowerManager';
import type { LineManager, ILine } from './LineManager';

// 电力线管理器 - 整合所有组件
export class PowerLineManager {
  private viewer: Cesium.Viewer;
  private modelCache: ModelCache;
  private instanceRenderer: InstanceRenderer;
  private dynamicOptimizer: DynamicOptimizer;
  private insulatorManager: InsulatorManager;
  private towerManager: TowerManager;
  private lineManager: LineManager;
  
  // 当前性能模式
  private currentMode: 'high' | 'balanced' | 'quality' = 'balanced';
  
  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer;
    
    // 初始化组件
    this.modelCache = new ModelCache();
    this.instanceRenderer = new InstanceRenderer(viewer, this.modelCache);
    this.dynamicOptimizer = new DynamicOptimizer(viewer);
    this.insulatorManager = new InsulatorManager(viewer, this.modelCache, this.instanceRenderer);
    this.towerManager = new TowerManager(viewer, this.modelCache, this.instanceRenderer);
    this.lineManager = new LineManager(viewer);
    
    // 设置初始性能模式
    this.setPerformanceMode('balanced');
  }

  // 加载绝缘子
  async loadInsulators(insulators: IInsulator[]): Promise<void> {
    await this.insulatorManager.loadInsulators(insulators);
  }

  // 加载杆塔
  async loadTowers(towers: ITower[]): Promise<void> {
    await this.towerManager.loadTowers(towers);
  }

  // 加载线路
  loadLines(lines: ILine[]): void {
    this.lineManager.loadLines(lines);
  }

  // 设置性能模式
  setPerformanceMode(mode: 'high' | 'balanced' | 'quality'): void {
    this.currentMode = mode;
    
    // 应用场景设置
    this.dynamicOptimizer.setMode(mode, false);
    
    // 更新实例化渲染设置
    const useInstancing = mode !== 'quality'; // 高质量模式可能禁用实例化以获得更好的视觉效果
    this.insulatorManager.setUseInstancing(useInstancing);
    this.towerManager.setUseInstancing(useInstancing);
  }

  // 启用动态性能优化
  enableDynamicOptimization(): PerformanceMonitor {
    // 启动动态优化器
    const monitor = this.dynamicOptimizer.start();
    
    // 启用自动调整
    this.dynamicOptimizer.setAutoAdjust(true);
    
    return monitor;
  }

  // 禁用动态性能优化
  disableDynamicOptimization(): void {
    this.dynamicOptimizer.stop();
  }

  // 获取缓存统计信息
  async getCacheStats(): Promise<{dbModels: number, memorySize: number}> {
    return await this.modelCache.getCacheStats();
  }

  // 清理过期缓存
  async cleanExpiredCache(maxAge?: number): Promise<void> {
    await this.modelCache.cleanExpiredCache(maxAge);
  }

  // 释放资源
  dispose(): void {
    // 释放各个管理器
    this.insulatorManager.dispose();
    this.towerManager.dispose();
    this.lineManager.dispose();
    
    // 释放实例渲染器
    this.instanceRenderer.dispose();
    
    // 释放模型缓存
    this.modelCache.dispose();
    
    // 停止动态优化
    this.dynamicOptimizer.stop();
  }
}