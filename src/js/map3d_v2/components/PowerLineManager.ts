
// src/js/map3d_v2/components/PowerLineManager.ts

import * as Cesium from 'cesium';
import { InsulatorManager, type IInsulator } from './InsulatorManager';
import { TowerManager, type ITower } from './TowerManager';
import { LineManager } from './LineManager';
import ModelCache from '@/js/common/modelCache';
import { InstanceRenderer } from '../core/InstanceRenderer';
import { DynamicOptimizer } from '../core/PerformanceMonitor';

export class PowerLineManager {
  private viewer: Cesium.Viewer;
  private insulatorManager: InsulatorManager;
  private towerManager: TowerManager;
  private lineManager: LineManager;
  private modelCache: ModelCache;
  private instanceRenderer: InstanceRenderer;
  private dynamicOptimizer: DynamicOptimizer;

  constructor(
    viewer: Cesium.Viewer,
    modelCache: ModelCache,
    instanceRenderer: InstanceRenderer,
    dynamicOptimizer: DynamicOptimizer
  ) {
    this.viewer = viewer;
    this.modelCache = modelCache;
    this.instanceRenderer = instanceRenderer;
    this.dynamicOptimizer = dynamicOptimizer;
    this.insulatorManager = new InsulatorManager(viewer, this.modelCache, this.instanceRenderer);
    this.towerManager = new TowerManager(viewer, this.modelCache, this.instanceRenderer);
    this.lineManager = new LineManager(viewer);

    // this.dynamicOptimizer.addManager(this.insulatorManager);
    // this.dynamicOptimizer.addManager(this.towerManager);
  }

  async loadPowerLine(towersData: ITower[], insulatorsData: IInsulator[], linesData: any[]) {
    await this.towerManager.loadTowers(towersData);
    await this.insulatorManager.loadInsulators(insulatorsData);

    for (const line of linesData) {
      const startTower = this.towerManager.getTowerById(line.startTowerId);
      const endTower = this.towerManager.getTowerById(line.endTowerId);

      if (startTower && endTower) {
        const startPosition = Cesium.Cartesian3.fromDegrees(startTower.position[0], startTower.position[1], startTower.position[2]);
        const endPosition = Cesium.Cartesian3.fromDegrees(endTower.position[0], endTower.position[1], endTower.position[2]);

        // This is a simplified line creation. In a real scenario, you would need to calculate the catenary curve.
        this.lineManager.createLine(line.id, [startPosition, endPosition], Cesium.Color.RED, 2);
      }
    }
  }

  // public getCacheStats() {
  //   return this.modelCache.getStats();
  // }

  // public cleanExpiredCache() {
  //   this.modelCache.cleanExpired();
  // }

  public dispose(): void {
    this.insulatorManager.dispose();
    this.towerManager.dispose();
    this.lineManager.dispose();
    this.modelCache.dispose();
    // this.dynamicOptimizer.dispose();
  }
}
