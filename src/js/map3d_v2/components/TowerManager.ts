// src/js/map3d_v2/components/TowerManager.ts
import { Cartesian3, Matrix4, type Viewer } from 'cesium';

import ModelCache from '@/js/common/modelCache';
import { InstanceRenderer } from '../core/InstanceRenderer';

// 杆塔数据接口
export interface ITower {
  id: string;
  url: string;
  position: number[];
  scale?: number;
  rotation?: number[];
  properties?: Record<string, any>;
  insulatorModelName?: string;
}

// 杆塔管理器
export class TowerManager {
  private viewer: Viewer;
  private modelCache: ModelCache;
  private instanceRenderer: InstanceRenderer;
  private towers: Map<string, any> = new Map();
  private useInstancing: boolean = true;
  
  // LOD模型映射
  private lodModels: Map<string, {high: string, medium: string, low: string}> = new Map();
  
  constructor(viewer: Viewer, modelCache: ModelCache, instanceRenderer: InstanceRenderer) {
    this.viewer = viewer;
    this.modelCache = modelCache;
    this.instanceRenderer = instanceRenderer;
    
    // 注册默认LOD模型
    this.registerDefaultLodModels();
  }

  // 注册默认LOD模型
  private registerDefaultLodModels(): void {
    // 这里可以根据实际项目中的模型路径进行配置
    this.lodModels.set('tower_type1', {
      high: '/models/towers/tower_type1_high.glb',
      medium: '/models/towers/tower_type1_medium.glb',
      low: '/models/towers/tower_type1_low.glb'
    });
    
    this.lodModels.set('tower_type2', {
      high: '/models/towers/tower_type2_high.glb',
      medium: '/models/towers/tower_type2_medium.glb',
      low: '/models/towers/tower_type2_low.glb'
    });
  }

  // 加载杆塔
  async loadTowers(towers: ITower[]): Promise<void> {
    console.log(`加载${towers.length}个杆塔...`);
    
    // 按URL分组
    const towersByUrl = this.groupByUrl(towers);
    
    // 加载每种类型的杆塔
    for (const [url, instances] of Object.entries(towersByUrl)) {
      if (this.useInstancing && instances.length > 1) {
        // 使用实例化渲染
        await this.instanceRenderer.createInstances(url, instances);
      } else {
        // 单独加载每个杆塔
        for (const tower of instances) {
          const model = await this.modelCache.getOneModel(url);
          
          // 设置模型位置
          const position = Cartesian3.fromDegrees(
            tower.position[0],
            tower.position[1],
            tower.position[2] || 0
          );
          
          const modelMatrix = Matrix4.fromTranslation(position);
          if (model) {
            (model as any).modelMatrix = modelMatrix;
          }
          
          // 添加到场景
          this.viewer.scene.primitives.add(model);
          
          // 保存引用
          this.towers.set(tower.id, model);
        }
      }
    }
    
    console.log(`杆塔加载完成`);
  }

  // 按URL分组
  private groupByUrl(towers: ITower[]): Record<string, ITower[]> {
    const result: Record<string, ITower[]> = {};
    
    for (const tower of towers) {
      if (!result[tower.url]) {
        result[tower.url] = [];
      }
      result[tower.url].push(tower);
    }
    
    return result;
  }

  getCount(): number {
    return this.towers.size;
  }

  getTowerById(id: string): any | undefined {
    return this.towers.get(id);
  }

  // 更新杆塔位置
  updateTowerPosition(id: string, newPosition: number[]): void {
    // 如果使用实例化渲染
    this.instanceRenderer.updateInstancePosition(id, newPosition);
    
    // 如果是单独加载的模型
    const tower = this.towers.get(id);
    if (tower) {
      const position = Cartesian3.fromDegrees(
        newPosition[0],
        newPosition[1],
        newPosition[2] || 0
      );
      
      const modelMatrix = Matrix4.fromTranslation(position);
      tower.modelMatrix = modelMatrix;
    }
  }

  // 设置是否使用实例化渲染
  setUseInstancing(enabled: boolean): void {
    this.useInstancing = enabled;
  }

  // 释放资源
  dispose(): void {
    // 清理单独加载的模型
    for (const model of this.towers.values()) {
      this.viewer.scene.primitives.remove(model);
    }
    this.towers.clear();
  }
}