// src/js/map3d_v2/core/InstanceRenderer.ts
import * as Cesium from 'cesium';
import ModelCache  from '@/js/common/modelCache';

// 实例数据接口
interface IInstanceData {
  id: string;
  position: number[];
  scale?: number;
  rotation?: number[];
  color?: string;
  properties?: Record<string, any>;
}

// 实例化渲染器
export class InstanceRenderer {
  private viewer: Cesium.Viewer;
  private instanceCollections: Map<string, Cesium.ModelInstanceCollection>;
  private instanceMap: Map<string, {collection: string, index: number}>;
  private modelCache: ModelCache;

  constructor(viewer: Cesium.Viewer, modelCache: ModelCache) {
    this.viewer = viewer;
    this.instanceCollections = new Map();
    this.instanceMap = new Map();
    this.modelCache = modelCache;
  }

  // 创建实例化集合
  async createInstances(modelUrl: string, instances: IInstanceData[]): Promise<void> {
    if (instances.length === 0) return;
    
    // 准备实例数据
    const instancesData = instances.map(instance => {
      return {
        modelMatrix: this.calculateModelMatrix(
          instance.position,
          instance.scale || 1.0,
          instance.rotation || [0, 0, 0]
        )
      };
    });
    
    // 创建实例集合
    const collection = await Cesium.ModelInstanceCollection.fromGltf({
      url: modelUrl,
      instances: instancesData,
      incrementallyLoadTextures: true,
      asynchronous: true
    });
    
    // 添加到场景
    this.viewer.scene.primitives.add(collection);
    
    // 保存集合引用
    const collectionId = `collection_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.instanceCollections.set(collectionId, collection);
    
    // 保存实例映射
    instances.forEach((instance, index) => {
      this.instanceMap.set(instance.id, {
        collection: collectionId,
        index
      });
    });
  }

  // 计算模型矩阵
  private calculateModelMatrix(position: number[], scale: number, rotation: number[]): Cesium.Matrix4 {
    // 创建位置
    const cartesian = Cesium.Cartesian3.fromDegrees(
      position[0],
      position[1],
      position[2] || 0
    );
    
    // 创建旋转矩阵
    const rotationMatrix = Cesium.Matrix3.fromHeadingPitchRoll(
      new Cesium.HeadingPitchRoll(
        Cesium.Math.toRadians(rotation[0] || 0),
        Cesium.Math.toRadians(rotation[1] || 0),
        Cesium.Math.toRadians(rotation[2] || 0)
      )
    );
    
    // 创建缩放矩阵
    const scaleMatrix = Cesium.Matrix4.fromScale(
      new Cesium.Cartesian3(scale, scale, scale)
    );
    
    // 组合变换
    const modelMatrix = Cesium.Matrix4.fromRotationTranslation(
      rotationMatrix,
      cartesian
    );
    
    // 应用缩放
    return Cesium.Matrix4.multiply(
      modelMatrix,
      scaleMatrix,
      new Cesium.Matrix4()
    );
  }

  // 更新实例位置
  updateInstancePosition(instanceId: string, newPosition: number[]): void {
    const instanceInfo = this.instanceMap.get(instanceId);
    if (!instanceInfo) return;
    
    const collection = this.instanceCollections.get(instanceInfo.collection);
    if (!collection) return;
    
    const cartesian = Cesium.Cartesian3.fromDegrees(
      newPosition[0],
      newPosition[1],
      newPosition[2] || 0
    );
    
    // 获取当前模型矩阵
    const currentMatrix = collection.getModelMatrix(instanceInfo.index);
    
    // 提取旋转和缩放
    const scale = Cesium.Matrix4.getScale(currentMatrix, new Cesium.Cartesian3());
    const rotation = Cesium.Matrix4.getRotation(currentMatrix, new Cesium.Matrix3());
    
    // 创建新的模型矩阵
    const translationMatrix = Cesium.Matrix4.fromTranslation(cartesian);
    const rotationMatrix = Cesium.Matrix4.fromRotationTranslation(rotation);
    const scaleMatrix = Cesium.Matrix4.fromScale(scale);
    
    // 组合变换
    const newMatrix = Cesium.Matrix4.multiply(
      translationMatrix,
      rotationMatrix,
      new Cesium.Matrix4()
    );
    Cesium.Matrix4.multiply(newMatrix, scaleMatrix, newMatrix);
    
    // 更新实例矩阵
    collection.updateModelMatrix(instanceInfo.index, newMatrix);
  }

  // 释放资源
  dispose(): void {
    for (const collection of this.instanceCollections.values()) {
      this.viewer.scene.primitives.remove(collection);
    }
    this.instanceCollections.clear();
    this.instanceMap.clear();
  }
}