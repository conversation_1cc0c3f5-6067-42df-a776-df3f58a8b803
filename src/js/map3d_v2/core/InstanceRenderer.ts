// src/js/map3d_v2/core/InstanceRenderer.ts

import * as Cesium from 'cesium';
import ModelCache from '@/js/common/modelCache';

// 实例数据接口
interface IInstanceData {
  id: string;
  position: number[];
  scale?: number;
  rotation?: number[];
  color?: string;
  properties?: Record<string, any>;
}

// 实例化渲染器
export class InstanceRenderer {
  private viewer: Cesium.Viewer;
  private dataSource: Cesium.CustomDataSource;
  private modelCache: ModelCache;

  constructor(viewer: Cesium.Viewer, modelCache: ModelCache) {
    this.viewer = viewer;
    this.dataSource = new Cesium.CustomDataSource('instanceRenderer');
    this.viewer.dataSources.add(this.dataSource);
    this.modelCache = modelCache;
  }

  // 创建实例化集合
  async createInstances(modelUrl: string, instances: IInstanceData[]): Promise<void> {
    if (instances.length === 0) return;

    const modelResource = new Cesium.Resource({ url: modelUrl });

    instances.forEach(instance => {
      const position = Cesium.Cartesian3.fromDegrees(instance.position[0], instance.position[1], instance.position[2] || 0);
      const heading = Cesium.Math.toRadians(instance.rotation?.[0] || 0);
      const pitch = Cesium.Math.toRadians(instance.rotation?.[1] || 0);
      const roll = Cesium.Math.toRadians(instance.rotation?.[2] || 0);
      const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
      const orientation = new Cesium.ConstantProperty(Cesium.Transforms.headingPitchRollQuaternion(position, hpr));

      this.dataSource.entities.add({
        id: instance.id,
        position: position,
        orientation: orientation,
        model: {
          uri: modelResource,
          scale: instance.scale || 1.0,
        },
        properties: instance.properties
      });
    });
  }

  // 更新实例位置
  updateInstancePosition(instanceId: string, newPosition: number[]): void {
    const entity = this.dataSource.entities.getById(instanceId);
    if (!entity) return;

    const position = Cesium.Cartesian3.fromDegrees(newPosition[0], newPosition[1], newPosition[2] || 0);
    entity.position = new Cesium.ConstantProperty(position) as any;
  }

  // 释放资源
  dispose(): void {
    this.viewer.dataSources.remove(this.dataSource, true);
  }
}