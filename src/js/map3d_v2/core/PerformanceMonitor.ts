// src/js/map3d_v2/core/PerformanceMonitor.ts
import * as Cesium from 'cesium';

// 性能状态类型
type PerformanceStatus = 'good' | 'warning' | 'critical';

// 性能监控器
export class PerformanceMonitor {
  private viewer: Cesium.Viewer;
  private frameRates: number[] = [];
  private maxSamples: number = 60;
  private lastFrameTime: number = 0;
  private isMonitoring: boolean = false;
  private optimizationCallback: (fps: number, status: PerformanceStatus) => void;
  
  // 性能阈值
  private fpsThresholds = {
    critical: 20,  // 低于20fps视为严重性能问题
    warning: 30,   // 低于30fps视为性能警告
    good: 45       // 高于45fps视为性能良好
  };

  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer;
    this.optimizationCallback = () => {};
  }

  // 开始监控
  start(callback?: (fps: number, status: PerformanceStatus) => void): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.lastFrameTime = performance.now();
    this.frameRates = [];
    
    if (callback) {
      this.optimizationCallback = callback;
    }
    
    this.viewer.scene.postRender.addEventListener(this.onFrameRendered.bind(this));
  }

  // 停止监控
  stop(): void {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    this.viewer.scene.postRender.removeEventListener(this.onFrameRendered.bind(this));
  }

  // 帧渲染回调
  private onFrameRendered(): void {
    const now = performance.now();
    const deltaTime = now - this.lastFrameTime;
    this.lastFrameTime = now;
    
    // 计算FPS
    const fps = 1000 / deltaTime;
    
    // 添加到采样数组
    this.frameRates.push(fps);
    if (this.frameRates.length > this.maxSamples) {
      this.frameRates.shift();
    }
    
    // 每秒评估性能
    if (this.frameRates.length >= 30) {
      this.evaluatePerformance();
    }
  }

  // 评估性能
  private evaluatePerformance(): void {
    // 计算平均FPS
    const avgFps = this.frameRates.reduce((sum, fps) => sum + fps, 0) / this.frameRates.length;
    
    // 确定性能状态
    let status: PerformanceStatus;
    if (avgFps < this.fpsThresholds.critical) {
      status = 'critical';
    } else if (avgFps < this.fpsThresholds.warning) {
      status = 'warning';
    } else {
      status = 'good';
    }
    
    // 调用优化回调
    this.optimizationCallback(avgFps, status);
  }

  // 获取当前FPS
  getCurrentFps(): number {
    if (this.frameRates.length === 0) return 0;
    return this.frameRates[this.frameRates.length - 1];
  }

  // 获取平均FPS
  getAverageFps(): number {
    if (this.frameRates.length === 0) return 0;
    return this.frameRates.reduce((sum, fps) => sum + fps, 0) / this.frameRates.length;
  }

  // 设置性能阈值
  setThresholds(critical: number, warning: number, good: number): void {
    this.fpsThresholds = {
      critical,
      warning,
      good
    };
  }
}

// 动态优化器
export class DynamicOptimizer {
  private viewer: Cesium.Viewer;
  private monitor: PerformanceMonitor;
  private currentMode: 'high' | 'balanced' | 'quality' = 'balanced';
  private autoAdjust: boolean = true;
  private stabilityCounter: number = 0;
  private lastStatus: PerformanceStatus | null = null;
  
  // 场景设置
  private sceneSettings = {
    high: {
      fog: false,
      msaa: false,
      ssao: false,
      bloom: false,
      shadowMapSize: 1024,
      fxaa: false,
      maximumScreenSpaceError: 4,
      tileCacheSize: 100
    },
    balanced: {
      fog: true,
      msaa: false,
      ssao: false,
      bloom: false,
      shadowMapSize: 2048,
      fxaa: true,
      maximumScreenSpaceError: 2,
      tileCacheSize: 200
    },
    quality: {
      fog: true,
      msaa: true,
      ssao: true,
      bloom: true,
      shadowMapSize: 4096,
      fxaa: true,
      maximumScreenSpaceError: 1,
      tileCacheSize: 500
    }
  };

  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer;
    this.monitor = new PerformanceMonitor(viewer);
  }

  // 启动动态优化
  start(): PerformanceMonitor {
    // 应用初始设置
    this.applySettings(this.currentMode);
    
    // 启动性能监控
    this.monitor.start((fps, status) => {
      if (this.autoAdjust) {
        this.adjustSettings(fps, status);
      }
    });
    
    return this.monitor;
  }

  // 停止动态优化
  stop(): void {
    this.monitor.stop();
  }

  // 设置性能模式
  setMode(mode: 'high' | 'balanced' | 'quality', autoAdjust: boolean = true): void {
    this.currentMode = mode;
    this.autoAdjust = autoAdjust;
    this.applySettings(mode);
  }

  // 应用设置
  private applySettings(mode: 'high' | 'balanced' | 'quality'): void {
    const settings = this.sceneSettings[mode];
    const scene = this.viewer.scene;
    
    // 应用场景设置
    scene.fog.enabled = settings.fog;
    scene.msaaSamples = settings.msaa ? 4 : 1;
    
    // 环境光遮蔽
    if (scene.postProcessStages) {
      const ssao = scene.postProcessStages.ambientOcclusion;
      if (ssao) {
        ssao.enabled = settings.ssao;
      }
    }
    
    // 泛光效果
    if (scene.postProcessStages) {
      const bloom = scene.postProcessStages.bloom;
      if (bloom) {
        bloom.enabled = settings.bloom;
      }
    }
    
    // 阴影设置
    if (scene.shadowMap) {
      scene.shadowMap.size = settings.shadowMapSize;
    }
    
    // FXAA抗锯齿
    scene.postProcessStages.fxaa.enabled = settings.fxaa;
    
    // 地形设置
    scene.globe.maximumScreenSpaceError = settings.maximumScreenSpaceError;
    scene.globe.tileCacheSize = settings.tileCacheSize;
    scene.globe.enableLighting = mode !== 'high';
    
    // 应用模型设置
    this.applyModelSettings(mode);
  }

  // 应用模型设置
  private applyModelSettings(mode: 'high' | 'balanced' | 'quality'): void {
    const primitives = this.viewer.scene.primitives;
    for (let i = 0; i < primitives.length; i++) {
      const primitive = primitives.get(i);
      
      if (primitive instanceof Cesium.Model) {
        // 调整模型设置
        switch (mode) {
          case 'high':
            primitive.minimumPixelSize = 64;
            primitive.maximumScale = 10000;
            primitive.shadows = false;
            break;
          case 'balanced':
            primitive.minimumPixelSize = 96;
            primitive.maximumScale = 15000;
            primitive.shadows = true;
            break;
          case 'quality':
            primitive.minimumPixelSize = 128;
            primitive.maximumScale = 20000;
            primitive.shadows = true;
            break;
        }
      } else if (primitive instanceof Cesium.Cesium3DTileset) {
        // 调整3D Tiles设置
        switch (mode) {
          case 'high':
            primitive.maximumScreenSpaceError = 32;
            primitive.dynamicScreenSpaceErrorFactor = 6.0;
            break;
          case 'balanced':
            primitive.maximumScreenSpaceError = 16;
            primitive.dynamicScreenSpaceErrorFactor = 4.0;
            break;
          case 'quality':
            primitive.maximumScreenSpaceError = 8;
            primitive.dynamicScreenSpaceErrorFactor = 2.0;
            break;
        }
      }
    }
  }

  // 动态调整设置
  private adjustSettings(fps: number, status: PerformanceStatus): void {
    // 如果状态与上次相同，增加稳定计数器
    if (status === this.lastStatus) {
      this.stabilityCounter++;
    } else {
      this.stabilityCounter = 0;
      this.lastStatus = status;
    }
    
    // 只有当状态稳定一段时间后才调整设置
    if (this.stabilityCounter < 5) return;
    
    // 根据性能状态调整设置
    switch (status) {
      case 'critical':
        // 性能严重不足，切换到高性能模式
        if (this.currentMode !== 'high') {
          console.log('性能不足，切换到高性能模式');
          this.setMode('high', true);
        }
        break;
        
      case 'warning':
        // 性能不佳，切换到平衡模式
        if (this.currentMode === 'quality') {
          console.log('性能不佳，切换到平衡模式');
          this.setMode('balanced', true);
        }
        break;
        
      case 'good':
        // 性能良好，可以考虑提高质量
        if (this.currentMode === 'high') {
          console.log('性能良好，切换到平衡模式');
          this.setMode('balanced', true);
        } else if (this.currentMode === 'balanced' && fps > 50) {
          console.log('性能优秀，切换到高质量模式');
          this.setMode('quality', true);
        }
        break;
    }
    
    // 重置稳定计数器
    this.stabilityCounter = 0;
  }

  // 获取当前模式
  getMode(): 'high' | 'balanced' | 'quality' {
    return this.currentMode;
  }

  // 设置自动调整
  setAutoAdjust(enabled: boolean): void {
    this.autoAdjust = enabled;
  }
}