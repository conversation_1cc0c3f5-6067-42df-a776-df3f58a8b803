import * as Cesium from 'cesium';

// 创建Cesium查看器
export function createViewer(container: HTMLElement): Cesium.Viewer {
  // 配置Cesium访问令牌（如果需要）
  // Cesium.Ion.defaultAccessToken = 'your-access-token';
  
  // 创建查看器
  const viewer = new Cesium.Viewer(container, {
    // 基础配置
    animation: false,
    baseLayerPicker: false,
    fullscreenButton: false,
    geocoder: false,
    homeButton: false,
    infoBox: false,
    sceneModePicker: false,
    selectionIndicator: false,
    timeline: false,
    navigationHelpButton: false,
    
    // 性能相关配置
    requestRenderMode: true,
    maximumRenderTimeChange: Infinity,
    
    // 地形配置
    terrainProvider: Cesium.createWorldTerrain({
      requestWaterMask: false,
      requestVertexNormals: true
    }),
    
    // 影像配置
    imageryProvider: new Cesium.ArcGisMapServerImageryProvider({
      url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer'
    }),
    
    // 抗锯齿
    scene3DOnly: true
  });
  
  // 配置场景
  const scene = viewer.scene;
  
  // 启用深度测试
  scene.globe.depthTestAgainstTerrain = true;
  
  // 配置光照
  scene.globe.enableLighting = true;
  
  // 配置阴影
  scene.shadowMap.enabled = true;
  scene.shadowMap.softShadows = true;
  
  // 配置雾效果
  scene.fog.enabled = true;
  scene.fog.density = 0.0002;
  scene.fog.screenSpaceErrorFactor = 2.0;
  
  // 配置抗锯齿
  scene.postProcessStages.fxaa.enabled = true;
  
  // 禁用默认的双击事件
  viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
  
  // 设置初始相机位置
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(116.4, 39.9, 1000),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    }
  });
  
  return viewer;
}

// 创建相机控制器
export function createCameraController(viewer: Cesium.Viewer): void {
  // 配置相机控制器
  const controller = viewer.scene.screenSpaceCameraController;
  
  // 设置缩放速度
  controller.zoomEventTypes = [Cesium.CameraEventType.WHEEL, Cesium.CameraEventType.PINCH];
  controller.zoomFactor = 3.0;
  
  // 设置旋转速度
  controller.rotateEventTypes = [Cesium.CameraEventType.LEFT_DRAG];
  controller.rotateRateRangeAdjustment = true;
  
  // 设置平移速度
  controller.translateEventTypes = [Cesium.CameraEventType.RIGHT_DRAG];
  controller.tiltEventTypes = [Cesium.CameraEventType.MIDDLE_DRAG, Cesium.CameraEventType.PINCH, {
    eventType: Cesium.CameraEventType.LEFT_DRAG,
    modifier: Cesium.KeyboardEventModifier.CTRL
  }];
  
  // 限制缩放范围
  controller.minimumZoomDistance = 10; // 最小缩放距离（米）
  controller.maximumZoomDistance = 20000; // 最大缩放距离（米）
}

// 创建性能优化的地形配置
export function createOptimizedTerrainProvider(): Cesium.TerrainProvider {
  return Cesium.createWorldTerrain({
    requestWaterMask: false,
    requestVertexNormals: true
  });
}

// 创建性能优化的影像配置
export function createOptimizedImageryProvider(): Cesium.ImageryProvider {
  return new Cesium.ArcGisMapServerImageryProvider({
    url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer',
    enablePickFeatures: false
  });
}

// 应用高性能设置
export function applyHighPerformanceSettings(viewer: Cesium.Viewer): void {
  const scene = viewer.scene;
  
  // 禁用不必要的特性
  scene.fog.enabled = false;
  scene.skyAtmosphere.show = false;
  scene.globe.showGroundAtmosphere = false;
  scene.shadowMap.enabled = false;
  
  // 降低地形细节
  scene.globe.maximumScreenSpaceError = 4;
  
  // 禁用后处理效果
  scene.postProcessStages.fxaa.enabled = false;
  if (scene.postProcessStages.bloom) {
    scene.postProcessStages.bloom.enabled = false;
  }
  if (scene.postProcessStages.ambientOcclusion) {
    scene.postProcessStages.ambientOcclusion.enabled = false;
  }
  
  // 减少缓存大小
  scene.globe.tileCacheSize = 100;
  
  // 禁用光照
  scene.globe.enableLighting = false;
}

// 应用平衡设置
export function applyBalancedSettings(viewer: Cesium.Viewer): void {
  const scene = viewer.scene;
  
  // 启用基本特性
  scene.fog.enabled = true;
  scene.skyAtmosphere.show = true;
  scene.globe.showGroundAtmosphere = true;
  scene.shadowMap.enabled = true;
  scene.shadowMap.softShadows = false;
  
  // 适中的地形细节
  scene.globe.maximumScreenSpaceError = 2;
  
  // 启用基本后处理效果
  scene.postProcessStages.fxaa.enabled = true;
  if (scene.postProcessStages.bloom) {
    scene.postProcessStages.bloom.enabled = false;
  }
  if (scene.postProcessStages.ambientOcclusion) {
    scene.postProcessStages.ambientOcclusion.enabled = false;
  }
  
  // 适中的缓存大小
  scene.globe.tileCacheSize = 200;
  
  // 启用光照
  scene.globe.enableLighting = true;
}

// 应用高质量设置
export function applyQualitySettings(viewer: Cesium.Viewer): void {
  const scene = viewer.scene;
  
  // 启用所有特性
  scene.fog.enabled = true;
  scene.skyAtmosphere.show = true;
  scene.globe.showGroundAtmosphere = true;
  scene.shadowMap.enabled = true;
  scene.shadowMap.softShadows = true;
  
  // 高质量地形细节
  scene.globe.maximumScreenSpaceError = 1;
  
  // 启用所有后处理效果
  scene.postProcessStages.fxaa.enabled = true;
  if (scene.postProcessStages.bloom) {
    scene.postProcessStages.bloom.enabled = true;
  }
  if (scene.postProcessStages.ambientOcclusion) {
    scene.postProcessStages.ambientOcclusion.enabled = true;
  }
  
  // 大缓存大小
  scene.globe.tileCacheSize = 500;
  
  // 启用光照
  scene.globe.enableLighting = true;
} 