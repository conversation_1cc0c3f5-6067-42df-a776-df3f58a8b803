
import { Constants } from './constants.js'
import {
    MODEL_STATIC_URL,
    LINE_POINTS_NUM,
} from '@/config/global'
import * as Cesium from 'cesium'

let DEBUG_MSG_ENABLED = true
let VirtualTowerModel = ['substationModel']

export default class Map3DTool_v2 {
    version: string = 'V2.0'
    projectCode: string
    linesInfo: any[]
    showDesign: boolean = true
    private projectId: string
    private projectMsg: any
    private towerModels: any[] = []
    private lineDetail: any[]
    private planRecordMap: any[]
    private viewer: any
    private towerCollection: any[] = [] // 场景中已加载的杆塔模型合集
    private insulatorCollection: any[] = [] // 场景中已加载的绝缘子模型合集
    private labelCollection: any[] = [] // 场景中已加载的label合集
    private lineCollection: any // 场景中已加载的电缆合集
    private towerMounts: any[] = []
    private insulatorModels: any[]
    private insulatorAllocation: any[]
    private linePoints: any[] = [] // 保存线路绘制的端点信息
    entitysLoadedId: {tower: string[], insulator: string[], label: string[]} = { tower: [], insulator: [], label: [] }
    private stopLoading: boolean
    private awaitToLoadInsulators: any[] = []

    constructor(viewer: any, projectCode: string, projectData: any, planRecordMap: any[]) {
        console.log("Map3DTool_v2 constructor, projectCode=", projectCode, " projectData=", projectData, " planRecordMap=", planRecordMap)
        this.projectMsg = projectData.projectMsg
        this.projectCode = projectCode
        this.viewer = viewer
        this.planRecordMap = planRecordMap || []

        // 初始化项目数据
        this.linesInfo = projectData.linesInfo || []
        this.lineDetail = projectData.lineDetail || []
        this.towerModels = projectData.towerModels || []
        this.towerMounts = projectData.towerMounts || []
        this.insulatorModels = projectData.insulatorModels || []
        this.insulatorAllocation = projectData.insulatorAllocation || []

        console.log('Map3DTool_v2 初始化完成:', {
            linesInfo: this.linesInfo.length,
            lineDetail: this.lineDetail.length,
            towerModels: this.towerModels.length,
            towerMounts: this.towerMounts.length,
            insulatorModels: this.insulatorModels.length,
            insulatorAllocation: this.insulatorAllocation.length
        })
    }

    setDesign(flag: boolean) {
        this.showDesign = flag
        console.log('设置设计模式:', flag)
    }

    autoDrawLine = async () => {
        console.log('开始自动绘制线路...')
        this.removeAllModels()

        try {
            // 加载绝缘子并获取待加载的绝缘子信息
            const awaitToLoadInsulators = await this.loadInsulators()
            this.awaitToLoadInsulators = awaitToLoadInsulators || []

            // 动态加载杆塔相关的绝缘子
            this.enableEntitiesLod(this.awaitToLoadInsulators, 2000)

            // 加载线路导线和地线
            this.loadLines()

            console.log('自动绘制线路完成')
            return Promise.resolve(true)
        } catch (error) {
            console.error('自动绘制线路失败:', error)
            return Promise.resolve(false)
        }
    }

    removeAllModels() {
        console.log('清除所有模型...')

        // 清除杆塔模型
        if (this.towerCollection.length > 0) {
            this.towerCollection.forEach(entity => {
                this.viewer.entities.remove(entity)
            })
            this.towerCollection = []
        }

        // 清除绝缘子模型
        if (this.insulatorCollection.length > 0) {
            this.insulatorCollection.forEach(entity => {
                this.viewer.entities.remove(entity)
            })
            this.insulatorCollection = []
        }

        // 清除标签
        if (this.labelCollection.length > 0) {
            this.labelCollection.forEach(entity => {
                this.viewer.entities.remove(entity)
            })
            this.labelCollection = []
        }

        // 清除线路
        if (this.lineCollection) {
            for (let key in this.lineCollection) {
                if (this.lineCollection[key] && this.lineCollection[key].removeAll) {
                    this.lineCollection[key].removeAll()
                }
            }
        }

        // 重置实体ID记录
        this.entitysLoadedId = { tower: [], insulator: [], label: [] }
        this.linePoints = []
    }

    loadInsulators = async () => {
        console.log('开始加载绝缘子...')

        // 先清除场景中已加载的绝缘子模型
        if (this.insulatorCollection.length > 0) {
            this.insulatorCollection.forEach(entity => {
                this.viewer.entities.remove(entity)
            })
            this.insulatorCollection = []
        }

        this.linePoints = [] // 加载绝缘子前清空场景原有的线路节点位置信息
        let awaitToLoadInsulators = []

        for (let line of this.linesInfo) {
            console.log(`处理线路: ${line.id || line.name}`)

            // 获取该线路的配串表
            let sortedInsulatorAllocation = this.getLineAllocations(line.id)

            if (sortedInsulatorAllocation.length == 0) {
                console.error(`线路(${line.id || 'unknown'})没有配串表insulatorAllocation的数据，无法加载`)
                continue
            }

            console.log(`线路(${line.id})的配串表信息:`, sortedInsulatorAllocation)

            // 处理每个配串
            for (let ind = 0; ind < sortedInsulatorAllocation.length; ind++) {
                let insulatorAllocation1 = sortedInsulatorAllocation[ind]
                let insulatorInfo1 = this.getInsulatorInfo(insulatorAllocation1)

                if (insulatorInfo1 == null) {
                    console.error(`线路${line.id}中序号id=${ind + 1}的配串信息解析错误`, insulatorAllocation1)
                    continue
                }

                console.log(`线路${insulatorInfo1.lineId}中序号id=${insulatorInfo1.positionId}解析得到的配串数据:`, insulatorInfo1)

                // 检查场景中是否已经加载该绝缘子
                let entity = this.checkInsulatorLoaded(insulatorInfo1)
                if (entity != undefined) {
                    console.log(`配串已经加载，无需再次加载`, insulatorInfo1)
                    insulatorInfo1.angRaid = entity.properties?.angle?.getValue() || 0
                    this.addLinePoint(insulatorInfo1, insulatorAllocation1, line.phaseSequence)
                    continue
                }

                // 设置绝缘子角度（简化处理）
                insulatorInfo1.angRaid = insulatorInfo1.mountTower.angle * Math.PI / 180

                // 添加到待加载列表
                awaitToLoadInsulators.push(insulatorInfo1)

                // 添加线路节点
                this.addLinePoint(insulatorInfo1, insulatorAllocation1, line.phaseSequence)
            }
        }

        console.log(`绝缘子加载完成，待加载绝缘子数量: ${awaitToLoadInsulators.length}`)
        return Promise.resolve(awaitToLoadInsulators)
    }

    getLineAllocations(lineId: any) {
        // 获取指定线路的配串表，并按positionId排序
        let allocations = this.insulatorAllocation.filter(item => item.lineId == lineId)
        return allocations.sort((a, b) => a.positionId - b.positionId)
    }

    getInsulatorInfo(insulatorAllocation: any) {
        try {
            // 获取杆塔信息
            let mountTower = this.lineDetail.find(tower => tower.towerNumber == insulatorAllocation.towerNumber)
            if (!mountTower) {
                console.error(`找不到杆塔: ${insulatorAllocation.towerNumber}`)
                return null
            }

            // 获取杆塔模型信息
            let towerModel = this.towerModels.find(model => model.towerModel == mountTower.towerModel)
            if (!towerModel) {
                console.error(`找不到杆塔模型: ${mountTower.towerModel}`)
                return null
            }

            // 获取挂点信息
            let mountInfo = this.towerMounts.find(mount =>
                mount.towerModel == mountTower.towerModel && mount.locationId == insulatorAllocation.locationId
            )
            if (!mountInfo) {
                console.error(`找不到挂点信息: towerModel=${mountTower.towerModel}, locationId=${insulatorAllocation.locationId}`)
                return null
            }

            // 获取绝缘子模型信息
            let insulatorModel = this.insulatorModels.find(model => model.modelName == insulatorAllocation.insulator)
            if (!insulatorModel) {
                console.error(`找不到绝缘子模型: ${insulatorAllocation.insulator}`)
                return null
            }

            return {
                lineId: insulatorAllocation.lineId,
                positionId: insulatorAllocation.positionId,
                towerNumber: insulatorAllocation.towerNumber,
                locationId: insulatorAllocation.locationId,
                mountTower: mountTower,
                towerModel: towerModel,
                mountInfo: mountInfo,
                insulatorModel: insulatorModel,
                insulatorAllocation: insulatorAllocation
            }
        } catch (error) {
            console.error('解析绝缘子信息失败:', error, insulatorAllocation)
            return null
        }
    }

    checkInsulatorLoaded(insulatorInfo: any) {
        // 检查绝缘子是否已经加载（简化实现）
        return undefined
    }

    addLinePoint(insulatorInfo: any, insulatorAllocation: any, phaseSequence: string) {
        try {
            // 计算杆塔基础位置
            let towerPosition = Cesium.Cartesian3.fromDegrees(
                insulatorInfo.mountTower.longitude,
                insulatorInfo.mountTower.latitude,
                insulatorInfo.mountTower.height
            )

            // 获取挂点的相对位置
            let mountOffset = insulatorInfo.mountInfo.mountPosition || [0, 0, 0]

            // 计算杆塔的变换矩阵（考虑杆塔角度）
            let heading = Cesium.Math.toRadians(insulatorInfo.mountTower.angle || 0)
            let pitch = 0
            let roll = 0
            let hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll)
            let orientation = Cesium.Transforms.headingPitchRollQuaternion(towerPosition, hpr)
            let transform = Cesium.Matrix4.fromRotationTranslation(
                Cesium.Matrix3.fromQuaternion(orientation),
                towerPosition
            )

            // 将挂点相对坐标转换为世界坐标
            let localOffset = new Cesium.Cartesian3(mountOffset[0], mountOffset[1], mountOffset[2])
            let worldPosition = Cesium.Matrix4.multiplyByPoint(transform, localOffset, new Cesium.Cartesian3())

            // 添加到线路点集合
            this.linePoints.push({
                lineId: insulatorInfo.lineId,
                positionId: insulatorInfo.positionId,
                towerNumber: insulatorInfo.towerNumber,
                locationId: insulatorInfo.locationId,
                position: worldPosition,
                phaseSequence: phaseSequence,
                insulatorInfo: insulatorInfo,
                mountOffset: mountOffset
            })

            console.log(`添加线路点: 线路${insulatorInfo.lineId}, 杆塔${insulatorInfo.towerNumber}, 挂点${insulatorInfo.locationId}, 偏移[${mountOffset.join(',')}]`)
        } catch (error) {
            console.error('计算线路点位置失败:', error, insulatorInfo)
        }
    }

    loadLines() {
        console.log('开始加载线路导线...')

        if (this.linePoints.length < 2) {
            console.warn('线路点数量不足，无法绘制线路')
            return
        }

        // 按线路ID分组
        let lineGroups: {[key: string]: any[]} = {}
        this.linePoints.forEach(point => {
            if (!lineGroups[point.lineId]) {
                lineGroups[point.lineId] = []
            }
            lineGroups[point.lineId].push(point)
        })

        // 为每条线路绘制导线
        for (let lineId in lineGroups) {
            let points = lineGroups[lineId].sort((a, b) => a.positionId - b.positionId)
            this.drawPowerLine(lineId, points)
        }

        console.log('线路导线加载完成')
    }

    drawPowerLine(lineId: string, points: any[]) {
        console.log(`绘制线路 ${lineId}, 点数: ${points.length}`)

        if (points.length < 2) {
            console.warn(`线路 ${lineId} 点数不足，无法绘制`)
            return
        }

        // 按相位分组绘制（A、B、C三相）
        let phaseGroups: {[key: string]: any[]} = {}
        points.forEach(point => {
            // 根据locationId确定相位
            let phase = this.getPhaseByLocationId(point.locationId)
            if (!phaseGroups[phase]) {
                phaseGroups[phase] = []
            }
            phaseGroups[phase].push(point)
        })

        // 为每相绘制导线
        for (let phase in phaseGroups) {
            let phasePoints = phaseGroups[phase].sort((a, b) => a.positionId - b.positionId)
            this.drawPhaseWire(lineId, phase, phasePoints)
        }

        console.log(`线路 ${lineId} 绘制完成，包含相位: ${Object.keys(phaseGroups).join(', ')}`)
    }

    getPhaseByLocationId(locationId: number): string {
        // 根据locationId确定相位
        // locationId 1,2 = A相, 3,4 = B相, 5,6 = C相
        if (locationId <= 2) return 'A'
        if (locationId <= 4) return 'B'
        return 'C'
    }

    drawPhaseWire(lineId: string, phase: string, points: any[]) {
        if (points.length < 2) return

        // 提取位置信息
        let positions = points.map(point => point.position)

        // 根据相位确定颜色
        let phaseColors = {
            'A': '#FF0000', // 红色 - A相
            'B': '#FFFF00', // 黄色 - B相
            'C': '#0000FF'  // 蓝色 - C相
        }

        // 创建相线实体
        let wireEntity = this.viewer.entities.add({
            id: `powerline-${lineId}-${phase}`,
            name: `${phase}相导线-线路${lineId}`,
            polyline: {
                positions: positions,
                width: 4,
                material: phaseColors[phase] || '#FFFF00',
                clampToGround: false
            }
        })

        console.log(`${phase}相导线绘制完成，点数: ${points.length}`)
    }

    enableEntitiesLod(awaitToLoadInsulators: any[], distance: number) {
        console.log(`启用LOD加载，绝缘子数量: ${awaitToLoadInsulators.length}, 距离阈值: ${distance}m`)

        // 简化实现：直接加载所有绝缘子（在实际项目中应该根据距离动态加载）
        awaitToLoadInsulators.forEach(insulatorInfo => {
            this.loadSingleInsulator(insulatorInfo)
        })
    }

    loadSingleInsulator(insulatorInfo: any) {
        try {
            // 计算绝缘子的精确位置（基于挂点位置）
            let towerPosition = Cesium.Cartesian3.fromDegrees(
                insulatorInfo.mountTower.longitude,
                insulatorInfo.mountTower.latitude,
                insulatorInfo.mountTower.height
            )

            // 获取挂点偏移
            let mountOffset = insulatorInfo.mountInfo.mountPosition || [0, 0, 0]

            // 计算变换矩阵
            let heading = Cesium.Math.toRadians(insulatorInfo.mountTower.angle || 0)
            let hpr = new Cesium.HeadingPitchRoll(heading, 0, 0)
            let orientation = Cesium.Transforms.headingPitchRollQuaternion(towerPosition, hpr)
            let transform = Cesium.Matrix4.fromRotationTranslation(
                Cesium.Matrix3.fromQuaternion(orientation),
                towerPosition
            )

            // 计算绝缘子位置
            let localOffset = new Cesium.Cartesian3(mountOffset[0], mountOffset[1], mountOffset[2])
            let insulatorPosition = Cesium.Matrix4.multiplyByPoint(transform, localOffset, new Cesium.Cartesian3())

            // 根据相位确定绝缘子颜色
            let phase = this.getPhaseByLocationId(insulatorInfo.locationId)
            let phaseColors = {
                'A': '#FF6666', // 浅红色 - A相绝缘子
                'B': '#FFFF66', // 浅黄色 - B相绝缘子
                'C': '#6666FF'  // 浅蓝色 - C相绝缘子
            }

            // 创建绝缘子标记
            let insulatorEntity = this.viewer.entities.add({
                id: `insulator-${insulatorInfo.lineId}-${insulatorInfo.positionId}-${insulatorInfo.locationId}`,
                name: `${phase}相绝缘子-${insulatorInfo.insulatorModel.modelName}`,
                position: insulatorPosition,
                point: {
                    pixelSize: 10,
                    color: phaseColors[phase] || '#00FF00',
                    outlineColor: '#FFFFFF',
                    outlineWidth: 2,
                    heightReference: Cesium.HeightReference.NONE
                },
                label: {
                    text: `${phase}-${insulatorInfo.insulatorModel.modelName}`,
                    font: '11pt Arial',
                    fillColor: '#FFFFFF',
                    outlineColor: '#000000',
                    outlineWidth: 1,
                    pixelOffset: new Cesium.Cartesian2(0, -25),
                    scale: 0.7,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM
                }
            })

            this.insulatorCollection.push(insulatorEntity)
            console.log(`加载${phase}相绝缘子: ${insulatorInfo.insulatorModel.modelName}, 位置偏移[${mountOffset.join(',')}]`)
        } catch (error) {
            console.error('加载绝缘子失败:', error, insulatorInfo)
        }
    }
}
