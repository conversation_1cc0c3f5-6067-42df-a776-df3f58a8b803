
import { Constants } from './constants.js'
import type { AllocationType, InsulatorLoadType, LineInfoType, TowerModelType, LineDetailType, TowerMountType, InsulatorModelType, LinePointTpye } from './userType.ts';
import {
    MODEL_STATIC_URL,
    LINE_POINTS_NUM,
} from '@/config/global'
import { createEntity_glb, createLineCollection, getPositionHeight } from "@/js/common/viewer"
import { showModelByStatus, getMountPosition, calAzimuth, overheadLine, straight_line } from "@/js/common/line_tower"
import { DivLabel } from "@/js/common/divLabel"
import { Roaming } from "@/js/common/roaming"
import * as Cesium from 'cesium'

let DEBUG_MSG_ENABLED = true
let VirtualTowerModel = ['substationModel']

export default class Map3DTool_v2 {
    version: string = 'V2.0'
    projectCode: string
    linesInfo: LineInfoType[]
    showDesign: boolean = true
    private projectId: string
    private projectMsg: any
    private towerModels: TowerModelType[] = []
    private lineDetail: LineDetailType[]
    private planRecordMap: any[]
    private viewer: any
    private towerCollection: any[] = [] // 场景中已加载的杆塔模型合集
    private insulatorCollection: any[] = [] // 场景中已加载的绝缘子模型合集
    private labelCollection: any[] = [] // 场景中已加载的label合集
    private lineCollection: any // 场景中已加载的电缆合集
    private towerMounts: TowerMountType[] = []
    private insulatorModels: InsulatorModelType[]
    private insulatorAllocation: any[]
    private linePoints: LinePointTpye[] = [] // 保存线路绘制的端点信息
    entitysLoadedId: {tower: string[], insulator: string[], label: string[]} = { tower: [], insulator: [], label: [] }
    private stopLoading: boolean
    private awaitToLoadInsulators: any[] = []

    constructor(viewer: any, projectCode: string, projectData: any, planRecordMap: any[]) {
        console.log("Map3DTool_v2 constructor, projectCode=", projectCode, " projectData=", projectData, " planRecordMap=", planRecordMap)
        this.projectMsg = projectData.projectMsg
        this.projectCode = projectCode
        this.projectId = projectData.projectMsg ? projectData.projectMsg.projectId : projectCode // projectID为0 则表示不对数据进行过滤处理
        this.viewer = viewer
        this.planRecordMap = planRecordMap || []

        // 从传递的参数中 提取匹配本工程ID的数据进行过滤后保存 避免其他工程数据的污染
        this.linesInfo = projectData.linesInfo.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.towerModels = projectData.towerModels.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.lineDetail = projectData.lineDetail.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.towerMounts = projectData.towerMounts.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.insulatorModels = projectData.insulatorModels.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.insulatorAllocation = projectData.insulatorAllocation.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))

        // 修复杆塔挂点数据
        for (let i = 0; i < this.towerModels.length; i++) {
            let towerModel = this.towerModels[i]
            let mounts = this.towerMounts.filter(item => item.towerModel == towerModel.towerModel)
            mounts.forEach(el => {
                if (el.fixBaseHeight != true) {
                    // 修复数据中杆塔挂点没有加入杆塔呼高的偏差
                    el.mountPosition[2] += towerModel.baseHeight
                    // 修正x和y轴相反的问题
                    let temp = el.mountPosition[0];
                    el.mountPosition[0] = -1 * el.mountPosition[1];
                    el.mountPosition[1] = -1 * temp;
                    el.fixBaseHeight = true
                }
            });
        }

        // 初始化线路集合
        this.lineCollection = {}
        this.linesInfo.forEach(line => {
            if (line.id) {
                this.lineCollection[line.id] = createLineCollection(this.viewer)
            }
        })
        this.stopLoading = false

        if (DEBUG_MSG_ENABLED) {
            console.log('Map3DTool_v2 初始化完成:', {
                projectId: this.projectId,
                linesInfo: this.linesInfo.length,
                lineDetail: this.lineDetail.length,
                towerModels: this.towerModels.length,
                towerMounts: this.towerMounts.length,
                insulatorModels: this.insulatorModels.length,
                insulatorAllocation: this.insulatorAllocation.length
            })
            console.log('杆塔挂点', this.towerMounts, this.lineDetail)
        }
    }

    setDesign(flag: boolean) {
        this.showDesign = flag
        console.log('设置设计模式:', flag)
    }

    autoDrawLine = async () => {
        console.log('开始自动绘制线路...')
        this.removeAllModels()

        try {
            // 加载绝缘子并获取待加载的绝缘子信息
            const awaitToLoadInsulators = await this.loadInsulators()
            this.awaitToLoadInsulators = awaitToLoadInsulators || []

            // 动态加载杆塔相关的绝缘子
            this.enableEntitiesLod(this.awaitToLoadInsulators, 2000)

            // 加载线路导线和地线
            this.loadLines()

            console.log('自动绘制线路完成')
            return Promise.resolve(true)
        } catch (error) {
            console.error('自动绘制线路失败:', error)
            return Promise.resolve(false)
        }
    }

    removeAllModels() {
        console.log('清除所有模型...')

        // 清除杆塔模型
        if (this.towerCollection.length > 0) {
            this.towerCollection.forEach(entity => {
                this.viewer.entities.remove(entity)
            })
            this.towerCollection = []
        }

        // 清除绝缘子模型
        if (this.insulatorCollection.length > 0) {
            this.insulatorCollection.forEach(entity => {
                this.viewer.entities.remove(entity)
            })
            this.insulatorCollection = []
        }

        // 清除标签
        if (this.labelCollection.length > 0) {
            this.labelCollection.forEach(entity => {
                this.viewer.entities.remove(entity)
            })
            this.labelCollection = []
        }

        // 清除线路
        if (this.lineCollection) {
            for (let key in this.lineCollection) {
                if (this.lineCollection[key] && this.lineCollection[key].removeAll) {
                    this.lineCollection[key].removeAll()
                }
            }
        }

        // 重置实体ID记录
        this.entitysLoadedId = { tower: [], insulator: [], label: [] }
        this.linePoints = []
    }

    loadInsulators = async () => {
        console.log('开始加载绝缘子...')

        // 先清除场景中已加载的绝缘子模型
        if (this.insulatorCollection.length > 0) {
            this.insulatorCollection.forEach(entity => {
                this.viewer.entities.remove(entity)
            })
            this.insulatorCollection = []
        }

        this.linePoints = [] // 加载绝缘子前清空场景原有的线路节点位置信息
        let awaitToLoadInsulators = []

        for (let line of this.linesInfo) {
            console.log(`处理线路: ${line.id || line.name}`)

            // 获取该线路的配串表
            let sortedInsulatorAllocation = this.getLineAllocations(line.id)

            if (sortedInsulatorAllocation.length == 0) {
                console.error(`线路(${line.id || 'unknown'})没有配串表insulatorAllocation的数据，无法加载`)
                continue
            }

            console.log(`线路(${line.id})的配串表信息:`, sortedInsulatorAllocation)

            // 处理每个配串
            for (let ind = 0; ind < sortedInsulatorAllocation.length; ind++) {
                let insulatorAllocation1 = sortedInsulatorAllocation[ind]
                let insulatorInfo1 = this.getInsulatorInfo(insulatorAllocation1)

                if (insulatorInfo1 == null) {
                    console.error(`线路${line.id}中序号id=${ind + 1}的配串信息解析错误`, insulatorAllocation1)
                    continue
                }

                console.log(`线路${insulatorInfo1.lineId}中序号id=${insulatorInfo1.positionId}解析得到的配串数据:`, insulatorInfo1)

                // 检查场景中是否已经加载该绝缘子
                let entity = this.checkInsulatorLoaded(insulatorInfo1)
                if (entity != undefined) {
                    console.log(`配串已经加载，无需再次加载`, insulatorInfo1)
                    insulatorInfo1.angRaid = entity.properties?.angle?.getValue() || 0
                    this.addLinePoint(insulatorInfo1, insulatorAllocation1, line.phaseSequence)
                    continue
                }

                // 设置绝缘子角度（简化处理）
                insulatorInfo1.angRaid = insulatorInfo1.mountTower.angle * Math.PI / 180

                // 添加到待加载列表
                awaitToLoadInsulators.push(insulatorInfo1)

                // 添加线路节点
                this.addLinePoint(insulatorInfo1, insulatorAllocation1, line.phaseSequence)
            }
        }

        console.log(`绝缘子加载完成，待加载绝缘子数量: ${awaitToLoadInsulators.length}`)
        return Promise.resolve(awaitToLoadInsulators)
    }

    getLineAllocations(lineId: any) {
        // 获取指定线路的配串表，并按positionId排序
        let allocations = this.insulatorAllocation.filter(item => item.lineId == lineId)
        return allocations.sort((a, b) => a.positionId - b.positionId)
    }

    getInsulatorInfo = (allocationInfo: AllocationType): InsulatorLoadType | null => {
        // 根据配串表的每一条信息 获取到对应绝缘子和杆塔的相关数据并封装
        if (DEBUG_MSG_ENABLED) {
            console.log('根据配串表中的数据获取绝缘子相关信息', allocationInfo)
        }

        // 获取绝缘子模型信息
        let model = this.insulatorModels.find(item => item.modelName == allocationInfo.insulator && item.projectId == allocationInfo.projectId)
        if (model == undefined) {
            console.error(`配串信息未在绝缘子模型列表中存在，线路${allocationInfo.lineId}中序号id=${allocationInfo.positionId}杆塔${allocationInfo.towerNumber}未找到绝缘子模型${allocationInfo.insulator}`, allocationInfo)
            return null
        }

        // 获取挂载杆塔的信息
        let towerInfo = this.lineDetail.find(item => item.towerNumber == allocationInfo.towerNumber)
        if (towerInfo == undefined) {
            console.error(`配串信息未在线路详情表中存在，线路${allocationInfo.lineId}中序号id=${allocationInfo.positionId}杆塔${allocationInfo.towerNumber}未找到杆塔信息${allocationInfo.towerNumber}`, allocationInfo)
            return null
        }

        // 由杆塔模型的挂点数据中 获取挂载点信息
        let mount = this.towerMounts.find(item => item.towerModel == towerInfo.modelNumber && item.projectId == allocationInfo.projectId && item.locationId == allocationInfo.locationId)
        if (mount == undefined) {
            console.error(`配串信息未在杆塔挂点表中存在，线路${allocationInfo.lineId}中序号id=${allocationInfo.positionId}杆塔${allocationInfo.towerNumber}未找到挂载点信息${towerInfo.modelNumber}的${allocationInfo.locationId}号挂点`, allocationInfo)
            return null
        }

        // 计算得到挂点的绝对位置信息 经纬度高度
        let mountPosition
        if (towerInfo.modelNumber == 'substationModel') {
            // 如果是变电站模型 则直接返回挂点坐标信息
            mountPosition = mount.mountPosition
            console.log('substationMount:', mount)
        } else {
            mountPosition = getMountPosition(towerInfo, [[mount.mountPosition]], this.viewer)[0][0]
        }

        if (mountPosition == undefined) {
            console.error(`配串信息杆塔挂点计算位置失败，杆塔${allocationInfo.towerNumber}挂点${mount.mountName}的坐标错误，请核查！配串和挂点信息：`, towerInfo, allocationInfo, mount)
            return null
        }

        return {
            lineId: allocationInfo.lineId, // 线路ID
            positionId: allocationInfo.positionId, // 配串顺序
            mountName: mount.mountName, // 挂点名称
            mountID: mount.locationId, // 挂点ID
            model: model,   // 绝缘子模型信息
            mountTower: towerInfo, // 绝缘子挂载的杆塔信息
            mountPosition: mountPosition, // 挂点绝对位置信息
        }
    }

    checkInsulatorLoaded(insulatorInfo: any) {
        // 检查绝缘子是否已经加载（简化实现）
        return undefined
    }

    addLinePoint = (insulatorInfo: InsulatorLoadType, insulatorAllocation: AllocationType, phaseSequence: string = 'N') => {
        let linePointMsg = this.linePoints.find(linePoint => linePoint.lineId == insulatorInfo.lineId)
        if (linePointMsg == undefined) {
            if (DEBUG_MSG_ENABLED) {
                console.log(`线路${insulatorInfo.lineId}的线路节点信息不存在，新创建保存对象`, insulatorInfo, this.linePoints)
            }
            linePointMsg = { lineId: insulatorInfo.lineId || 0, positionDetails: [] } // 创建一个对象 保存线路绘制的节点信息
            this.linePoints.push(linePointMsg)
        }

        // 根据绝缘子 计算绝缘子的全部挂点
        let mountArray = []
        if (insulatorInfo.model.splitNum == 4) {
            mountArray.push([insulatorInfo.model.inputLine1, insulatorInfo.model.inputLine2, insulatorInfo.model.inputLine3, insulatorInfo.model.inputLine4])
            mountArray.push([insulatorInfo.model.outputLine1, insulatorInfo.model.outputLine2, insulatorInfo.model.outputLine3, insulatorInfo.model.outputLine4])
        } else if (insulatorInfo.model.splitNum == 2) {
            mountArray.push([insulatorInfo.model.inputLine1, insulatorInfo.model.inputLine2])
            mountArray.push([insulatorInfo.model.outputLine1, insulatorInfo.model.outputLine2])
        } else if (insulatorInfo.model.splitNum == 1) {
            mountArray.push([insulatorInfo.model.inputLine1])
            mountArray.push([insulatorInfo.model.outputLine1])
        } else {
            if (DEBUG_MSG_ENABLED) {
                console.log(`${insulatorInfo.mountTower.towerNumber}杆塔的绝缘子${insulatorInfo.model.modelName}的分割数量错误，分割数量应为2或4`)
            }
            return
        }

        const originModel = {
            version: 'V2',
            longitude: insulatorInfo.mountPosition[0],
            latitude: insulatorInfo.mountPosition[1],
            height: insulatorInfo.mountPosition[2],
            angle: insulatorInfo.angRaid ? insulatorInfo.angRaid * 180 / Math.PI + 90 : 90,
        }

        let positions = getMountPosition(originModel, mountArray, this.viewer)
        if (positions == undefined) {
            console.error(`${insulatorInfo.lineId}线路${insulatorInfo.mountTower.towerNumber}杆塔的${insulatorAllocation.locationId}#绝缘子${insulatorInfo.model.modelName}的挂点获取失败。绝缘子信息：`, insulatorInfo, mountArray)
            return
        }

        let swapArrayElements = (arr: any[], index1: number, index2: number) => {
            if (!arr[index1] && !arr[index2]) {
                return
            }
            const temp = arr[index1];
            arr[index1] = arr[index2];
            arr[index2] = temp;
        }

        if (insulatorAllocation.inOutFlag == 1) {
            // inOutFlag==1 时 表示进线端和出线端需要取反 位置交叉
            if (insulatorInfo.model.splitNum == 4) {
                swapArrayElements(positions[0], 0, 1)
                swapArrayElements(positions[0], 2, 3)
                swapArrayElements(positions[1], 0, 1)
                swapArrayElements(positions[1], 2, 3)
            } else if (insulatorInfo.model.splitNum == 2) {
                swapArrayElements(positions[0], 0, 1)
                swapArrayElements(positions[1], 0, 1)
            }
        }

        // 保存线路节点信息
        let linePointDetail = {
            id: `${insulatorInfo.mountTower.towerNumber}@${insulatorInfo.mountID}@${insulatorInfo.model.modelName}`,
            pointType: 'start',
            lineType: 0, // 0=悬链线 1=直线
            phaseSequence: phaseSequence,
            positions: positions[0] // 进线端位置
        }
        linePointMsg.positionDetails.push(linePointDetail)

        linePointDetail = {
            id: `${insulatorInfo.mountTower.towerNumber}@${insulatorInfo.mountID}@${insulatorInfo.model.modelName}`,
            pointType: 'end',
            lineType: 0, // 0=悬链线 1=直线
            phaseSequence: phaseSequence,
            positions: positions[1] // 出线端位置
        }
        linePointMsg.positionDetails.push(linePointDetail)

        if (DEBUG_MSG_ENABLED) {
            console.log(`添加线路点: 线路${insulatorInfo.lineId}, 杆塔${insulatorInfo.mountTower.towerNumber}, 挂点${insulatorInfo.mountID}, 分割数${insulatorInfo.model.splitNum}`)
        }
    }

    loadLines = () => {
        if (this.linePoints.length == 0) {
            console.error('线路节点信息为空，无法加载线路')
            return false
        } else {
            if (DEBUG_MSG_ENABLED) {
                console.log(`开始加载线路，线路数量共${this.linePoints.length}，线路数据：`, this.linePoints)
            }
        }

        if (this.lineCollection == undefined) {
            if (DEBUG_MSG_ENABLED) {
                console.log(`创建线路合集 lineCollection`)
            }
            this.lineCollection = {}
            this.linesInfo.forEach(line => {
                this.lineCollection[line.id] = createLineCollection(this.viewer)
            })
        } else {
            // 清除线路合集中的全部对象
            if (DEBUG_MSG_ENABLED) {
                console.log(`清除场景中线路合集中的全部对象，重新加载线路`)
            }
            for (let key in this.lineCollection) {
                this.lineCollection[key].removeAll()
            }
        }

        this.linePoints.forEach(linePoint => {
            for (let ind = 0; ind < linePoint.positionDetails.length; ind++) {
                // 提取 start和end 两个节点的信息
                let start = null
                let end = null
                while (ind < linePoint.positionDetails.length - 1) {
                    if ((linePoint.positionDetails[ind].pointType == 'start' && linePoint.positionDetails[ind + 1].pointType == 'end')
                        || (linePoint.positionDetails[ind].pointType == 'inStart' && linePoint.positionDetails[ind + 1].pointType == 'inEnd')) {
                        // 必须提取到两个相邻节点为start和end的数据
                        start = linePoint.positionDetails[ind]
                        end = linePoint.positionDetails[ind + 1]
                        ind++
                        break
                    } else {
                        ind++
                    }
                }
                if (start == null || end == null) {
                    if (DEBUG_MSG_ENABLED) {
                        console.log(`线路${linePoint.lineId}已没有找到合适的start和end节点数据，当前线路绘制结束`)
                    }
                    break // 跳出循环 开始下一条回路
                }

                // 增加判断施工模式下的线路是否加载
                if (this.showDesign == false) {
                    let startPointTowerNumber = start.id.split('@')[0]
                    let endPointTowerNumber = end.id.split('@')[0]
                    if ((this.planRecordMap && startPointTowerNumber && this.planRecordMap[startPointTowerNumber] && this.planRecordMap[startPointTowerNumber].procedureStatus < Constants.PROGRESS_5) ||
                        (this.planRecordMap && endPointTowerNumber && this.planRecordMap[endPointTowerNumber] && this.planRecordMap[endPointTowerNumber].procedureStatus < Constants.PROGRESS_5)) {
                        if (DEBUG_MSG_ENABLED) {
                            console.log(`杆塔施工状态未完成 不加载该杆塔线路:${startPointTowerNumber} - ${endPointTowerNumber}`)
                        }
                        continue // 跳出循环 开始下一条回路
                    }
                }

                if (DEBUG_MSG_ENABLED) {
                    console.log(`开始加载线路${linePoint.lineId}节点：${start.id}~${end.id}的连线`, start, end)
                }

                // 加载start和end节点之间的连线
                for (let i = 0; i < Math.min(start.positions.length, end.positions.length); i++) {
                    let point1 = start.positions[i]
                    let point2 = end.positions[i]
                    if (point1.length != 3 || point2.length != 3) {
                        console.error(`线路${linePoint.lineId}节点：${start.id}~${end.id}的连线中有一点位置信息错误，请核查`, point1, point2)
                        continue
                    }
                    let color = Constants.PHASE_COLOR[start.phaseSequence]
                    let width = 1.5 // 线宽
                    if (start.lineType == 0) {
                        // 绘制悬链线
                        let sagHeight = Math.min(point1[2], point2[2]) - 1; // 设置弧垂最低点的高度
                        let polyline = overheadLine(this.lineCollection[linePoint.lineId], point1, point2, sagHeight, LINE_POINTS_NUM, color, width); // 绘制悬链线
                    } else {
                        // 绘制直线
                        let polyline = straight_line(this.lineCollection[linePoint.lineId], point1, point2, color, width); // 绘制直线
                    }
                }
            }
        })

        console.log('线路导线加载完成')
    }

    getLineAllocations(lineId: any) {
        // 获取指定线路的配串表，并按positionId排序
        let allocations = this.insulatorAllocation.filter(item => item.lineId == lineId)
        return allocations.sort((a, b) => a.positionId - b.positionId)
    }

    checkInsulatorLoaded(insulatorInfo: any) {
        // 检查绝缘子是否已经加载（简化实现）
        return undefined
    }

    enableEntitiesLod = (awaitToLoadInsulators: any[], distance: number) => {
        console.log('awaitToLoadInsulators', this.awaitToLoadInsulators);

        let postRenderCallback = () => {
            var cameraPosition = this.viewer.camera.positionWC // 获取摄像机位置
            this.lineDetail.forEach(towerInfo => {
                let towerPositionCartesian3 = Cesium.Cartesian3.fromDegrees(towerInfo.longitude, towerInfo.latitude, towerInfo.height)
                // 计算摄像机与模型之间的距离
                let distance1 = Cesium.Cartesian3.distance(cameraPosition, towerPositionCartesian3)
                let towerID = `tower@${towerInfo.towerNumber}`
                if (distance1 < distance) {
                    // 加载杆塔模型
                    if (!towerInfo.loaded) {
                        towerInfo.loaded = true
                        this.addOneTowerToViewer(towerInfo)
                    } else {
                        let towerEnty = this.viewer.entities.getById(towerID)
                        if (towerEnty) {
                            towerEnty.show = true
                        }
                    }
                } else if (towerInfo.loaded) {
                    // 移除杆塔模型
                    let towerEnty = this.viewer.entities.getById(towerID)
                    if (towerEnty) {
                        towerEnty.show = false
                    }
                }
            })

            this.awaitToLoadInsulators.forEach(insulatorInfo => {
                let insulatorPositionCartesian3 = Cesium.Cartesian3.fromDegrees(...insulatorInfo.mountPosition)
                // 计算摄像机与模型之间的距离
                let distance1 = Cesium.Cartesian3.distance(cameraPosition, insulatorPositionCartesian3)
                let insulatorID = `insulator@${insulatorInfo.mountTower.towerNumber}@${insulatorInfo.mountID}@${insulatorInfo.model.modelName}`
                if (distance1 < distance) {
                    // 加载模型
                    if (!insulatorInfo.loaded) {
                        insulatorInfo.loaded = true
                        this.addOneInsulatorToViewer(insulatorInfo)
                    } else {
                        let insulatorEnty = this.viewer.entities.getById(insulatorID)
                        if (insulatorEnty) {
                            insulatorEnty.show = true
                        }
                    }
                } else if (insulatorInfo.loaded) {
                    // 移除模型
                    let insulatorEnty = this.viewer.entities.getById(insulatorID)
                    if (insulatorEnty) {
                        insulatorEnty.show = false
                    }
                }
            })
        }

        this.viewer.scene.postRender.addEventListener(postRenderCallback)
    }

    addOneTowerToViewer = async (towerInfo: any) => {
        // 简化实现：创建杆塔标记点
        let towerID = `tower@${towerInfo.towerNumber}`
        if (this.viewer.entities.getById(towerID)) {
            if (DEBUG_MSG_ENABLED) {
                console.log(`杆塔${towerID}当前场景中已经加载`)
            }
            return []
        }

        let towerEntity = this.viewer.entities.add({
            id: towerID,
            name: `杆塔-${towerInfo.towerNumber}`,
            position: Cesium.Cartesian3.fromDegrees(towerInfo.longitude, towerInfo.latitude, towerInfo.height),
            point: {
                pixelSize: 15,
                color: '#FF0000',
                outlineColor: '#FFFFFF',
                outlineWidth: 2
            },
            label: {
                text: towerInfo.towerNumber,
                font: '14pt Arial',
                fillColor: '#FFFFFF',
                outlineColor: '#000000',
                outlineWidth: 2,
                pixelOffset: new Cesium.Cartesian2(0, -40),
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM
            }
        })

        this.towerCollection.push(towerEntity)
        console.log(`加载杆塔标记: ${towerInfo.towerNumber}`)
    }

    addOneInsulatorToViewer = async (insulatorInfo: InsulatorLoadType) => {
        // 简化实现：创建绝缘子标记点
        let insulatorID = `insulator@${insulatorInfo.mountTower.towerNumber}@${insulatorInfo.mountID}@${insulatorInfo.model.modelName}`

        let insulatorEntity = this.viewer.entities.add({
            id: insulatorID,
            name: `绝缘子-${insulatorInfo.model.modelName}`,
            position: Cesium.Cartesian3.fromDegrees(...insulatorInfo.mountPosition),
            point: {
                pixelSize: 8,
                color: '#00FF00',
                outlineColor: '#FFFFFF',
                outlineWidth: 1
            },
            label: {
                text: insulatorInfo.model.modelName,
                font: '10pt Arial',
                fillColor: '#FFFFFF',
                outlineColor: '#000000',
                outlineWidth: 1,
                pixelOffset: new Cesium.Cartesian2(0, -20),
                scale: 0.8
            }
        })

        this.insulatorCollection.push(insulatorEntity)
        console.log(`加载绝缘子标记: ${insulatorInfo.model.modelName}`)
    }
}
