import { Constants } from './constants.js'
import type { AllocationType, InsulatorLoadType, LineInfoType, TowerModelType, LineDetailType, TowerMountType, InsulatorModelType, LinePointTpye } from './userType.ts';
import {
    MODEL_STATIC_URL,
    LINE_POINTS_NUM,
} from '@/config/global'
import { createEntity_glb, createEntity_glb_v2, createLineCollection } from "@/js/common/viewer"
import { showModelByStatus, getMountPosition, calAzimuth, overheadLine, straight_line } from "@/js/common/line_tower"
import { DivLabel } from "@/js/common/divLabel"
import { Roaming } from "@/js/common/roaming"
import * as Cesium from 'cesium'
import { ConstructionModelManager } from '@/js/construction/ConstructionModelManager'

let DEBUG_MSG_ENABLED = true
let VirtualTowerModel = ['substationModel']

// 初始化全局CommandTest对象，用于处理来自UE的消息
if (typeof window !== 'undefined' && !window.CommandTest) {
    window.CommandTest = {
        HandleResponse(data: any) {
            try {
                // 详细的调试信息
                console.log('CommandTest.HandleResponse被调用，参数类型:', typeof data);
                
                // 安全地处理来自UE的消息
                let parsedData;
                if (typeof data === 'string') {
                    try {
                        // 尝试解析JSON字符串
                        parsedData = JSON.parse(data);
                    } catch (parseError) {
                        // 如果解析失败，保留原始字符串
                        console.warn('JSON解析失败，使用原始字符串:', parseError);
                        parsedData = data;
                    }
                } else {
                    // 如果不是字符串，直接使用原始数据
                    parsedData = data;
                }
                
                console.log('CommandTest.HandleResponse收到消息(已处理):', parsedData);
                
                // 如果有acceptMessage函数，可以转发给它
                if (typeof window.acceptMessage === 'function') {
                    try {
                        // 确保传递正确的数据格式
                        if (typeof parsedData === 'object' && parsedData !== null) {
                            window.acceptMessage(parsedData);
                        } else if (typeof parsedData === 'string') {
                            window.acceptMessage(parsedData);
                        } else {
                            window.acceptMessage(String(parsedData));
                        }
                    } catch (messageError) {
                        console.error('转发消息到acceptMessage失败:', messageError);
                    }
                }
            } catch (err) {
                console.error('CommandTest处理消息错误:', err);
                console.error('错误堆栈:', err instanceof Error ? err.stack : '无堆栈信息');
            }
        }
    };

    // 添加一个全局函数用于调试
    window.debugCommandTest = () => {
        console.log('当前 window.CommandTest 是:', window.CommandTest);
        if(window.CommandTest && window.CommandTest.HandleResponse) {
            console.log('HandleResponse 函数定义:', window.CommandTest.HandleResponse.toString());
        }
    };
}

export default class Map3DTool_v2 {
    version: string = 'V2.0'
    projectCode: string
    linesInfo: LineInfoType[]
    showDesign: boolean = true
    private projectId: string
    private projectMsg: any
    private towerModels: TowerModelType[] = []
    private lineDetail: LineDetailType[]
    private planRecordMap: any[]
    private viewer: any
    private towerCollection: any[] = [] // 场景中已加载的杆塔模型合集
    private insulatorCollection: any[] = [] // 场景中已加载的绝缘子模型合集
    private labelCollection: any[] = [] // 场景中已加载的label合集
    private lineCollection: any // 场景中已加载的电缆合集
    private towerMounts: TowerMountType[] = []
    private insulatorModels: InsulatorModelType[]
    private insulatorAllocation: any[]
    private linePoints: LinePointTpye[] = [] // 保存线路绘制的端点信息
    entitysLoadedId: {tower: string[], insulator: string[], label: string[]} = { tower: [], insulator: [], label: [] }
    private stopLoading: boolean
    private awaitToLoadInsulators: any[] = []

    // constructor(projectCode,towerModels,insulatorModels,towerProfiles,showDesign, viewer, flag, planRecordMap) {
    constructor(viewer: any, projectCode: string, projectData: any, planRecordMap: any[]) {
        console.log("Map3DTool_v2 constructor, projectCode=", projectCode, " projectData=", projectData, " planRecordMap=", planRecordMap)
        this.projectMsg = projectData.projectMsg
        this.projectCode = projectCode
        this.projectId = projectData.projectMsg ? projectData.projectMsg.projectId : projectCode // projectID为0 则表示不对数据进行过滤处理
        // 从传递的参数中 提取匹配本工程ID的数据进行过滤后保存 避免其他工程数据的污染
        this.linesInfo = projectData.linesInfo.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.towerModels = projectData.towerModels.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.lineDetail = projectData.lineDetail.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.towerMounts = projectData.towerMounts.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.insulatorModels = projectData.insulatorModels.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.insulatorAllocation = projectData.insulatorAllocation.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))

        this.planRecordMap = planRecordMap
        this.viewer = viewer
        for (let i = 0; i < this.towerModels.length; i++) {
            let towerModel = this.towerModels[i]
            let mounts = this.towerMounts.filter(item => item.towerModel == towerModel.towerModel)
            mounts.forEach(el => {
                if (el.fixBaseHeight != true) {
                    // 修复数据中杆塔挂点没有加入杆塔呼高的偏差
                    el.mountPosition[2] += towerModel.baseHeight
                    // 修正x和y轴相反的问题
                    let temp = el.mountPosition[0];
                    el.mountPosition[0] = -1 * el.mountPosition[1];
                    el.mountPosition[1] = -1 * temp;
                    el.fixBaseHeight = true
                }
            });
        }
        if (DEBUG_MSG_ENABLED) {
            console.log('杆塔挂点', this.towerMounts, this.lineDetail)
        }
        // this.lineCollection = createLineCollection(this.viewer)
        this.lineCollection = {}
        this.linesInfo.forEach(line => {
            if (line.id) {
                this.lineCollection[line.id] = createLineCollection(this.viewer)
            }
        })
        this.stopLoading = false
    }
    updateProjectData = (projectData: any, planRecordMap: any[]) => {
        this.projectMsg = projectData.projectMsg
        //this.projectCode = projectCode
        //this.projectId = projectData.projectMsg ? projectData.projectMsg.//projectId : projectCode // projectID为0 则表示不对数据进行过滤处理
        // 从传递的参数中 提取匹配本工程ID的数据进行过滤后保存 避免其他工程数据的污染
        this.linesInfo = projectData.linesInfo.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId));
        //this.towerModels = projectData.towerModels.filter(item => (this.projectId == '0' || item.projectId == this.projectId))
        this.lineDetail = projectData.lineDetail.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.towerMounts = projectData.towerMounts.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.insulatorModels = projectData.insulatorModels.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))
        this.insulatorAllocation = projectData.insulatorAllocation.filter((item: any) => (this.projectId == '0' || item.projectId == this.projectId))

        this.planRecordMap = planRecordMap
    }
    getAllLineTowerDetail = () => {
        return this.lineDetail
    }
    getLineCollection = () => {
        return this.lineCollection
    }
    getModelCollection = () => {
        // 返回杆塔模型和绝缘子模型 insulatorCollection
        let modelCollection = this.towerCollection.concat(this.insulatorCollection)
        return modelCollection
    }
    getAllLineInfo = () => {
        let linesInfo = []
        for (let i = 0; i < this.linesInfo.length; i++) {
            let lineId = this.linesInfo[i].id
            let insulatorAllocation = this.getLineAllocations(lineId)
            linesInfo.push({
                lineId: lineId,
                insulatorAllocation: insulatorAllocation
            })
        }
        return linesInfo
    }
    removeAllModels = () => {
        if (DEBUG_MSG_ENABLED) {
            console.log('移除所有模型,FFFFFFFFF')
        }
        this.stopLoading = true
        // 移除场景中全部的模型对象和lebael对象
        // this.viewer.entities.removeAll(); // 移除所有实体对象

        // this.viewer.scene.primitives.removeAll(); // 移除所有图元对象
        if (this.lineCollection != undefined) {
            // this.lineCollection.removeAll()
            for (let key in this.lineCollection) {
                this.lineCollection[key].removeAll()
            }
        }
        this.lineCollection = null
        for (let i = 0; i < this.towerCollection.length; i++) {
            // this.viewer.entities.remove(this.towerCollection[i])
            this.viewer.scene.primitives.remove(this.towerCollection[i])
        }
        for (let i = 0; i < this.insulatorCollection.length; i++) {
            // this.viewer.entities.remove(this.insulatorCollection[i])
            this.viewer.scene.primitives.remove(this.insulatorCollection[i])
        }
        for (let i = 0; i < this.labelCollection.length; i++) {
            this.viewer.entities.remove(this.labelCollection[i])
        }
        this.towerCollection = []
        this.insulatorCollection = []
        this.labelCollection = []

        // 清除杆塔加载状态
        this.lineDetail.forEach(towerInfo => {
          towerInfo.loaded = false
        })
    }
    // 设置当前模式是设计模式还是施工模式
    setDesign = (showDesign: boolean) => {
        this.showDesign = showDesign
    }
    loadTowers = async () => {
        // 先清除场景中已加载的杆塔模型 和 label
        if (this.towerCollection.length > 0) {
            this.towerCollection.forEach(entity => {
                // this.viewer.entities.remove(entity)
                this.viewer.scene.primitives.remove(entity)
            })
            this.labelCollection.forEach(entity => {
                // this.viewer.entities.remove(entity)
                this.viewer.scene.primitives.remove(entity)
            })
            this.towerCollection = []
            this.labelCollection = []
        }
        // this.linesInfo.forEach(line => {
        //     let lineDetail = this.lineDetail.filter(item => item.lineId == line.id)
        //     // let sortedLineDetail = lineDetail.sort((a, b) => a.positionId - b.positionId) // 按positionId排序
        //     console.log(`加载线路(${line.name})的全部杆塔，工程ID=${line.projectId}，线路ID=${line.id}，杆塔数量=${lineDetail.length}`)
        //     for (let i = 0; i < lineDetail.length; i++) {
        //         let towerInfo = lineDetail[i]
        //         this.addOneTowerToViewer(towerInfo, line.projectLine_flag)
        //     }
        // });

        // 加载lineDetail中全部的杆塔
        for (let i = 0; i < this.lineDetail.length; i++) {
            let towerInfo = this.lineDetail[i]
            let entities = this.addOneTowerToViewer(towerInfo)
            if (entities.length > 0) {
                // this.entitysLoadedId.tower.push(entities[0])
                this.entitysLoadedId.tower.push(entities[1])
            }
        }
        if (DEBUG_MSG_ENABLED) {
            console.log('全部杆塔加载完成', this.towerCollection, this.entitysLoadedId.tower)
        }
        return this.towerCollection
    }
    getLineAllocations = (lineId: string) => {
        // 根据线路ID 提取到正确顺序的配串表
        let insulatorAllocation = this.insulatorAllocation.filter(item => item.lineId == lineId) // 从所有配串纤细中 提取对应lineID的线路数据
        let sortedInsulatorAllocation = insulatorAllocation.sort((a, b) => a.positionId - b.positionId) // 将线路按positionId排序 确保顺序正确
        return sortedInsulatorAllocation
    }
    // 获取施工索道线路信息
    getConstructionLineInfo = async (cablewayData: any, mountsData: any) => {
    const constructionManager = new ConstructionModelManager(this.viewer);
    const groups: { [segment: number]: any[] } = {};
    const mountGroups: { [segment: number]: any[] } = {};

    // 处理每个索道位置并进行分组
    for (let i = 1; i <= 46; i++) {
        const key = `cableway${i}`;
        if (cablewayData[key] && cablewayData[key].position) {
            const segment = cablewayData[key].segment;
            const [lon, lat] = cablewayData[key].position;
            const angle = cablewayData[key].rotation[0];
            
            try {
                // 获取地形高度
                const height = await constructionManager.getTerrainHeightAsync(lon, lat);
                const position = [lon, lat, height, angle];
                
                // 初始化分组
                if (!groups[segment]) groups[segment] = [];
                if (!mountGroups[segment]) mountGroups[segment] = [];
                
                // 添加到分组
                groups[segment].push(position);
                
                // 处理每个位置的挂点
                const mountResults = [];
                for (const mount of mountsData) {
                    const mountPos = getMountPosition(
                        { longitude: lon, latitude: lat, height, angle, version: 'V2' },
                        [[mount.mountPosition]],
                        this.viewer
                    );
                    if (mountPos && mountPos[0] && mountPos[0][0]) {
                        mountResults.push({
                            mountName: mount.mountName,
                            position: mountPos[0][0]
                        });
                    }
                }
                mountGroups[segment].push(mountResults);
                
            } catch (error) {
                console.error(`获取地形高度失败: ${key}`, error);
            }
        }
    }

    return {
        groups, // 按segment分组的位置
        mountGroups // 按segment分组的挂点
    };
}

    getInsulatorInfo = (allocationInfo: AllocationType): InsulatorLoadType | null => {
        // 根据配串表的每一条信息 获取到对应绝缘子和杆塔的相关数据并封装
        if (DEBUG_MSG_ENABLED) {
            console.log('根据配串表中的数据获取绝缘子相关信息', allocationInfo)
        }
        // 获取绝缘子模型信息
        let model = this.insulatorModels.find(item => item.modelName == allocationInfo.insulator && item.projectId == allocationInfo.projectId)
        if (model == undefined) {
            console.error(`配串信息未在绝缘子模型列表中存在，线路${allocationInfo.lineId}中序号id=${allocationInfo.positionId}杆塔${allocationInfo.towerNumber}未找到绝缘子模型${allocationInfo.insulator}`, allocationInfo)
            return null
        }
        // 获取挂载杆塔的信息
        let towerInfo = this.lineDetail.find(item => item.towerNumber == allocationInfo.towerNumber)
        if (towerInfo == undefined) {
            console.error(`配串信息未在线路详情表中存在，线路${allocationInfo.lineId}中序号id=${allocationInfo.positionId}杆塔${allocationInfo.towerNumber}未找到杆塔信息${allocationInfo.towerNumber}`, allocationInfo)
            return null
        }
        // 由杆塔模型的挂点数据中 获取挂载点信息
        let mount = this.towerMounts.find(item => item.towerModel == towerInfo.modelNumber && item.projectId == allocationInfo.projectId && item.locationId == allocationInfo.locationId)
        if (mount == undefined) {
            console.error(`配串信息未在杆塔挂点表中存在，线路${allocationInfo.lineId}中序号id=${allocationInfo.positionId}杆塔${allocationInfo.towerNumber}未找到挂载点信息${towerInfo.modelNumber}的${allocationInfo.locationId}号挂点`, allocationInfo)
            return null
        }
        // 计算得到挂点的绝对位置信息 经纬度高度
        let mountPosition
        if (towerInfo.modelNumber == 'substationModel') {
            // 如果是变电站模型 则直接返回挂点坐标信息
            mountPosition = mount.mountPosition
            console.log('substationMount:', mount)
        } else {
            mountPosition = getMountPosition(towerInfo, [[mount.mountPosition]], this.viewer)[0][0]
        }
        if (mountPosition == undefined) {
            console.error(`配串信息杆塔挂点计算位置失败，杆塔${allocationInfo.towerNumber}挂点${mount.mountName}的坐标错误，请核查！配串和挂点信息：`, towerInfo, allocationInfo, mount)
            return null
        }
        return {
            lineId: allocationInfo.lineId, // 线路ID
            positionId: allocationInfo.positionId, // 配串顺序
            mountName: mount.mountName, // 挂点名称
            mountID: mount.locationId, // 挂点ID
            model: model,   // 绝缘子模型信息
            mountTower: towerInfo, // 绝缘子挂载的杆塔信息
            mountPosition: mountPosition, // 挂点绝对位置信息
        }

    }
    addOneInsulatorToViewer = async (insulatorInfo: InsulatorLoadType) => {
        let entity = this.checkInsulatorLoaded(insulatorInfo)
        if (entity != undefined) {
            if (DEBUG_MSG_ENABLED) {
                console.log(`线路${insulatorInfo.lineId}的杆塔${insulatorInfo.mountTower.towerNumber}${insulatorInfo.mountName}绝缘子已经加载过了，绝缘子信息：`, insulatorInfo)
            }
            return null
        }
        // 如果是施工模式 则需要判断杆塔进度状态
        if (this.linesInfo == undefined) {
            console.error('线路基本信息表为空 lineInfos:', this.linesInfo)
            return null
        }
        // let projectLine_flag = this.linesInfo.find(item => item.lineId == insulatorInfo.lineId).projectLine_flag // 获取绝缘子挂载的线路是否为工程线路 工程线路需要考虑杆塔的进度情况
        // if (this.showDesign == false && projectLine_flag == true) {
        if (this.showDesign == false) {
            // 施工模式下 并且 该线路是工程施工线路 进一步判断杆塔进度状态
            let procedureStatus = insulatorInfo.mountTower.procedureStatus || Constants.PROGRESS_5
            if (this.planRecordMap.hasOwnProperty(insulatorInfo.mountTower.towerNumber)) {
                procedureStatus = this.planRecordMap[insulatorInfo.mountTower.towerNumber].procedureStatus
            }
            if (procedureStatus != Constants.PROGRESS_5) {
                // 杆塔不是完成状态 则不加载绝缘子模型 直接返回
                if (DEBUG_MSG_ENABLED) {
                    console.log(`线路${insulatorInfo.lineId}的杆塔${insulatorInfo.mountTower.towerNumber}当前是未完成状态 不加载绝缘子`, insulatorInfo)
                }
                return null
            }
        }

        let modelUrl = `${MODEL_STATIC_URL}/${this.projectCode}${insulatorInfo.model.url}`
        
        let modelId = `insulator@${insulatorInfo.mountTower.towerNumber}@${insulatorInfo.mountID}@${insulatorInfo.model.modelName}`
        let modelInfo1 = {
            id: modelId,
            name: `${insulatorInfo.model.modelName}`,
            position: insulatorInfo.mountPosition,
            angle: insulatorInfo.angRaid || 0,
            scale: 1,
            url: modelUrl,
            properties: { angle: insulatorInfo.angRaid || 0 } // 记录用户自定义信息 用于后续计算
        }
        console.log('addOneInsulatorToViewer', modelInfo1)
        // 异步创建实体
        createEntity_glb_v2(modelInfo1)
            .then((model: any) => {
                this.viewer.scene.primitives.add(model);

                this.insulatorCollection.push(model);
                if (model && model.id) {
                    this.entitysLoadedId.insulator.push(model.id); // 假设 modelId 是 entity 的一个属性
                }
            })
            .catch((error) => {
                console.error('创建实体失败', error);
            });

        return modelId
    }
    checkInsulatorLoaded = (insulatorInfo: InsulatorLoadType) => {
        // let id = `insulator@${insulatorInfo.mountTower.towerNumber}@${insulatorInfo.mountName}@${insulatorInfo.mountID}@${insulatorInfo.model.modelName}`
        let id = `insulator@${insulatorInfo.mountTower.towerNumber}@${insulatorInfo.mountID}@${insulatorInfo.model.modelName}`
        return this.viewer.entities.getById(id)
    }
    /**
     * 根据绝缘子挂载信息 提取到绘制线路的起点或终点位置信息
     * @param insulatorInfo 绝缘子挂载信息
     * @param start_end_flag 是否为起点或终点位置 true为起点位置 false为终点位置
     */
    addLinePoint = (insulatorInfo: InsulatorLoadType, insulatorAllocation: AllocationType, phaseSequence: string = 'N') => {
        let linePointMsg = this.linePoints.find(linePoint => linePoint.lineId == insulatorInfo.lineId)
        if (linePointMsg == undefined) {
            if (DEBUG_MSG_ENABLED) {
                console.log(`线路${insulatorInfo.lineId}的线路节点信息不存在，新创建保存对象`, insulatorInfo, this.linePoints)
            }
            linePointMsg = { lineId: insulatorInfo.lineId || 0, positionDetails: [] } // 创建一个对象 保存线路绘制的节点信息
            this.linePoints.push(linePointMsg)
        }
        // 根据绝缘子 计算绝缘子的全部挂点
        let mountArray = []
        if (insulatorInfo.model.splitNum == 4) {
            mountArray.push([insulatorInfo.model.inputLinePoint1, insulatorInfo.model.inputLinePoint2, insulatorInfo.model.inputLinePoint3, insulatorInfo.model.inputLinePoint4])
            mountArray.push([insulatorInfo.model.outputLinePoint1, insulatorInfo.model.outputLinePoint2, insulatorInfo.model.outputLinePoint3, insulatorInfo.model.outputLinePoint4])
        } else if (insulatorInfo.model.splitNum == 2) {
            mountArray.push([insulatorInfo.model.inputLinePoint1, insulatorInfo.model.inputLinePoint2])
            mountArray.push([insulatorInfo.model.outputLinePoint1, insulatorInfo.model.outputLinePoint2])
        } else if (insulatorInfo.model.splitNum == 1) {
            mountArray.push([insulatorInfo.model.inputLinePoint1])
            mountArray.push([insulatorInfo.model.outputLinePoint1])
        } else {
            if (DEBUG_MSG_ENABLED) {
                console.log(`${insulatorInfo.mountTower.towerNumber}杆塔的绝缘子${insulatorInfo.model.modelName}的分割数量错误，分割数量应为2或4`)
            }
            return
        }
        const originModel = {
            version: 'V2',
            longitude: insulatorInfo.mountPosition[0],
            latitude: insulatorInfo.mountPosition[1],
            height: insulatorInfo.mountPosition[2],
            angle: insulatorInfo.angRaid ? insulatorInfo.angRaid * 180 / Math.PI + 90 : 90,
        }
        let positions = getMountPosition(originModel, mountArray, this.viewer)
        if (positions == undefined) {
            console.error(`${insulatorInfo.lineId}线路${insulatorInfo.mountTower.towerNumber}杆塔的${insulatorAllocation.locationId}#绝缘子${insulatorInfo.model.modelName}的挂点获取失败。绝缘子信息：`, insulatorInfo, mountArray)
            return
        }
        let swapArrayElements = (arr: any[], index1: number, index2: number) => {
            if (!arr[index1] && !arr[index2]) {
                return
            }
            const temp = arr[index1];
            arr[index1] = arr[index2];
            arr[index2] = temp;
        }
        if (insulatorAllocation.inOutFlag == 1) {
            // inOutFlag==1 时 表示进线端和出线端需要取反 位置交叉
            if (insulatorInfo.model.splitNum == 4) {
                swapArrayElements(positions[0], 0, 1)
                swapArrayElements(positions[0], 2, 3)
                swapArrayElements(positions[1], 0, 1)
                swapArrayElements(positions[1], 2, 3)
            } else if (insulatorInfo.model.splitNum == 2) {
                swapArrayElements(positions[0], 0, 1)
                swapArrayElements(positions[1], 0, 1)
            }
            // 先推入一个end节点
            linePointMsg.positionDetails.push({
                id: `${insulatorAllocation.towerNumber}@${insulatorInfo.mountName}@${insulatorAllocation.insulator}`,
                pointType: 'end',
                lineType: insulatorAllocation.lineTpye || 0,
                positions: positions[1],
                phaseSequence: phaseSequence
            })
            // 如果绝缘子需要内部连线
            if (insulatorAllocation.internalLine == 1) {
                linePointMsg.positionDetails.push({
                    id: `${insulatorAllocation.towerNumber}@${insulatorInfo.mountName}@${insulatorAllocation.insulator}`,
                    pointType: 'inStart',
                    lineType: 1,
                    positions: positions[1],
                    phaseSequence: phaseSequence
                })
                linePointMsg.positionDetails.push({
                    id: `${insulatorAllocation.towerNumber}@${insulatorInfo.mountName}@${insulatorAllocation.insulator}`,
                    pointType: 'inEnd',
                    lineType: insulatorAllocation.lineTpye || 0,
                    positions: positions[0],
                    phaseSequence: phaseSequence
                })
            }
            // 再推入一个start节点
            linePointMsg.positionDetails.push({
                id: `${insulatorAllocation.towerNumber}@${insulatorInfo.mountName}@${insulatorAllocation.insulator}`,
                pointType: 'start',
                lineType: insulatorAllocation.lineTpye || 0,
                positions: positions[0],
                phaseSequence: phaseSequence
            })
        } else {
            // 位置不需要取反 正常推入连接点信息
            // 先推入一个end节点
            linePointMsg.positionDetails.push({
                id: `${insulatorAllocation.towerNumber}@${insulatorInfo.mountName}@${insulatorAllocation.insulator}`,
                pointType: 'end',
                lineType: insulatorAllocation.lineTpye || 0,
                positions: positions[0],
                phaseSequence: phaseSequence
            })
            // 如果绝缘子需要内部连线
            if (insulatorAllocation.internalLine == 1) {
                linePointMsg.positionDetails.push({
                    id: `${insulatorAllocation.towerNumber}@${insulatorInfo.mountName}@${insulatorAllocation.insulator}`,
                    pointType: 'inStart',
                    lineType: 1,
                    positions: positions[0],
                    phaseSequence: phaseSequence
                })
                linePointMsg.positionDetails.push({
                    id: `${insulatorAllocation.towerNumber}@${insulatorInfo.mountName}@${insulatorAllocation.insulator}`,
                    pointType: 'inEnd',
                    lineType: insulatorAllocation.lineTpye || 0,
                    positions: positions[1],
                    phaseSequence: phaseSequence
                })
            }
            // 再推入一个start节点
            linePointMsg.positionDetails.push({
                id: `${insulatorAllocation.towerNumber}@${insulatorInfo.mountName}@${insulatorAllocation.insulator}`,
                pointType: 'start',
                lineType: insulatorAllocation.lineTpye || 0,
                positions: positions[1],
                phaseSequence: phaseSequence
            })
        }
    }
    loadInsulators = async () => {
        // 先清除场景中已加载的绝缘子模型
        if (this.insulatorCollection.length > 0) {
            this.insulatorCollection.forEach(entity => {
                this.viewer.entities.remove(entity)
            })
            this.insulatorCollection = []
        }
        this.linePoints = [] // 加载绝缘子前 清空场景原有的线路节点位置信息 更新线路
        let awaitToLoadInsulators = []
        for (let line of this.linesInfo) {
            // this.linesInfo.forEach(async line => {
            let sortedInsulatorAllocation = []
            if (line.id) {
                sortedInsulatorAllocation = this.getLineAllocations(line.id) // 根据线路ID 提取到正确顺序的配串表
            }
            if (sortedInsulatorAllocation.length == 0) {
                console.error(`线路(${line.id || 'unknown'})没有配串表insulatorAllocation的数据，无法加载`)
                return
            } else {
                if (DEBUG_MSG_ENABLED) {
                    console.log(`开始加载线路(${line.id})的全部绝缘子模型，工程ID=${line.projectId}，线路ID=${line.id}，配串表信息：`, sortedInsulatorAllocation)
                }
            }
            for (let ind = 0; ind < sortedInsulatorAllocation.length; ind++) {
                let insulatorAllocation1 = sortedInsulatorAllocation[ind]
                let insulatorInfo1 = this.getInsulatorInfo(insulatorAllocation1) // 获取到该配串的绝缘子模型 挂载杆塔 和 挂载位置
                if (insulatorInfo1 == null) {
                    console.error(`线路${line.id}中序号id=${ind + 1}的配串信息解析错误，无法加载绝缘子和对应线路。配串信息：`, insulatorAllocation1)
                    continue
                } else {
                    if (DEBUG_MSG_ENABLED) {
                        console.log(`线路${insulatorInfo1.lineId}中序号id=${insulatorInfo1.positionId}解析得到的配串数据为：`, insulatorInfo1)
                    }
                }
                // 查询场景中是否已经加载该绝缘子
                let entity = this.checkInsulatorLoaded(insulatorInfo1)
                if (entity != undefined) {
                    if (DEBUG_MSG_ENABLED) {
                        console.log(`线路(${line.id})中配串表序号id=${ind + 1}的配串信息已经加载，无需再次加载。`, insulatorInfo1)
                    }
                    // 获取到角度信息后 加载当前线路节点
                    insulatorInfo1.angRaid = entity.properties.angle.getValue()
                    this.addLinePoint(insulatorInfo1, insulatorAllocation1, line.phaseSequence) // 加载当前线路节点
                    continue
                }
                if (insulatorInfo1.model.modelType == '耐张串' || insulatorInfo1.model.modelType == '地线串') {
                    // 如果是耐张串/地线串 需要根据上一个杆塔或下一个杆塔的挂载点确定角度 悬垂串则对应杆塔自身角度
                    if (ind != sortedInsulatorAllocation.length - 1 && sortedInsulatorAllocation[ind + 1].towerNumber != insulatorInfo1.mountTower.towerNumber) {
                        // 与下一个绝缘子不同杆塔 
                        let insulatorAllocation2 = sortedInsulatorAllocation[++ind]
                        let insulatorInfo2 = this.getInsulatorInfo(insulatorAllocation2) // 获取到该配串的绝缘子模型 挂载杆塔 和 挂载位置
                        if (insulatorInfo2 == null) {
                            console.error(`线路(${line.id}中序号id=${ind + 1}的配串信息解析错误，无法加载绝缘子和对应线路。配串信息：`, insulatorAllocation2)
                            continue
                        } else {
                            if (DEBUG_MSG_ENABLED) {
                                console.log(`线路${insulatorInfo2.lineId}中序号id=${insulatorInfo2.positionId}解析得到的配串数据为：`, insulatorInfo2)
                            }
                        }
                        // 计算绝缘子角度
                        let angRaid = calAzimuth(insulatorInfo2.mountPosition, insulatorInfo1.mountPosition)
                        insulatorInfo1.angRaid = angRaid - Math.PI / 2 // 绝缘子模型默认位置沿y轴放置 加载时候需要将角度转90度
                        // let entity1 = this.addOneInsulatorToViewer(insulatorInfo1)// 加载当前绝缘子
                        // this.entitysLoadedId.insulator.push(entity1)
                        awaitToLoadInsulators.push(insulatorInfo1) // 先存储待加载的绝缘子信息 后续异步加载到场景
                        this.addLinePoint(insulatorInfo1, insulatorAllocation1, line.phaseSequence) // 加载当前线路节点
                        insulatorInfo2.angRaid = (insulatorInfo2.model.modelType != '悬垂串') ? angRaid + Math.PI / 2 : insulatorInfo2.mountTower.angle * Math.PI / 180// 计算下一个绝缘子角度
                        // let entity2 = this.addOneInsulatorToViewer(insulatorInfo2)// 加载下一绝缘子
                        // this.entitysLoadedId.insulator.push(entity2)
                        awaitToLoadInsulators.push(insulatorInfo2) // 先存储待加载的绝缘子信息 后续异步加载到场景
                        this.addLinePoint(insulatorInfo2, insulatorAllocation2, line.phaseSequence) // 加载下一线路节点
                    } else if (ind != 0 && sortedInsulatorAllocation[ind - 1].towerNumber != insulatorInfo1.mountTower.towerNumber) {
                        // 与上一个绝缘子不同杆塔
                        let insulatorAllocation2 = sortedInsulatorAllocation[ind - 1]
                        let insulatorInfo2 = this.getInsulatorInfo(insulatorAllocation2) // 获取到上一位置该配串的绝缘子模型 挂载杆塔 和 挂载位置
                        if (insulatorInfo2 == null) {
                            console.error(`线路(${line.name}中需要${ind - 1}的配串信息解析错误，无法加载绝缘子和对应线路。配串信息`, insulatorAllocation2)
                            continue
                        } else {
                            if (DEBUG_MSG_ENABLED) {
                                console.log(`线路${insulatorInfo2.lineId}中序号${insulatorInfo2.positionId}解析得到的配串数据为：`, insulatorInfo2)
                            }
                        }
                        // 计算绝缘子角度
                        let angRaid = calAzimuth(insulatorInfo2.mountPosition, insulatorInfo1.mountPosition)
                        insulatorInfo1.angRaid = angRaid - Math.PI / 2 // 绝缘子模型默认位置沿y轴放置 加载时候需要将角度转90度
                        // let entity1 = this.addOneInsulatorToViewer(insulatorInfo1)// 加载当前绝缘子
                        // this.entitysLoadedId.insulator.push(entity1)
                        awaitToLoadInsulators.push(insulatorInfo1) // 先存储待加载的绝缘子信息 后续异步加载到场景
                        this.addLinePoint(insulatorInfo1, insulatorAllocation1, line.phaseSequence) // 加载当前线路节点
                    } else {
                        console.error(`线路${line.id}中 序号为${insulatorInfo1.positionId}绝缘子挂载位置或型号可能错误，该位置无法确定其角度，请核查配串表(绝缘子信息，配串信息)`, insulatorInfo1, sortedInsulatorAllocation[ind])
                    }
                } else {
                    // 悬垂串的角度与所在杆塔角度相关
                    insulatorInfo1.angRaid = insulatorInfo1.mountTower.angle * Math.PI / 180
                    // let entity1 = this.addOneInsulatorToViewer(insulatorInfo1)// 加载当前绝缘子
                    // this.entitysLoadedId.insulator.push(entity1)
                    awaitToLoadInsulators.push(insulatorInfo1) // 先存储待加载的绝缘子信息 后续异步加载到场景
                    this.addLinePoint(insulatorInfo1, insulatorAllocation1, line.phaseSequence) // 加载当前线路节点
                }
            }
            // })
        }
        if (DEBUG_MSG_ENABLED) {
            console.log(`工程所有线路绝缘子和连线节点加载完成，共${this.linePoints.length}条线路，线路节点信息=`, this.linePoints)
        }
        return awaitToLoadInsulators
    }
    addOneTowerToViewer = async (towerInfo: any) => {
        let towerID = `tower@${towerInfo.towerNumber}`
        if (this.viewer.entities.getById(towerID)) {
            if (DEBUG_MSG_ENABLED) {
                console.log(`杆塔${towerID}当前场景中已经加载`)
            }
            return []
        }
        // let towerModel = this.towerModels.find(item => item.towerModel == towerInfo.towerModel)
        let towerModel = this.towerModels.find(item => item.towerModel == towerInfo.modelNumber)
        if (towerModel == undefined) {
            console.error(`杆塔模型列表中未找到${towerInfo.modelNumber}的详情，请核查杆塔模型：`, towerInfo)
            return []
        } else {
            if (DEBUG_MSG_ENABLED) {
                console.log(`加载杆塔模型${towerInfo.modelNumber} 参数详情：`, towerModel)
            }
            if (towerModel.towerModel == 'substationModel') {
                // 如果是变电站模型（虚拟杆塔） 则不加载 直接返回
                return []
            }
        }
        let procedureStatus = towerInfo.procedureStatus || Constants.PROGRESS_5
        if (this.planRecordMap.hasOwnProperty(towerInfo.towerNumber)) {
            procedureStatus = this.planRecordMap[towerInfo.towerNumber].procedureStatus
            towerInfo.procedureStatus = procedureStatus // 更新杆塔状态
        }
        let gltfShow1
        if (this.showDesign == false && (procedureStatus === Constants.PROGRESS_0 || procedureStatus === Constants.PROGRESS_1)) {
            gltfShow1 = showModelByStatus(procedureStatus, [towerInfo.longitude, towerInfo.latitude, towerInfo.height], [procedureStatus], this.viewer)
            let labelID = this.addTowerLabelToViewer(towerInfo)
            return [towerID, labelID]
        }
        let modelUrl
        if (this.showDesign == false && (procedureStatus === Constants.PROGRESS_2 || procedureStatus === Constants.PROGRESS_3)) {
            // 根据施工进度 加载不同的模型文件
            modelUrl = `${MODEL_STATIC_URL}/${this.projectCode}/gltf/procedure/` + towerInfo.modelNumber + '/' + towerInfo.modelNumber + '_' + (procedureStatus - 1) + '.glb'
        } else {
            modelUrl = `${MODEL_STATIC_URL}/${this.projectCode}` + towerModel.url
        }

        let modelInfo = {
            id: towerID,
            name: towerInfo.towerNumber,
            position: [towerInfo.longitude, towerInfo.latitude, towerInfo.height],
            // angle: 180*Math.PI/180,
            angle: towerInfo.angle * Math.PI / 180,
            scale: 1,
            url: modelUrl,
            procedureStatus: procedureStatus
        }
        // 异步创建实体
        createEntity_glb_v2(modelInfo)
            .then((model: any) => {
            this.viewer.scene.primitives.add(model);
            
            this.towerCollection.push(model);
            if (model && model.id) {
                this.entitysLoadedId.tower.push(model.id); // 假设 modelId 是 entity 的一个属性
            }
        })
        .catch((error) => {
            console.error('创建实体失败', error);
        });

        let labelID = this.addTowerLabelToViewer(towerInfo)
        // this.labelCollection.push(label)
        return [towerID, labelID]
    }
    addTowerLabelToViewer = (towerInfo: any) => {
        let label_id = `label@${towerInfo.towerNumber}`
        let label_text = `${towerInfo.towerNumber}`
        let towerModel = this.towerModels.find(item => item.towerModel == towerInfo.modelNumber)
        if (towerModel == undefined) {
            console.error(`杆塔模型列表中未找到${towerInfo.towerModel}的详情，请核查杆塔模型：`, towerInfo)
            return
        }
        let height = towerInfo.height + towerModel.towerHeight + 3
        if (this.showDesign == false && (towerInfo.procedureStatus != undefined)) {
            // 施工模式下 杆塔为非完成状态 则需要在塔号增加当前状态信息
            let procedureStatusName = this.planRecordMap[towerInfo.towerNumber]?.procedureStatusName
            if (towerInfo.procedureStatus == Constants.PROGRESS_1) {
              procedureStatusName = "征地青赔"
            }
            if (procedureStatusName == undefined) {
                if (DEBUG_MSG_ENABLED) {
                    console.log(`杆塔${towerInfo.towerNumber}没有进度信息或者进度名称`)
                }
                procedureStatusName = ''
            }
            label_text = `${label_text}(${procedureStatusName})`
            if (towerInfo.procedureStatus === Constants.PROGRESS_0
                || towerInfo.procedureStatus === Constants.PROGRESS_1
                || towerInfo.procedureStatus === Constants.PROGRESS_2
                || towerInfo.procedureStatus === Constants.PROGRESS_3) {
                // 在杆塔这几种施工状态下 需要将标签高度下降处理
                height = towerInfo.height + 50 + 3
            }

        }
        let val = {
            id: label_id,
            text: label_text,
            viewer: this.viewer,
            position: [towerInfo.longitude, towerInfo.latitude, height],
            img: (towerInfo.vrUrl !== undefined && towerInfo.vrUrl !== null) ? import.meta.env.VITE_CONTEXT + 'static/img/vr.png' : undefined,
            url: (towerInfo.vrUrl !== undefined && towerInfo.vrUrl !== null) ? import.meta.env.VITE_CONTEXT + `vr?tower=${towerInfo.towerNumber}&projectCode=${this.projectCode}` : undefined,
            scale: 0.07
        }
        let label = new DivLabel()
        // label.addDynamicLabel(this.viewer, val, isxf, num)
        let label_entity = label.addDynamicLabel_cesium(this.viewer, val) // 调用新的标签加载函数 by lkj
        this.labelCollection.push(label_entity)
        return label_id
    }
    loadLines = () => {
        if (this.linePoints.length == 0) {
            console.error('线路节点信息为空，无法加载线路')
            return false
        } else {
            if (DEBUG_MSG_ENABLED) {
                console.log(`开始加载线路，线路数量共${this.linePoints.length}，线路数据：`, this.linePoints)
            }
        }
        if (this.lineCollection == undefined) {
            if (DEBUG_MSG_ENABLED) {
                console.log(`创建线路合集 lineCollection`)
            }
            // this.lineCollection = createLineCollection(this.viewer)
            this.lineCollection = {}
            this.linesInfo.forEach(line => {
                this.lineCollection[line.id] = createLineCollection(this.viewer)
            })
        } else {
            // 清除线路合集中的全部对象
            if (DEBUG_MSG_ENABLED) {
                console.log(`清除场景中线路合集中的全部对象，重新加载线路`)
            }
            // this.lineCollection.removeAll()
            for (let key in this.lineCollection) {
                this.lineCollection[key].removeAll()
            }
        }
        this.linePoints.forEach(linePoint => {
            for (let ind = 0; ind < linePoint.positionDetails.length; ind++) {
                // 提取 start和end 两个节点的信息
                let start = null
                let end = null
                while (ind < linePoint.positionDetails.length - 1) {
                    if ((linePoint.positionDetails[ind].pointType == 'start' && linePoint.positionDetails[ind + 1].pointType == 'end')
                        || (linePoint.positionDetails[ind].pointType == 'inStart' && linePoint.positionDetails[ind + 1].pointType == 'inEnd')) {
                        // 必须提取到两个相邻节点为start和end的数据
                        start = linePoint.positionDetails[ind]
                        end = linePoint.positionDetails[ind + 1]
                        ind++
                        break
                    } else {
                        ind++
                    }
                }
                if (start == null || end == null) {
                    if (DEBUG_MSG_ENABLED) {
                        console.log(`线路${linePoint.lineId}已没有找到合适的start和end节点数据，当前线路绘制结束`)
                    }
                    break // 跳出循环 开始下一条回路
                }
                // 增加判断施工模式下的线路是否加载
                if (this.showDesign == false) {
                    let startPointTowerNumber = start.id.split('@')[0]
                    let endPointTowerNumber = end.id.split('@')[0]
                    if ((this.planRecordMap && startPointTowerNumber && this.planRecordMap[startPointTowerNumber] && this.planRecordMap[startPointTowerNumber].procedureStatus < Constants.PROGRESS_5) || 
                        (this.planRecordMap && endPointTowerNumber && this.planRecordMap[endPointTowerNumber] && this.planRecordMap[endPointTowerNumber].procedureStatus < Constants.PROGRESS_5)) {
                        if (DEBUG_MSG_ENABLED) {
                            console.log(`杆塔施工状态未完成 不加载该杆塔线路:${startPointTowerNumber} - ${endPointTowerNumber}`)
                        }
                        continue // 跳出循环 开始下一条回路
                    }
                }
                if (DEBUG_MSG_ENABLED) {
                    console.log(`开始加载线路${linePoint.lineId}节点：${start.id}~${end.id}的连线`, start, end)
                }
                // 加载start和end节点之间的连线
                for (let i = 0; i < Math.min(start.positions.length, end.positions.length); i++) {
                    let point1 = start.positions[i]
                    let point2 = end.positions[i]
                    if (point1.length != 3 || point2.length != 3) {
                        console.error(`线路${linePoint.lineId}节点：${start.id}~${end.id}的连线中有一点位置信息错误，请核查`, point1, point2)
                        continue
                    }
                    let color = Constants.PHASE_COLOR[start.phaseSequence]
                    let width = 1.5 // 线宽
                    if (start.lineType == 0) {
                        // 绘制悬链线
                        let sagHeight = Math.min(point1[2], point2[2]) - 1; // 设置弧垂最低点的高度
                        let polyline = overheadLine(this.lineCollection[linePoint.lineId], point1, point2, sagHeight, LINE_POINTS_NUM, color, width); // 绘制悬链线
                    } else {
                        // 绘制直线
                        let polyline = straight_line(this.lineCollection[linePoint.lineId], point1, point2, color, width)
                    }
                }
            }
        })
    }
    setAllModelShow = (show: boolean) => {
        this.entitysLoadedId.insulator.forEach(id => {
            let entity = this.viewer.entities.getById(id)
            if (entity) {
                entity.show = show
            }
        })
        this.entitysLoadedId.tower.forEach(id => {
            let entity = this.viewer.entities.getById(id)
            if (entity) {
                entity.show = show
            }
        })
        this.entitysLoadedId.label.forEach(id => {
            let entity = this.viewer.entities.getById(id)
            if (entity) {
                entity.show = show
            }
        })
    }
    // 异步加载绝缘子模型
    loadAllInsulators = async (awaitToLoadInsulators: any[], concurrencyLimit: number = 10) => {
        const queue = awaitToLoadInsulators.slice() // 创建一个待处理绝缘子的并发副本
        const processQueue = async () => {
            const batch = queue.splice(0, concurrencyLimit) // 取出当前批次的绝缘子信息
            if (batch.length === 0) { // 如果队列为空，说明加载完成
                return
            }
            await Promise.all(batch.map(async (insulatorInfo) => {
                const entity = await this.addOneInsulatorToViewer(insulatorInfo)
            }))
            // 递归处理剩余的绝缘子
            await processQueue()
        }
        await processQueue()
        return Promise.resolve(true)
    }

    autoDrawLine = async () => {
        this.removeAllModels()
        // await this.loadTowers() // 加载线路杆塔和塔号
        this.loadInsulators().then(res => {
            this.awaitToLoadInsulators = res || [];
            // 添加显示/隐藏逻辑
            this.enableEntytiesLod(this.awaitToLoadInsulators, 2000) // 动态加载杆塔相关的绝缘子
        }) // 加载线路绝缘子 返回待加载的绝缘子信息合集
        // this.loadAllInsulators(awaitToLoadInsulators, 20)
        // console.log('awaitToLoadInsulators:', awaitToLoadInsulators)
        this.loadLines() // 加载线路导线和地线
        return Promise.resolve(true)
    }
    roaming = (lineName: string) => {

    }
    loadSubstationInsulator = (awaitToLoadInsulators: any[]) => {
        // 处理与变电站等特殊模型连接的绝缘子模型加载 
        let insulatorInfors = awaitToLoadInsulators.filter((insulatorInfo: any) => { return VirtualTowerModel.includes(insulatorInfo.mountTower.modelNumber) })
        // console.log('loadSubstation', awaitToLoadInsulators, insulatorInfors)
        if (insulatorInfors) {
            insulatorInfors.forEach(insulatorInfo => {
                this.addOneInsulatorToViewer(insulatorInfo)
            })
        }

    }
    enableEntytiesLod = (awaitToLoadInsulators: any[], distance: number) => {
        // this.lineDetail
        console.log('awaitToLoadInsulators', this.awaitToLoadInsulators);

        let postRenderCallback = () => {
            var cameraPosition = this.viewer.camera.positionWC // 获取摄像机位置
            this.lineDetail.forEach(towerInfo => {
                let towerPositionCartesian3 = Cesium.Cartesian3.fromDegrees(towerInfo.longitude, towerInfo.latitude, towerInfo.height)
                // 计算摄像机与模型之间的距离
                let distance1 = Cesium.Cartesian3.distance(cameraPosition, towerPositionCartesian3)
                let towerID = `tower@${towerInfo.towerNumber}`
                if (distance1 < distance) {
                    // 加载杆塔模型
                    if (!towerInfo.loaded) {
                        towerInfo.loaded = true
                        this.addOneTowerToViewer(towerInfo)
                    } else {
                        // let towerEnty = this.viewer.entities.getById(towerID)
                        // if (towerEnty) {
                        //     towerEnty.show = true
                        // }
                        // 根据towerID 从 towerCollection中 查询
                        let model = this.towerCollection.find((el) => {
                            return el.id == towerID
                        })
                        if (model) {
                            model.show = true
                        }
                    }
                } else if (towerInfo.loaded) {
                    // 移除杆塔模型
                    // towerInfo.loaded = false
                    // let towerEnty = this.viewer.entities.getById(towerID)
                    // if (towerEnty) {
                    //     towerEnty.show = false
                    // }
                    // this.viewer.entities.removeById(towerID)

                    // 根据towerID 从 towerCollection中 查询
                    let model = this.towerCollection.find((el) => {
                        return el.id == towerID
                    })
                    if (model) {
                        model.show = false
                    }
                }

            })
            this.awaitToLoadInsulators.forEach(insulatorInfo => {
                let insulatorPositionCartesian3 = Cesium.Cartesian3.fromDegrees(...insulatorInfo.mountPosition)
                // 计算摄像机与模型之间的距离
                let distance1 = Cesium.Cartesian3.distance(cameraPosition, insulatorPositionCartesian3)
                let insulatorID = `insulator@${insulatorInfo.mountTower.towerNumber}@${insulatorInfo.mountID}@${insulatorInfo.model.modelName}`
                if (distance1 < distance) {
                    // 加载模型
                    if (!insulatorInfo.loaded) {
                        insulatorInfo.loaded = true
                        this.addOneInsulatorToViewer(insulatorInfo)
                    } else {
                        let insulatorEnty = this.viewer.entities.getById(insulatorID)
                        if (insulatorEnty) {
                            insulatorEnty.show = true
                        }
                    }
                } else if (insulatorInfo.loaded) {
                    // 移除模型
                    // insulatorInfo.loaded = false
                    let insulatorEnty = this.viewer.entities.getById(insulatorID)
                    if (insulatorEnty) {
                        insulatorEnty.show = false
                    }
                    // this.viewer.entities.removeById(insulatorID)
                }
            })

            // this.entitysLoadedId.tower.forEach(entityID => {
            //     let entity = this.viewer.entities.getById(entityID)
            //     if (!entity) {
            //         return // 如果实体不存在，跳过该实体
            //     }
            //     var entityPosition = entity.position.getValue(Cesium.JulianDate.now()) // 获取模型位置
            //     if (!Cesium.defined(entityPosition)) {
            //         return // 如果模型位置未定义，跳过
            //     }
            //     // 计算摄像机与模型之间的距离
            //     var distance1 = Cesium.Cartesian3.distance(cameraPosition, entityPosition)
            //     // 根据距离设置模型的可见性
            //     if (entity.show !== (distance1 < distance)) {
            //         entity.show = distance1 < distance
            //         // 处理该杆塔相关的绝缘子模型
            //         // let insulators = awaitToLoadInsulators.filter((insulatorInfo: any) => {
            //         //     return `tower@${insulatorInfo.mountTower.towerNumber}` == entityID
            //         // })
            //         // if (insulators) {
            //         //     insulators.forEach(insulatorInfo => {
            //         //         let insulatorEntity = this.checkInsulatorLoaded(insulatorInfo)
            //         //         if (insulatorEntity != undefined) {
            //         //             // 绝缘子模型已经加载 直接设置显示隐藏
            //         //             insulatorEntity.show = distance1 < distance
            //         //         } else {
            //         //             if (distance1 < distance) {
            //         //                 this.addOneInsulatorToViewer(insulatorInfo)
            //         //             }
            //         //         }
            //         //     })
            //         // }

            //         // if(this.entitysLoadedId.insulator.includes(entityID))
            //     }
            // })
        }
        // 检查并防止重复添加事件监听器
        const listeners = this.viewer.scene.postRender._listeners || []
        const isCallbackRegistered = listeners.find((callback: any) => callback.name === "postRenderCallback")

        if (!isCallbackRegistered) {
            // 只有当回调未注册时，才添加
            this.viewer.scene.postRender.addEventListener(postRenderCallback)
        }
        console.log('remove')
        // 返回一个函数以供外部调用，能够移除该监听器
        return () => {
            this.viewer.scene.postRender.removeEventListener(postRenderCallback)
        };
    }
}
