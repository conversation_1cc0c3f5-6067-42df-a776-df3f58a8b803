
import * as Cesium from 'cesium';
import { PowerLineManager } from './components/PowerLineManager';
import type { IInsulator } from './components/InsulatorManager';
import type { ITower } from './components/TowerManager';
import ModelCache from '@/js/common/modelCache';
import { InstanceRenderer } from './core/InstanceRenderer';
import { DynamicOptimizer } from './core/PerformanceMonitor';
import { MODEL_STATIC_URL } from '@/config/global';

export default class Map3dV2 {
  private viewer: Cesium.Viewer;
  private powerLineManager!: PowerLineManager;

  constructor(container: HTMLElement) {
    this.viewer = new Cesium.Viewer(container, {
      // Add Cesium Viewer options here
    });

    const modelCache = new ModelCache();
    const instanceRenderer = new InstanceRenderer(this.viewer, modelCache);
    const dynamicOptimizer = new DynamicOptimizer(this.viewer);

    this.powerLineManager = new PowerLineManager(this.viewer, modelCache, instanceRenderer, dynamicOptimizer);
  }

  async loadData(projectData: any) {
    const { towers, insulators, lines } = this.transformData(projectData);
    await this.powerLineManager.loadPowerLine(towers, insulators, lines);
  }

  transformData(projectData: any): { towers: ITower[], insulators: IInsulator[], lines: any[] } {
    const towers: ITower[] = projectData.lineDetail.map((tower: any) => ({
      id: tower.towerNumber,
      url: `${MODEL_STATIC_URL}/${projectData.projectCode}${tower.url}`,
      position: [tower.longitude, tower.latitude, tower.height],
      properties: {
        ...tower
      }
    }));

    const insulators: IInsulator[] = projectData.insulatorAllocation.map((insulator: any) => {
        const tower = projectData.lineDetail.find((t: any) => t.towerNumber === insulator.towerNumber);
        if (!tower) return null;

        const insulatorModel = projectData.insulatorModels.find((m: any) => m.modelName === insulator.insulator);
        if (!insulatorModel) return null;

        return {
            id: `${insulator.towerNumber}-${insulator.locationId}`,
            url: `${MODEL_STATIC_URL}/${projectData.projectCode}${insulatorModel.url}`,
            position: [tower.longitude, tower.latitude, tower.height], // This is a placeholder, you need to calculate the exact position
            properties: {
                ...insulator
            }
        }
    }).filter((i: any) => i !== null);

    const lines: any[] = [];
    for (let i = 0; i < projectData.linesInfo.length; i++) {
        const lineInfo = projectData.linesInfo[i];
        const lineAllocations = projectData.insulatorAllocation.filter((a: any) => a.lineId === lineInfo.id).sort((a: any, b: any) => a.positionId - b.positionId);

        for (let j = 0; j < lineAllocations.length - 1; j++) {
            lines.push({
                id: `${lineInfo.id}-${j}`,
                startTowerId: lineAllocations[j].towerNumber,
                endTowerId: lineAllocations[j + 1].towerNumber,
            });
        }
    }

    return { towers, insulators, lines };
  }

  destroy() {
    this.powerLineManager.dispose();
    this.viewer.destroy();
  }
}
