// 使用示例
import PowerLine3D, { IInsulator, ITower, ILine } from './js/map3d_v2';

// 初始化
const container = document.getElementById('cesium-container');
if (container) {
  const powerLine3D = new PowerLine3D(container);
  powerLine3D.initialize().then(() => {
    // 加载示例数据
    loadSampleData(powerLine3D);
    
    // 添加性能面板
    createPerformancePanel(powerLine3D);
  });
}

// 加载示例数据
async function loadSampleData(powerLine3D: PowerLine3D): Promise<void> {
  // 加载绝缘子
  const insulators: IInsulator[] = generateSampleInsulators();
  await powerLine3D.loadInsulators(insulators);
  
  // 加载杆塔
  const towers: ITower[] = generateSampleTowers();
  await powerLine3D.loadTowers(towers);
  
  // 加载线路
  const lines: ILine[] = generateSampleLines();
  powerLine3D.loadLines(lines);
}

// 生成示例绝缘子数据
function generateSampleInsulators(): IInsulator[] {
  const insulators: IInsulator[] = [];
  const insulatorTypes = [
    '/models/insulators/insulator_type1.glb',
    '/models/insulators/insulator_type2.glb'
  ];
  
  // 生成500个绝缘子
  for (let i = 0; i < 500; i++) {
    const typeIndex = i % insulatorTypes.length;
    insulators.push({
      id: `insulator-${i}`,
      url: insulatorTypes[typeIndex],
      position: [
        116.4 + (Math.random() - 0.5) * 0.1,
        39.9 + (Math.random() - 0.5) * 0.1,
        10 + Math.random() * 50
      ],
      scale: 1.0,
      rotation: [0, 0, Math.random() * 360]
    });
  }
  
  return insulators;
}

// 生成示例杆塔数据
function generateSampleTowers(): ITower[] {
  const towers: ITower[] = [];
  const towerTypes = [
    '/models/towers/tower_type1.glb',
    '/models/towers/tower_type2.glb'
  ];
  
  // 生成50个杆塔
  for (let i = 0; i < 50; i++) {
    const typeIndex = i % towerTypes.length;
    towers.push({
      id: `tower-${i}`,
      url: towerTypes[typeIndex],
      position: [
        116.4 + (Math.random() - 0.5) * 0.1,
        39.9 + (Math.random() - 0.5) * 0.1,
        0
      ],
      scale: 1.0,
      rotation: [0, 0, Math.random() * 360]
    });
  }
  
  return towers;
}

// 生成示例线路数据
function generateSampleLines(): ILine[] {
  const lines: ILine[] = [];
  
  // 生成10条线路
  for (let i = 0; i < 10; i++) {
    const positions: number[][] = [];
    const startPos = [
      116.4 + (Math.random() - 0.5) * 0.1,
      39.9 + (Math.random() - 0.5) * 0.1,
      10 + Math.random() * 50
    ];
    
    // 每条线路有5个点
    positions.push(startPos);
    for (let j = 1; j < 5; j++) {
      positions.push([
        startPos[0] + (Math.random() - 0.5) * 0.02 * j,
        startPos[1] + (Math.random() - 0.5) * 0.02 * j,
        startPos[2] + (Math.random() - 0.5) * 10
      ]);
    }
    
    lines.push({
      id: `line-${i}`,
      positions,
      width: 2,
      color: '#FFFFFF'
    });
  }
  
  return lines;
}

// 创建性能面板
function createPerformancePanel(powerLine3D: PowerLine3D): void {
  const panel = document.createElement('div');
  panel.className = 'performance-panel';
  document.body.appendChild(panel);
  
  // 更新性能面板
  setInterval(() => {
    const { fps, modelCount, memoryUsage } = powerLine3D.getPerformanceData();
    
    panel.innerHTML = `
      <div>FPS: ${fps}</div>
      <div>模型数量: ${modelCount}</div>
      <div>内存使用: ${Math.round(memoryUsage / (1024 * 1024))} MB</div>
      <div class="mode-selector">
        <button onclick="setMode('high')">High</button>
        <button onclick="setMode('balanced')">Balanced</button>
        <button onclick="setMode('quality')">Quality</button>
      </div>
    `;
  }, 1000);
  
  // 添加全局函数以切换模式
  (window as any).setMode = (mode: 'high' | 'balanced' | 'quality') => {
    powerLine3D.setPerformanceMode(mode);
  };
}