// import { number } from "echarts"

export type AllocationType = {
    id?: number,
    lineId: number,
    towerID?: string,
    insulator: string,
    positionID: string | number,
    k1?: 0,
    k2?: 0,
    locationId: number,
    in_out_flag: number,
    projectId: number,
    lineTpye: number,
    internalLine: number,
    towerNumber: string,
    positionId?: string | number,
    inOutFlag?: number
}

// 加载绝缘子时需要的参数
export type InsulatorLoadType = {
    lineId?: number, // 线路ID
    positionId?: string | number, // 配串顺序
    mountName: string, // 挂点名称
    mountID: number, // 挂点ID
    model: any,   // 绝缘子模型信息
    mountTower: any, // 绝缘子挂载的杆塔详情信息
    mountPosition: number[], // 挂点绝对位置信息
    angRaid?: number, // 角度 单位弧度
}

// 线路信息表
export type LineInfoType = {
    id?: string,
    name: string,
    overview: string,
    projectId: number,
    lineId: number,
    phaseSequence: string,
    version: string
}

// 杆塔挂点表
export type TowerMountType = {
    id?: number,
    towerModel: string
    mountName: string,
    mountPosition: [number, number, number],
    locationId: number,
    projectId: number
}

// 工程线路杆塔详情表
export type LineDetailType = {
    id?: string,
    projectId: number,
    towerID: string,
    towerModel: string,
    longitude: number,
    latitude: number,
    height: number,
    angle: number,
    kv: number,
    version: string,
    projectTower_flag: number,
    procedureStatus: number,
    towerNumber?: string,
    modelNumber?: string,
    loaded?: boolean
}

// 杆塔模型详情表
export type TowerModelType = {
    id?: number,
    series: string,
    towerModel: string,
    url: string,
    unit: string,
    voltage: string,
    material: string,
    towerStyle: string,
    lineStyle: string,
    baseHeight: number,
    towerHeight: number,
    projectId: number,
    version: string
}

// 绝缘子模型详情表
export type InsulatorModelType = {
    id?: number,
    series: string,
    modelName: string,
    url: string,
    modelType: string,
    lineType: string,
    length: number,
    connectNum: number,
    splitNum: number,
    projectId: number,
    version: string,
    inputLine1?: number[],
    inputLine2?: number[],
    inputLine3?: number[],
    inputLine4?: number[],
    outputLine1?: number[],
    outputLine2?: number[],
    outputLine3?: number[],
    outputLine4?: number[],
    inputLinePoint1?: number[],
    inputLinePoint2?: number[],
    inputLinePoint3?: number[],
    inputLinePoint4?: number[],
    outputLinePoint1?: number[],
    outputLinePoint2?: number[],
    outputLinePoint3?: number[],
    outputLinePoint4?: number[]
}

// 线路绘制节点数据类型
export type PositionDetail = {
    id: string,
    pointType: string,
    lineType: number,
    positions: number[][],
    phaseSequence: string
}
export type LinePointTpye = {
    lineId: number,
    positionDetails: PositionDetail[]
}
