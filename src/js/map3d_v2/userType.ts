export interface AllocationType {
    insulator: string;
    projectId: string;
    towerNumber: string;
    lineId: string;
    positionId: string;
    modelNumber: string;
    locationId: string;
    lineTpye?: number;
    internalLine?: number;
    inOutFlag?: number;
    phaseSequence?: PhaseSequenceType;
}

export interface InsulatorModelType {
    modelName: string;
    projectId: string;
    [key: string]: any;
}

export interface TowerModelType {
    towerNumber: string;
    projectId: string;
    [key: string]: any;
}

export interface InsulatorLoadType {
    lineId: string;
    positionId: string;
    mountName: string;
    mountID: string;
    model: InsulatorModelType;
    mountTower: TowerModelType;
    mountPosition: [number, number, number];
    angRaid?: number;
}

export interface LineInfoType {
    [key: string]: any;
}

export interface LineDetailType {
    towerNumber: string;
    projectId: string;
    [key: string]: any;
}

export interface TowerMountType {
    [key: string]: any;
}

export type PhaseSequenceType = 'A' | 'B' | 'C' | 'G' | 'G1';

export interface LinePointGroupType {
    id: string;
    positions: number[][];
    phaseSequence: PhaseSequenceType;
    lineType: number;
    pointType: 'start' | 'end' | 'inStart' | 'inEnd';
}

export interface LinePointTpye {
    lineId: string;
    positionDetails: LinePointGroupType[];
    insulatorAllocation: AllocationType[];
    mountGroups: TowerMountType[];
}