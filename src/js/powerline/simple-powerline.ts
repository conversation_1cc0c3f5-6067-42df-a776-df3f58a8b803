/**
 * 简单的电力线路数据模型和渲染
 * 基于main-server分支的数据结构，专注于核心功能
 */

export interface TowerData {
  id: string;
  towerNumber: string;
  longitude: number;
  latitude: number;
  height: number;
  altitude: number;
  towerModel?: string;
  angle?: number;
}

export interface LineData {
  lineId: string;
  lineName: string;
  towers: TowerData[];
  voltage?: number; // 电压等级
  lineType?: string; // 线路类型
}

export interface PowerLineProject {
  projectId: string;
  projectName: string;
  lines: LineData[];
  centerPosition?: {
    longitude: number;
    latitude: number;
    height: number;
  };
}

export class SimplePowerLineRenderer {
  private viewer: any;
  private Cesium: any;
  private entities: any[] = [];

  constructor(cesiumViewer: any) {
    this.viewer = cesiumViewer;
    this.Cesium = window.Cesium;
    
    if (!this.Cesium) {
      throw new Error('Cesium未加载');
    }
  }

  /**
   * 渲染电力线路项目
   */
  renderProject(project: PowerLineProject) {
    console.log('开始渲染电力线路项目:', project.projectName);
    
    try {
      // 清除之前的渲染
      this.clearAll();

      // 渲染每条线路
      project.lines.forEach(line => {
        this.renderLine(line);
      });

      // 飞行到项目中心
      if (project.centerPosition) {
        this.flyToProject(project.centerPosition);
      } else if (project.lines.length > 0 && project.lines[0].towers.length > 0) {
        // 计算并飞行到第一条线路的中心
        const firstLine = project.lines[0];
        const center = this.calculateLineCenter(firstLine.towers);
        this.flyToProject(center);
      }

      console.log('电力线路项目渲染完成');
      
    } catch (error) {
      console.error('渲染电力线路项目失败:', error);
      throw error;
    }
  }

  /**
   * 渲染单条线路
   */
  renderLine(line: LineData) {
    console.log('渲染线路:', line.lineName);
    
    try {
      // 渲染杆塔
      line.towers.forEach(tower => {
        this.renderTower(tower, line);
      });

      // 渲染线路（杆塔之间的连线）
      if (line.towers.length >= 2) {
        this.renderPowerLines(line);
      }
      
    } catch (error) {
      console.error('渲染线路失败:', error, line);
    }
  }

  /**
   * 渲染杆塔
   */
  renderTower(tower: TowerData, line: LineData) {
    try {
      const totalHeight = tower.altitude + tower.height;
      
      const entity = this.viewer.entities.add({
        name: `${line.lineName}-${tower.towerNumber}`,
        position: this.Cesium.Cartesian3.fromDegrees(
          tower.longitude, 
          tower.latitude, 
          totalHeight
        ),
        point: {
          pixelSize: 12,
          color: this.Cesium.Color.RED,
          outlineColor: this.Cesium.Color.WHITE,
          outlineWidth: 2,
          heightReference: this.Cesium.HeightReference.RELATIVE_TO_GROUND
        },
        label: {
          text: tower.towerNumber,
          font: '14pt sans-serif',
          fillColor: this.Cesium.Color.WHITE,
          outlineColor: this.Cesium.Color.BLACK,
          outlineWidth: 2,
          style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new this.Cesium.Cartesian2(0, -50),
          heightReference: this.Cesium.HeightReference.RELATIVE_TO_GROUND
        }
      });

      this.entities.push(entity);
      console.log(`杆塔 ${tower.towerNumber} 渲染完成`);
      
    } catch (error) {
      console.error('渲染杆塔失败:', error, tower);
    }
  }

  /**
   * 渲染电力线路（杆塔之间的连线）
   */
  renderPowerLines(line: LineData) {
    try {
      const positions: number[] = [];
      
      // 构建位置数组
      line.towers.forEach(tower => {
        const totalHeight = tower.altitude + tower.height;
        positions.push(tower.longitude, tower.latitude, totalHeight);
      });

      // 创建主线路
      const mainLine = this.viewer.entities.add({
        name: `${line.lineName}-主线`,
        polyline: {
          positions: this.Cesium.Cartesian3.fromDegreesArrayHeights(positions),
          width: 4,
          material: this.Cesium.Color.YELLOW,
          clampToGround: false,
          show: true
        }
      });

      this.entities.push(mainLine);

      // 如果是高压线路，添加多根导线效果
      if (line.voltage && line.voltage >= 220) {
        this.renderMultipleWires(line, positions);
      }

      console.log(`线路 ${line.lineName} 渲染完成`);
      
    } catch (error) {
      console.error('渲染电力线路失败:', error, line);
    }
  }

  /**
   * 渲染多根导线（高压线路）
   */
  renderMultipleWires(line: LineData, basePositions: number[]) {
    try {
      const wireCount = line.voltage >= 500 ? 6 : 3; // 500kV用6根线，220kV用3根线
      const wireSpacing = 5; // 导线间距（米）

      for (let i = 1; i < wireCount; i++) {
        const offsetPositions = [...basePositions];
        
        // 为每根导线添加轻微的高度偏移
        for (let j = 2; j < offsetPositions.length; j += 3) {
          offsetPositions[j] -= i * 2; // 每根线降低2米
        }

        const wire = this.viewer.entities.add({
          name: `${line.lineName}-导线${i}`,
          polyline: {
            positions: this.Cesium.Cartesian3.fromDegreesArrayHeights(offsetPositions),
            width: 2,
            material: this.Cesium.Color.YELLOW.withAlpha(0.8),
            clampToGround: false,
            show: true
          }
        });

        this.entities.push(wire);
      }
      
    } catch (error) {
      console.error('渲染多根导线失败:', error);
    }
  }

  /**
   * 计算线路中心点
   */
  calculateLineCenter(towers: TowerData[]) {
    const centerLon = towers.reduce((sum, tower) => sum + tower.longitude, 0) / towers.length;
    const centerLat = towers.reduce((sum, tower) => sum + tower.latitude, 0) / towers.length;
    const avgHeight = towers.reduce((sum, tower) => sum + tower.altitude + tower.height, 0) / towers.length;
    
    return {
      longitude: centerLon,
      latitude: centerLat,
      height: avgHeight + 500 // 在平均高度基础上增加500米作为观察高度
    };
  }

  /**
   * 飞行到项目位置
   */
  flyToProject(position: { longitude: number; latitude: number; height: number }) {
    try {
      this.viewer.camera.flyTo({
        destination: this.Cesium.Cartesian3.fromDegrees(
          position.longitude, 
          position.latitude, 
          position.height
        ),
        duration: 3.0,
        complete: () => {
          console.log('相机飞行完成');
        }
      });
    } catch (error) {
      console.error('飞行失败:', error);
    }
  }

  /**
   * 清除所有渲染的实体
   */
  clearAll() {
    this.entities.forEach(entity => {
      this.viewer.entities.remove(entity);
    });
    this.entities = [];
    console.log('清除所有电力线路渲染');
  }
}

export default SimplePowerLineRenderer;
