import { Viewer, Cartesian3, Color, PolygonHierarchy, ClassificationType, DistanceDisplayCondition, defined, defaultValue, Entity, Model, Cartesian2, Cartographic, Math as CesiumMath, Quaternion, Matrix3, Matrix4, Transforms, Ellipsoid, HeadingPitchRoll, ColorBlendMode } from 'cesium';
import { Scene, type SceneElement } from "./scene.ts"
import { getPositionHeight } from "../common/viewer.ts"
import { useLineProjStore } from '@/store/lineProj.js'

import * as turf from "@turf/turf";
import axios from "axios"
import { flattenTerrainForSubstation1 } from '../common/flattenTerrain.ts';

export default class SCENE_TOOL {
    private static LineBufferIds: Array<string> = []
    private static LineBufferLineMap: Map<string, Array<string>> = new Map()
    /**
     * 
     * @param viewer Cesium Viewer
     * @param projectId Current project id
     * @param flag true:show, false: hidden
     * @param bufferWidth The width of this buffer
     * @returns 
     */
    public static loadLineBuffer(viewer: Viewer, projectId: number, flag: boolean = true, bufferWidth: number = 40): void {
        let doBreak: boolean = SCENE_TOOL.LineBufferIds.length > 0
        if (doBreak) {
            SCENE_TOOL.LineBufferIds.forEach((entityId: string) => {
                var foundEntity = viewer.entities.getById(entityId)
                if (foundEntity) {
                    foundEntity.show = flag
                    doBreak = doBreak && true
                }
                else {
                    doBreak = false
                }
            })

        }
        if (doBreak) return
        SCENE_TOOL.LineBufferIds = []
        SCENE_TOOL.LineBufferLineMap.clear()
        const store_lineProj = useLineProjStore();
        let dataVersion = '1.0'
        if (store_lineProj.lineDataV20 && store_lineProj.lineDataV20.linesInfo) {
            if (store_lineProj.lineDataV20.linesInfo.some((item: any) => item.projectId == projectId)) {
                dataVersion = '2.0'
                let loops: Array<any> = store_lineProj.lineDataV20.linesInfo.filter((item: any) => item.phaseSequence === 'A' && item.projectId == projectId)
                loops.forEach((loop: any) => {
                    const ps = Array.from(store_lineProj.lineDataV20.insulatorAllocation.filter((item: any) => {
                        return item.lineId == loop.id
                    }).reduce((map: Map<any, any>, item: any) => {
                        map.set(item.towerNumber, item);
                        return map;
                    }, new Map()).values())
                        .map((item: any) => {
                            return store_lineProj.lineDataV20.lineDetail.find((ld: any) => ld.towerNumber == item.towerNumber)
                        })
                    const loopData = ps.filter((item: any) => !(item.modelNumber == "substationModel" || item.modelNumber == "tower_virtual"))
                    const subLoopData = SCENE_TOOL.splitLoopData(loop.lineName, loopData)
                    subLoopData.forEach((ld: any, index: number) => {
                        const points = ld.map((item: any) => [parseFloat(item.longitude), parseFloat(item.latitude)])
                        SCENE_TOOL.drawBuffer(viewer, points, bufferWidth, `line_loop_${loop.id}_${index}`)
                    })

                })
            }
        }
        if (dataVersion == '1.0') {
            //针对珠东南，直接用线路数据来绘制缓冲区
            store_lineProj.proLines.forEach((line: any, index: number) => {
                const points = line.children.filter((item: any) => !(item.modelNumber == "substationModel" || item.modelNumber == "tower_virtual")).map((item: any) => [parseFloat(item.longitude), parseFloat(item.latitude)])
                SCENE_TOOL.drawBuffer(viewer, points, bufferWidth, `line_${line.id}`)
            });
        }
    }
    private static splitLoopData(lineName: string, loopData: Array<any>): Array<any> {
        const result: Array<any> = []
        if (!SCENE_TOOL.LineBufferLineMap.has(lineName)) {
            SCENE_TOOL.LineBufferLineMap.set(lineName, loopData.map((item: any) => item.towerNumber))
            result.push(loopData)
            return result
        }
        const towerNumbers = SCENE_TOOL.LineBufferLineMap.get(lineName)
        let existPre: boolean = true, existCurrent: boolean = true, existNext = false
        let subLoopData: Array<any> = []
        for (let i = 0; i < loopData.length; i++) {
            existCurrent = (towerNumbers as string[]).findIndex(item => item === loopData[i].towerNumber) > -1
            if (existPre && (!existCurrent)) {
                if (subLoopData.length > 0) result.push(subLoopData)
                subLoopData = new Array<string>()
                if (i > 0) {
                    subLoopData.push(loopData[i - 1])
                }
                subLoopData.push(loopData[i])
            } else if ((!existPre) && !existCurrent) {
                subLoopData.push(loopData[i])
            } else if (existCurrent && !existPre) {
                subLoopData.push(loopData[i])
            }
            existPre = existCurrent
        }
        if (subLoopData.length > 0) result.push(subLoopData)
        return result
    }
    private static drawBuffer(viewer: Viewer, points: Array<any>, bufferWidth: number, bufferId: string): void {
        const pointF = turf.lineString(points, { name: bufferId })
        const buffered = turf.buffer(pointF, bufferWidth, { units: 'meters' });//缓冲区边界坐标，这是创建缓冲区面的关键
        const coordinates = buffered?.geometry.coordinates;
        if (!coordinates || !coordinates[0]) return;
        let point1s: Array<any> = coordinates[0];
        let degreesArray = SCENE_TOOL.pointsToDegreesArray(point1s);
        SCENE_TOOL.addBufferPolyogn(viewer, Cartesian3.fromDegreesArray(degreesArray), bufferId);
    }
    private static addBufferPolyogn(viewer: Viewer, positions: Array<any>, bufferId: string) {
        console.log("turf缓冲区坐标:", positions);
        viewer.entities.add({
            id: bufferId,
            polygon: {
                hierarchy: new PolygonHierarchy(positions),
                material: Color.RED.withAlpha(0.6),
                classificationType: ClassificationType.BOTH,
                distanceDisplayCondition: new DistanceDisplayCondition(0, 15000),

            },
        });
        SCENE_TOOL.LineBufferIds.push(bufferId)
    }
    // 坐标点格式转换
    private static pointsToDegreesArray(points: Array<any>) {
        const degreesArray: Array<any> = [];

        points.map((item) => {
            degreesArray.push(item[0]);
            degreesArray.push(item[1]);
        });

        return degreesArray;

    }
    public static async loadScene(viewer: Viewer, projectId: string): Promise<Scene> {
        return new Scene(viewer)

    }
    public static pickModel(viewer: Viewer, windowPosition: Cartesian2): any {
        let picked = viewer.scene.pick(windowPosition);
        if (defined(picked)) {
            let entity = defaultValue(picked.id, picked.primitive.id);
            if (entity instanceof Entity) {
                if (entity.model) {
                    console.log('model');
                    return entity;
                }
            }
        }
        return null;
    }
    public static async getEntityProperty(viewer: Viewer, entity: Entity, model: Model) {
        var position: Cartesian3 | undefined = entity.position?.getValue(viewer.clock.currentTime);
        if (defined(position)) {
            // 转换为经纬度坐标
            var cartographic = Cartographic.fromCartesian(position as Cartesian3);
            var longitudeString = CesiumMath.toDegrees(cartographic.longitude);
            var latitudeString = CesiumMath.toDegrees(cartographic.latitude);

            const h = await getPositionHeight(viewer, [[longitudeString, latitudeString, 0]])

            var altitude = h[0].height//cartographic.height
            //console.log(cartographic,'当前坐标：经度=' + longitudeString + ', 纬度=' + latitudeString);
            // 获取模型的比例
            let scale = entity.model?.scale?.getValue(viewer.clock.currentTime);
            console.log('scale=' + scale);

            const hpr = SCENE_TOOL.takeHpr(entity.orientation?.getValue(viewer.clock.currentTime, new Quaternion()), position as Cartesian3)
            console.log(hpr)
            return Promise.resolve({
                longitude: longitudeString, latitude: latitudeString, scale, altitude, distance: cartographic.height - altitude,
                heading: CesiumMath.toDegrees(hpr.heading),
                picth: CesiumMath.toDegrees(hpr.pitch), roll: CesiumMath.toDegrees(hpr.roll)
            })

        }



        return null
    }
    public static takeHpr(orientation: Quaternion, position: Cartesian3): HeadingPitchRoll {
        //1、由四元数计算三维旋转矩阵
        var mtx3 = Matrix3.fromQuaternion(orientation, new Matrix3());
        //2、计算四维转换矩阵：
        var mtx4 = Matrix4.fromRotationTranslation(mtx3, position, new Matrix4());
        //3、计算角度：
        var hpr = Transforms.fixedFrameToHeadingPitchRoll(mtx4, Ellipsoid.WGS84, Transforms.eastNorthUpToFixedFrame, new HeadingPitchRoll());
        return hpr;
    }
    //> 获取模型颜色与透明度
    public static getColor(colorName: string, alpha: number) {
        const color = (Color as any)[colorName.toUpperCase()];
        return Color.fromAlpha(color, alpha);
    }
    //> 获取目标颜色和图元的源颜色之间混合的不同模式  HIGHLIGHT将源颜色乘以目标颜色 REPLACE将源颜色替换为目标颜色 MIX将源颜色和目标颜色混合在一起
    public static getColorBlendMode(colorBlendMode: any) {
        return ColorBlendMode[colorBlendMode.toUpperCase()];
    }
        public static showSilhouette(entity: Entity, show: boolean = true, colorName: string = 'green') {
        if (!defined(entity) || !entity.model) return;
        entity.model.silhouetteColor = show ? SCENE_TOOL.getColor(colorName, 1) as any : undefined;
        entity.model.silhouetteSize = show ? 5 as any : undefined;
    }
    public static async takeMapRouteInfo(p1: Array<number>, p2: Array<number>) {
        const param = {
            "points": [
                p1, p2
            ],
            "profile": "car",
            "elevation": true,
            "instructions": true,
            "locale": "zh_CN",
            "points_encoded": false,
            "points_encoded_multiplier": 1000000,
            "snap_preventions": [
                "ferry"
            ],
            "details": [
                "road_class",
                "road_environment",
                "max_speed",
                "average_speed"
            ],
            "alternative_route.max_paths": 3,
            "algorithm": "alternative_route"
        }
        return axios.create({
            timeout: 60000 * 20,
            baseURL: '/'
        }).post("http://*************:8989/route", param)
    }
    /**
     * 为变电站1创建地形压平
     * @param viewer Cesium查看器
     * @param customHeight 可选的自定义压平高度
     * @returns 是否成功创建地形压平
     */
    public static async flattenSubstation1Terrain(viewer: Viewer, customHeight?: number): Promise<boolean> {
        return flattenTerrainForSubstation1(viewer, customHeight);
    }
}
