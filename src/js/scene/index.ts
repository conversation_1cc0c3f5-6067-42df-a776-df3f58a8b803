import { Scene, type SceneElement } from "./scene"
import { getPositionHeight } from "@/js/3dViewer.js"
import { useLineProjStore } from '@/store/lineProj.js'
import { MAPROUTE_URL } from '@/config/global.js'
import * as turf from "@turf/turf";
import axios from "axios"
import * as Cesium from '@/Cesium'
import { flattenTerrainForSubstation1 } from '../common/flattenTerrain';
// import * as Cesium from 'cesium';
export default class SCENE_TOOL {
    private static LineBufferIds: Array<string> = []
    private static LineBufferLineMap: Map<string, Array<string>> = new Map()
    /**
     * 
     * @param viewer Cesium Viewer
     * @param projectId Current project id
     * @param flag true:show, false: hidden
     * @param bufferWidth The width of this buffer
     * @returns 
     */
    public static loadLineBuffer(viewer: Cesium.Viewer, projectId: number, flag: boolean = true, bufferWidth: number = 40): void {
        let doBreak: boolean = SCENE_TOOL.LineBufferIds.length > 0
        if (doBreak) {
            SCENE_TOOL.LineBufferIds.forEach((entityId: string) => {
                var foundEntity = viewer.entities.getById(entityId)
                if (foundEntity) {
                    foundEntity.show = flag
                    doBreak = doBreak && true
                }
                else {
                    doBreak = false
                }
            })

        }
        if (doBreak) return
        SCENE_TOOL.LineBufferIds = []
        SCENE_TOOL.LineBufferLineMap.clear()
        const store_lineProj = useLineProjStore();
        let dataVersion = '1.0'
        if (store_lineProj.lineDataV20 && store_lineProj.lineDataV20.linesInfo) {
            if (store_lineProj.lineDataV20.linesInfo.some((item: any) => item.projectId == projectId)) {
                dataVersion = '2.0'
                let loops: Array<any> = store_lineProj.lineDataV20.linesInfo.filter((item: any) => item.phaseSequence === 'A' && item.projectId == projectId)
                loops.forEach((loop: any) => {
                    const ps = Array.from(store_lineProj.lineDataV20.insulatorAllocation.filter((item: any) => {
                        return item.lineId == loop.id
                    }).reduce((map: Map<any, any>, item: any) => {
                        map.set(item.towerNumber, item);
                        return map;
                    }, new Map()).values())
                        .map((item: any) => {
                            return store_lineProj.lineDataV20.lineDetail.find((ld: any) => ld.towerNumber == item.towerNumber)
                        })
                    const loopData = ps.filter((item: any) => !(item.modelNumber == "substationModel" || item.modelNumber == "tower_virtual"))
                    const subLoopData = SCENE_TOOL.splitLoopData(loop.lineName, loopData)
                    subLoopData.forEach((ld: any, index: number) => {
                        const points = ld.map((item: any) => [parseFloat(item.longitude), parseFloat(item.latitude)])
                        SCENE_TOOL.drawBuffer(viewer, points, bufferWidth, `line_loop_${loop.id}_${index}`)
                    })

                })
            }
        }
        if (dataVersion == '1.0') {
            //针对珠东南，直接用线路数据来绘制缓冲区
            store_lineProj.proLines.forEach((line: any, index: number) => {
                const points = line.children.filter((item: any) => !(item.modelNumber == "substationModel" || item.modelNumber == "tower_virtual")).map((item: any) => [parseFloat(item.longitude), parseFloat(item.latitude)])
                SCENE_TOOL.drawBuffer(viewer, points, bufferWidth, `line_${line.id}`)
            });
        }
    }
    private static splitLoopData(lineName: string, loopData: Array<any>): Array<any> {
        const result: Array<any> = []
        if (!SCENE_TOOL.LineBufferLineMap.has(lineName)) {
            SCENE_TOOL.LineBufferLineMap.set(lineName, loopData.map((item: any) => item.towerNumber))
            result.push(loopData)
            return result
        }
        const towerNumbers = SCENE_TOOL.LineBufferLineMap.get(lineName)
        let existPre: boolean = true, existCurrent: boolean = true, existNext = false
        let subLoopData: Array<any> = []
        for (let i = 0; i < loopData.length; i++) {
            existCurrent = (towerNumbers as string[]).findIndex(item => item === loopData[i].towerNumber) > -1
            if (existPre && (!existCurrent)) {
                if (subLoopData.length > 0) result.push(subLoopData)
                subLoopData = new Array<string>()
                if (i > 0) {
                    subLoopData.push(loopData[i - 1])
                }
                subLoopData.push(loopData[i])
            } else if ((!existPre) && !existCurrent) {
                subLoopData.push(loopData[i])
            } else if (existCurrent && !existPre) {
                subLoopData.push(loopData[i])
            }
            existPre = existCurrent
        }
        if (subLoopData.length > 0) result.push(subLoopData)
        return result
    }
    private static drawBuffer(viewer: Cesium.Viewer, points: Array<any>, bufferWidth: number, bufferId: string): void {
        const pointF = turf.lineString(points, { name: bufferId })
        const buffered = turf.buffer(pointF, bufferWidth, { units: 'meters' });//缓冲区边界坐标，这是创建缓冲区面的关键
        const coordinates = buffered?.geometry.coordinates;
        let point1s: Array<any> = coordinates[0];
        let degreesArray = SCENE_TOOL.pointsToDegreesArray(point1s);
        SCENE_TOOL.addBufferPolyogn(viewer, Cesium.Cartesian3.fromDegreesArray(degreesArray), bufferId);
    }
    private static addBufferPolyogn(viewer: Cesium.Viewer, positions: Array<any>, bufferId: string) {
        console.log("turf缓冲区坐标:", positions);
        viewer.entities.add({
            id: bufferId,
            polygon: {
                hierarchy: new Cesium.PolygonHierarchy(positions),
                material: Cesium.Color.RED.withAlpha(0.6),
                classificationType: Cesium.ClassificationType.BOTH,
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 15000),

            },
        });
        SCENE_TOOL.LineBufferIds.push(bufferId)
    }
    // 坐标点格式转换
    private static pointsToDegreesArray(points: Array<any>) {
        const degreesArray: Array<any> = [];

        points.map((item) => {
            degreesArray.push(item[0]);
            degreesArray.push(item[1]);
        });

        return degreesArray;

    }
    public static async loadScene(projectId: string): Promise<Scene> {
        return new Scene()

    }
    public static pickModel(viewer: Cesium.Viewer, windowPosition: Cesium.Cartesian2): any {
        let picked = viewer.scene.pick(windowPosition);
        if (Cesium.defined(picked)) {
            let entity = Cesium.defaultValue(picked.id, picked.primitive.id);
            if (entity instanceof Cesium.Entity) {
                if (entity.model) {
                    console.log('model');
                    return entity;
                }
            }
        }
        return null;
    }
    public static async getEntityProperty(viewer: Cesium.Viewer, entity: Cesium.Entity, model: Cesium.Model) {
        var position: Cesium.Cartesian3 | undefined = entity.position?.getValue(viewer.clock.currentTime);
        if (Cesium.defined(position)) {
            // 转换为经纬度坐标
            var cartographic = Cesium.Cartographic.fromCartesian(position as Cesium.Cartesian3);
            var longitudeString = Cesium.Math.toDegrees(cartographic.longitude);
            var latitudeString = Cesium.Math.toDegrees(cartographic.latitude);

            const h = await getPositionHeight(viewer, [[longitudeString, latitudeString, 0]])

            var altitude = h[0].height//cartographic.height
            //console.log(cartographic,'当前坐标：经度=' + longitudeString + ', 纬度=' + latitudeString);
            // 获取模型的比例
            let scale = entity.model?.scale?.getValue(viewer.clock.currentTime);
            console.log('scale=' + scale);

            const hpr = SCENE_TOOL.takeHpr(entity.orientation?.getValue(viewer.clock.currentTime, new Cesium.Quaternion()), position as Cesium.Cartesian3)
            console.log(hpr)
            return Promise.resolve({
                longitude: longitudeString, latitude: latitudeString, scale, altitude, distance: cartographic.height - altitude,
                heading: Cesium.Math.toDegrees(hpr.heading),
                picth: Cesium.Math.toDegrees(hpr.pitch), roll: Cesium.Math.toDegrees(hpr.roll)
            })

        }



        return null
    }
    public static takeHpr(orientation: Cesium.Quaternion, position: Cesium.Cartesian3): Cesium.HeadingPitchRoll {
        //1、由四元数计算三维旋转矩阵
        var mtx3 = Cesium.Matrix3.fromQuaternion(orientation, new Cesium.Matrix3());
        //2、计算四维转换矩阵：
        var mtx4 = Cesium.Matrix4.fromRotationTranslation(mtx3, position, new Cesium.Matrix4());
        //3、计算角度：
        var hpr = Cesium.Transforms.fixedFrameToHeadingPitchRoll(mtx4, Cesium.Ellipsoid.WGS84, Cesium.Transforms.eastNorthUpToFixedFrame, new Cesium.HeadingPitchRoll());
        return hpr;
    }
    //> 获取模型颜色与透明度
    public static getColor(colorName: string, alpha: number) {
        const color = Cesium.Color[colorName.toUpperCase()];
        return Cesium.Color.fromAlpha(color, alpha);
    }
    //> 获取目标颜色和图元的源颜色之间混合的不同模式  HIGHLIGHT将源颜色乘以目标颜色 REPLACE将源颜色替换为目标颜色 MIX将源颜色和目标颜色混合在一起
    public static getColorBlendMode(colorBlendMode: any) {
        return Cesium.ColorBlendMode[colorBlendMode.toUpperCase()];
    }
    public static showSilhouette(entity: Cesium.Entity, show: boolean = true, colorName: string = 'green') {
        if (!Cesium.defined(entity)) return;
        entity.model.silhouetteColor = show ? SCENE_TOOL.getColor(colorName, 1) : undefined;
        entity.model.silhouetteSize = show ? 5 : undefined;
    }
    public static async takeMapRouteInfo(p1: Array<number>, p2: Array<number>) {
        const param = {
            "points": [
                p1, p2
            ],
            "profile": "car",
            "elevation": true,
            "instructions": true,
            "locale": "zh_CN",
            "points_encoded": false,
            "points_encoded_multiplier": 1000000,
            "snap_preventions": [
                "ferry"
            ],
            "details": [
                "road_class",
                "road_environment",
                "max_speed",
                "average_speed"
            ],
            "alternative_route.max_paths": 3,
            "algorithm": "alternative_route"
        }
        return axios.create({
            timeout: 60000 * 20,
            baseURL: '/'
        }).post(MAPROUTE_URL, param)
    }
    /**
     * 为变电站1创建地形压平
     * @param viewer Cesium查看器
     * @param customHeight 可选的自定义压平高度
     * @returns 是否成功创建地形压平
     */
    public static async flattenSubstation1Terrain(viewer: Cesium.Viewer, customHeight?: number): Promise<boolean> {
        return flattenTerrainForSubstation1(viewer, customHeight);
    }
}
