import { addModel_glb } from "../common/viewer";
import type { FlyToOption } from "../common/viewer";
import {MODEL_STATIC_URL} from "@/config/global.js"
import { addLabelNum, DivLabel } from '../common/divLabel';
import * as Cesium from 'cesium';
// import * as Cesium from 'cesium';

export class Scene {
    public static tipId:string = "tooltipDiv"
    private viewer: Cesium.Viewer;
    id? : string;
    projectId?: string;
    scenceDesc?:string
    towerId?:string
    scenceStage?:string
    scenceCoordinate:Array<number>= [114.445076,22.576387]
    scenceSize?:Array<number>
    scenceAngle?:number
    elements: Array<SceneElement> = []
    constructor(viewer: Cesium.Viewer){
        this.viewer = viewer;
        this.scenceStage = '05'
        this.scenceAngle = 0
        this.scenceDesc = 'A2组塔施工场景'
        this.scenceSize = [20,10]
        this.elements.push({
            count:1,
            isShow:1,
            position:[114.445076,22.576387],
            scale:0.001,
            angle:Math.PI/180 * 90,
            model:{
                id:'221321343243',
                modelName:'吊机1号',
                modelType:'吊装设备',
                modelFormat:'glb',
                modelUrl:`${MODEL_STATIC_URL}/gltf/basemodel/nissan_titan.glb`
            }
        })
    }
    
    start = ():void=>{
        const viewConfig: FlyToOption = {
            longitude: this.scenceCoordinate[0] || 0,
            latitude: (this.scenceCoordinate[1] || 0) - 0.002,
            height: 200,
            heading: 0,
            pitch: -1 * Math.PI / 10,
            duration: 1
        };

      try{
      this.flyTo(viewConfig);
        }catch(ex){

      }finally{
        const tipElement = document.getElementById(Scene.tipId);
        if (tipElement) {
            tipElement.style.display = "block";
        }
      }
      // 点
//       const point = this.viewer.entities.add({
//     position: Cesium.Cartesian3.fromDegrees(this.scenceCoordinate[0], this.scenceCoordinate[1]),
//     point: {
//         pixelSize: 50,
//         color: Cesium.Color.RED
//     }
// });
      const point = this.viewer.entities.add({
    position: Cesium.Cartesian3.fromDegrees(114.44484223330248, 
        22.57610202627816,100),
    point: {
        color: Cesium.Color.SKYBLUE,
        pixelSize: 10,
        outlineColor: Cesium.Color.YELLOW,
        outlineWidth: 3,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    }
});
const point1 = this.viewer.entities.add({
    position: Cesium.Cartesian3.fromDegrees(114.44544637827033, 
        22.577013352649537),
    point: {
        color: Cesium.Color.SKYBLUE,
        pixelSize: 10,
        outlineColor: Cesium.Color.RED,
        outlineWidth: 3,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    }
});
const rectangle = this.viewer.entities.add({
    rectangle: {
      coordinates: Cesium.Rectangle.fromDegrees(114.44484223330248, 
        22.57610202627816,114.44544637827033, 22.577013352649537),
      material: Cesium.Color.GREEN.withAlpha(0.5),
    },
  });

  //viewer.zoomTo(rectangle);
  //return 
    //   const polygon = viewer.entities.add({
    //     polygon: {
    //         hierarchy: new Cesium.PolygonHierarchy(Cesium.Cartesian3.fromDegreesArray([
    //             114.44488575706369, 22.576501794159597,
    //             114.44514691755646, 22.578378621495865,
    //             114.44536430194566, 22.576678307665762,
    //             114.4450920751451, 22.57613457480355
    //         ])),
    //         material: Cesium.Color.GREEN.withAlpha(0.5)
    //     }
    // });
    this.loadSceneElement()
    }
    loadEntity = (ele:SceneElement)=>{
        this.viewer.scene.globe.depthTestAgainstTerrain = true;
 
    const e = this.viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(ele.position[0], ele.position[1]),
      model: {
        uri: ele.model?.modelUrl,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        minimumPixelSize: 128,
        maximumScale: 100,
        scale: ele.scale
      },
    //   label: {
    //     text: "紧贴地面",
    //     font: "14pt sans-serif",
    //     //heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
    //     horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
    //     verticalOrigin: Cesium.VerticalOrigin.BASELINE,
    //     fillColor: Cesium.Color.BLACK,
    //     showBackground: true,
    //     backgroundColor: new Cesium.Color(1, 1, 1, 0.7),
    //     backgroundPadding: new Cesium.Cartesian2(8, 4),
    //     disableDepthTestDistance: Number.POSITIVE_INFINITY, // draws the label in front of terrain
    //   },
    });
 
    this.viewer.trackedEntity = e;


    }
    loadSceneElement = ()=>{
        for(var i =0;i<this.elements.length;i++){
            const ele = this.elements[i]
            if(!ele.isShow) continue
            //addModel_glb(viewer,{position:ele.position,angle:ele.angle || ele.model?.angle,url:ele.model?.modelUrl,scale:ele.scale || ele.model?.scale,
            //id:ele.model?.id,name:ele.model?.modelName})
            this.loadEntity(ele)
            const divHtmlStyle = '<div style="width:auto;height:auto;background:rgba(64,158,255,0.9);color: white;padding:10px;border-radius: 10px;font-size: 15px">'
      
          +  `${ele.model?.modelName}(${ele.count}台)
          </div>`;
            let val = {
                id: `${ele.model?.id}_text`,
                text: ele.model?.modelName,
                viewer: this.viewer,
                position: ele.position as [number, number, number],
                // title: '广告牌'
                divHTML: divHtmlStyle,
                bgColor: `rgba(64,158,255,0.9)`,
                fillColor: `rgba(255,255,255,1)`,
                font: '14px sans-serif'
              }
              
              // label.addDynamicLabelZnd(this.viewer, val)
              const divLabel = new DivLabel();
              divLabel.addDynamicLabel(this.viewer, val);
              addLabelNum(val);
        }
    }

    flyTo(option: FlyToOption) {
        const { longitude, latitude, height, heading, pitch, roll, duration } = option;
        this.viewer.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
            orientation: {
                heading: Cesium.Math.toRadians(heading || 0.0),
                pitch: Cesium.Math.toRadians(pitch || -90.0),
                roll: roll || 0.0,
            },
            duration: duration || 3,
        });
    }
    closeTip = ():void => {
        const tipElement = document.getElementById(Scene.tipId);
        if (tipElement) {
            tipElement.style.display = "none";
        }
    }
}
export interface SceneElement {
    id?:string
    model?: BaseModel
    count:number
    isShow:number
    angle?:number
    scale?:number
    position:Array<number>
}
export interface BaseModel {
    id?:string
    modelName:string
    modelType:string
    modelFormat:string
    modelUrl:string
    scale?:number
    angle?:number
}