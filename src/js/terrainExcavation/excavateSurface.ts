import * as Cesium from '@/Cesium'
// import * as Cesium from 'cesium'
type ExcavateSurfaceDataType = {
    type: string,
    height: number,
    coordinates: [number, number, number][],
    color: string,
    clockwise: boolean,
    msg?: string
}
/**
 * 计算两点之间的水平距离和方位角
 * @param {array} origin [lon, lat, height]
 * @param {array} target [lon, lat, height]
 * @returns 水平距离和方位角
 */
let distance_ang = (origin: [number, number, number?], target: [number, number, number?]) => {
    let geodesic = new Cesium.EllipsoidGeodesic(Cesium.Cartographic.fromDegrees(...origin), Cesium.Cartographic.fromDegrees(...target));
    return [geodesic.surfaceDistance, geodesic.endHeading];
}

/**
 * 本函数计算两个经纬度坐标的垂直方向法向量 orientation确定垂直法向量的正负方向
 * @param point1 起点的经纬度 [lon lat]
 * @param point2 终点的经纬度 [lon lat]
 * @param orientation 方向是否取反
 * @returns 方向向量
 */
let getNormalVector = (point1: [number, number], point2: [number, number], orientation = false) => {
    let len_ang = distance_ang(point1, point2) // 计算两个点的夹角
    return orientation == false ? [Math.sin(Math.PI / 2 + len_ang[1]), Math.cos(Math.PI / 2 + len_ang[1]), 0] : [-1 * Math.sin(Math.PI / 2 + len_ang[1]), -1 * Math.cos(Math.PI / 2 + len_ang[1]), 0]
}

/**
 * 函数计算原始点 到某直线（起点-终点）的垂足点
 * @param origin 原始点的经纬度 [lon lat] 
 * @param start 直线连接点的经纬度 [lon lat]
 * @param end 直线连接点的经纬度 [lon lat]
 * @returns 垂足点的经纬度 [lon lat]
 */
let getFootPoint = (origin: [number, number], start: [number, number], end: [number, number]): [number, number] => {
    // 计算点到线段直线的垂直点（垂足）
    var A = start[0] - end[0]
    var B = end[1] - start[1]
    var C = end[0] * start[1] - start[0] * end[1]
    if (A * A + B * B < 1e-13) {
        return start; // start与end重叠
    } else if (Math.abs(B * origin[0] + A * origin[1] + C) < 1e-13) {
        return origin; // point在直线上(start_end)
    } else {
        var longitude = (A * A * origin[0] - A * B * origin[1] - B * C) / (A * A + B * B);
        var latitude = (-B * A * origin[0] + B * B * origin[1] - A * C) / (A * A + B * B);
        return [longitude, latitude]
    }
}

export function loadExcavateSurface(jsonFile: ExcavateSurfaceDataType, viewer: Cesium.Viewer) {
    if (jsonFile.type != 'excavateSurface') {
        console.error('开挖地表效果创建错误 数据jsonFile=', jsonFile)
        return null
    }
    if (jsonFile.coordinates.length < 3) {
        console.error('开挖地表效果创建错误 坐标数量小于3 jsonFile=', jsonFile)
        return null
    }
    // 创建裁剪屏幕合集 ClippingPlaneCollection 对象
    let origin: [number, number] = [jsonFile.coordinates[0][0], jsonFile.coordinates[0][1]]
    let clippingPlanes = new Cesium.ClippingPlaneCollection({
        modelMatrix: Cesium.Transforms.eastNorthUpToFixedFrame(Cesium.Cartographic.toCartesian(Cesium.Cartographic.fromDegrees(origin[0], origin[1], 0))),
        edgeColor: Cesium.Color.WHITE,
        edgeWidth: 3
    })
    for (let i = 0; i < jsonFile.coordinates.length; i++) {
        // for (let i = 0; i < 2; i++) {
        let start: [number, number] = [jsonFile.coordinates[i][0], jsonFile.coordinates[i][1]]
        let end: [number, number] = (i + 1) == jsonFile.coordinates.length ? [jsonFile.coordinates[0][0], jsonFile.coordinates[0][1]] : [jsonFile.coordinates[i + 1][0], jsonFile.coordinates[i + 1][1]]
        let normal = getNormalVector(start, end, jsonFile.clockwise)
        let footPoint = getFootPoint(origin, start, end)
        let distance = Cesium.Cartesian3.distance(Cesium.Cartesian3.fromDegrees(...origin), Cesium.Cartesian3.fromDegrees(...footPoint))
        clippingPlanes.add(new Cesium.ClippingPlane(new Cesium.Cartesian3(...normal), -1 * distance))
    }
    viewer.scene.globe.clippingPlanes = clippingPlanes

    // 创建多边形实体并添加到场景中
    var polygonPositions = [] // 创建多边形的坐标
    let minHeight = jsonFile.coordinates[0][2]
    for (let i = 0; i < jsonFile.coordinates.length; i++) {
        polygonPositions.push(...jsonFile.coordinates[i])
        if (minHeight > jsonFile.coordinates[i][2]) {
            // 更新最低点的高程
            minHeight = jsonFile.coordinates[i][2]
        }
    }
    let color = jsonFile.color ? `${jsonFile.color}` : '#e0c8ae'
    var polygonEntity = viewer.entities.add({
        id: 'Polygon@excavateSurface',
        name: 'Polygon@excavateSurface',
        polygon: {
            hierarchy: Cesium.Cartesian3.fromDegreesArrayHeights(polygonPositions),
            // hierarchy: new Cesium.PolygonHierarchy(polygonPositions),
            material: Cesium.Color.fromCssColorString(color),
            closeTop: false, // 这个要设置为false
            extrudedHeight: minHeight + jsonFile.height,
            perPositionHeight: true // 这个要设置true
        },
    });
    // viewer.zoomTo(polygonEntity)
    return polygonEntity
} 

export function clearExcavateSurface(viewer: Cesium.Viewer) {
    console.log('电缆沟 clearExcavateSurface', viewer.scene.globe.clippingPlanes)
    viewer.entities.removeById('Polygon@excavateSurface')
    viewer.scene.globe.clippingPlanes = undefined
}