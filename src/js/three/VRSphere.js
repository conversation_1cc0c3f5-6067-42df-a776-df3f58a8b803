import * as THREE from 'three';

export default class VRSphereTool {
    constructor(myThree) {
        this.myThree = myThree;    
        this.VRSphere = null;
    }
    createVRSphere = (imgUrl, radius = 5000, loadCallBack=(flag)=>{}) => {
        this.removeVRSphere(); // 移除之前已经加载的全景图
        const geometry = new THREE.SphereGeometry(radius, 500, 500);
        geometry.scale(-1, 1, 1); // Invert the geometry to match the texture
        const material = new THREE.MeshBasicMaterial({map: new THREE.TextureLoader().load(imgUrl, (result) => {
                loadCallBack(false)
            }, (xhr) => {
                console.log('加载中')
            }, (err) => {
                console.log(err)
                loadCallBack(false)
            })});
        this.VRsphere = new THREE.Mesh(geometry, material);
        this.myThree.scene.add(this.VRsphere);    
    }
    removeVRSphere = () => {
        if (this.VRsphere) {
            this.myThree.scene.remove(this.VRsphere);
            this.VRsphere = null;
        }
    }
    onMouseDown = (event) => {
        event.preventDefault();
        var mouse = new THREE.Vector2();
        mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        let raycaster = new THREE.Raycaster()
      
        // 通过屏幕坐标构建射线
        raycaster.setFromCamera(mouse, this.myThree.camera);

        // 计算射线与球体的相交情况
        let intersects = raycaster.intersectObject(this.VRsphere);

        if (intersects.length > 0) {
            // 获取相交点的坐标
            let intersection = intersects[0].point;
            console.log('debug onMouseDown:', intersection);
        }
    }
}
