import * as THREE from 'three';
import { CAMERA_POSITION } from '@/config/global.js';


export function createThreeScene (htmlTag, background='#1E90FF') {
    if (htmlTag.tagName != 'CANVAS') {
        console.error('调用创建threejs场景需要用到html画布对象');
        return;
    }
    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({
        canvas: htmlTag,
        antialias: true, // 执行抗锯齿
        alpha: true // 运行透明渲染
    });
    renderer.setSize(window.innerWidth, window.innerHeight);
    // renderer.autoClear = false;

    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(background);

    // 创建光源
    const light1 = new THREE.AmbientLight('#999999');
    scene.add(light1);
    const light2 = new THREE.PointLight('#ffffff', 0.9);
    light2.position.set(-3000, 9000, -3000);
    scene.add(light2);

    // 创建一个透视相机
    const camera = new THREE.PerspectiveCamera();
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.far = 100000;
    camera.position.set(CAMERA_POSITION.x, CAMERA_POSITION.y , CAMERA_POSITION.z);
    camera.lookAt(0, 0, 0);
    camera.updateProjectionMatrix()

    // 添加监听浏览器尺寸变化更新画面
    window.addEventListener('resize', () => {
        // Update camera
        camera.aspect = window.innerWidth / window.innerHeight
        camera.updateProjectionMatrix()
        // Update renderer
        renderer.setSize(window.innerWidth, window.innerHeight)
        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
    })




    // 监听鼠标事件
    // window.addEventListener('click', (event) => { this.sceneEvent(event) }, false)
    // window.addEventListener('mousedown', (event) => { this.sceneEvent(event) }, false)
    // window.addEventListener('mouseup', (event) => { this.sceneEvent(event) }, false)
    // window.addEventListener('mousemove', (event) => { this.sceneEvent(event) }, false)

    return { scene: scene, renderer: renderer, camera: camera };
}

export function createBox (conf) {
    const boxGeom = new THREE.BoxGeometry(conf.width, conf.height, conf.depth)
    boxGeom.translate(0, conf.height / 2, 0)
    // boxGeom.rotateX(Math.PI / 2)
    const meshLamberMat = new THREE.MeshLambertMaterial({
        color: conf.color,
        side: THREE.DoubleSide
    })
    meshLamberMat.transparent = true // 是否允许透明
    meshLamberMat.opacity = conf.opacity // 透明度

    const box = new THREE.Mesh(boxGeom, meshLamberMat)
    if (conf.shadow) {
        box.castShadow = true
        box.receiveShadow = true
    }
    box.name = conf.name
    box.userData = conf.userData
    box.position.set(conf.position.x, conf.position.y, conf.position.z)

    // 长方体作为EdgesGeometry参数创建一个新的几何体
    const edges = new THREE.EdgesGeometry(boxGeom);
    const edgesMaterial = new THREE.LineBasicMaterial({
        color: conf.lineColor,
    })
    const line = new THREE.LineSegments(edges, edgesMaterial);
    box.add(line);
    return box
}

export async function createPlane (conf) {
    const planeGeom = new THREE.PlaneGeometry(conf.width, conf.height)
    if (conf.type === 2) {
        const textureLoader = new THREE.TextureLoader()
        return new Promise((resolve, reject) => {
            textureLoader.load(conf.img, function (texture) {
                const material = new THREE.MeshLambertMaterial({
                    map: texture
                })
                material.transparent = true // 是否允许透明   不设置默认渲染出来透明的部分为
                material.opacity = conf.opacity
                const plane = new THREE.Mesh(planeGeom, material)
                plane.receiveShadow = true
                plane.name = conf.name
                resolve(plane)
            })
        })
    } else if (conf.type === 1) {
        const material = new THREE.MeshLambertMaterial({
            color: conf.color,
            side: THREE.DoubleSide
        })
        material.transparent = true // 是否允许透明   不设置默认渲染出来透明的部分为
        material.opacity = conf.opacity
        const plane = new THREE.Mesh(planeGeom, material)
        plane.receiveShadow = true
        plane.name = conf.name
        return new Promise((resolve, reject) => {
            resolve(plane)
        })
    }
}

export async function addPlane (myThree, planeConf) {
    if (myThree.plane === undefined) {
        const plane = await createPlane(planeConf)
        plane.rotateX(-Math.PI / 2)
        myThree.scene.add(plane)
        myThree.plane = plane
        // 显示坐标轴
        if (planeConf.enableAxes) {
            axesOn(myThree)
        }
    }
}

export function removePlane(myThree) {
    if (myThree.plane) {
        myThree.scene.remove(myThree.plane)
        myThree.plane = undefined
    }
}

export function axesOn (myThree) {
    if (myThree._axes === undefined) {
        myThree._axes = new THREE.AxesHelper(500)
        // 将坐标轴添加进场景
        myThree.scene.add(myThree._axes)
    }
}

export function axesOff (myThree) {
    if (myThree._axes) {
        myThree.scene.remove(this._axes)
        myThree._axes = undefined
    }
}
