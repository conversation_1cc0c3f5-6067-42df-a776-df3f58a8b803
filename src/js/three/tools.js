import * as THREE from 'three';
import { CSS3DRenderer, CSS3DObject} from 'three/addons/renderers/CSS3DRenderer.js';
import { CAMERA_POSITION } from '@/config/global.ts';
export function convert3To2(x,y,z,camera,renderer){
    const worldVectorsss = new THREE.Vector3(x, y, z)
    return convertVector3To2(worldVectorsss,camera,renderer)
}
export function convertVector3To2(worldVectorsss,camera,renderer){
    const stdVectorsss = worldVectorsss.project(camera);
    //console.log(`stdVectorsss`,stdVectorsss)
    const dom = renderer.domElement
    const a = dom.clientWidth / 2;
    const b = dom.clientHeight / 2;
    //标准设备坐标转屏幕坐标x,y
    const x = Math.round(stdVectorsss.x * a + a);
    const y = Math.round(-stdVectorsss.y * b + b);
    return {x,y}
}
export function  conver2ToVector3(event, camera,renderer,scene) {
    event.preventDefault()
    const { clientX, clientY } = event
    const dom = renderer.domElement
    // 拿到canvas画布到屏幕的距离
    const domRect = dom.getBoundingClientRect()
    // 计算标准设备坐标
    const x = ((clientX - domRect.left) / dom.clientWidth) * 2 - 1
    const y = -((clientY - domRect.top) / dom.clientHeight) * 2 + 1
    const vector = new THREE.Vector3(x, y, 0)
    // 转世界坐标
    const worldVector = vector.unproject(camera)
    // 射线
    const ray = worldVector.sub(camera.position).normalize()
    // 射线投射对象
    const raycaster = new THREE.Raycaster(camera.position, ray)
    raycaster.camera = camera

    //返回射线选中的对象 //第一个参数是检测的目标对象 第二个参数是目标对象的子元素
    const intersects = raycaster.intersectObjects(scene.children)
    var result = null
    if (intersects.length > 0) {
      // console.log('捕获到对象', intersects)
      result = intersects[0].point
    } else {
      // console.log('没捕获到对象')
    }
    // console.log(`坐标`,result)
    return result
}
export function addLable(html,worldVectorsss,camera,renderer){
    // 检查或创建固定容器
    let container = document.getElementById('css3d-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'css3d-container';
        container.style.position = 'fixed';
        container.style.top = '0';
        container.style.left = '0';
        container.style.width = '100%';
        container.style.height = '100%';
        container.style.pointerEvents = 'none';
        container.style.zIndex = '1';
        container.style.overflow = 'hidden';
        document.body.appendChild(container);
    }

    const position = new THREE.Vector3(worldVectorsss.x, worldVectorsss.y, worldVectorsss.z)
    const point = convertVector3To2(new THREE.Vector3(worldVectorsss.x, worldVectorsss.y, worldVectorsss.z),camera,renderer)
    let element = document.createElement("div");
    element.style.opacity = 0.75;
    element.innerHTML = html;
    
    let css3DObject = new CSS3DObject(element);
    const css3Renderer = new CSS3DRenderer(css3DObject);
    css3Renderer.domElement.style.position = 'absolute';
    css3Renderer.domElement.style.top = `${point.y}px`;
    css3Renderer.domElement.style.left = `${point.x}px`;
    css3Renderer.domElement.style.pointerEvents = 'none';
    
    container.appendChild(css3Renderer.domElement);
    return {css3Renderer,position}
}

export function remodeCSS3DRenderer(cssRenderer){
    // 销毁CSS3DRenderer实例
    cssRenderer.domElement.parentNode.removeChild(cssRenderer.domElement);
}
