import * as Cesium from 'cesium';
// import * as Cesium from "cesium"
import { MODEL_STATIC_URL } from '@/config/global.js'
import { flyTo } from "../common/viewer"
import { addLabelNum, deleteZndLabel } from '../common/divLabel'

export default class ZhongNanDianHandler {
    viewer: Cesium.Viewer
    $cache: any
    buildingConfig: any
    manager: Cesium.PrimitiveCollection
    tilesetD3dms: any
    zndLable: any
    constructor(viewer: Cesium.Viewer, cache: any, buildingConfig: any) {
        this.viewer = viewer
        this.$cache = cache
        this.buildingConfig = buildingConfig
        this.manager = new Cesium.PrimitiveCollection()
        this.manager.destroyPrimitives = false
        this.viewer.scene.primitives.add(this.manager)
        this.tilesetD3dms = []
        this.zndLable = []
    }

    // 关闭重难点场景
    close() {
        for (let i = 0; i < this.tilesetD3dms.length; i++) {
            this.manager.remove(this.tilesetD3dms[i])
          }
          this.tilesetD3dms = []
          deleteZndLabel(this.viewer)
    }
    load(key: string) {
        if (key != 'znd_close' && this.buildingConfig[key] == undefined) {
            return false
        }
        let showZndDetail = this.buildingConfig[key]['detail'] // 获取重难点详情简介文字
        let url = `${MODEL_STATIC_URL}/${this.$cache.getProjectCode()}${this.buildingConfig[key]['url']}`;
        const tileset = new Cesium.Cesium3DTileset({
            url: new Cesium.Resource({ url: url }),
            maximumScreenSpaceError: 16,
            // modelMatrix: m //形状矩阵
        } as any)
        this.tilesetD3dms.push(tileset)
        this.manager.add(tileset)
        if (this.buildingConfig[key]['view']) {
            this._flytoPosition(this.buildingConfig[key]['view'], this.buildingConfig[key]['heading'], this.buildingConfig[key]['pitch'], 0)
        } else {
            this.viewer.zoomTo(tileset);
        }
        for (var i = 0; i < this.buildingConfig[key]['fonts'].length; i++) {
            var label = this.buildingConfig[key]['fonts'][i]
            this._addZndLabel(label['font'], label['center'], label['size'])
        }
        for (var i = 0; i < this.buildingConfig[key]['polygon'].length; i++) {
            var polygon = this.buildingConfig[key]['polygon'][i]
            this._addPolyGon(polygon)
        }
        return true
    }
    private _flytoPosition(position: number[], heading = 0, pitch = -1 * Math.PI / 10, isTower = 1) {
        let viewConfig = {
            longitude: position[0],
            latitude: position[1],
            height: position[2],
            heading: heading,
            pitch: pitch,
            duration: 1
        }
        flyTo(this.viewer, viewConfig)
    }
    private _addZndLabel(text: string, position: number[], size: string) {
        const divHtmlStyle = '<div style="width:auto;height:auto;background:rgba(64,158,255,0.9);color: white;padding:10px;border-radius: 10px;font-size: 15px">'
        let divHtml = divHtmlStyle
            + text
            + `</div>`;
        let val = {
            id: text,
            text: text,
            viewer: this.viewer,
            position: position as [number, number, number],
            // title: '广告牌'
            divHTML: divHtml,
            bgColor: `rgba(64,158,255,0.9)`,
            fillColor: `rgba(255,255,255,1)`,
            font: size
        }
        
        // label.addDynamicLabelZnd(this.viewer, val)
          addLabelNum(this.viewer, val, true)
    }
    private _addPolyGon(positions: any) {
        var dynamicPositions = new Cesium.CallbackProperty(() => {
            return new Cesium.PolygonHierarchy(positions)
        }, false)
        this.zndLable.push(this.viewer.entities.add(new Cesium.Entity({
            polygon: {
                hierarchy: dynamicPositions,
                material: Cesium.Color.fromCssColorString('rgba(0,225,255,0.31)'),
                // material: Cesium.Color.RED.withAlpha(0.6),
                classificationType: Cesium.ClassificationType.BOTH // 贴地表和贴模型,如果设置了，这不能使用挤出高度
            }
        })))
    }
}