import './assets/main.css'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'

// 直接使用环境变量
const BACKEN_URL = import.meta.env.VITE_BACKEN_URL;

import axios from 'axios'
import { createPinia } from 'pinia'
// @ts-ignore - Missing type declarations in package
import piniaPersist from 'pinia-plugin-persist'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 引入Cesium的源码
import * as Cesium from '@/Cesium'
import '@/Cesium/Widgets/widgets.css' // 引入Cesium的Widget样式

// import * as Cesium from 'cesium'
// import 'cesium/Source/Widgets/widgets.css' // 引入Cesium的Widget样式

import Cache from './js/cache'

const MAX_ERRORS = 10; // 定义允许的最大错误次数

// 添加全局类型声明
declare global {
  interface Window {
    Cesium: typeof Cesium; // Use the imported Cesium module type
    viewer?: any;
    acceptMessage?: (msg: any) => void;
    CommandTest: {
      /**
       * Safe version of the HandleResponse function.
       * Can safely handle any input type.
       */
      HandleResponse: (jsonStr: any) => void;
    };
    /**
     * 全局WebGL错误处理变量
     */
    displayWebGLError?: (message: string) => void;
    webGLErrorDisplayed?: boolean;
    /**
     * Cesium fetchImage原始方法引用
     */
    _originalFetchImage?: any;
    /**
     * 标记是否已应用fetchImage补丁
     */
    _fetchImagePatchApplied?: boolean;
    /**
     * 记录已知失败的资源URL
     */
    _failedResources?: Set<string>;
    /**
     * 记录每个URL的错误次数
     */
    _imageErrorCounts?: Map<string, number>;
    /**
     * 重置图像加载错误计数的辅助方法
     */
    resetImageLoadingErrors?: () => void;
    /**
     * 记录已返回空值的URL，用于减少日志
     */
    _emptyResponseUrls?: Set<string>; // 新增，用于跟踪返回空响应的URL
  }
}

// 安全版本的HandleResponse函数，避免JSON.parse错误导致程序崩溃
const safeHandleResponse = (jsonStr: any) => {
  console.log('CommandTest处理响应:', jsonStr);
  
  try {
    // 如果已经是对象，直接处理
    if (typeof jsonStr === 'object' && jsonStr !== null) {
      console.log('CommandTest处理对象:', jsonStr);
      return;
    }
    
    // 如果是字符串，尝试解析JSON
    if (typeof jsonStr === 'string') {
      const obj = JSON.parse(jsonStr);
      console.log('CommandTest解析JSON结果:', obj);
    } else {
      console.log('CommandTest收到非字符串非对象数据:', typeof jsonStr);
    }
  } catch (e) {
    console.error('CommandTest处理响应时出错:', e);
    console.log('原始数据:', jsonStr);
  }
};

// 确保全局CommandTest对象存在并可以安全处理所有类型的输入
window.CommandTest = window.CommandTest || {};
window.CommandTest.HandleResponse = safeHandleResponse as (jsonStr: any) => void;

// 用于调试的函数，输出CommandTest的状态
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function debugCommandTest() {
  console.log('CommandTest状态:', window.CommandTest);
}

/**
 * 增强Cesium渲染引擎健壮性，预防shader编译错误导致的渲染中断
 * 此函数会拦截shader编译错误并提供备用shader
 */
function patchCesiumShaderSystem() {
  if (!window.Cesium) {
    console.warn('Cesium未初始化，无法应用shader防护');
    return;
  }

  console.log('正在应用Cesium Shader系统防护...');
  
  // 保存原始Shader创建方法
  const originalCreateShaderProgram = (window.Cesium as any).ShaderProgram.fromCache;
  const originalCreateShaderSource = (window.Cesium as any).ShaderSource.createCombinedVertexShader;
  const originalCreateFragmentShaderSource = (window.Cesium as any).ShaderSource.createCombinedFragmentShader;
  
  // 重写ShaderSource.createCombinedVertexShader以添加错误处理
  (window.Cesium as any).ShaderSource.createCombinedVertexShader = function(shaderSource: any) {
    try {
      return originalCreateShaderSource.call(this, shaderSource);
    } catch (error) {
      console.error('顶点着色器组合错误被拦截:', error);
      
      // 返回包含常用varying变量的简单顶点着色器
      return `
        attribute vec3 position;
        attribute vec2 st;
        attribute vec3 normal;
        uniform mat4 modelViewMatrix;
        uniform mat4 projectionMatrix;
        uniform mat3 normalMatrix;
        varying vec2 v_texCoord_0;
        varying vec2 v_st;
        varying vec3 v_positionEC;
        varying vec3 v_normalEC;
        
        void main() {
          v_texCoord_0 = st;
          v_st = st;
          v_positionEC = (modelViewMatrix * vec4(position, 1.0)).xyz;
          v_normalEC = normalMatrix * normal;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `;
    }
  };
  
  // 重写ShaderSource.createCombinedFragmentShader以添加错误处理
  (window.Cesium as any).ShaderSource.createCombinedFragmentShader = function(shaderSource: any) {
    try {
      return originalCreateFragmentShaderSource.call(this, shaderSource);
    } catch (error) {
      console.error('片元着色器组合错误被拦截:', error);
      
      // 返回包含常用varying变量的简单片元着色器
      return `
        #ifdef GL_ES
          precision highp float;
        #endif
        varying vec2 v_texCoord_0;
        varying vec2 v_st;
        varying vec3 v_positionEC;
        varying vec3 v_normalEC;
        
        void main() {
          // 使用纹理坐标创建简单效果，避免未声明变量错误
          vec2 coord = v_texCoord_0;
          gl_FragColor = vec4(1.0, 0.4 + coord.x * 0.2, 0.8 - coord.y * 0.2, 0.7);
        }
      `;
    }
  };
  
  // Helper function to get a safe fallback vertex shader source string
  function getSafeFallbackVertexShaderSource(): string {
    return `
      attribute vec3 position;
      attribute vec2 st;
      attribute vec3 normal;
      uniform mat4 modelViewMatrix;
      uniform mat4 projectionMatrix;
      uniform mat3 normalMatrix;
      varying vec2 v_texCoord_0;
      varying vec2 v_st;
      varying vec3 v_positionEC;
      varying vec3 v_normalEC;
      
      void main() {
        v_texCoord_0 = st;
        v_st = st;
        v_positionEC = (modelViewMatrix * vec4(position, 1.0)).xyz;
        v_normalEC = normalMatrix * normal;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;
  }

  // Helper function to get a safe fallback fragment shader source string
  function getSafeFallbackFragmentShaderSource(): string {
    return `
      #ifdef GL_ES
        precision highp float;
      #endif
      varying vec2 v_texCoord_0;
      varying vec2 v_st;
      varying vec3 v_positionEC;
      varying vec3 v_normalEC;
      
      void main() {
        vec2 coord = v_texCoord_0;
        gl_FragColor = vec4(1.0, 0.4 + coord.x * 0.2, 0.8 - coord.y * 0.2, 0.7); // Ensure alpha is not 0
      }
    `;
  }

  // 重写ShaderProgram.fromCache以添加错误处理
  if (originalCreateShaderProgram && (window.Cesium as any).ShaderProgram) {
    (window.Cesium as any).ShaderProgram.fromCache = function(options: any) {
      try {
        // Attempt to create the shader program using the original method
        return originalCreateShaderProgram.apply(this, arguments);
      } catch (error) {
        console.error('Shader编译错误被拦截 (ShaderProgram.fromCache):', error);
        
        const vsPreview = typeof options.vertexShaderSource === 'string' 
          ? options.vertexShaderSource.substring(0, 200) + '...' 
          : (typeof options.vertexShaderSource?.sources === 'object' ? JSON.stringify(options.vertexShaderSource.sources).substring(0,200) + '...' : '[复杂顶点着色器]');
        
        const fsPreview = typeof options.fragmentShaderSource === 'string'
          ? options.fragmentShaderSource.substring(0, 200) + '...'
          : (typeof options.fragmentShaderSource?.sources === 'object' ? JSON.stringify(options.fragmentShaderSource.sources).substring(0,200) + '...' : '[复杂片元着色器]');
        
        console.warn('着色器编译失败，尝试使用完全独立的备用着色器。');
        console.debug('原始顶点着色器预览:', vsPreview);
        console.debug('原始片元着色器预览:', fsPreview);

        // Create a completely new, isolated options object for the fallback shader
        const fallbackOptions = {
          gl: options.gl, // Essential: the WebGL rendering context
          logShaderCompilation: true, // Enable logging for fallback shader for debugging
          vertexShaderSource: getSafeFallbackVertexShaderSource(),
          fragmentShaderSource: getSafeFallbackFragmentShaderSource(),
          attributeLocations: {
            position: 0, // Explicitly define attribute locations
            st: 1,
            normal: 2
          },
          // Attempt to carry over the keyword if it exists, might be important for some Cesium internals
          keyword: options.keyword ? options.keyword + '_FALLBACK' : 'FALLBACK_SHADER',
          // rawRenderState: options.rawRenderState // If render state is critical, might need to pass it too
        };

        try {
          console.log('创建备用ShaderProgram实例 (using fallbackOptions)...');
          const fallbackProgram = new (window.Cesium as any).ShaderProgram(fallbackOptions);
          console.log('备用ShaderProgram实例创建成功。');
          return fallbackProgram;
        } catch (fallbackError) {
          console.error('创建备用ShaderProgram实例失败:', fallbackError);
          // If even the fallback fails, something is seriously wrong (e.g., GL context lost)
          // Returning null might lead to further errors down the line, but it's better than re-throwing
          // and potentially getting into an unrecoverable loop if the error isn't handled higher up.
          return null; 
        }
      }
    };
  }
  
  // 重写Scene的渲染方法，捕获可能的着色器相关错误
  const originalRender = (window.Cesium as any).Scene?.prototype?.render;
  if (originalRender && (window.Cesium as any).Scene?.prototype) {
    (window.Cesium as any).Scene.prototype.render = function() {
      try {
        return originalRender.apply(this, arguments);
      } catch (error) {
        console.error('场景渲染错误被拦截 (Scene.render):', error);
        attemptRenderRecovery();
        return false; // Indicate rendering failed
      }
    };
  }
  // The attemptRenderRecovery function remains as is, or can be enhanced later.
  // function attemptRenderRecovery() { ... } // Definition should be elsewhere or ensure it's hoisted/defined before use


  console.log('Cesium Shader系统防护已启用');
}

/**
 * 处理高德地图瓦片请求
 */
function handleAmapTileRequest(url: string, options: any, resourceInstance: any): Promise<HTMLImageElement | HTMLCanvasElement> {
  return new Promise((resolve) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    const timeoutId = setTimeout(() => {
      console.warn(`高德地图瓦片请求超时: ${url}`);
      const fallback = createFallbackImage(url, options);
      resolve(fallback);
    }, 10000); // 10秒超时
    
    img.onload = () => {
      clearTimeout(timeoutId);
      resolve(img);
    };
    
    img.onerror = (error) => {
      clearTimeout(timeoutId);
      console.warn(`高德地图瓦片加载失败: ${url}`, error);
      const fallback = createFallbackImage(url, options);
      resolve(fallback);
    };
    
    try {
      img.src = url;
    } catch (error) {
      clearTimeout(timeoutId);
      console.error(`设置高德地图瓦片URL失败: ${url}`, error);
      const fallback = createFallbackImage(url, options);
      resolve(fallback);
    }
  });
}

/**
 * 创建备用图像
 */
function createFallbackImage(url: string, options: any): HTMLCanvasElement | Promise<HTMLCanvasElement> {
  const canvas = document.createElement('canvas');
  const size = options?.width || options?.height || 256;
  canvas.width = size;
  canvas.height = size;
  const ctx = canvas.getContext('2d');
  
  if (ctx) {
    // 根据URL类型绘制不同的占位符
    if (url.includes('amap') || url.includes('gaode') || url.includes('autonavi')) {
      // 高德地图瓦片占位符
      ctx.fillStyle = '#f0f0f0';
      ctx.fillRect(0, 0, size, size);
      ctx.fillStyle = '#999';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('高德地图', size/2, size/2 - 10);
      ctx.fillText('瓦片加载失败', size/2, size/2 + 10);
    } else {
      // 通用占位符
      ctx.fillStyle = '#e0e0e0';
      ctx.fillRect(0, 0, size, size);
      ctx.fillStyle = '#666';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Image Load Failed', size/2, size/2);
    }
  }
  
  return canvas;
}

/**
 * 增强Cesium资源加载健壮性，主要针对图像加载和请求错误
 */
function patchCesiumResourceHandlers() {
  if (!window.Cesium) {
    console.warn('Cesium未初始化，无法应用资源加载防护');
    return;
  }

  console.log('正在应用Cesium资源加载防护...');
  
  // 确保全局变量初始化
  if (!window._failedResources) {
    window._failedResources = new Set();
  }
  
  if (!window._imageErrorCounts) {
    window._imageErrorCounts = new Map();
  }
  
  // 保存原始fetchImage方法
  if (!window._originalFetchImage) {
    window._originalFetchImage = window.Cesium.Resource.prototype.fetchImage;
  }
  
  // 如果已经应用过补丁，不再重复应用
  if (window._fetchImagePatchApplied) {
    console.log('Resource.fetchImage已经被补丁，不再重复应用');
    return;
  }
  



  
  // 完全替换fetchImage方法，确保所有情况都正确处理
  (window.Cesium as any).Resource.prototype.fetchImage = function(options?: any): Promise<any> | undefined {
    try {
      const urlString: string = this.url || ''; // Explicitly string
      
      // 防止undefined或null选项
      const safeOptions = options || {};
      
      // 检查是否为已知失败的URL
      if (window._failedResources && window._failedResources.has(urlString)) {
        console.debug(`资源 ${urlString} 曾加载失败，尝试重新加载...`);
      }
      
      // 特殊情况: URL为空或无效
      if (!urlString || typeof urlString !== 'string' || urlString.trim() === '') {
        console.debug('Resource.fetchImage收到空URL');
        const fallback = createFallbackImage(urlString, safeOptions);
        if (fallback instanceof Promise) {
          return fallback as Promise<HTMLImageElement | HTMLCanvasElement>; // Type assertion
        }
        return Promise.resolve(fallback as HTMLCanvasElement) as Promise<HTMLImageElement | HTMLCanvasElement>;
      }
      
      // 检查是否为数据URL
      if (urlString.startsWith('data:')) {
        return new Promise((resolve) => {
          const img = new Image();
          img.onload = function() {
            resolve(img);
          };
          img.onerror = function() {
            console.warn('数据URL图像加载失败');
            const fallback = createFallbackImage(urlString, safeOptions);
            if (fallback instanceof Promise) {
              fallback.then(resolve).catch(resolve); // Resolve with fallback even if it errors
            } else {
              resolve(fallback as HTMLCanvasElement);
            }
          };
          img.src = urlString;
        });
      }
      
      // 检查是否为高德地图瓦片URL
      if (urlString.includes('wprd01.is.autonavi.com') || 
          urlString.includes('webrd01.is.autonavi.com') ||
          urlString.includes('webst01.is.autonavi.com') ||
          urlString.includes('webrd0') || urlString.includes('webst0') || urlString.includes('wprd0') || 
          urlString.includes('amap') || urlString.includes('autonavi') || urlString.includes('gaode')) {
        return handleAmapTileRequest(urlString, safeOptions, this) as Promise<HTMLImageElement | HTMLCanvasElement>;
      }

      // 正常加载流程
      const promise = window._originalFetchImage.call(this, options);
      return promise.then((image: HTMLImageElement | HTMLCanvasElement) => {
        // 成功加载，清除错误计数
        if (window._imageErrorCounts && window._imageErrorCounts.has(urlString)) {
          window._imageErrorCounts.delete(urlString);
        }
        if (window._failedResources && window._failedResources.has(urlString)) {
          window._failedResources.delete(urlString);
          console.debug(`资源 ${urlString} 重新加载成功，已从失败列表移除。`);
        }
        return image as HTMLImageElement | HTMLCanvasElement;
      }).catch((error: any) => {
        if (window._imageErrorCounts) {
          const count = (window._imageErrorCounts.get(urlString) || 0) + 1;
          window._imageErrorCounts.set(urlString, count);

          if (count === 1 || count % 5 === 0 || count === MAX_ERRORS) { // Log first, every 5th, and max errors
            console.warn(`图像加载失败 (${count}/${MAX_ERRORS}): ${urlString}`, error);
          }

          if (count >= MAX_ERRORS && window._failedResources) {
            window._failedResources.add(urlString);
            console.error(`图像 ${urlString} 已达到最大失败次数，标记为永久失败。`);
          }
        }
        const fallback = createFallbackImage(urlString, safeOptions);
        if (fallback instanceof Promise) {
          return fallback as Promise<HTMLImageElement | HTMLCanvasElement>;
        }
        return fallback as HTMLCanvasElement;
      });

    } catch (e) {
      console.error('Resource.fetchImage 内部发生意外错误:', e);
      const fallback = createFallbackImage('fetchImage-internal-error', options || {});
      if (fallback instanceof Promise) {
          return fallback as Promise<HTMLImageElement | HTMLCanvasElement>;
      }
      return Promise.resolve(fallback as HTMLCanvasElement);
    };
  };

  // 标记补丁已应用
  window._fetchImagePatchApplied = true;

  // 额外保护：修补ImageryProvider的loadImage方法
  if (window.Cesium.ImageryProvider && window.Cesium.ImageryProvider.loadImage) {
    try {
      const originalLoadImage = window.Cesium.ImageryProvider.loadImage;
      
      (window.Cesium as any).ImageryProvider.loadImage = function(imageryProvider: any, url: any) {
        try {
          // 安全检查：确保imageryProvider不为空
          if (!imageryProvider) {
            console.warn('ImageryProvider.loadImage调用时provider为空');
            return Promise.resolve(createFallbackImage('empty-provider', {}));
          }
          
          // 转换url到Resource对象
          let resourceUrl = url;
          try {
            if (typeof url === 'string' || url instanceof String) {
              resourceUrl = new (window.Cesium as any).Resource({ url: String(url) });
            }
          } catch (urlError) {
            console.error('创建Resource对象失败:', urlError);
            // 如果创建Resource失败，尝试继续使用原始url
          }
          
          // 调用原始方法并捕获任何异常
          let result;
          try {
            // originalLoadImage is static: static loadImage(imageryProvider: ImageryProvider, url: Resource | string)
            result = originalLoadImage(imageryProvider, resourceUrl); // Corrected call
          } catch (loadError) {
            console.error('ImageryProvider.loadImage调用失败:', loadError);
            return Promise.resolve(createFallbackImage(typeof url === 'string' ? url : 'unknown', {}));
          }
          
          // 处理返回值类型
          // 1. 如果是Promise，添加额外的错误处理
          if (result && typeof result.then === 'function') {
            return result.then(
              (image: any) => {
                // 检查图像是否有效
                if (!image) {
                  return createFallbackImage('empty-result', {});
                }
                return image as HTMLImageElement | HTMLCanvasElement;
              },
              (error: any) => {
                console.error('ImageryProvider.loadImage Promise失败:', error);
                return createFallbackImage(typeof url === 'string' ? url : 'promise-error', {});
              }
            );
          } 
          // 2. 如果是图像对象，将其包装成Promise
          else if (result instanceof HTMLImageElement || result instanceof HTMLCanvasElement) {
            return Promise.resolve(result);
          }
          // 3. 如果是其他值，创建替代图像
          else {
            console.warn('ImageryProvider.loadImage返回了非Promise、非图像值:', typeof result);
            return Promise.resolve(createFallbackImage('unknown-result', {}));
          }
        } catch (criticalError) {
          // 捕获所有可能的错误
          console.error('ImageryProvider.loadImage发生严重错误:', criticalError);
          return Promise.resolve(createFallbackImage('critical-error', {}));
        }
      };
      
      console.log('已增强 ImageryProvider.loadImage 方法');
    } catch (patchError) {
      console.error('修补ImageryProvider.loadImage失败:', patchError);
    }
  }
  
  // 修补影像层处理方法
  if ((window.Cesium as any).ImageryLayer && ((window.Cesium as any).ImageryLayer.prototype as any)._requestImagery) {
    try {
      const original_requestImagery = ((window.Cesium as any).ImageryLayer.prototype as any)._requestImagery;
      
      ((window.Cesium as any).ImageryLayer.prototype as any)._requestImagery = function(imagery: any) {
        try {
          // 确保imagery参数有效
          if (!imagery) {
            console.warn('_requestImagery调用时imagery为空');
            return Promise.resolve(undefined);
          }
          
          // 调用原始方法
          let result;
          try {
            result = original_requestImagery.call(this, imagery);
          } catch (requestError) {
            console.error('_requestImagery调用失败:', requestError);
            return Promise.resolve(undefined);
          }
          
          // 确保返回有效值
          if (!result) {
            return Promise.resolve(undefined);
          }
          
          // 处理可能的Promise错误
          if (typeof result.then === 'function') {
            return result.catch((error: any) => {
              console.error('_requestImagery Promise失败:', error);
              return undefined;
            });
          }
          
          return result;
        } catch (criticalError) {
          console.error('_requestImagery发生严重错误:', criticalError);
          return Promise.resolve(undefined);
        }
      };
      
      console.log('已增强 ImageryLayer._requestImagery 方法');
    } catch (patchError) {
      console.error('修补ImageryLayer._requestImagery失败:', patchError);
    }
  }
  
  // 标记已应用补丁
  window._fetchImagePatchApplied = true;

  // 添加全局失败资源和错误计数到window对象，供其他地方访问
  if (!window._failedResources) {
    window._failedResources = new Set();
  }
  
  if (!window._imageErrorCounts) {
    window._imageErrorCounts = new Map();
  }
  
  // 添加全局辅助方法，用于重置资源错误状态
  window.resetImageLoadingErrors = function() {
    if (window._failedResources) {
      window._failedResources.clear();
    }
    
    if (window._imageErrorCounts) {
      window._imageErrorCounts.clear();
    }
    
    console.log('已重置图像加载错误状态');
  };

  /**
   * 创建备用图像，用于替换加载失败的图像资源
   * @param url 原始图像URL，用于调试和可能的重试
   * @param options 原始请求选项，可能包含尺寸等信息
   * @returns 返回一个Canvas元素作为备用图像，或在极端情况下返回空对象
   */
  function createFallbackImage(url: string, options: any): HTMLCanvasElement | Promise<HTMLImageElement | HTMLCanvasElement> {
    // 尝试创建一个256x256的Canvas作为备用图像
    const canvas = document.createElement('canvas');
    const size = options?.width || options?.height || 256; // 使用选项中的尺寸或默认256
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
  
    if (ctx) {
      // 检查URL是否在高德地图的已知错误列表中
      if (url.includes('is.autonavi.com') && (url.includes('wprd01') || url.includes('webrd01') || url.includes('webst01'))) {
        // 对于高德地图的特定错误，使用更明显的错误提示
        drawErrorPlaceholder(ctx, canvas.width, canvas.height);
      } else {
        // 其他错误使用通用占位符
        drawTilePlaceholder(ctx, canvas.width, canvas.height);
      }
      return canvas;
    } else {
      // 如果无法获取2D上下文（不太可能发生），尝试返回一个1x1的透明GIF作为最终备用
      // 这是一个base64编码的1x1透明GIF
      return new Promise((resolve) => {
        const tinyTransparentGif = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
        const img = new Image();
        img.onload = () => {
          resolve(img);
        };
        img.onerror = (err) => {
          console.error('Fallback GIF image failed to load, using 1x1 blank canvas as ultimate fallback:', err);
          // 即使GIF加载失败，也返回一个有效的1x1 canvas
          const finalFallbackCanvas = document.createElement('canvas');
          finalFallbackCanvas.width = 1;
          finalFallbackCanvas.height = 1;
          resolve(finalFallbackCanvas);
        };
        img.src = tinyTransparentGif;
      });
    }
  }
  
  /**
   * 在Canvas上绘制瓦片占位符
   * @param ctx Canvas 2D上下文
   * @param width Canvas宽度
   * @param height Canvas高度
   */
  function drawTilePlaceholder(ctx: CanvasRenderingContext2D, width: number, height: number) {
    ctx.fillStyle = 'rgba(128, 128, 128, 0.5)'; // 半透明灰色背景
    ctx.fillRect(0, 0, width, height);
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)'; // 深灰色网格线
    ctx.lineWidth = 1;
  
    // 绘制网格
    for (let i = 0; i < width; i += 32) {
      ctx.beginPath();
      ctx.moveTo(i, 0);
      ctx.lineTo(i, height);
      ctx.stroke();
    }
    for (let i = 0; i < height; i += 32) {
      ctx.beginPath();
      ctx.moveTo(0, i);
      ctx.lineTo(width, i);
      ctx.stroke();
    }
  
    // 绘制对角线
    ctx.strokeStyle = 'rgba(255, 0, 0, 0.4)'; // 半透明红色对角线
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(width, height);
    ctx.stroke();
    ctx.beginPath();
    ctx.moveTo(width, 0);
    ctx.lineTo(0, height);
    ctx.stroke();
  }
  
  /**
   * 在Canvas上绘制错误占位符（例如，红色叉）
   * @param ctx Canvas 2D上下文
   * @param width Canvas宽度
   * @param height Canvas高度
   */
  function drawErrorPlaceholder(ctx: CanvasRenderingContext2D, width: number, height: number) {
    ctx.fillStyle = 'rgba(255, 192, 203, 0.8)'; // 粉色背景，更明显
    ctx.fillRect(0, 0, width, height);
    ctx.strokeStyle = 'red';
    ctx.lineWidth = Math.max(2, Math.min(width, height) / 16); // 根据尺寸调整线条粗细
  
    // 绘制红色叉
    ctx.beginPath();
    ctx.moveTo(width * 0.1, height * 0.1);
    ctx.lineTo(width * 0.9, height * 0.9);
    ctx.stroke();
    ctx.beginPath();
    ctx.moveTo(width * 0.9, height * 0.1);
    ctx.lineTo(width * 0.1, height * 0.9);
    ctx.stroke();
  }
} // Closing brace for patchCesiumResourceHandlers

// 全局错误拦截：捕获未处理的渲染错误
function setupGlobalErrorHandling() {
  // 拦截window错误
  window.addEventListener('error', (event) => {
    // 检查是否为shader编译错误或WebGL错误
    if (event.message && (
        event.message.includes('shader failed to compile') ||
        event.message.includes('WebGL') ||
        event.message.includes('rendering') ||
        event.message.includes('Rendering has stopped')
    )) {
      console.error('捕获到渲染错误:', event.message);
      
      // 记录错误堆栈
      console.debug('错误堆栈:', event.error?.stack || '无堆栈信息');
      
      // 阻止错误传播
      event.preventDefault();
      
      // 异步尝试恢复渲染
      setTimeout(() => {
        attemptRenderRecovery();
      }, 100);
      
      return true; // 防止默认错误处理
    }
  }, true);
  
  console.log('全局错误拦截已设置');
}

/**
 * 尝试恢复渲染
 */
function attemptRenderRecovery() {
  if (!window.viewer) {
    console.warn('Cesium viewer不可用，无法恢复渲染');
    return;
  }
  
  console.log('正在尝试恢复渲染...');
  
  try {
    // 标记要移除的可疑模型
    const suspiciousModels = [];
    
    // 遍历所有primitive查找可能有问题的模型
    const primitives = window.viewer.scene.primitives;
    for (let i = primitives.length - 1; i >= 0; i--) {
      const primitive = primitives.get(i);
      
      // 检查是否为Model类型
        if (primitive && primitive instanceof (window.Cesium as any).Model) {
        // 查找最近添加的Model
        suspiciousModels.push(primitive);
        if (suspiciousModels.length >= 3) break; // 限制数量
      }
    }
    
    // 移除可疑模型
    suspiciousModels.forEach(model => {
      try {
        if (primitives.contains(model)) {
          console.log('移除可能有问题的模型:', model.id);
          primitives.remove(model);
        }
      } catch (e) {
        console.warn('移除模型时出错:', e);
      }
    });
    
    // 强制重新渲染
    window.viewer.scene.requestRender();
    
    console.log('渲染恢复尝试完成');
  } catch (error) {
    console.error('恢复渲染时出错:', error);
  }
}

/**
 * 增强Cesium模型加载健壮性，防止模型加载错误导致应用崩溃
 */
function patchCesiumModelLoading() {
  if (!window.Cesium) {
    console.warn('Cesium未初始化，无法应用模型加载防护');
    return;
  }

  console.log('正在应用Cesium模型加载防护...');

  // 保存原始模型加载方法
  const originalModelLoad = (window.Cesium as any).Model.fromGltf;

  // 重写模型加载方法
  (window.Cesium as any).Model.fromGltf = function(options: any) {
    try {
      // 验证基本参数
      if (!options) {
        console.error('模型加载参数无效');
        return null;
      }

      // 尝试创建模型
      const model = originalModelLoad.call(this, options);

      // 不能直接替换readyPromise，因为它是只读属性
      // 而是添加监听，在catch中处理错误
      if (model && model.readyPromise) {
        // 添加错误处理但不修改原始readyPromise
        model.readyPromise.catch((error: any) => { // Added any type to error
          console.error(`模型加载失败: ${options.url || 'unknown'}`, error);
          
          // 提供降级处理
          const scene = options.scene;
          if (scene && scene.primitives && scene.primitives.contains && scene.primitives.contains(model)) {
            // 从场景中移除模型
            try {
              scene.primitives.remove(model);
            } catch (e) {
              console.warn('移除失败的模型时出错:', e);
            }
          }
          
          // 错误已经被处理，防止未捕获的Promise rejection
          return null;
        });
      }

      return model;
    } catch (error) {
      console.error('创建模型实例失败:', error);
      
      // 在创建实例时出错，直接返回null
      return null;
    }
  };

  // 为3D模型瓦片集添加错误处理
  if ((window.Cesium as any).Cesium3DTileset) {
    const original3DTilesetFromUrl = (window.Cesium as any).Cesium3DTileset.fromUrl;
    
    (window.Cesium as any).Cesium3DTileset.fromUrl = function(url: any, options?: any): Promise<any> {
      try {
        const tileset = original3DTilesetFromUrl.call(this, url, options);
        
        // 增强tileset的readyPromise，使用监听方式而非替换
        if (tileset && (tileset as any).readyPromise) {
          (tileset as any).readyPromise.catch((error: any) => { // Added any type to error
            const tilesetUrlString = url instanceof (window.Cesium as any).Resource ? url.url : String(url);
            console.error(`3D瓦片集加载失败: ${tilesetUrlString}`, error);
            // 已处理错误，防止未捕获的Promise rejection
            return null;
          });
        }
        
        return tileset || Promise.resolve(null as any);
      } catch (error) {
        const tilesetUrlStringOnError = url instanceof (window.Cesium as any).Resource ? url.url : String(url);
        console.error(`3D瓦片集创建失败: ${tilesetUrlStringOnError}`, error);
        // 根据Cesium的原始行为，这里可能应该返回一个立即失败的Promise或null/undefined
        // 为了安全起见，我们返回null，因为原始函数可能在失败时返回undefined
        return Promise.resolve(null as any);
      }
    };
  }

  console.log('Cesium模型加载防护已启用');
}

/**
 * 应用所有Cesium补丁
 */
function applyAllCesiumPatches() {
  console.log('正在应用所有Cesium防护措施...');
  
  // 1. Shader编译错误防护：防止Shader编译失败导致渲染中断
  patchCesiumShaderSystem();
  
  // 2. 资源加载错误防护：防止图像等资源加载失败导致应用崩溃
  patchCesiumResourceHandlers();
  
  // 3. 模型加载错误防护：防止3D模型加载失败导致应用崩溃
  patchCesiumModelLoading();
  
  // 4. 全局错误拦截：捕获未处理的渲染错误
  setupGlobalErrorHandling();
  
  console.log('所有Cesium防护措施已应用，应用现在更加健壮');
  console.log('防护系统工作原理：');
  console.log('1. Shader系统防护：拦截编译错误并提供备用着色器');
  console.log('2. 资源加载防护：处理图像加载失败并提供占位图');
  console.log('3. 模型加载防护：处理模型加载失败并移除有问题的模型');
  console.log('4. 全局错误拦截：捕获渲染循环中的错误并尝试恢复渲染');
}

// 初始化时调用这些防护措施
window.addEventListener('load', () => {
  // 等待Cesium和viewer初始化完成
  const checkAndApplyPatches = () => {
    if (window.Cesium && window.viewer) {
      // 应用防护措施
      applyAllCesiumPatches();
    } else {
      // 延迟检查
      setTimeout(checkAndApplyPatches, 500);
    }
  };
  
  // 开始检查
  checkAndApplyPatches();
});

const $cache: Cache = new Cache([], 0)

// 设置Cesium的访问令牌
if (Cesium && Cesium.Ion) {
Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIyMDNiY2M5MS0xMWY5LTQ2ZmItOGFmMi0zOTExYTU3M2U3NDEiLCJpZCI6ODE5MzQsImlhdCI6MTY2NTEzMTU3NX0.oGKBB47s-CAIDhbfPGgczcj3NSmSq6EJt5NVv_a2TS4'
}

window.Cesium = Cesium

//数据请求字符
axios.defaults.baseURL = BACKEN_URL
// 如果请求话费了超过 `timeout` 的时间，请求将被中断
axios.defaults.timeout = 3000;
// 表示跨域请求时是否需要使用凭证
axios.defaults.withCredentials = false;
// axios.defaults.headers.common['token'] =  AUTH_TOKEN
axios.defaults.headers.post['Content-Type'] = 'application/json;charset=UTF-8';
// 允许跨域
axios.defaults.headers.post["Access-Control-Allow-Origin-Type"] = "*";

const app = createApp(App)
app.config.globalProperties.$cache = $cache

// 显式指定createPinia的返回类型
const pinia = createPinia();
pinia.use(piniaPersist);
app.use(pinia);

app.use(router)

app.use(ElementPlus)

app.mount('#app')
