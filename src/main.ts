import './assets/main.css'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'

// 直接使用环境变量
const BACKEN_URL = import.meta.env.VITE_BACKEN_URL;

import axios from 'axios'
import { createPinia } from 'pinia'
// @ts-ignore - Missing type declarations in package
import piniaPersist from 'pinia-plugin-persist'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 引入Cesium的源码
// import * as Cesium from '@/Cesium'
// import '@/Cesium/Widgets/widgets.css' // 引入Cesium的Widget样式

import * as Cesium from 'cesium'
// import '/Cesium/Widgets/widgets.css' // 引入Cesium的Widget样式 - 改为在HTML中引用

import Cache from './js/cache/index'

// 添加全局类型声明
declare global {
  interface Window {
    Cesium: typeof Cesium;
    viewer?: any;
    acceptMessage?: (msg: any) => void;
    CommandTest?: {
      HandleResponse: (jsonStr: any) => void;
    };
  }
}

// 安全版本的HandleResponse函数，避免JSON.parse错误导致程序崩溃
const safeHandleResponse = (jsonStr: any) => {
  console.log('CommandTest处理响应:', jsonStr);

  try {
    // 如果已经是对象，直接处理
    if (typeof jsonStr === 'object' && jsonStr !== null) {
      console.log('CommandTest处理对象:', jsonStr);
      return;
    }

    // 如果是字符串，尝试解析JSON
    if (typeof jsonStr === 'string') {
      const obj = JSON.parse(jsonStr);
      console.log('CommandTest解析JSON结果:', obj);
    } else {
      console.log('CommandTest收到非字符串非对象数据:', typeof jsonStr);
    }
  } catch (e) {
    console.error('CommandTest处理响应时出错:', e);
    console.log('原始数据:', jsonStr);
  }
};

// 确保全局CommandTest对象存在并可以安全处理所有类型的输入
window.CommandTest = window.CommandTest || { HandleResponse: safeHandleResponse };
window.CommandTest.HandleResponse = safeHandleResponse as (jsonStr: any) => void;

const $cache: Cache = new Cache([], 0)

// 设置Cesium的访问令牌
if (Cesium && Cesium.Ion) {
Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIyMDNiY2M5MS0xMWY5LTQ2ZmItOGFmMi0zOTExYTU3M2U3NDEiLCJpZCI6ODE5MzQsImlhdCI6MTY2NTEzMTU3NX0.oGKBB47s-CAIDhbfPGgczcj3NSmSq6EJt5NVv_a2TS4'
}

window.Cesium = Cesium

//数据请求字符
axios.defaults.baseURL = BACKEN_URL
// 如果请求话费了超过 `timeout` 的时间，请求将被中断
axios.defaults.timeout = 3000;
// 表示跨域请求时是否需要使用凭证
axios.defaults.withCredentials = false;
// axios.defaults.headers.common['token'] =  AUTH_TOKEN
axios.defaults.headers.post['Content-Type'] = 'application/json;charset=UTF-8';
// 允许跨域
axios.defaults.headers.post["Access-Control-Allow-Origin-Type"] = "*";

const app = createApp(App)
app.config.globalProperties.$cache = $cache

// 显式指定createPinia的返回类型
const pinia = createPinia();
pinia.use(piniaPersist);
app.use(pinia);

app.use(router)

app.use(ElementPlus)

app.mount('#app')


