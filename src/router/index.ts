import { createRouter, createWebHistory } from 'vue-router'
import PowerLineView from '../views/PowerLineView.vue'
import Threejs3DView from '../views/Threejs3D.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: PowerLineView
    },
    {
      path: '/login',
      name: 'login',
      component: PowerLineView // 暂时使用PowerLineView，后续可以创建专门的登录页面
    },
    {
      path: '/vr',
      name: 'vr',
      component: Threejs3DView
    }
    // {
    //   path: '/about',
    //   name: 'about',
    //   // route level code-splitting
    //   // this generates a separate chunk (About.[hash].js) for this route
    //   // which is lazy-loaded when the route is visited.
    //   component: () => import('../views/AboutView.vue')
    // }
  ]
})

export default router
