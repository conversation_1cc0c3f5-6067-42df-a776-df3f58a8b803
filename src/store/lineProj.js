import { API_URL, WGS84_to_GCJ02 } from '@/config/global';
import { distance_ang } from '@/js/common/line_tower'
import { defineStore } from 'pinia'

import coordtransform  from 'coordtransform';
import axios from "@/utils/requires"
import { useUserStore } from '@/store/user.js';

export const useLineProjStore = defineStore('lineProjStore', {
    state: () => {
        return {
            msg: 'this is Line Project store',
            ver: '',
            projMsg: {},
            linesInfo: [], // 整个工程中回路简介信息
            lineDetail: [],
            towers:[],
            dataUseful: false, // 更新数据标志 false则需要更新
            insulators:[],      // 绝缘子模型信息
            proLines: [], // 整个工程对应的线路列表
            tower_angles_fixed: [], // 杆塔角度梳理记录列表
            lineDataV20: {}, // 版本V2.0的全部线路数据
        }
    },
    persist: {
        enabled: true // true 表示开启持久化保存
    },
    actions: {
        setProLines (data) {
            this.proLines = data
        },
        async updateProjInfo (axioseee, token, projID) {
            this.projMsg = {};
            const postPara = { "projectId": projID };
            return new Promise((resolve, reject) => {
                axios.post(API_URL.projectDetail+`?t=${new Date().getTime()}`, postPara).then((response) => {
                    const res = response.data;
                    console.log('updateProjInfo', postPara, res)
                    if (res.code === 0) {
                        this.projMsg = res.data[0];
                        resolve(true);
                    } else {
                        console.log('updateProjInfo', res);
                        resolve(false);
                    }
                });
            });
        },
        async updateLinesInfo (axioseee, token) {
            this.linesInfo = [];
            return new Promise((resolve, reject) => {
                if (this.projMsg.projectId) {
                    const postPara = { "id": this.projMsg.projectId  };
                    axios.post(API_URL.lines+`?t=${new Date().getTime()}`, postPara).then((response) => {
                            const res = response.data;
                        if (res.code === 0) {
                                let lines = res.data.filter((item,index)=>{return item.projectId===this.projMsg.projectId})
                                this.linesInfo = lines;
                                resolve(true);
                            } else {
                                console.log('updateLinesInfo false', res);
                                resolve(false);
                            }
                        });
                    } else {
                    resolve(false)
                }
            });
        },
        async updateLineDetail () {
            this.lineDetail = [];
            let promiseArr = [];
            this.tower_angles_fixed = [] // 更新线路数据前清空角度修复的记录
            this.linesInfo.forEach(line => {
                if(line.projectId!=this.projMsg.projectId) {
                   return Promise.resolve(false)
                }
                const postPara = {"lineId": line.lineId };
                let p = new Promise((resolve, reject) => {
                    axios.post(API_URL.lineDetail+`?t=${new Date().getTime()}`, postPara).then((response) => {
                        const res = response.data;
                        if (res.code === 0) {
                            if(WGS84_to_GCJ02){
                                this.wgs84togcj02(res.data);
                            }
                            console.log('updateLineDetail',postPara, res.data);
                            if(res.data.length==0){
                                console.log('updateLineDetail 获取线路详情错误 没有杆塔信息:',postPara, res.data);
                                resolve(false);
                            }
                            this.fixTowerAng(res.data); // 因为设计图的杆塔转角是以电缆走向为基准给定 所以要修正杆塔转角
                            this.lineDetail.push({lineId:line.lineId, towers:res.data});
                            resolve(true);
                        } else {
                            this.lineDetail.push({lineId:line.lineId, towers:[]});
                            resolve(false);
                        }
                    });
                });
                promiseArr.push(p);
            });
            return new Promise((resolve,reject)=>{
                Promise.all(promiseArr).then((res) => {
                    resolve(true)
                })
            })
            
        },
        async initData (axioseee, token, projectId) {
            this.ver = "1.0"
            // debugger
            // return new Promise((resolve, reject) => {
                // await this.updateProjInfo(axioseee, token, projectId).then((res) => {
                //     if (res === true) {
                //         console.log('updata project info ok');
                //         await this.updateLinesInfo(axioseee, token).then((res) => {
                //             if (res === true) {
                //                 console.log('updata line info ok');
                //                 this.updateLineDetail(axioseee, token, (res) => {
                //                     if (res === true) {
                //                         console.log('LineProjStore initData ok', this.lineDetail);
                //                         this.dataUseful = true;
                //                         // resolve(true);
                //                     }
                //                 });
                //             } else {
                //                 // resolve(false);
                //             }
                //         });
                //     } else {
                //         // resolve(false);
                //     }
                // });
            // });
            if (await this.updateProjInfo(axioseee, token, projectId) === true) {
                console.log('updata project info ok', this.projMsg);
                if (await this.updateLinesInfo(axioseee, token) === true) {
                    console.log('updata line info ok', this.linesInfo);
                    if(await this.updateLineDetail()==true){
                        console.log('LineProjStore initData ok', this.lineDetail);
                        return this.towersRequest(axioseee, token);
                    }
                } else {
                    console.error('updateLinesInfo error');
                }
            } else {
                console.error('updateProjInfo error');
            }
            return Promise.resolve(false);
        },
        // 该函数请求后端数据工程相关的全部杆塔信息 需要在线路更新后才能调用
        async towersRequest (axioseee, token) {
                if(this.dataUseful===true || this.lineDetail.length===0){
                return Promise.resolve(false);
            }
            let towersName = [];
            this.lineDetail.forEach(line => {
                for(let ind=0;ind<line.towers.length;ind++){
                    const towerName = line.towers[ind].towerModel;
                    let _exist_ = false;
                    towersName.forEach(el => {
                        if (el === towerName) {
                            _exist_ = true;
                        }
                    });
                    if (_exist_ === false) {
                        towersName.push(towerName);
                    }
                }
            });
            return new Promise((resolve, reject) => {
                const postPara = { "modelNames": towersName};
                
                axios.post(API_URL.towersInfo+`?t=${new Date().getTime()}`, postPara).then((response) => {
                    const res = response.data;
                    //将字符串转为json
                    

                    if (res.code === 0) {
                        this.towers = res.data;
                        this.towers.forEach(e=>{
                            for(let key in e){
                                if(key.includes("qian")||key.includes("hou")||key.includes("xuan"))
                                    if(e[key])
                                        e[key] = JSON.parse(e[key])
                            }

                        })
                        // this.fixBaseHeight(); // 修正挂点坐标的高度值
                        console.log('towersRequest ok', this.towers);
                        resolve(true);
                    } else {
                        this.towers = [];
                        resolve(false);
                    }
                });
            });
        },
        async get_insulatorAllocation (axioseee, token, callback = function () { }) {
            if(this.dataUseful===true || this.linesInfo.length===0){
                return false;
            }
            let promiseArr = [];
            this.linesInfo.forEach(line => {
                const postPara = { "lineId": line.lineId };
                let p = new Promise((resolve, reject) => {
                    axios.post(API_URL.insulatorAllocation+`?t=${new Date().getTime()}`, postPara).then((response) => {
                        const res = response.data;
                        if (res.code === 0) {
                            for(let ind=0;ind<res.data.length;ind++){
                                const info = res.data[ind];
                                let lineDetail = this.lineDetail.find((item, index) => { return line.lineId===item.lineId});
                                if(!lineDetail){
                                    continue
                                }
                                const tower = lineDetail.towers.find((item,index)=>{return item.towerId===info.towerId});
                                if (tower == undefined) {
                                    console.error('无法找到配串表', info, lineDetail);
                                    continue;
                                }
                                tower.tensionInsulator = info.tensionInsulator;
                                tower.overhangInsulator = info.overhangInsulator;
                                tower.groundInsulator = info.groundInsulator;
                            }
                            console.log('line ',  line.lineId, 'get insulator ok:', line);
                            resolve(true);
                        } else {
                            resolve(false);
                        }
                    });
                });
                promiseArr.push(p);
            });
            Promise.all(promiseArr).then((res) => {
                this.get_insulators(axios, token).then(res=>{
                    callback(true);
                });
                
            })
        },
        async get_insulators(axioseee, token){
            if(this.dataUseful===true || this.lineDetail.length===0){
                return Promise.resolve(false);
            }
            function is_exist(name, modelsName) {
                if(name==undefined || modelsName==undefined){
                    return;
                }
                let _exist_ = false;
                modelsName.forEach(el => {
                    if (el === name || name === '/') {
                        _exist_ = true;
                    }
                });
                if (_exist_ === false) {
                    modelsName.push(name);
                }
            }
            let insulatorsName = [];
            this.lineDetail.forEach(line => {
                for(let ind=0;ind<line.towers.length;ind++){
                    is_exist(line.towers[ind].tensionInsulator, insulatorsName);
                    is_exist(line.towers[ind].overhangInsulator, insulatorsName);
                    is_exist(line.towers[ind].groundInsulator, insulatorsName);
                }
            });
            return new Promise((resolve, reject) => {
                const postPara = {"modelNames": insulatorsName};
                axios.post(API_URL.insulatorsInfo+`?t=${new Date().getTime()}`, postPara).then((response) => {
                    
                    const res = response.data;
                    if (res.code === 0) {
                        this.insulators = res.data;
                        //将当中的字符串转为数据
                        this.insulators.forEach(e=>{
                            for(let key in e){
                                
                                if(key.includes("point"))
                                if(e[key])
                                    e[key] = JSON.parse(e[key])
                            }
                            
                        })
                        console.log('insulator models request ok:', this.insulators);
                        this.towers = this.convertData2NewFormat_towers(this.towers, this.lineDetail);

                        resolve(true);
                    } else {
                        this.insulators= [];
                        resolve(false);
                    }
                });
            });
        },
        fixBaseHeight(){
            // 修复数据中杆塔挂点没有加入杆塔呼高的偏差
            const keys = ["houdao1", "houdao10", "houdao11", "houdao12", "houdao2", "houdao3",
                "houdao4", "houdao5", "houdao6", "houdao7", "houdao8", "houdao9", "houdi1", "houdi2",
                "qiandao1", "qiandao10", "qiandao11", "qiandao12", "qiandao2", "qiandao3", "qiandao4",
                "qiandao5", "qiandao6", "qiandao7", "qiandao8", "qiandao9", "qiandi1", "qiandi2",
                "xuanchui1", "xuanchui10", "xuanchui11", "xuanchui12", "xuanchui2", "xuanchui3", "xuanchui4",
                "xuanchui5", "xuanchui6", "xuanchui7", "xuanchui8", "xuanchui9"];
            for (let ind = 0; ind < this.towers.length; ind++) {
                const tower = this.towers[ind];
                const towerBaseHeight = tower.baseHeight;
                keys.forEach(key => {
                    if(tower[key].length==3){
                        tower[key][2] += towerBaseHeight;
                        // 修正x和y轴相反的问题
                        let temp = tower[key][0];
                        tower[key][0] = tower[key][1];
                        tower[key][1] = temp;
                    }
                });
            }
        },
        fixTowerAng(towers){
            let lastTowerPosition = [towers[0].longitude, towers[0].latitude, towers[0].height]; // 第一个杆塔的上一个塔没有 所以取它本身的值
            for(let ind=1; ind<towers.length; ind++){
                let nowTowerPosition = [towers[ind].longitude, towers[ind].latitude, towers[ind].height];
                let _check_fixed = this.tower_angles_fixed.find(item => item.towerId == towers[ind].towerId);
                if(!_check_fixed){
                    let baseAngle = distance_ang(lastTowerPosition, nowTowerPosition)[1]*180/ Math.PI + 90;
                    towers[ind].angle = baseAngle+towers[ind].angle;
                    this.tower_angles_fixed.push(towers[ind]) // 将修改过角度的杆塔进行记录 避免重复修改
                }else{
                    towers[ind].angle = _check_fixed.angle;
                }
                lastTowerPosition = nowTowerPosition;
                // console.log('fixtowerang:',towers[ind].towerId, baseAngle, towers[ind].angle);
            }
        },
        wgs84togcj02(towers){
            for(let ind=0; ind<towers.length; ind++){
                const gcj02Coords = coordtransform.wgs84togcj02(towers[ind].longitude, towers[ind].latitude);
                towers[ind].longitude = gcj02Coords[0];
                towers[ind].latitude = gcj02Coords[1];
            }
        },
        convertData2NewFormat_towers (towersInfo, linesDetail) {
            console.log('convertData2NewFormat_towers...',towersInfo);
            let oldData_towerModels = towersInfo;
            let newData = [];

            let find_tower = (modelName) => {
                let _result = [];
                linesDetail.forEach(item => {
                    // let t = item.towers.filter(element => element.towerModel == modelName);
                    item.towers.forEach(element => {
                        if (element.towerModel == modelName) {
                            _result.push(element);
                        }
                    });
                })
                if(_result.length<2){
                    // 如果该线路配串表只出现一次该塔号，则表示当前工程在该塔只走了一回线路
                    // 但是为了信息完整 直接将另一边的绝缘子赋值同样信息
                    _result.push(_result[0]);
                }
                return _result;
            }

            oldData_towerModels.forEach(item => {
                let nd = {}
                newData.push(nd)
                Object.assign(nd, item)
                nd.PointDetail = { qiandao: [], houdao: [], zhongdao: [], houdi: [], qiandi: [], zhongdi: [] }
                // 根据杆塔模型 从线路详情表中查询得到塔信息 塔信息中存在绝缘子配串信息
                let line_tower = find_tower(item['towerModel']); // 从线路中可能获取到多个同塔型号的对象
                if (!line_tower) {
                    return;
                }
                
                for (var i = 0; i < 12; i++) {
                    // 其中模型横担位置从1计数 i+1表示横担位置 135左上角；246右上角；7 9 11左下角；81012右下角
                    // 根据横担位置选择回路配串信息 合并生成新的杆塔信息表
                    let item_line_tower
                    if(i==0||i==2||i==4){
                        // 左上角 同杆线路位置是1
                        item_line_tower = line_tower.filter(item => item.lineLocation == 1)[0];
                    }else if(i==1||i==3||i==5){
                        // 右上角 同杆线路位置是2
                        item_line_tower = line_tower.filter(item => item.lineLocation == 2)[0];
                    }else if(i==6||i==8||i==10){
                        // 左下角 同杆线路位置是3
                        item_line_tower = line_tower.filter(item => item.lineLocation == 3)[0];
                    }else if(i==7||i==9||i==11){
                        // 右下角 同杆线路位置是4
                        item_line_tower = line_tower.filter(item => item.lineLocation == 4)[0];
                    }
                    // console.log('line_tower:',i,item_line_tower, line_tower);
                    // 杆塔信息表中不存在该横担位置 为了信息完整 这里直接给一个值
                    if(!item_line_tower){
                        item_line_tower = line_tower[0];
                    }

                    // 数据库的杆塔模型的挂点信息XY轴相反 这里先通过代码处理 后续需要模型组修正
                    // let temp
                    // temp = item[`qiandao${i + 1}`][0];
                    // item[`qiandao${i + 1}`][0] = item[`qiandao${i + 1}`][1]
                    // item[`qiandao${i + 1}`][1] = temp
                    // temp = item[`xuanchui${i + 1}`][0];
                    // item[`xuanchui${i + 1}`][0] = item[`xuanchui${i + 1}`][1]
                    // item[`xuanchui${i + 1}`][1] = temp
                    // temp = item[`houdao${i + 1}`][0];
                    // item[`houdao${i + 1}`][0] = item[`houdao${i + 1}`][1]
                    // item[`houdao${i + 1}`][1] = temp
                    // temp = item[`houdao${i + 1}`][0];
                    // item[`houdao${i + 1}`][0] = item[`houdao${i + 1}`][1]
                    // item[`houdao${i + 1}`][1] = temp
                    // if (i < 2) {
                    //     temp = item[`houdi${i + 1}`][0];
                    //     item[`houdi${i + 1}`][0] = item[`houdi${i + 1}`][1]
                    //     item[`houdi${i + 1}`][1] = temp
                    //     temp = item[`qiandi${i + 1}`][0];
                    //     item[`qiandi${i + 1}`][0] = item[`qiandi${i + 1}`][1]
                    //     item[`qiandi${i + 1}`][1] = temp
                    // }
                    //////////////////////////////////////////////////////////////////////
                    // nd[`qiandao${i + 1}`] = item[`qiandao${i + 1}`]
                    // nd[`xuanchui${i + 1}`] = item[`xuanchui${i + 1}`]
                    // nd[`houdao${i + 1}`] = item[`houdao${i + 1}`]
                    // nd[`qiandao${i + 1}`] = []
                    // nd[`xuanchui${i + 1}`] =[]
                    // nd[`houdao${i + 1}`] = []

                    let qiandao = {
                        position: item[`qiandao${i + 1}`] || [],
                        number: i + 1,
                        name: "前导" + (i + 1),
                        // insulatorNo: "D0302-13-1"
                        insulatorNo: item_line_tower.tensionInsulator
                    }
                    let zhongdao = {
                        position: item[`xuanchui${i + 1}`] || [],
                        number: i + 1,
                        name: "中导" + (i + 1),
                        // insulatorNo: "D0302-01"
                        insulatorNo: item_line_tower.overhangInsulator                        
                    }
                    let houdao = {
                        position: item[`houdao${i + 1}`] || [],
                        number: i + 1,
                        name: "后导" + (i + 1),
                        // insulatorNo: "D0302-13-1"
                        insulatorNo: item_line_tower.tensionInsulator
                    }
                    nd.PointDetail.qiandao.push(qiandao)
                    nd.PointDetail.zhongdao.push(zhongdao)
                    nd.PointDetail.houdao.push(houdao)
                    if (i < 4) {
                        let houdi = {
                            position: item[`houdi${i + 1}`] || [],
                            number: i + 1,
                            name: "后地" + (i + 1),
                            // insulatorNo: "D0302-15"
                            insulatorNo: item_line_tower.groundInsulator
                        }
                        let qiandi = {
                            position: item[`qiandi${i + 1}`] || [],
                            number: i + 1,
                            name: "前地" + (i + 1),
                            // insulatorNo: "D0302-15"
                            insulatorNo: item_line_tower.groundInsulator
                        }
                        nd.PointDetail.houdi.push(houdi)
                        nd.PointDetail.qiandi.push(qiandi)
                        // nd[`houdi${i + 1}`] = item[`houdi${i + 1}`]
                        // nd[`qiandi${i + 1}`] = item[`qiandi${i + 1}`]

                        // nd[`houdi${i + 1}`] = []
                        // nd[`qiandi${i + 1}`] = []
                    }
                }
            })
            console.log('convertData2NewFormat_towers ok:', newData, );
            return newData;
        },
        // 额外请求绝缘子串信息
        getInsulators_ext(insulatorsName) {
            // 判断传递的绝缘子名称数组insulatorsName 是否已经存在this.insulators中
            if (this.insulators.length > 0) {
                const existingNames = this.insulators.map(item => item.modelName); // 获取已存在的绝缘子名称数组
                const newNames = insulatorsName.filter(name => !existingNames.includes(name)); // 过滤出不在已存在数组中的
                // 如果过滤后没有需要请求的 则直接返回
                if (newNames.length === 0) {
                    return Promise.resolve(true);
                }
                return new Promise((resolve, reject) => {
                    const postPara = { "modelNames": newNames };
                    axios.post(API_URL.insulatorsInfo + `?t=${new Date().getTime()}`, postPara).then((response) => {
                        const res = response.data;
                        if (res.code === 0) {
                            res.data.forEach(insulator => {
                                // this.insulators.push(...res.data);
                                if (this.insulators.find(item => item.modelName == insulator.modelName) == undefined) {
                                    this.insulators.push(insulator);
                                }
                            });
                            console.log('insulator models request ok:', this.insulators);
                            this.towers = this.convertData2NewFormat_towers(this.towers, this.lineDetail);

                            resolve(true);
                        } else {
                            resolve(false);
                        }
                    });
                });
            }
        },
        async getSwinsulatorAllocationlist(projectId) {
            const store = useUserStore();
            const res = await axios({
                url: API_URL.swinsulatorAllocationlist,
                method: "post",
                data: {
                    projectId: projectId
                }
            })
            return Promise.resolve(res.data)
        },
        async getSwtowerMountslist(projectId) {
            const store = useUserStore();
            const res = await axios({
                url: API_URL.swtowerMountslist,
                method: "post",
                data: {
                    projectId: projectId
                }
            })
            return Promise.resolve(res.data)
        },
        async getSwinsulatorModellist(projectId) {
            const store = useUserStore();
            const res = await axios({
                url: API_URL.swinsulatorModellist,
                method: "post",
                data: {
                    projectId: projectId
                }
            })
            return Promise.resolve(res.data)
        },
        async getSwlinelist(projectId) {
            const store = useUserStore();
            const res = await axios({
                url: API_URL.swlinelist,
                method: "post",
                data: {
                    projectId: projectId
                }
            })
            return Promise.resolve(res.data)
        },
        async getSwtowerModellist(projectId) {
            const store = useUserStore();
            const res = await axios({
                url: API_URL.swtowerModellist,
                method: "post",
                data: {
                    projectId: projectId
                }
            })
            return Promise.resolve(res.data)
        },
        async getSwlineDetail(projectId) {
            const store = useUserStore();
            const res = await axios({
                url: API_URL.swlineDetail,
                method: "post",
                data: {
                    projectId: projectId
                }
            })
            return Promise.resolve(res.data)
        },
        async getSwtowerList(projectId) {
            const store = useUserStore();
            const res = await axios({
                url: API_URL.swtowerList,
                method: "get",
                params: {
                    projectId: projectId
                }
            })
            return Promise.resolve(res.data)
        },
        async initDataV2(projectId) {
            this.ver = "2.0"
            let res = await this.getSwinsulatorAllocationlist(projectId)
            if(res.code != 0){
                console.error('请求线路数据 SwinsulatorAllocationlist 错误，', res)
                return Promise.resolve(null)
            }
            let insulatorAllocationList = res.data
            
            res = await this.getSwtowerMountslist(projectId)
            if(res.code != 0){
                console.error('请求线路数据 SwtowerMountslist 错误，', res)
                return Promise.resolve(null)
            }
            let towerMountslist = res.data
            // 这里处理后端返回的挂点数据 XY和符号相反的情况 
            const substationTowers = towerMountslist.filter(mount => mount.towerModel === "substationModel")
            substationTowers.forEach(mount => {
                let temp = mount.mountPosition[0]
                mount.mountPosition[0] = mount.mountPosition[1]*-1
                mount.mountPosition[1] = temp*-1
            })

            res = await this.getSwinsulatorModellist(projectId)
            if(res.code != 0){
                console.error('请求线路数据 SwinsulatorModellist 错误，', res)
                return Promise.resolve(null)
            }
            let insulatorModelList = res.data
            for (let i = 0; i < insulatorModelList.length; i++) {
                switch(insulatorModelList[i].modelType){
                    case '0':
                        insulatorModelList[i].modelType = '耐张串'
                        break
                    case '1':
                        insulatorModelList[i].modelType = '悬垂串'
                        break
                }
            }

            res = await this.getSwlinelist(projectId)
            if(res.code != 0){
                console.error('请求线路数据 Swlinelist 错误，', res)
                return Promise.resolve(null)
            }
            let lineList = res.data

            res = await this.getSwtowerModellist(projectId)
            if(res.code != 0){
                console.error('请求线路数据 SwtowerModellist 错误，', res)
                return Promise.resolve(null)
            }
            let towerModelList = res.data

            res = await this.getSwtowerList(projectId)
            if(res.code != 0){
                console.error('请求线路数据 SwtowerList 错误，', res)
                return Promise.resolve(null)
            }
            let towerList = res.data
            // 转换角度字符串为数字
            for(let i=0; i<towerList.length; i++){
                towerList[i].angle = parseFloat(towerList[i].absoluteAngle)
                towerList[i].version = 'V2'
            }

            this.lineDataV20 = {
                linesInfo: lineList,
                towerModels: towerModelList,
                lineDetail: towerList,
                towerMounts: towerMountslist,
                insulatorModels: insulatorModelList,
                insulatorAllocation: insulatorAllocationList,
                projectMsg:{projectId:projectId}
            }
            console.log('initDataV2:', this.lineDataV20)
            return Promise.resolve(this.lineDataV20)
        }
    }
})
