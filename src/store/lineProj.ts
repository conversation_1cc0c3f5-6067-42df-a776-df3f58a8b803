import { API_URL } from '../config/global';
import { distance_ang } from '../js/common/line_tower'
import { defineStore } from 'pinia'
import coordtransform from 'coordtransform'
import axios from '../utils/requires'

async function getSwtowerMountslist(projectId: string) {
  const res = await axios({
    url: API_URL.swtowerMountslist,
    method: "post",
    data: {
      projectId: projectId
    }
  })
  return Promise.resolve(res.data)
}
async function getSwinsulatorModellist(projectId: string) {
  const res = await axios({
    url: API_URL.swinsulatorModellist,
    method: "post",
    data: {
      projectId: projectId
    }
  })
  return Promise.resolve(res.data)
}
async function getSwlinelist(projectId: string) {
  const res = await axios({
    url: API_URL.swlinelist,
    method: "post",
    data: {
      projectId: projectId
    }
  })
  return Promise.resolve(res.data)
}
async function getSwtowerModellist(projectId: string) {
  const res = await axios({
    url: API_URL.swtowerModellist,
    method: "post",
    data: {
      projectId: projectId
    }
  })
  return Promise.resolve(res.data)
}
async function getSwlineDetail(projectId: string) {
  const res = await axios({
    url: API_URL.lineDetail,
    method: "post",
    data: {
      projectId: projectId
    }
  })
  return Promise.resolve(res.data)
}
async function getSwtowerList(projectId: string) {
  const res = await axios({
    url: API_URL.swtowerList,
    method: "get",
    params: {
      projectId: projectId
    }
  })
  return Promise.resolve(res.data)
}
async function getSwinsulatorAllocationlist(projectId: string) {
  const res = await axios({
    url: API_URL.swinsulatorAllocationlist,
    method: "post",
    data: {
      projectId: projectId
    }
  })
  return Promise.resolve(res.data)
}

import { ref } from 'vue'
import type { ILine } from '@/types/line_tower'

export const useLineProjStore = defineStore('lineProjStore', () => {
  const msg = ref('this is Line Project store')
  const ver = ref('')
  const projMsg = ref<any>(null)
  const linesInfo = ref<any[]>([]) // 整个工程中回路简介信息
  const lineDetail = ref<ILine[]>([])
  const towers = ref<any[]>([])
  const dataUseful = ref(false) // 更新数据标志 false则需要更新
  const insulators = ref<any[]>([])      // 绝缘子模型信息
  const proLines = ref<any[]>([]) // 整个工程对应的线路列表
  const tower_angles_fixed = ref<any[]>([]) // 杆塔角度梳理记录列表
  const lineDataV20 = ref<any>({}) // 版本V2.0的全部线路数据

  const setProLines = (data: any) => {
    proLines.value = data
  }

  const updateProjInfo = async (token: string, projID: string) => {
    projMsg.value = {};
    const postPara = { "projectId": projID };
    try {
      const response = await axios.post(API_URL.projectDetail + `?t=${new Date().getTime()}`, postPara);
      const res = response.data;
      console.log('updateProjInfo', postPara, res);
      if (res.code === 0) {
        projMsg.value = res.data[0];
        return true;
      } else {
        console.log('updateProjInfo', res);
        return false;
      }
    } catch (error) {
      console.error('updateProjInfo failed:', error);
      return false;
    }
  }

  const updateLinesInfo = async (token: string) => {
    linesInfo.value = [];
    if (projMsg.value && projMsg.value.projectId) {
      const postPara = { "id": projMsg.value.projectId };
      try {
        const response = await axios.post(API_URL.lines + `?t=${new Date().getTime()}`, postPara);
        const res = response.data;
        if (res.code === 0) {
          let lines = res.data.filter((item: any, index: number) => {
            return item.projectId === projMsg.value.projectId;
          });
          linesInfo.value = lines;
          return true;
        } else {
          console.log('updateLinesInfo false', res);
          return false;
        }
      } catch (error) {
        console.error('updateLinesInfo failed:', error);
        return false;
      }
    } else {
      return false;
    }
  }

  const updateLineDetail = async () => {
    lineDetail.value = [];
    tower_angles_fixed.value = []; // 更新线路数据前清空角度修复的记录
    for (const line of linesInfo.value) {
      if (projMsg.value && line.projectId != projMsg.value.projectId) {
        continue;
      }
      const postPara = { "lineId": line.lineId };
      try {
        const response = await axios.post(API_URL.lineDetail + `?t=${new Date().getTime()}`, postPara);
        const res = response.data;
        if (res.code === 0) {
          wgs84togcj02(res.data);
          console.log('updateLineDetail', postPara, res.data);
          if (res.data.length == 0) {
            console.log('updateLineDetail 获取线路详情错误 没有杆塔信息:', postPara, res.data);
          }
          fixTowerAng(res.data, tower_angles_fixed.value);
          lineDetail.value.push({ lineId: line.lineId, towers: res.data });
        } else {
          lineDetail.value.push({ lineId: line.lineId, towers: [] });
        }
      } catch (error) {
        console.error('updateLineDetail failed:', error);
        lineDetail.value.push({ lineId: line.lineId, towers: [] });
      }
    }
    return true;
  }

  const towersRequest = async (token: string) => {
    if (dataUseful.value === true || lineDetail.value.length === 0) {
      return false;
    }
    let towersName: any[] = [];
    lineDetail.value.forEach((line: any) => {
      for (let ind = 0; ind < line.towers.length; ind++) {
        const towerName = line.towers[ind].towerModel;
        let _exist_ = false;
        towersName.forEach((el: any) => {
          if (el === towerName) {
            _exist_ = true;
          }
        });
        if (_exist_ === false) {
          towersName.push(towerName);
        }
      }
    });

    const postPara = { "modelNames": towersName };
    try {
      const response = await axios.post(API_URL.towersInfo + `?t=${new Date().getTime()}`, postPara);
      const res = response.data;
      if (res.code === 0) {
        towers.value = res.data;
        towers.value.forEach((e: any) => {
          for (let key in e as any) {
            if (key.includes("qian") || key.includes("hou") || key.includes("xuan"))
              if ((e as any)[key])
                (e as any)[key] = JSON.parse((e as any)[key])
          }
        });
        console.log('towersRequest ok', towers.value);
        return true;
      } else {
        towers.value = [];
        return false;
      }
    } catch (error) {
      console.error('towersRequest failed:', error);
      towers.value = [];
      return false;
    }
  }

  const initData = async (token: string, projectId: string) => {
    ver.value = "1.0"
    if (await updateProjInfo(token, projectId) === true) {
      console.log('updata project info ok', projMsg.value);
      if (await updateLinesInfo(token) === true) {
        console.log('updata line info ok', linesInfo.value);
        if (await updateLineDetail() == true) {
          console.log('LineProjStore initData ok', lineDetail.value);
          return towersRequest(token);
        }
      } else {
        console.error('updateLinesInfo error');
      }
    } else {
      console.error('updateProjInfo error');
    }
    return Promise.resolve(false);
  }

  const get_insulators = async (token: string) => {
    if (dataUseful.value === true || lineDetail.value.length === 0) {
      return false;
    }
    function is_exist(name: string, modelsName: string[]) {
      if (name == undefined || modelsName == undefined) {
        return;
      }
      let _exist_ = false;
      modelsName.forEach((el: any) => {
        if (el === name || name === '/') {
          _exist_ = true;
        }
      });
      if (_exist_ === false) {
        modelsName.push(name);
      }
    }
    let insulatorsName: any[] = [];
    lineDetail.value.forEach((line: any) => {
      for (let ind = 0; ind < line.towers.length; ind++) {
        is_exist(line.towers[ind].tensionInsulator, insulatorsName);
        is_exist(line.towers[ind].overhangInsulator, insulatorsName);
        is_exist(line.towers[ind].groundInsulator, insulatorsName);
      }
    });

    const postPara = { "modelNames": insulatorsName };
    try {
      const response = await axios.post(API_URL.insulatorsInfo + `?t=${new Date().getTime()}`, postPara);
      const res = response.data;
      if (res.code === 0) {
        insulators.value = res.data;
        insulators.value.forEach((e: any) => {
          for (let key in e as any) {
            if (key.includes("point"))
              if ((e as any)[key])
                (e as any)[key] = JSON.parse((e as any)[key])
          }
        });
        console.log('insulator models request ok:', insulators.value);
        towers.value = convertData2NewFormat_towers(towers.value, lineDetail.value, insulators.value);
        return true;
      } else {
        insulators.value = [];
        return false;
      }
    } catch (error) {
      console.error('get_insulators failed:', error);
      insulators.value = [];
      return false;
    }
  }

  const get_insulatorAllocation = async (token: string, callback: any = function () { }) => {
    if (dataUseful.value === true || linesInfo.value.length === 0) {
      return false;
    }
    for (const line of linesInfo.value) {
      const postPara = { "lineId": line.lineId };
      try {
        const response = await axios.post(API_URL.insulatorAllocation + `?t=${new Date().getTime()}`, postPara);
        const res = response.data;
        if (res.code === 0) {
          for (let ind = 0; ind < res.data.length; ind++) {
            const info = res.data[ind];
            let _lineDetail_ = lineDetail.value.find((item: any, index: number) => { return line.lineId === item.lineId });
            if (!_lineDetail_) {
              continue;
            }
            const tower = _lineDetail_.towers.find((item: any, index: number) => { return item.towerId === info.towerId });
            if (tower == undefined) {
              console.error('无法找到配串表', info, _lineDetail_);
              continue;
            }
            tower.tensionInsulator = info.tensionInsulator;
            tower.overhangInsulator = info.overhangInsulator;
            tower.groundInsulator = info.groundInsulator;
          }
          console.log('line ', line.lineId, 'get insulator ok:', line);
        }
      } catch (error) {
        console.error('get_insulatorAllocation for line failed:', error);
      }
    }
    await get_insulators(token);
    callback(true);
  }

  const getInsulators_ext = async (insulatorsName: string[]) => {
    if (insulators.value.length === 0) {
      return false;
    }

    const existingNames = insulators.value.map((item: any) => item.modelName);
    const newNames = insulatorsName.filter((name: any) => !existingNames.includes(name));

    if (newNames.length === 0) {
      return true;
    }

    const postPara = { "modelNames": newNames };
    try {
      const response = await axios.post(API_URL.insulatorsInfo + `?t=${new Date().getTime()}`, postPara);
      const res = response.data;
      if (res.code === 0) {
        res.data.forEach((insulator: any) => {
          if (insulators.value.find((item: any) => item.modelName == insulator.modelName) == undefined) {
            insulators.value.push(insulator);
          }
        });
        console.log('insulator models request ok:', insulators.value);
        towers.value = convertData2NewFormat_towers(towers.value, lineDetail.value, insulators.value);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error('Error fetching insulators:', error);
      return false;
    }
  }

  const initDataV2 = async (projectId: string) => {
    ver.value = "2.0"
    let res = await getSwinsulatorAllocationlist(projectId)
    if (res.code != 0) {
      console.error('请求线路数据 SwinsulatorAllocationlist 错误，', res)
      return Promise.resolve(null)
    }
    let insulatorAllocationList = res.data

    res = await getSwtowerMountslist(projectId)
    if (res.code != 0) {
      console.error('请求线路数据 SwtowerMountslist 错误，', res)
      return Promise.resolve(null)
    }
    let towerMountslist = res.data
    // 这里处理后端返回的挂点数据 XY和符号相反的情况 
    const substationTowers = towerMountslist.filter((mount: any) => mount.towerModel === "substationModel")
    substationTowers.forEach((mount: any) => {
      let temp = mount.mountPosition[0]
      mount.mountPosition[0] = mount.mountPosition[1] * -1
      mount.mountPosition[1] = temp * -1
    })

    res = await getSwinsulatorModellist(projectId)
    if (res.code != 0) {
      console.error('请求线路数据 SwinsulatorModellist 错误，', res)
      return Promise.resolve(null)
    }
    let insulatorModelList = res.data
    for (let i = 0; i < insulatorModelList.length; i++) {
      switch (insulatorModelList[i].modelType) {
        case '0':
          insulatorModelList[i].modelType = '耐张串'
          break
        case '1':
          insulatorModelList[i].modelType = '悬垂串'
          break
      }
    }

    res = await getSwlinelist(projectId)
    if (res.code != 0) {
      console.error('请求线路数据 Swlinelist 错误，', res)
      return Promise.resolve(null)
    }
    let lineList = res.data

    res = await getSwtowerModellist(projectId)
    if (res.code != 0) {
      console.error('请求线路数据 SwtowerModellist 错误，', res)
      return Promise.resolve(null)
    }
    let towerModelList = res.data

    res = await getSwtowerList(projectId)
    if (res.code != 0) {
      console.error('请求线路数据 SwtowerList 错误，', res)
      return Promise.resolve(null)
    }
    let towerList = res.data
    // 转换角度字符串为数字
    for (let i = 0; i < towerList.length; i++) {
      towerList[i].angle = parseFloat(towerList[i].absoluteAngle)
      towerList[i].version = 'V2'
    }

    lineDataV20.value = {
      linesInfo: lineList,
      towerModels: towerModelList,
      lineDetail: towerList,
      towerMounts: towerMountslist,
      insulatorModels: insulatorModelList,
      insulatorAllocation: insulatorAllocationList,
      projectMsg: { projectId: projectId }
    }
    console.log('initDataV2:', lineDataV20.value)
      return Promise.resolve(lineDataV20.value)
    }

    return {
      msg,
      ver,
      projMsg,
      linesInfo,
      lineDetail,
      towers,
      dataUseful,
      insulators,
      proLines,
      tower_angles_fixed,
      lineDataV20,
      setProLines,
      updateProjInfo,
      updateLinesInfo,
      updateLineDetail,
      towersRequest,
      initData,
      get_insulators,
      get_insulatorAllocation,
      getInsulators_ext,
      initDataV2
    }
  }
)

function fixBaseHeight(towers: any[]) {
  // 修复数据中杆塔挂点没有加入杆塔呼高的偏差
  const keys = ["houdao1", "houdao10", "houdao11", "houdao12", "houdao2", "houdao3",
    "houdao4", "houdao5", "houdao6", "houdao7", "houdao8", "houdao9", "houdi1", "houdi2",
    "qiandao1", "qiandao10", "qiandao11", "qiandao12", "qiandao2", "qiandao3", "qiandao4",
    "qiandao5", "qiandao6", "qiandao7", "qiandao8", "qiandao9", "qiandi1", "qiandi2",
    "xuanchui1", "xuanchui10", "xuanchui11", "xuanchui12", "xuanchui2", "xuanchui3", "xuanchui4",
    "xuanchui5", "xuanchui6", "xuanchui7", "xuanchui8", "xuanchui9"];
  for (let ind = 0; ind < towers.length; ind++) {
    const tower = towers[ind];
    const towerBaseHeight = tower.baseHeight;
    keys.forEach((key: any) => {
      if (tower[key] && tower[key].length == 3) {
        tower[key][2] += towerBaseHeight;
        // 修正x和y轴相反的问题
        let temp = tower[key][0];
        tower[key][0] = tower[key][1];
        tower[key][1] = temp;
      }
    });
  }
}
function fixTowerAng(towers: any[], tower_angles_fixed: any[]) {
  let lastTowerPosition = [towers[0].longitude, towers[0].latitude, towers[0].height]; // 第一个杆塔的上一个塔没有 所以取它本身的值
  for (let ind = 1; ind < towers.length; ind++) {
    let nowTowerPosition = [towers[ind].longitude, towers[ind].latitude, towers[ind].height];
    let _check_fixed = tower_angles_fixed.find((item: any) => item.towerId == towers[ind].towerId);
    if (!_check_fixed) {
      let baseAngle = distance_ang(lastTowerPosition, nowTowerPosition)[1] * 180 / Math.PI + 90;
      towers[ind].angle = baseAngle + towers[ind].angle;
      tower_angles_fixed.push(towers[ind]) // 将修改过角度的杆塔进行记录 避免重复修改
    } else {
      towers[ind].angle = _check_fixed.angle;
    }
    lastTowerPosition = nowTowerPosition;
    // console.log('fixtowerang:',towers[ind].towerId, baseAngle, towers[ind].angle);
  }
}
function wgs84togcj02(towers: any[]) {
  for (let ind = 0; ind < towers.length; ind++) {
    const gcj02Coords = coordtransform.wgs84togcj02(towers[ind].longitude, towers[ind].latitude);
    towers[ind].longitude = gcj02Coords[0];
    towers[ind].latitude = gcj02Coords[1];
  }
}
function convertData2NewFormat_towers(towersInfo: any[], linesDetail: any[], insulators: any[]) {
  console.log('convertData2NewFormat_towers...', towersInfo);
  let oldData_towerModels = towersInfo;
  let newData: any[] = [];

  let find_tower = (modelName: string) => {
    let _result: any[] = [];
    linesDetail.forEach((item: any) => {
      // let t = item.towers.filter(element => element.towerModel == modelName);
      item.towers.forEach((element: any) => {
        if (element.towerModel == modelName) {
          _result.push(element);
        }
      });
    })
    if (_result.length < 2) {
      // 如果该线路配串表只出现一次该塔号，则表示当前工程在该塔只走了一回线路
      // 但是为了信息完整 直接将另一边的绝缘子赋值同样信息
      _result.push(_result[0]);
    }
    return _result;
  }

  oldData_towerModels.forEach((item: any) => {
    let nd: any = {}
    newData.push(nd)
    Object.assign(nd, item)
    nd.PointDetail = { qiandao: [], houdao: [], zhongdao: [], houdi: [], qiandi: [], zhongdi: [] }
    // 根据杆塔模型 从线路详情表中查询得到塔信息 塔信息中存在绝缘子配串信息
    let line_tower = find_tower(item['towerModel']); // 从线路中可能获取到多个同塔型号的对象
    if (!line_tower) {
      return;
    }

    for (var i = 0; i < 12; i++) {
      // 其中模型横担位置从1计数 i+1表示横担位置 135左上角；246右上角；7 9 11左下角；81012右下角
      // 根据横担位置选择回路配串信息 合并生成新的杆塔信息表
      let item_line_tower: any
      if (i == 0 || i == 2 || i == 4) {
        // 左上角 同杆线路位置是1
        item_line_tower = line_tower.filter((item: any) => item.lineLocation == 1)[0];
      } else if (i == 1 || i == 3 || i == 5) {
        // 右上角 同杆线路位置是2
        item_line_tower = line_tower.filter((item: any) => item.lineLocation == 2)[0];
      } else if (i == 6 || i == 8 || i == 10) {
        // 左下角 同杆线路位置是3
        item_line_tower = line_tower.filter((item: any) => item.lineLocation == 3)[0];
      } else if (i == 7 || i == 9 || i == 11) {
        // 右下角 同杆线路位置是4
        item_line_tower = line_tower.filter((item: any) => item.lineLocation == 4)[0];
      }
      // console.log('line_tower:',i,item_line_tower, line_tower);
      // 杆塔信息表中不存在该横担位置 为了信息完整 这里直接给一个值
      if (!item_line_tower) {
        item_line_tower = line_tower[0];
      }

      // 数据库的杆塔模型的挂点信息XY轴相反 这里先通过代码处理 后续需要模型组修正
      // let temp
      // temp = item[`qiandao${i + 1}`][0];
      // item[`qiandao${i + 1}`][0] = item[`qiandao${i + 1}`][1]
      // item[`qiandao${i + 1}`][1] = temp
      // temp = item[`xuanchui${i + 1}`][0];
      // item[`xuanchui${i + 1}`][0] = item[`xuanchui${i + 1}`][1]
      // item[`xuanchui${i + 1}`][1] = temp
      // temp = item[`houdao${i + 1}`][0];
      // item[`houdao${i + 1}`][0] = item[`houdao${i + 1}`][1]
      // item[`houdao${i + 1}`][1] = temp
      // temp = item[`houdao${i + 1}`][0];
      // item[`houdao${i + 1}`][0] = item[`houdao${i + 1}`][1]
      // item[`houdao${i + 1}`][1] = temp
      // if (i < 2) {
      //     temp = item[`houdi${i + 1}`][0];
      //     item[`houdi${i + 1}`][0] = item[`houdi${i + 1}`][1]
      //     item[`houdi${i + 1}`][1] = temp
      //     temp = item[`qiandi${i + 1}`][0];
      //     item[`qiandi${i + 1}`][0] = item[`qiandi${i + 1}`][1]
      //     item[`qiandi${i + 1}`][1] = temp
      // }
      //////////////////////////////////////////////////////////////////////
      // nd[`qiandao${i + 1}`] = item[`qiandao${i + 1}`]
      // nd[`xuanchui${i + 1}`] = item[`xuanchui${i + 1}`]
      // nd[`houdao${i + 1}`] = item[`houdao${i + 1}`]
      // nd[`qiandao${i + 1}`] = []
      // nd[`xuanchui${i + 1}`] =[]
      // nd[`houdao${i + 1}`] = []

      let qiandao = {
        position: item[`qiandao${i + 1}`] || [],
        number: i + 1,
        name: "前导" + (i + 1),
        // insulatorNo: "D0302-13-1"
        insulatorNo: item_line_tower.tensionInsulator
      }
      let zhongdao = {
        position: item[`xuanchui${i + 1}`] || [],
        number: i + 1,
        name: "中导" + (i + 1),
        // insulatorNo: "D0302-01"
        insulatorNo: item_line_tower.overhangInsulator
      }
      let houdao = {
        position: item[`houdao${i + 1}`] || [],
        number: i + 1,
        name: "后导" + (i + 1),
        // insulatorNo: "D0302-13-1"
        insulatorNo: item_line_tower.tensionInsulator
      }
      nd.PointDetail.qiandao.push(qiandao)
      nd.PointDetail.zhongdao.push(zhongdao)
      nd.PointDetail.houdao.push(houdao)
      if (i < 4) {
        let houdi = {
          position: item[`houdi${i + 1}`] || [],
          number: i + 1,
          name: "后地" + (i + 1),
          // insulatorNo: "D0302-15"
          insulatorNo: item_line_tower.groundInsulator
        }
        let qiandi = {
          position: item[`qiandi${i + 1}`] || [],
          number: i + 1,
          name: "前地" + (i + 1),
          // insulatorNo: "D0302-15"
          insulatorNo: item_line_tower.groundInsulator
        }
        nd.PointDetail.houdi.push(houdi)
        nd.PointDetail.qiandi.push(qiandi)
        // nd[`houdi${i + 1}`] = item[`houdi${i + 1}`]
        // nd[`qiandi${i + 1}`] = item[`qiandi${i + 1}`]

        // nd[`houdi${i + 1}`] = []
        // nd[`qiandi${i + 1}`] = []
      }
    }
  })
  console.log('convertData2NewFormat_towers ok:', newData);
  return newData;
}
