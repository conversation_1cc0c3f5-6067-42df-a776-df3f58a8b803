declare module '@/js/common/line_tower';

export interface ITower {
  towerId: string;
  towerModel: string;
  longitude: number;
  latitude: number;
  height: number;
  angle: number;
  procedureStatusName: string;
  procedureStatus: number;
  [key: string]: any;
}

export interface ILine {
   lineId: string;
   towers: ITower[];
   [key: string]: any;
 }

export interface IInsulator {
    id?: string,
    series: string,
    modelName: string,
    url: string,
    modelType: string,
    lineType: string,
    length: number,
    connectNum: number,
    splitNum: number,
    projectId: number,
    version: string,
    inputLine1?: number[],
    inputLine2?: number[],
    inputLine3?: number[],
    inputLine4?: number[],
    outputLine1?: number[],
    [key: string]: any;
}