// 动态优化器
export class DynamicOptimizer {
  private viewer: any;
  private monitor: PerformanceMonitor;
  private currentMode: 'high' | 'balanced' | 'quality' = 'balanced';
  private autoAdjust: boolean = true;
  private stabilityCounter: number = 0;
  private lastStatus: 'good' | 'warning' | 'critical' | null = null;
  
  // 场景设置
  private sceneSettings = {
    high: {
      fog: false,
      msaa: false,
      ssao: false,
      bloom: false,
      shadowMapSize: 1024,
      fxaa: false
    },
    balanced: {
      fog: true,
      msaa: false,
      ssao: false,
      bloom: false,
      shadowMapSize: 2048,
      fxaa: true
    },
    quality: {
      fog: true,
      msaa: true,
      ssao: true,
      bloom: true,
      shadowMapSize: 4096,
      fxaa: true
    }
  };

  constructor(viewer: any) {
    this.viewer = viewer;
    this.monitor = new PerformanceMonitor(viewer);
  }

  // 启动动态优化
  start(): PerformanceMonitor {
    // 应用初始设置
    this.applySettings(this.currentMode);
    
    // 启动性能监控
    this.monitor.start((fps, status) => {
      if (this.autoAdjust) {
        this.adjustSettings(fps, status);
      }
    });
    
    return this.monitor;
  }

  // 停止动态优化
  stop(): void {
    this.monitor.stop();
  }

  // 设置性能模式
  setMode(mode: 'high' | 'balanced' | 'quality', autoAdjust: boolean = true): void {
    this.currentMode = mode;
    this.autoAdjust = autoAdjust;
    this.applySettings(mode);
  }

  // 应用设置
  private applySettings(mode: 'high' | 'balanced' | 'quality'): void {
    const settings = this.sceneSettings[mode];
    const scene = this.viewer.scene;
    
    // 应用场景设置
    scene.fog.enabled = settings.fog;
    scene.msaaSamples = settings.msaa ? 4 : 1;
    
    // 环境光遮蔽
    if (scene.postProcessStages) {
      const ssao = scene.postProcessStages.ambientOcclusion;
      if (ssao) {
        ssao.enabled = settings.ssao;
      }
    }
    
    // 泛光效果
    if (scene.postProcessStages) {
      const bloom = scene.postProcessStages.bloom;
      if (bloom) {
        bloom.enabled = settings.bloom;
      }
    }
    
    // 阴影设置
    if (scene.shadowMap) {
      scene.shadowMap.size = settings.shadowMapSize;
    }
    
    // FXAA抗锯齿
    scene.postProcessStages.fxaa.enabled = settings.fxaa;
    
    // 其他性能相关设置
    scene.globe.maximumScreenSpaceError = mode === 'high' ? 4 : (mode === 'balanced' ? 2 : 1);
    scene.globe.tileCacheSize = mode === 'high' ? 100 : (mode === 'balanced' ? 200 : 500);
    scene.globe.enableLighting = mode !== 'high';
    
    // 模型相关设置
    this.applyModelSettings(mode);
  }

  // 应用模型设置
  private applyModelSettings(mode: 'high' | 'balanced' | 'quality'): void {
    // 这里可以遍历场景中的所有模型并应用相应设置
    // 例如调整模型的最小像素大小、最大缩放等
    const primitives = this.viewer.scene.primitives;
    for (let i = 0; i < primitives.length; i++) {
      const primitive = primitives.get(i);
      
      if (primitive instanceof Cesium.Model) {
        // 调整模型设置
        switch (mode) {
          case 'high':
            primitive.minimumPixelSize = 64;
            primitive.maximumScale = 10000;
            primitive.shadows = false;
            break;
          case 'balanced':
            primitive.minimumPixelSize = 96;
            primitive.maximumScale = 15000;
            primitive.shadows = true;
            break;
          case 'quality':
            primitive.minimumPixelSize = 128;
            primitive.maximumScale = 20000;
            primitive.shadows = true;
            break;
        }
      } else if (primitive instanceof Cesium.Cesium3DTileset) {
        // 调整3D Tiles设置
        switch (mode) {
          case 'high':
            primitive.maximumScreenSpaceError = 32;
            primitive.dynamicScreenSpaceErrorFactor = 6.0;
            break;
          case 'balanced':
            primitive.maximumScreenSpaceError = 16;
            primitive.dynamicScreenSpaceErrorFactor = 4.0;
            break;
          case 'quality':
            primitive.maximumScreenSpaceError = 8;
            primitive.dynamicScreenSpaceErrorFactor = 2.0;
            break;
        }
      }
    }
  }

  // 动态调整设置
  private adjustSettings(fps: number, status: 'good' | 'warning' | 'critical'): void {
    // 如果状态与上次相同，增加稳定计数器
    if (status === this.lastStatus) {
      this.stabilityCounter++;
    } else {
      this.stabilityCounter = 0;
      this.lastStatus = status;
    }
    
    // 只有当状态稳定一段时间后才调整设置
    if (this.stabilityCounter < 5) return;
    
    // 根据性能状态调整设置
    switch (status) {
      case 'critical':
        // 性能严重不足，切换到高性能模式
        if (this.currentMode !== 'high') {
          console.log('性能不足，切换到高性能模式');
          this.setMode('high', true);
        }
        break;
        
      case 'warning':
        // 性能不佳，切换到平衡模式
        if (this.currentMode === 'quality') {
          console.log('性能不佳，切换到平衡模式');
          this.setMode('balanced', true);
        }
        break;
        
      case 'good':
        // 性能良好，可以考虑提高质量
        if (this.currentMode === 'high') {
          console.log('性能良好，切换到平衡模式');
          this.setMode('balanced', true);
        } else if (this.currentMode === 'balanced' && fps > 50) {
          console.log('性能优秀，切换到高质量模式');
          this.setMode('quality', true);
        }
        break;
    }
    
    // 重置稳定计数器
    this.stabilityCounter = 0;
  }

  // 获取当前模式
  getMode(): 'high' | 'balanced' | 'quality' {
    return this.currentMode;
  }

  // 设置自动调整
  setAutoAdjust(enabled: boolean): void {
    this.autoAdjust = enabled;
  }
} 