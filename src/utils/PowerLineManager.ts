import { EnhancedModelCache } from './ModelCache';
import { ModelLoader, ModelOptimizer } from './ModelLoader';
import { GpuInstanceRenderer, LodManager } from './InstanceRenderer';
import { PerformanceMonitor, DynamicOptimizer } from './PerformanceMonitor';

// 电力线管理器 - 整合所有优化技术
export default class PowerLineManager {
  private viewer: any;
  private modelCache: EnhancedModelCache;
  private modelLoader: ModelLoader;
  private modelOptimizer: ModelOptimizer;
  private instanceRenderer: GpuInstanceRenderer;
  private lodManager: LodManager;
  private dynamicOptimizer: DynamicOptimizer;
  
  // 模型集合
  private insulators: Map<string, any> = new Map();
  private towers: Map<string, any> = new Map();
  private lines: Map<string, any> = new Map();
  
  // 是否使用实例化渲染
  private useInstancing: boolean = true;
  
  // 是否使用LOD
  private useLod: boolean = true;
  
  // 当前性能模式
  private currentMode: 'high' | 'balanced' | 'quality' = 'balanced';

  constructor(viewer: any) {
    this.viewer = viewer;
    
    // 初始化组件
    this.modelCache = new EnhancedModelCache();
    this.modelLoader = new ModelLoader();
    this.modelOptimizer = new ModelOptimizer();
    this.instanceRenderer = new GpuInstanceRenderer(viewer, this.modelCache);
    this.lodManager = new LodManager(viewer, this.modelCache);
    this.dynamicOptimizer = new DynamicOptimizer(viewer);
    
    // 注册LOD模型
    this.registerDefaultLodModels();
    
    // 设置初始性能模式
    this.setPerformanceMode('balanced');
  }

  // 注册默认LOD模型
  private registerDefaultLodModels(): void {
    // 注册绝缘子LOD模型
    this.lodManager.registerLodModel(
      'insulator_type1',
      '/models/insulators/insulator_type1_high.glb',
      '/models/insulators/insulator_type1_medium.glb',
      '/models/insulators/insulator_type1_low.glb'
    );
    
    this.lodManager.registerLodModel(
      'insulator_type2',
      '/models/insulators/insulator_type2_high.glb',
      '/models/insulators/insulator_type2_medium.glb',
      '/models/insulators/insulator_type2_low.glb'
    );
    
    this.lodManager.registerLodModel(
      'insulator_type3',
      '/models/insulators/insulator_type3_high.glb',
      '/models/insulators/insulator_type3_medium.glb',
      '/models/insulators/insulator_type3_low.glb'
    );
    
    // 注册杆塔LOD模型
    this.lodManager.registerLodModel(
      'tower_type1',
      '/models/towers/tower_type1_high.glb',
      '/models/towers/tower_type1_medium.glb',
      '/models/towers/tower_type1_low.glb'
    );
    
    this.lodManager.registerLodModel(
      'tower_type2',
      '/models/towers/tower_type2_high.glb',
      '/models/towers/tower_type2_medium.glb',
      '/models/towers/tower_type2_low.glb'
    );
  }

  // 加载绝缘子
  async loadInsulators(insulators: any[]): Promise<void> {
    console.log(`加载${insulators.length}个绝缘子...`);
    
    // 按URL分组
    const insulatorsByUrl = this.groupByUrl(insulators);
    
    // 加载每种类型的绝缘子
    for (const [url, instances] of Object.entries(insulatorsByUrl)) {
      if (this.useInstancing && instances.length > 1) {
        // 使用实例化渲染
        await this.instanceRenderer.createInstanceCollection(url, instances);
      } else {
        // 单独加载每个绝缘子
        for (const insulator of instances) {
          if (this.useLod) {
            // 使用LOD加载
            const modelType = this.getModelTypeFromUrl(url);
            const model = await this.lodManager.loadModel(
              modelType,
              insulator.position,
              {
                scale: insulator.scale,
                angle: insulator.angle,
                metadata: { type: 'insulator', id: insulator.id }
              }
            );
            this.insulators.set(insulator.id, model);
          } else {
            // 直接加载
            const model = await this.modelCache.getModel(
              insulator.id,
              url,
              {
                scale: insulator.scale,
                angle: insulator.angle,
                metadata: { type: 'insulator', id: insulator.id },
                optimizationLevel: this.currentMode
              }
            );
            this.insulators.set(insulator.id, model);
          }
        }
      }
    }
    
    console.log(`绝缘子加载完成`);
  }

  // 加载杆塔
  async loadTowers(towers: any[]): Promise<void> {
    console.log(`加载${towers.length}个杆塔...`);
    
    // 按URL分组
    const towersByUrl = this.groupByUrl(towers);
    
    // 加载每种类型的杆塔
    for (const [url, instances] of Object.entries(towersByUrl)) {
      if (this.useInstancing && instances.length > 1) {
        // 使用实例化渲染
        await this.instanceRenderer.createInstanceCollection(url, instances);
      } else {
        // 单独加载每个杆塔
        for (const tower of instances) {
          if (this.useLod) {
            // 使用LOD加载
            const modelType = this.getModelTypeFromUrl(url);
            const model = await this.lodManager.loadModel(
              modelType,
              tower.position,
              {
                scale: tower.scale,
                angle: tower.angle,
                metadata: { type: 'tower', id: tower.id }
              }
            );
            this.towers.set(tower.id, model);
          } else {
            // 直接加载
            const model = await this.modelCache.getModel(
              tower.id,
              url,
              {
                scale: tower.scale,
                angle: tower.angle,
                metadata: { type: 'tower', id: tower.id },
                optimizationLevel: this.currentMode
              }
            );
            this.towers.set(tower.id, model);
          }
        }
      }
    }
    
    console.log(`杆塔加载完成`);
  }

  // 加载线路
  async loadLines(lines: any[]): Promise<void> {
    console.log(`加载${lines.length}条线路...`);
    
    // 线路通常使用Cesium的Polyline或自定义着色器实现
    // 这里简化处理，实际项目中可能需要更复杂的实现
    for (const line of lines) {
      const polyline = this.viewer.entities.add({
        polyline: {
          positions: line.positions.map((pos: number[]) => 
            Cesium.Cartesian3.fromDegrees(pos[0], pos[1], pos[2] || 0)
          ),
          width: line.width || 2,
          material: new Cesium.PolylineOutlineMaterialProperty({
            color: Cesium.Color.fromCssColorString(line.color || '#FFFFFF'),
            outlineWidth: 1,
            outlineColor: Cesium.Color.BLACK
          })
        }
      });
      
      this.lines.set(line.id, polyline);
    }
    
    console.log(`线路加载完成`);
  }

  // 按URL分组
  private groupByUrl(models: any[]): Record<string, any[]> {
    const result: Record<string, any[]> = {};
    
    for (const model of models) {
      if (!result[model.url]) {
        result[model.url] = [];
      }
      result[model.url].push(model);
    }
    
    return result;
  }

  // 从URL获取模型类型
  private getModelTypeFromUrl(url: string): string {
    // 从URL中提取模型类型
    // 例如：/models/insulators/insulator_type1.glb -> insulator_type1
    const match = url.match(/\/([^\/]+)\.glb$/);
    return match ? match[1] : 'unknown';
  }

  // 设置性能模式
  setPerformanceMode(mode: 'high' | 'balanced' | 'quality'): void {
    this.currentMode = mode;
    
    // 应用场景设置
    this.dynamicOptimizer.setMode(mode, false);
    
    // 更新所有已加载模型的设置
    this.updateAllModelSettings();
  }

  // 更新所有模型设置
  private updateAllModelSettings(): void {
    // 更新绝缘子设置
    for (const model of this.insulators.values()) {
      this.modelOptimizer.optimizeModel(model, this.currentMode);
    }
    
    // 更新杆塔设置
    for (const model of this.towers.values()) {
      this.modelOptimizer.optimizeModel(model, this.currentMode);
    }
  }

  // 启用动态性能优化
  enableDynamicOptimization(): PerformanceMonitor {
    // 启动动态优化器
    const monitor = this.dynamicOptimizer.start();
    
    // 启用自动调整
    this.dynamicOptimizer.setAutoAdjust(true);
    
    return monitor;
  }

  // 禁用动态性能优化
  disableDynamicOptimization(): void {
    this.dynamicOptimizer.stop();
  }

  // 设置是否使用实例化渲染
  setUseInstancing(enabled: boolean): void {
    this.useInstancing = enabled;
  }

  // 设置是否使用LOD
  setUseLod(enabled: boolean): void {
    this.useLod = enabled;
  }

  // 获取缓存统计信息
  async getCacheStats(): Promise<{dbModels: number, memorySize: number}> {
    return await this.modelCache.getCacheStats();
  }

  // 清理过期缓存
  async cleanExpiredCache(maxAge?: number): Promise<void> {
    await this.modelCache.cleanExpiredCache(maxAge);
  }

  // 释放资源
  dispose(): void {
    // 释放实例渲染器
    this.instanceRenderer.dispose();
    
    // 释放LOD管理器
    this.lodManager.dispose();
    
    // 释放模型缓存
    this.modelCache.dispose();
    
    // 停止动态优化
    this.dynamicOptimizer.stop();
    
    // 清理绝缘子
    for (const model of this.insulators.values()) {
      this.viewer.scene.primitives.remove(model);
    }
    this.insulators.clear();
    
    // 清理杆塔
    for (const model of this.towers.values()) {
      this.viewer.scene.primitives.remove(model);
    }
    this.towers.clear();
    
    // 清理线路
    for (const entity of this.lines.values()) {
      this.viewer.entities.remove(entity);
    }
    this.lines.clear();
  }
} 