import {decrypt, encrypt} from "@/utils/jsencrypt";

const AccessTokenKey = 'B_ACCESS_TOKEN'
const RefreshTokenKey = 'B_REFRESH_TOKEN'
const ProjectId  = 'B_DefaultProjectId'
const ExpiresTime = 'B_EXPIRES_TIME'

// ========== Token 相关 ==========

export function getAccessToken() {
  return localStorage.getItem(AccessTokenKey)
}

export function getRefreshToken() {
  return localStorage.getItem(RefreshTokenKey)
}

export function setToken(token) {
  localStorage.setItem(AccessTokenKey, token.accessToken)
  localStorage.setItem(RefreshTokenKey, token.refreshToken)
  localStorage.setItem(ProjectId,token.defaultProjectId)
  localStorage.setItem(ExpiresTime,token.expiresTime)
}
export function setSimpleToken(token) {
  localStorage.setItem(AccessTokenKey, token.accessToken)
}
export function getDefaultProjectId(){
  return localStorage.getItem(ProjectId) || "1"
}
export function getExpiresTime(){
  return localStorage.getItem(ExpiresTime)
}



export function removeToken() {
  localStorage.removeItem(AccessTokenKey)
  localStorage.removeItem(RefreshTokenKey)
}

// ========== 账号相关 ==========

const UsernameKey = 'B_USERNAME'
const PasswordKey = 'B_PASSWORD'
const RememberMeKey = 'B_REMEMBER_ME'

export function getUsername() {
  return localStorage.getItem(UsernameKey)
}

export function setUsername(username) {
  localStorage.setItem(UsernameKey, username)
}

export function removeUsername() {
  localStorage.removeItem(UsernameKey)
}

export function getPassword() {
  const password = localStorage.getItem(PasswordKey)
  return password ? decrypt(password) : undefined
}

export function setPassword(password) {
  localStorage.setItem(PasswordKey, encrypt(password))
}

export function removePassword() {
  localStorage.removeItem(PasswordKey)
}

export function getRememberMe() {
  return localStorage.getItem(RememberMeKey) === 'true'
}

export function setRememberMe(rememberMe) {
  localStorage.setItem(RememberMeKey, rememberMe)
}

export function removeRememberMe() {
  localStorage.removeItem(RememberMeKey)
}


export class getToken {
}
