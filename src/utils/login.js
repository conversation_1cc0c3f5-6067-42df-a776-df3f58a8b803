import require from "@/utils/requires"

export async function applogin(username, password,base64Data=0) {
  const data = {
    username,
    password,
    base64Data
  }
  return require({
    url: '/admin-api/system/auth/applogin',
    method: 'post',
    data: data
  })
}


export async function refreshToken(data) {
  
  return require({
    url: '/admin-api/system/auth/tokenRefresh',
    method: 'post',
    data: data
  })
}
