/**
 * 地图服务URL配置接口
 */
export interface MapServiceConfig {
    url: string;
    subdomains?: string[];
    minimumLevel?: number;
    maximumLevel?: number;
}

/**
 * 获取地图服务配置
 * @returns 地图服务配置对象
 */
export function getMapServiceUrl(): MapServiceConfig {
    return {
        url: '/geoserver/gwc/service/wmts',
        minimumLevel: 0,
        maximumLevel: 18
    };
}

/**
 * 获取地形服务URL
 * @returns 地形服务URL
 */
export function getTerrainServiceUrl(): string {
    return '/terrain';
}

/**
 * 获取完整的地图服务URL，优先使用环境变量
 * @returns 完整的地图服务URL
 */
export function getFullMapServiceUrl(): string {
    // 检查环境变量中是否配置了本地地图URL
    const envLocalMapUrl = (import.meta as any).env.VITE_LOCALMAP_URL;
    const localMapEnabled = (import.meta as any).env.VITE_LOCALMAP === '1';
    
    console.log('地图服务配置:', {
        VITE_LOCALMAP: (import.meta as any).env.VITE_LOCALMAP,
        VITE_LOCALMAP_URL: envLocalMapUrl,
        localMapEnabled
    });
    
    // 如果启用了本地地图且配置了URL，则使用环境变量中的URL
    if (localMapEnabled && envLocalMapUrl) {
        // 确保URL不以/结尾，以便后续可以安全地拼接路径
        const baseUrl = envLocalMapUrl.endsWith('/') 
            ? envLocalMapUrl.slice(0, -1) 
            : envLocalMapUrl;
            
        console.log('使用环境变量中配置的地图服务URL:', baseUrl);
        return baseUrl;
    }
    
    // 默认返回相对路径，由代理处理
    console.log('使用默认地图服务URL: /geoserver');
    return '/geoserver';
} 