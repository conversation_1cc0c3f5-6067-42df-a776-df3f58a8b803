import router from "@/router";
import { ElMessage } from 'element-plus'
import { getAccessToken,removeToken,setToken } from "@/utils/auth";
import { applogin } from "@/utils/login";
// 增加三方登陆 update by 芋艿
const whiteList = [
  "/login"
];

function applogin2(username) {
  return new Promise((resolve, reject) => {
    let password = "kkkkkkkkkkkkk"
    let base64Data = 1
    applogin(username, password,base64Data).then(res => {
      if (res) {
        console.log(res.data.code === 0)
        if (res.data.code === 0) {
          res = res.data.data;
          // 设置 token
          setToken(res)
          // debugger
          resolve(true)
          // 跳转到 /home
          // this.$router.push("/");

        } else {
          ElMessage.error('登录失败,请检查账号密码')
          resolve(false)
        }
        // this.loading = false
        // resolve()
      }
    })
  })

}

router.beforeEach(async (to, from, next) => {
  // debugger
  if(to.query.username) {
    let loginSuccess = await applogin2(to.query.username)
    if(!loginSuccess){
      removeToken()
    }
  }
  
  if (getAccessToken() && getAccessToken().length === 32) {
    // if (to.query.userName)
    //   applogin2().then(success => {
    //     if (success) {
    //       window.location.reload()
    //     } else {

    //     }
    //   })
    next();
    /* has token*/
    // if (to.path === "/login") {
    //   // next({ path: "/" });
    // }
    // else {
    //   const redirect = encodeURIComponent(to.fullPath); // 编码 URI，保证参数跳转回去后，可以继续带上
    //   next(`/login?redirect=${redirect}`); // 否则全部重定向到登录页
    // }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入

      

      // 
       next();
    } else {
      const redirect = encodeURIComponent(to.fullPath); // 编码 URI，保证参数跳转回去后，可以继续带上
      next(`/login?redirect=${redirect}`); // 否则全部重定向到登录页
    }
  }
});

router.afterEach(() => {
});
