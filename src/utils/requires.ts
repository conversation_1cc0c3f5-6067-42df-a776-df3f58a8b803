import axios from "axios";
// @ts-ignore
import { getAccessToken } from "@/utils/auth";
const server = axios.create({
    timeout: 60000 * 20,
    baseURL: import.meta.env.VITE_BASE_URL
})

// request拦截器
server.interceptors.request.use(config => {
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false
    if (getAccessToken() && !isToken) {
        config.headers['Authorization'] = 'Bearer ' + getAccessToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
        let url = config.url + '?';
        for (const propName of Object.keys(config.params)) {
            const value = config.params[propName];
            const part = encodeURIComponent(propName) + '='
            if (value !== null && typeof(value) !== "undefined") {
                if (typeof value === 'object') {
                    for (const key of Object.keys(value)) {
                        let params = propName + '[' + key + ']';
                        const subPart = encodeURIComponent(params) + '='
                        url += subPart + encodeURIComponent(value[key]) + "&";
                    }
                } else {
                    url += part + encodeURIComponent(value) + "&";
                }
            }
        }
        url = url.slice(0, -1);
        config.params = {};
        config.url = url;
    }
    return config
}, error => {
    console.log(error)
    Promise.reject(error)
})

// 添加响应拦截器
server.interceptors.response.use(function (response) {
    // 如果响应状态码为401，则重定向到登录页面
    if (response.data.code === 401) {
        console.warn('认证已过期或无效，正在重定向到登录页面...');

        // 检查是否为演示模式，如果是则不重定向
        const currentToken = localStorage.getItem('token');
        const isDemoMode = currentToken && currentToken.startsWith('demo-token-');

        if (isDemoMode) {
            console.log('演示模式：跳过登录重定向');
            // 在演示模式下，直接返回错误而不重定向
            return Promise.reject(new Error('认证已过期，请重新登录'));
        }

        // 非演示模式才重定向
        console.log('非演示模式：执行登录重定向');
        window.location.href = import.meta.env.VITE_CONTEXT+'login';
        return Promise.reject(new Error('认证已过期，请重新登录'));
    } else {
        return response;
    }
}, function (error) {
    // 对响应错误做点什么
    console.log('请求错误：', error)
    if (error.response && error.response.status === 401) {
        console.warn('认证已过期或无效，正在重定向到登录页面...');

        // 检查是否为演示模式
        const currentToken = localStorage.getItem('token');
        const isDemoMode = currentToken && currentToken.startsWith('demo-token-');

        if (!isDemoMode) {
            console.log('非演示模式：执行登录重定向');
            window.location.href = import.meta.env.VITE_CONTEXT+'login';
        } else {
            console.log('演示模式：跳过登录重定向');
        }
    }
    return Promise.reject(error);
});
export default server
