export interface ResizeHandler {
    handleResize: () => void;
    cleanup: () => void;
}

export interface EChartsInstance {
    resize: (opts?: { width?: number; height?: number }) => void;
}

export function createResizeHandler(chart: EChartsInstance, container: HTMLElement): ResizeHandler {
    let resizeTimeout: number | null = null;

    const handleResize = () => {
        if (resizeTimeout) {
            window.clearTimeout(resizeTimeout);
        }

        resizeTimeout = window.setTimeout(() => {
            if (chart && container) {
                chart.resize({
                    width: container.clientWidth,
                    height: container.clientHeight
                });
            }
        }, 100);
    };

    const cleanup = () => {
        if (resizeTimeout) {
            window.clearTimeout(resizeTimeout);
            resizeTimeout = null;
        }
    };

    // 初始化大小
    handleResize();

    return {
        handleResize,
        cleanup
    };
}

// 其他辅助函数... 