<template>
    <LeafletMap ref="leafletMap" class="leafletMapDiv" v-if="isShowleafletMap"></LeafletMap>
    <div id="cesiumDiv"></div>
</template>

<script>
import { getCurrentInstance } from 'vue'
import { addModel_glb } from "@/js/3dViewer.js";
import { getMountPosition } from "@/js/common/line_tower";
import {
    addTowers,
    addLabels,
    addSgTowers,
    addSgLabels,
    getInsulatorPos,
    addInsulators,
    addLine,
    distance_ang,
    getLine,
    computeHeight,
    adddivHTML
} from '@/js/common/line_tower.js'
import { MODEL_STATIC_URL, getToken, getInitProjectId } from '@/config/global.js'
import {
    initViewer,
    flyTo,
    clickGetPoint,
    getPositionHeight
} from '@/js/common/viewer'
import Vehicle from '@/js/animation/vehicle.ts'
import Road from '@/js/animation/road.ts'
import { getproject, getprojectStructure } from '@/js/common/requestData.ts'
import { ElMessage } from 'element-plus'
import { applogin, refreshToken } from "@/utils/login"
import { setSimpleToken, getAccessToken } from '@/utils/auth.js'
import { useLineProjStore } from '@/store/lineProj.js'
import { useProjectStore } from '@/store/computedLineDetail'
import Map3DTool_v2 from "@/js/map3d_v2/index.ts"
// import Map3DTool from "@/js/map3d/index.js";
import CommandManager from '@/js/commandManager/index.ts'
import { DivLabel, deleteLabel, deleteAllLabels } from '@/js/common/divLabel.js'
import { protectedAreaDataPreprocessing } from '@/js/common/protectedAreaDataPreprocessing.ts'
import { straight_line, overheadLine } from '@/js/common/line_tower.js'
import GeojsonService from '@/js/common/geojson.ts'
import { Roaming } from "@/js/common/roaming.js"
import ZhongNanDianHandler from '@/js/zhongnandian/index.ts'
import { loadExcavateSurface, clearExcavateSurface } from '@/js/common/excavateSurface'
import { addPolylineVolume } from '@/js/common/undergroundLine'
import SCENE_TOOL from "@/js/scene";
import LeafletMap from '@/components/LeafletMap.vue';
import {addFlattenTerrain} from '@/js/common/flattenTerrain.ts'
import { createLineCollection, destroyLineCollection } from "@/js/common/viewer.ts"
import ModelCache from '@/js/common/modelCache.ts'

export default {
    name: 'myDemo01',
    components: {
      LeafletMap
    },
    data () {
        return {
            $cache: {},
            loading: false,
            projectList: null,
            projectStructure: null,
            planRecordMap: {},
            map3d_v2: null,
            map3d: null,
            buildingConfig: {},
            obliquePhotographyConfig: {},
            obliquePhotographyLabelList: [],
            constructionModelConfig: {},
            roaming: null,
            roamingFlag: { enable: false, lineName: null },
            flagBIM: true,
            zhongNanDianIndex: 'znd_close',
            jiaoChaKuaYueIndex: 'jiaoChaKuaYue_close',
            tilesetD3dms: [],
            currentLoadedModel: null,
            myZhongNanDianHandler: null,
            myJiaoChaKuaYueHandler: null,
            flagShigongjinduLabel: false,
            flagConstructionLabel: { type: '全部', enable: false }, // 初始化为对象，默认类型为'全部'，状态为false
            myShigongjinduLabelHandler: null,
            flagLoadLineBuffer: false,
            flagLoadInsulator: false,
            innovateData: [{
              "name":"绝缘子片",
              "index":"innovate_1",
              "innovateView":{
                  "longitude": 114.44727059162395,
                  "latitude": 22.580275680721055,
                  "height": 206.24023718991714,
                  "duration": 6,
                  "heading": 5.969023110735243,
                  "pitch":  -9.153144908680133e-11
              },
              "modelId":"insulator@A4@xuanchui4@SA20151S-D0401-03",
              "desc":"<div style=\"font-size:16px\">绝缘子片技术，第一次在架空线路中实施，在广东、全国都是属于首创，在本次能东项目中实施，验证的此技术的同时，取得了较好的经济效益。本项目共采用了100片，节约成本30万余元！"
            }],
            protectedAreaEntities: [],
            constructionLabelEntities: [],
            flagProtectedAreaLabel: false,
            isShowleafletMap: true, // 控制小地图开启 隐藏
            cameraPosition: {position:[23.08, 113.14], angle:45},
            constructionModelExample: null,
            projectDataReady: false, // 添加项目数据加载状态标志
            pendingRoamingCommand: null, // 添加待处理的巡航命令存储
            cablewayLineCollection: null, // 索道连线
            crossLineCollection: null, // 新建跨越新线路
            subsidiaryModels: [] // 倾斜摄影附属模型
        }
    },
    watch: {
        async projectStructure (newValue, oldValue) {
            console.log('projectStructure2:', oldValue, newValue)
            if (!newValue || oldValue?.id == newValue?.id) {
                return
            }
            
            this.projectDataReady = false; // Reset loading state
            this.removeModel()
            this.flyToHome(4.5)
            await this.loadPowerLine()
            this.loadPowerLineInLeafletMap()
            await this.loadBuilding()
            await this.loadProtectedArea() // Load protected area data
            deleteLabel()
            
            // Only mark ready after ALL initialization is complete
            this.projectDataReady = true;
            console.log('Project data fully loaded, ready for roaming');
            
            // 处理任何待处理的巡航命令
            if (this.pendingRoamingCommand) {
                console.log('Processing pending roaming command', this.pendingRoamingCommand);
                this.roamingFlag = this.pendingRoamingCommand;
                this.pendingRoamingCommand = null;
            }
        },
        roamingFlag (newValue, oldValue) {
            // 停止巡航的情况
            if (newValue.enable == false) {
                console.log('stop roaming')
                // 停止巡航
                if (this.roaming) {
                    this.roaming.roaming_stop(viewer)
                    // 显示全部线路 和 实体对象
                    let lineCollection = this.map3d_v2.getLineCollection()
                    for (let key in lineCollection) {
                        lineCollection[key].show = true
                    }
                    this.map3d_v2.setAllModelShow(true)
                    this.roaming = undefined
                    this.flyToHome(0.5)
                }
                return
            }

            // 检查数据是否准备好
            if (!this.projectDataReady) {
                console.log('Project data not fully loaded, queueing roaming command');
                this.pendingRoamingCommand = newValue;
                
                // 通知用户
                this.myCommandManager.sendMessageToUE({
                    id: "0000000",
                    type: "startRoaming",
                    success: true,
                    msg: "巡航请求已接收，正在等待项目数据加载完成"
                });
                return;
            }
            
            // 初始化roaming实例
            if (this.roaming == undefined) {
                this.roaming = new Roaming()
            }

            console.log('roaming new data', newValue)
            // 1- 根据key获取对应的巡航路径杆塔
            const store_lineProj = useLineProjStore()
            
            // 调试输出全部线路
            console.log('All available lines:', store_lineProj.proLines.map(line => line.name))
            
            // 检查线路是否存在
            const targetLine = store_lineProj.proLines.find(item => item.name === newValue.lineName)
            if (!targetLine) {
                console.error(`找不到线路: ${newValue.lineName}`)
                // 通知UI显示错误
                this.myCommandManager.sendMessageToUE({
                    id: "0000000",
                    type: "startRoaming",
                    success: false,
                    msg: `找不到线路: ${newValue.lineName}`
                })
                return
            }
            
            // 检查线路是否有子塔
            if (!targetLine.children || targetLine.children.length === 0) {
                console.error(`线路 ${newValue.lineName} 没有关联的塔`)
                this.myCommandManager.sendMessageToUE({
                    id: "0000000",
                    type: "startRoaming",
                    success: false,
                    msg: `线路 ${newValue.lineName} 没有关联的塔`
                })
                return
            }
            
            console.log('Target line children before filtering:', targetLine.children);
            let towers = targetLine.children.filter(item => {
                console.log('Tower item:', item);
                return item.towerNumber && !item.towerNumber.includes('tower_virtual');
            });
            console.log('Filtered towers:', towers);
            
            if (towers.length < 2) {
                console.error(`线路 ${newValue.lineName} 的有效杆塔数量不足(至少需要2个)`)
                this.myCommandManager.sendMessageToUE({
                    id: "0000000",
                    type: "startRoaming",
                    success: false,
                    msg: `线路杆塔数量不足，无法进行巡航`
                })
                return
            }
            
            // 2- 根据杆塔 获取对应的坐标
            let allLineTowerDetail = this.map3d_v2 ? this.map3d_v2.getAllLineTowerDetail() : null;
            console.log('allLineTowerDetail count:', allLineTowerDetail ? allLineTowerDetail.length : 0)
            let positions = []
            for (let i = 0; i < towers.length; i++) {
                let tower = towers[i]
                let towerDetail = allLineTowerDetail.find(item => item.towerNumber == tower.name)
                if (towerDetail != undefined) {
                    console.log('towerDetail', towerDetail)
                    positions.push([towerDetail.longitude, towerDetail.latitude, towerDetail.height + 100])
                }
            }
            
            // 确认收集到的位置是否足够
            if (positions.length < 2) {
                console.error(`无法获取足够的杆塔坐标进行巡航，找到 ${positions.length} 个坐标点`)
                this.myCommandManager.sendMessageToUE({
                    id: "0000000",
                    type: "startRoaming",
                    success: false,
                    msg: `无法获取足够的杆塔坐标进行巡航`
                })
                return
            }
            
            console.log('positions', positions)
            // 3- 根据杆塔 将无关线路及其杆塔进行隐藏
            let lineCollection = this.map3d_v2.getLineCollection()
            let linesInfo = this.map3d_v2.getAllLineInfo()
            console.log('lineCollection', lineCollection, linesInfo)
            for (let i = 0; i < towers.length; i++) {
                let towerName = towers[i].name
                console.log('towerName', towerName)
                for (let j = 0; j < linesInfo.length; j++) {
                    let lineInfo = linesInfo[j]
                    if (lineInfo.insulatorAllocation.some(item => item.towerNumber == towerName)) {
                        linesInfo.pop(lineInfo)
                    }
                }
            }
            console.log('linesInfo', linesInfo)
            for (let i = 0; i < linesInfo.length; i++) {
                let lineID = linesInfo[i].lineId
                lineCollection[lineID].show = false
                for (let j = 0; j < linesInfo[i].insulatorAllocation.length; j++) {
                        // 隐藏杆塔
                    let towerID = linesInfo[i].insulatorAllocation[j].towerID
                    let towerModel = viewer.entities.getById(`tower@${towerID}`)
                    if (towerModel != undefined) {
                        towerModel.show = false
                    }
                        // 隐藏label
                    let labelID = `label@${towerID}`
                    let labelModel = viewer.entities.getById(labelID)
                    if (labelModel != undefined) {
                        labelModel.show = false
                    }
                        // 隐藏绝缘子
                    let insulatorID = linesInfo[i].insulatorAllocation[j].insulator
                    let insulatorPositionID = linesInfo[i].insulatorAllocation[j].locationID
                    let insulatorModel = viewer.entities.getById(`insulator@${towerID}@${insulatorPositionID}@${insulatorID}`)
                    if (insulatorModel != undefined) {
                        console.log(`insulator@${towerID}@${insulatorPositionID}@${insulatorID}`)
                        insulatorModel.show = false
                    }
                }
            }
            // 4- 开始巡航
            this.roaming.roaming_start(viewer, positions, 2, false, () => {
                console.log('roaming stop')
                this.flyToHome(0.5)
            })
        },
        flagBIM (newValue, oldValue) {
            // 处理显示/隐藏场景中全部模型
            if (viewer) {
                viewer.entities.show = newValue
                if (this.map3d_v2) {
                    let lineCollection = this.map3d_v2.getLineCollection()
                    for (let key in lineCollection) {
                        lineCollection[key].show = newValue
                    }
                }
            }
        },
        flagProtectedAreaLabel (newValue, oldValue) {
          console.log('wwww');
          
            // 处理显示/隐藏场景中的保护区的名称
            this.loadProtectedAreaLabel(newValue)
        },
        flagLoadLineBuffer (newValue, oldValue) {
            // 处理显示/隐藏场景中的缓冲区
            this.loadLineBuffer(newValue)
        },
        flagLoadInsulator (newValue, oldValue) {
           // 处理显示/隐藏场景中的绝缘子片
           this.loadInsulator(newValue)
        },
        jiaoChaKuaYueIndex (newValue, oldValue) {
            // 处理重难点展示具体操作
            console.log('交叉跨越index=', newValue)

            this.loadObliquePhotography(newValue)
            if (newValue == 'jiaoChaKuaYue_close') {
                this.flyToHome(2.5)
                return
            }
        },
        zhongNanDianIndex (newValue, oldValue) {
            // 处理重难点展示具体操作
            console.log('重难点index=', newValue)
            if (this.myZhongNanDianHandler == undefined) {
                this.myZhongNanDianHandler = new ZhongNanDianHandler(viewer, this.$cache, this.buildingConfig)
            }
            this.myZhongNanDianHandler.close() // 清除上一次加载的模型数据
            if (newValue == 'znd_close') {
                this.flyToHome(0.5)
                return
            }
            if (this.myZhongNanDianHandler.load(newValue) == false) {
                console.error('重难点加载失败 index=', newValue)
            }
        },
        flagShigongjinduLabel (newValue, oldValue) {
          if(newValue) {
            const store = useProjectStore()
            this.myShigongjinduLabelHandler.forEach((option) => {
              store.lineDetail.forEach((item) => {
                if(item.towerNumber == option.name) {
                  option.id = item.towerNumber
                  option.position = [item.longitude, item.latitude, item.height + 135]
                  let label = new DivLabel()
                  label.addDynamicLabel_cesium(this.viewer, option)
                }
              })
            })
          } else {
            console.log('deleteAllLabels',newValue);
            
              deleteAllLabels(this.viewer)
          }
        },
        flagConstructionLabel (newValue, oldValue) {
          console.log('flagConstructionLabel watcher newValue:', newValue);
          if (newValue && typeof newValue === 'object') {
            this.loadConstructionLabel(newValue.enable);
            this.loadConstructionModel(newValue.enable ? newValue.type : false);
          } else {
            // 处理旧的布尔值或字符串类型，兼容旧逻辑或将其视为关闭
            this.loadConstructionLabel(false);
            this.loadConstructionModel(false);
          }
        },
    },
    created () {
        this.login() // 这里处理直接登录 
    },
    async mounted () {
        window.acceptMessage = (msg) => {
            // this.myCommandManager.sendMessageToUE({ id: "00000000", success: true, msg: "get msg" })
            // 确保msg是字符串才进行JSON解析
            const command = typeof msg === 'string' ? JSON.parse(msg) : msg;

            // 如果是巡航命令且数据尚未准备好，则直接设置待处理
            if (command.type === 'startRoaming' && !this.projectDataReady) {
                console.log('Received roaming command but project data not ready, queueing');
                this.pendingRoamingCommand = {
                    lineName: command.name,
                    enable: true
                };
                
                // 返回接收状态
                this.myCommandManager.sendMessageToUE({
                    id: command.id,
                    type: "startRoaming",
                    success: true,
                    msg: "巡航请求已接收，正在等待项目数据加载完成"
                });
                return;
            }

            // Check if tower data is actually available
            if (command.type === 'startRoaming' && this.projectDataReady) {
                // Only check for towers if map3d_v2 is initialized
                if (this.map3d_v2) {
                    const allTowers = this.map3d_v2.getAllLineTowerDetail();
                    if (!allTowers || allTowers.length === 0) {
                        console.log('Tower data not available yet, retrying in 2 seconds');
                        setTimeout(() => {
                            window.acceptMessage(msg);
                        }, 2000);
                        return;
                    }
                } else {
                    console.log('map3d_v2 not initialized yet, retrying in 2 seconds');
                    setTimeout(() => {
                        window.acceptMessage(msg);
                    }, 2000);
                    return;
                }
            }

            // console.log('get msg:', command)
            this.myCommandManager.parseCommand(command).then(res => {
                this.myCommandManager.sendMessageToUE(res[0])

                // 更新页面监听的参数变量
                if (res[0].type == 'changeProject' && res[1] != undefined) {
                    this.projectDataReady = false; // 重置项目数据加载状态
                    this.projectStructure = res[1]
                }
                if (['startRoaming', 'roaming'].includes(res[0].type) && res[1] != undefined) {
                    this.roamingFlag = res[1]
                }
                if (command.type == 'protectedArea') {
                    this.flagProtectedAreaLabel = command.enable
                }
                if (res[0].type == 'showBim') {
                    this.flagBIM = command.enable
                }
                if (command.type == 'loadLineBuffer') {
                    this.flagLoadLineBuffer = command.enable
                }
                if (command.type == 'loadInsulator') {
                    this.flagLoadInsulator = command.enable
                }
                if (command.type == 'zhongNanDian') {
                    this.zhongNanDianIndex = command.index
                }
                if (command.type == 'jiaoChaKuaYue') {
                    this.jiaoChaKuaYueIndex = command.index
                }
                if (command.type == 'shigongjinduLabel') {
                  this.flagShigongjinduLabel = command.enable
                  this.myShigongjinduLabelHandler = command.options?command.options:[]
                }
                if (command.type == 'constructionLabel') {
                  // 如果 command.enable 是布尔值，则适配新的对象结构
                  if (typeof command.enable === 'boolean') {
                    this.flagConstructionLabel = { 
                      type: command.enable ? (this.flagConstructionLabel.type || '全部') : '全部', 
                      enable: command.enable 
                    };
                  } else {
                    // 如果 command.enable 已经是对象，则直接赋值
                    this.flagConstructionLabel = command.enable;
                  }
                }
                if (command.type == 'shejiMode') {
                  if (command.projectId == '70') {
                    this.draw3DLineV2()
                  } else if(command.projectId == '1') {
                    this.draw3DLine()
                  }
                }
                if (command.type == 'shigongMode') {
                  if (command.projectId == '70') {
                    this.draw3DLineNoDesignV2(command.options)
                  } else if(command.projectId == '1') {
                    this.planRecordMap = command.options
                    this.draw3DLineNoDesign(true)
                  }
                }
            })
        }
        if(window.ue && navigator.userAgent.indexOf("ZdnWebView")>=0){
            ue.interface.acceptMessage = window.acceptMessage
        }else if(!(window.ue)){
            //iframe集成需要
            addEventListener('message', (e) => {
                console.log('Received message from parent:', e.data);
                window.acceptMessage(e.data)
            })
        }
        // addEventListener('message', (e) => {
        //     console.log('Received message from parent:', e.data);
        // })

        this.$cache = getCurrentInstance()?.appContext?.config.globalProperties?.$cache
        window.projectStructure = this.projectStructure
        this.$refs.leafletMap.updateCenterIcon()

        // 由于initViewer是异步函数，需要使用await
        this.viewer = await initViewer('cesiumDiv')
        window.viewer = this.viewer
        console.log('viewer', viewer)

        // 监听相机视角的变化
        // viewer.camera.changed.addEventListener(this.cameraChangedHandler)
        viewer.scene.postRender.addEventListener(this.cameraChangedHandler)
        
        // setTimeline(viewer, new Date("2023-7-15 12:00:00"), new Date("2023-7-15 12:01:00")) 
        this.myCommandManager = new CommandManager(viewer, this.$cache)
        clickGetPoint(viewer,this.myCommandManager)
        
        await this.initProject()
        console.log('projectList:', this.projectList)
        // await this.changeProject('1') // 默认进入能东项目      
        // this.projectStructure = await this.myCommandManager.changeProject(getInitProjectId())
        console.log(`${this.projectStructure.name} `, 'projectStructure:', this.projectStructure)
        this.myCommandManager.sendMessageToUE({ id: "00000000", success: true, msg: "i am ready" })

        // this.flattenTerrain()
        // setTimeout(() => {
        //     this.testCommond()
        // }, 4000)

    },
    methods: {
        async testCommond() {
            console.log('testCommond')
            const pointArray = [
              114.44099174638491, 22.573273743487725, 
              114.44524830039566, 22.57337128634743, 
              114.44482298931237, 22.578198050874338, 
              114.44101033709589, 22.578346800611378, 
              114.44099174638491, 22.573273743487725, 
            ]
        
            let polygonEntity = new Cesium.Entity({
              name: '拍平区域面对象',
              polygon: {
                hierarchy: new Cesium.CallbackProperty(() => {
                  return new Cesium.PolygonHierarchy(Cesium.Cartesian3.fromDegreesArray(pointArray));
                }, false),
                material: Cesium.Color.RED.withAlpha(0.5)
              }
            });
            viewer.entities.add(polygonEntity);

            viewer.terrainProvider.addTerrainEditsData('test_uuid', pointArray, 16);
            viewer.scene.globe._surface.invalidateAllTiles(); // 压缩
            console.log('flattenTerrain ok')

            const pointArray2 = [
              114.44865733428232, 22.580865360261487, 
              114.45349350612528, 22.582242447923583,
              114.45371724730322, 22.578797369826418,
              114.44853284384482, 22.576973749185576,
              114.44865733428232, 22.580865360261487, 
            ]
            let polygonEntity2 = new Cesium.Entity({
              name: '拍平区域面对象',
              polygon: {
                hierarchy: new Cesium.CallbackProperty(() => {
                  return new Cesium.PolygonHierarchy(Cesium.Cartesian3.fromDegreesArray(pointArray2));
                }, false),
                material: Cesium.Color.GREEN.withAlpha(0.5)
              }
            });
            viewer.entities.add(polygonEntity2);

            viewer.terrainProvider.addTerrainEditsData('test_uuid2', pointArray2, 50);
            viewer.scene.globe._surface.invalidateAllTiles(); // 压缩
            console.log('flattenTerrain ok2')

            // const store_lineProj = useLineProjStore()
            // const store = useProjectStore()
            // let projectData = store_lineProj.lineDataV20
            // console.log('lineDetail:', projectData)
            // const constructionMsg = {
            //     'A2/J2': {
            //         towerName: 'A2/J2',
            //         procedureStatus: 2,
            //         procedureStatusName: '青培',
            //         reportTime: '2024-12-11'
            //     },
            //     'A3': {
            //         towerName: 'A3',
            //         procedureStatus: 3,
            //         procedureStatusName: '基础施工',
            //         reportTime: '2024-12-11'                
            //     }
            // }
            // this.map3d_v2.setDesign(false)
            // this.map3d_v2.removeAllModels()
            // // 更新杆塔的施工状态
            // projectData.lineDetail.forEach(item => {
            //     if (constructionMsg[item.towerNumber]) {
            //         item.procedureStatus = constructionMsg[item.towerNumber].procedureStatus
            //     } else {
            //         item.procedureStatus = 5
            //     }
            // })
            // store.setLineDetail(projectData.lineDetail)
            // this.map3d_v2.updateProjectData(projectData, constructionMsg)
            // this.map3d_v2.autoDrawLine()



            // let command = {
            //     id: '123456789',
            //     type: 'changeProject',
            //     // projectID: '70', //切换到70工程
            //     // token: '7f2908841a464a83977c2033771cb267'
            //     projectID: '1',
            //     token: '54ea6cfd6d1b47e5b3f372670b8193bf'
            // }
            // console.log('command:', command)

            // this.myCommandManager.parseCommand(command).then((res) => {
            //     console.log('res:', res[0], res[1])
            //     if (res[0].type == 'changeProject' && res[1] != undefined) {
            //         this.projectStructure = res[1]
            //     }
            //     // this.myCommandManager.sendMessageToUE(res)
            // })
        },
        cameraChangedHandler () {
            let angle = viewer.camera.heading*180/Math.PI
            let pos = Cesium.Cartographic.fromCartesian(viewer.camera.position)
            this.$refs.leafletMap.setCenter({position: [(pos.latitude / Math.PI) * 180, (pos.longitude / Math.PI) * 180], angle: angle})
        },
        flyToHome (seconds) {
            // const DEFAULT_VIEW = {
            //     longitude: 114.44758998973101,
            //     latitude: 22.5772335654441,
            //     height: 1750.826,
            //     heading: 0,
            //     pitch: -1.5,
            //     duration: seconds,
            // }
            let DEFAULT_VIEW = this.$cache.getDefaultView()
            console.log('DEFAULT_VIEW', DEFAULT_VIEW)
            if (seconds > 0) {
                DEFAULT_VIEW.duration = seconds
            }
            return new Promise((resolve, reject) => {
                flyTo(viewer, DEFAULT_VIEW).then(() => {
                    console.log('flyToHome done')
                    resolve(true)
                })
            })
        },
        _flytoPosition(position, heading = 0, pitch = -1 * Math.PI / 10, isTower = 1) {
            let viewConfig = {
                longitude: position[0],
                latitude: position[1],
                height: position[2],
                heading: heading,
                pitch: pitch,
                duration: 1
            }
            flyTo(this.viewer, viewConfig)
        },
        async initProject () {
            return new Promise((resolve, reject) => {
                console.log('正在初始化项目数据...');
                
                // 检查认证状态
                if (!this.login()) {
                    reject(new Error('认证失败，无法初始化项目'));
                    return;
                }
                
                getproject(null).then(
                    (res) => {
                        if (!res.data.data) {
                            ElMessage.error(res.data.msg || '获取项目列表失败');
                            reject(new Error('获取项目列表失败'));
                            return;
                        }
                        
                        this.projectList = res.data.data;
                        let cur_projectId = getInitProjectId();
                        console.log('当前项目ID:', cur_projectId);
                        
                        this.$cache.setProjects(res.data.data);
                        
                        // 查找匹配的项目
                        const currentProject = res.data.data.find((e) => e.id == cur_projectId);
                        if (!currentProject) {
                            ElMessage.warning(`未找到ID为${cur_projectId}的项目，将使用第一个可用项目`);
                            if (res.data.data.length > 0) {
                                cur_projectId = res.data.data[0].id;
                                this.$cache.setProject(res.data.data[0]);
                            } else {
                                reject(new Error('没有可用的项目'));
                                return;
                            }
                        } else {
                            this.$cache.setProject(currentProject);
                        }
                        
                        let params = {
                            projectId: cur_projectId
                        };
                        
                        getprojectStructure(params).then((res) => {
                            if (!res.data.data) {
                                //ElMessage.error(res.data.msg || '获取项目结构失败');
                                reject(new Error('获取项目结构失败'));
                                return;
                            }
                            
                            this.projectStructure = res.data.data;
                            if (res.data.data && res.data.data.children) {
                                const store_lineProj = useLineProjStore();
                                store_lineProj.setProLines(res.data.data.children);
                            }
                            console.log('项目初始化完成');
                            this.projectDataReady = true; // 标记项目数据已加载完成
                            resolve();
                        }).catch(error => {
                            console.error('获取项目结构失败:', error);
                            //ElMessage.error('获取项目结构失败: ' + (error.message || '未知错误'));
                            reject(error);
                        });
                    }
                ).catch(error => {
                    console.error('获取项目列表失败:', error);
                    //ElMessage.error('获取项目列表失败: ' + (error.message || '未知错误'));
                    reject(error);
                });
            });
        },
        login () {
            const token = getToken();
            console.log('当前token:', token);
            
            if (token) {
                // 如果URL中有token，则使用该token
                setSimpleToken({ accessToken: token });
                console.log('已设置token:', token);
                return true;
            } else {
                // 如果URL中没有token，检查localStorage中是否有token
                const storedToken = getAccessToken();
                if (storedToken) {
                    console.log('使用已存储的token');
                    return true;
                } else {
                    // 如果既没有URL参数也没有存储的token，重定向到登录页面
                    console.warn('未找到有效的认证token，将重定向到登录页面');
                    window.location.href = import.meta.env.VITE_CONTEXT+'login';
                    return false;
                }
            }
        },
        async loadPowerLine () {
            console.log(`start load power line, projectID=${this.projectStructure.id}, name=${this.projectStructure.name}`)
            if (this.$cache.getProjectCode() == 'ZDN') {
                // 如果是珠东南的项目 则需要使用旧版本的线路绘制
                this.draw3DLine_V1()
                return
            }
            const store_lineProj = useLineProjStore()
            
            const store = useProjectStore()
            if (this.map3d_v2 == undefined) {
                // 根据经纬度坐标 计算高程
                let projectData = await store_lineProj.initDataV2(this.projectStructure.id)
                console.log('projectStructure3', projectData)
                // 将 lineDetail 保存到 Pinia store 中
                store.setLineDetail(projectData.lineDetail)
                return new Promise((resolve, reject) => {
                    projectData.lineDetail = projectData.lineDetail.filter(item => {
                        if (item.longitude == undefined || item.latitude == undefined) {
                            console.log('杆塔经纬度为空，请检查杆塔数据', item)
                            return false
                        }
                        return true
                    })
                    let positions = []
                    for (let i = 0; i < projectData.lineDetail.length; i++) {
                        positions.push([projectData.lineDetail[i].longitude, projectData.lineDetail[i].latitude])
                    }
                    getPositionHeight(this.viewer, positions).then(async positions_new => {
                        for (let i = 0; i < projectData.lineDetail.length; i++) {
                            projectData.lineDetail[i].height = projectData.lineDetail[i].altitude + positions_new[i].height
                        }
                        console.log('compute Height ok', projectData.lineDetail)
                        this.map3d_v2 = new Map3DTool_v2(viewer, this.$cache.getProjectCode(), projectData, {})
                        this.map3d_v2.autoDrawLine()
                        this.loadUnderGroundLine()// 加载地下电缆数据
                        resolve(true)
                    })
                })
            } else {
                this.map3d_v2.autoDrawLine()
                this.loadUnderGroundLine()// 加载地下电缆数据
                return Promise.resolve(true)
            }
        },
        async loadUnderGroundLine () {
          // 先清除场景中已经加载的电缆沟
          clearExcavateSurface(this.viewer)
          // 加载新的电缆沟
          if (this.$cache.getProjectCode() == 'TEST') {
            // 只有TEST项目需要加载电缆沟 这里通过直接给定 后续需要进行优化
            this.$cache.loadJsdata('excavateSurface.json').then(async (excavateSurface) => {
              loadExcavateSurface(excavateSurface, this.viewer)
              console.log('电缆沟 loadExcavateSurface')

              // zoomTo(this.viewer, res, { heading: 0, pitch: -0.15, range: 2000 })
            })

            this.$cache.loadJsdata('underGroundPolylineVolume.json').then(async (res) => {
              this.viewer.scene.globe.translucency.frontFaceAlpha = 0.7; // 固定地面半透明程度
              let entities = await addPolylineVolume(res, this.viewer)
              // zoomTo(this.viewer, entities, { heading: 0, pitch: -0.15, range: 2000 })
            })
          }
        },
        removeModel () {
            // this.deleteDynamicLabel();
            this.viewer.dataSources.removeAll();
            this.viewer.entities.removeAll();
            // this.towerEntities = [];
            // this.towersLable = [];
            // this.insulatorLoaded = [];
            // this.otherEntities.label = [];
            // if (this.lineCollection) {
            //     this.lineCollection.removeAll();
            // }
        },

        async draw3DLine_V1 () {
            // this.viewer.scene.globe.depthTestAgainstTerrain = true // 打开遮挡效果,避免模型悬空
            this.removeModel();
            const store_lineProj = useLineProjStore();
            store_lineProj.dataUseful = false
            await store_lineProj.initData(this.$axios, '', this.cur_projectId); // 获取线路详情等基本数据
            // --以下处理线序 如果线序在后端请求中已经处理完成 则可以不需要这部分内容
            let linePhaseSequence = await this.$cache.getLinePhaseSequence();
            console.log('linePhaseSequence=', linePhaseSequence);
            store_lineProj.linesInfo.forEach(line => {
                if (line.phaseSequence != undefined) {
                    return;
                }
                let phase = linePhaseSequence.find(item => item.lineId == line.lineId);
                line.phaseSequence = phase.phaseSequence;
                console.log('phaseSequence=', line);
            });
            // --以上处理线序 如果线序在后端请求中已经处理完成 则可以不需要这部分内容
            await store_lineProj.towersRequest(this.$axios, ''); // 获取杆塔详情
            store_lineProj.get_insulatorAllocation(this.$axios, '', () => {
                // store_lineProj.convertData2NewFormat_towers(); // 修正杆塔模型的挂点信息细节 代替之前json文件的生成 数据缓存
                store_lineProj.dataUseful = true;
                this.towerModels = store_lineProj.towers;
                // 更新高程数据
                computeHeight(this.viewer, () => {
                    console.log('compute height ok');
                    store_lineProj.lineDetail.forEach(line => {
                        line.insulatorList = [];
                        line.linePositions = [];
                    })
                    console.log('start draw line ...')
                    this.map3d = new Map3DTool(this.$cache.getProjectCode(), store_lineProj.towers, store_lineProj.insulators, store_lineProj.towerProfile || [], true, this.viewer, false, {})
                    store_lineProj.linesInfo.forEach(line => {
                        this.map3d.draw3DLine(line,
                            store_lineProj.lineDetail.find(item => item.lineId == line.lineId).towers
                        )
                    })
                })
            })
        },
        async loadObliquePhotography(modelName) {
            // 初始化标签存储列表
            if (!this.obliquePhotographyLabelList) {
                this.obliquePhotographyLabelList = [];
            }
            // 参数类型检查
            if (typeof modelName !== 'string' && modelName !== undefined) {
                console.error('参数必须为字符串类型');
                return;
            }

            const res = await this.$cache.getObliquePhotography();
            this.obliquePhotographyConfig = res;

            // 如果传入新模型且与当前模型不同，先清理旧内容
            if (modelName === 'jiaoChaKuaYue_close' || (this.currentLoadedModel && this.currentLoadedModel !== modelName)) {
                // 清除旧标签
                this.obliquePhotographyLabelList.forEach(label => {
                    if (label instanceof Cesium.Entity) {
                        this.viewer.entities.remove(label);
                    } else if (label instanceof Cesium.LabelCollection) {
                        this.viewer.scene.primitives.remove(label);
                    }
                });
                this.obliquePhotographyLabelList = [];
                
                // 移除附属模型和绘制的直线
                if (this.crossLineCollection && !this.crossLineCollection.isDestroyed()) {
                    destroyLineCollection(this.viewer, this.crossLineCollection);
                    this.crossLineCollection.show = false;
                    this.crossLineCollection = null;
                    console.log('清除绘制的直线');
                }
                
                // 移除所有附属模型
                if (this.subsidiaryModels && this.subsidiaryModels.length > 0) {
                    const primitives = this.viewer.scene.primitives;
                    for (let i = this.subsidiaryModels.length - 1; i >= 0; i--) {
                        const primitive = this.subsidiaryModels[i];
                        if (primitive && !primitive.isDestroyed()) {
                            primitive.show = false
                        }
                    }
                    console.log('清除附属模型');
                }
                
                // 移除旧模型
                if (this.tilesetD3dms && this.tilesetD3dms.length > 0) {
                    const primitives = this.viewer.scene.primitives;
                    // 逆向遍历tilesetD3dms数组
                    for (let i = this.tilesetD3dms.length - 1; i >= 0; i--) {
                        const currentTileset = this.tilesetD3dms[i];
                        
                        if (!currentTileset || currentTileset.isDestroyed()) continue;
                        
                        try {
                            // 获取当前tileset的URL
                            const targetUrl = currentTileset.url;
                            
                            // 遍历场景中的所有图元
                            for (let j = primitives.length - 1; j >= 0; j--) {
                                const primitive = primitives.get(j);
                                
                                if (primitive instanceof Cesium.Cesium3DTileset && 
                                    primitive.url === targetUrl && 
                                    !primitive.isDestroyed()) 
                                {
                                    primitives.remove(primitive);
                                    break; // 找到匹配后退出循环
                                }
                            }
                        } catch (error) {
                            console.error('Error while processing tileset:', error);
                        }
                    }
                    
                    this.tilesetD3dms = []; // 清空引用数组
                }
            }

            const targetModel = modelName || this.obliquePhotographyConfig.keepLoading?.[0];
            if (!targetModel || !this.obliquePhotographyConfig[targetModel]) {
                console.warn(`模型 ${targetModel} 不存在`);
                return;
            }

            const modelConfig = this.obliquePhotographyConfig[targetModel];
    
            // 模型加载逻辑
            if (modelConfig.modelType === '3Dtiles' || !modelConfig.modelType) {
                // URL构建
                const url = modelConfig.url.startsWith('http') 
                    ? modelConfig.url 
                    : `${MODEL_STATIC_URL}/${this.$cache.getProjectCode()}${modelConfig.url}`;

                try {
                    // 加载3D Tiles
                    const tileset = await Cesium.Cesium3DTileset.fromUrl(url, {
                        maximumScreenSpaceError: 16
                    });
                    
                    // 添加模型到场景
                    let tmpTileset = this.viewer.scene.primitives.add(tileset);
                    if (!this.tilesetD3dms) {
                        this.tilesetD3dms = [];
                    }
                    this.tilesetD3dms.push(tmpTileset);
                    console.log(`${targetModel} 模型加载成功`, tileset);

                    // 记录当前加载的模型
                    this.currentLoadedModel = targetModel;

                    if (this.obliquePhotographyConfig[targetModel]['view']) {
                      this._flytoPosition(this.obliquePhotographyConfig[targetModel]['view'], this.obliquePhotographyConfig[targetModel]['heading'], this.obliquePhotographyConfig[targetModel]['pitch'], 0)
                    } 
                    if (modelConfig.fonts) {
                        modelConfig.fonts.forEach(label => {
                            const labelOption = {
                                id: `${label.font}_${targetModel}`,
                                text: label.font.replace(/<br\/>/g, '\n'),
                                position: label.center,
                                font: label.size,
                                bgColor: 'rgba(64,158,255,0.9)',
                                fillColor: 'rgba(255,255,255,1)'
                            };
                            // 添加标签并存储到列表
                            const labelObject = new DivLabel().addDynamicLabel_cesium(this.viewer, labelOption);
                            this.obliquePhotographyLabelList.push(labelObject);  // 存储标签对象
                        });
                    }
                    
                    // 有附属模型的存在就执行
                    if (modelConfig.subsidiaryModels && modelConfig.subsidiaryModels.length > 0) {
                        await this.loadSubsidiaryModels(modelConfig.subsidiaryModels);
                    }

                } catch (error) {
                    console.error(`模型加载失败: ${url}`, error);
                    if (modelConfig.fonts) {
                        modelConfig.fonts.forEach(label => {
                            const labelOption = {
                                id: `${label.font}_error_${targetModel}`,
                                text: `${label.font.replace(/<br\/>/g, '\n')} (加载失败)`,
                                position: label.center,
                                font: label.size,
                                bgColor: 'rgba(220,20,60,0.8)',
                                fillColor: 'rgba(255,255,255,1)'
                            };
                            const errorLabel = new DivLabel().addDynamicLabel_cesium(this.viewer, labelOption);
                            this.obliquePhotographyLabelList.push(errorLabel);  // 存储错误标签
                        });
                    }
                }
            }
        },
        // 附属模型加载方法
        async loadSubsidiaryModels(subsidiaryModels) {
            // 确保线集合已创建
            if (!this.crossLineCollection) {
                this.crossLineCollection = createLineCollection(this.viewer);
            }
            
            // 按group分组处理模型
            const groupMap = new Map();
            
            // 按group分组
            for (const model of subsidiaryModels) {
                const groupKey = model.group || 'ungrouped';
                if (!groupMap.has(groupKey)) {
                    groupMap.set(groupKey, []);
                }
                groupMap.get(groupKey).push(model);
            }

            // 处理每个分组
            for (const [group, models] of groupMap.entries()) {
                if (group === 'ungrouped') {
                    // 加载独立模型
                    for (const model of models) {
                        await this.loadModel(model);
                    }
                } else {
                    // 处理分组模型：加载模型并使用计算后的位置绘制直线
                    await this.processGroupModels(models, group);
                }
            }
        },

        // 处理分组模型：加载模型并绘制直线
        async processGroupModels(models, groupName) {
            // 存储每个模型的所有挂点位置（三维数组：模型索引 -> 挂点索引 -> 位置）
            const allModelMountPositions = [];

            for (const [index, model] of models.entries()) {
                // 为每个模型创建独立的towerInfo
                const towerInfo = {
                    longitude: model.position[0],
                    latitude: model.position[1],
                    height: model.position[2],
                    angle: model.rotation[0],
                    version: 'V2'
                };
                
                // 当前模型的挂点数组（多个挂点）
                const mountPoints = model.absolutePosition;
                
                // 存储当前模型所有挂点的计算结果
                const modelMountPositions = [];
                
                // 计算每个挂点的位置
                for (const [pointIndex, point] of mountPoints.entries()) {
                    // 创建当前挂点的数组（getMountPosition需要这种格式）
                    const mountArray = [point];
                    
                    // 计算当前挂点位置
                    const mountPositions = getMountPosition(towerInfo, [mountArray], this.viewer);
                    
                    if (mountPositions && mountPositions.length > 0 && mountPositions[0].length > 0) {
                        const pos = mountPositions[0][0];
                        modelMountPositions.push(pos);
                        console.log(`模型[${index}]挂点[${pointIndex}]位置:`, pos);
                    } else {
                        // 计算失败，使用原始位置
                        console.warn(`模型[${index}]挂点[${pointIndex}]计算失败，使用原始位置`);
                        modelMountPositions.push(model.position);
                    }
                }
                
                allModelMountPositions.push(modelMountPositions);
            }
            
            // 加载组内所有模型
            for (const model of models) {
                await this.loadModel(model);
            }
            
            // 绘制组内所有相邻模型之间的挂点连线
            if (allModelMountPositions.length >= 2) {
                const color = '#FF00FF';
                const width = 1.5;
                
                // 遍历所有模型对（相邻模型）
                for (let i = 0; i < allModelMountPositions.length - 1; i++) {
                    const currentModelPoints = allModelMountPositions[i];
                    const nextModelPoints = allModelMountPositions[i + 1];

                    // 绘制所有挂点之间的连线
                    const minPoints = Math.min(currentModelPoints.length, nextModelPoints.length);
                    let sagHeight = Math.min(currentModelPoints[2], nextModelPoints[2]) - 1;
                    
                    for (let j = 0; j < minPoints; j++) {
                        overheadLine(
                            this.crossLineCollection,
                            currentModelPoints[j],
                            nextModelPoints[j],
                            sagHeight,
                            10,
                            color,
                            width
                        );
                        console.log(`绘制模型${i}到模型${i+1}的挂点${j}连线，颜色: ${color}`);
                    }
                }
            } else {
                console.warn(`分组"${groupName}"只有${allModelMountPositions.length}个模型，无法绘制连线`);
            }
        },

        // 加载单个模型（使用原始位置）
        async loadModel(model) {
            try {
                // URL构建
                const url = model.url.startsWith('http') 
                    ? model.url 
                    : `${MODEL_STATIC_URL}/${this.$cache.getProjectCode()}${model.url}`;
                
                // 检查是否已存在相同ID的模型
                const modelId = `${url} + ${model.name}`;
                const existingModel = this.subsidiaryModels.find(m => m.id === modelId);
                if (existingModel) {
                    // 如果存在则直接显示
                    existingModel.show = true;
                    return;
                }

                // 从缓存获取模型数据
                let localModelCache = new ModelCache();
                const modelData = await localModelCache.getOneModel(url);
                if (!modelData) {
                    console.warn(`模型数据加载失败: ${url}`);
                    return;
                }

                // 生成内存URL
                const modelUrl = URL.createObjectURL(new Blob([modelData]));
                
                const position = Cesium.Cartesian3.fromDegrees(...model.position);
                
                // 使用配置中的rotation参数（如果有的话）
                let heading = 0, pitch = 0, roll = 0;
                
                if (model.rotation && Array.isArray(model.rotation) && model.rotation.length >= 3) {
                    // 使用rotation参数 [heading, pitch, roll]
                    heading = Cesium.Math.toRadians(model.rotation[0] || 0);
                    pitch = Cesium.Math.toRadians(model.rotation[1] || 0);
                    roll = Cesium.Math.toRadians(model.rotation[2] || 0);
                    console.log(model.url, `使用rotation参数进行旋转: [${model.rotation[0]}, ${model.rotation[1]}, ${model.rotation[2]}]度`);
                } else {
                    // 回退使用heading, pitch, roll参数
                    heading = Cesium.Math.toRadians(model.heading || 0);
                    pitch = Cesium.Math.toRadians(model.pitch || 0);
                    roll = Cesium.Math.toRadians(model.roll || 0);
                }
                
                const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
                const modelMatrix = Cesium.Transforms.headingPitchRollToFixedFrame(position, hpr);

                let modelInstance;
                // 根据类型加载模型
                switch (model.modelType || '3Dtiles') {
                    case '3Dtiles':
                        modelInstance = await Cesium.Cesium3DTileset.fromUrl(modelUrl, {
                            maximumScreenSpaceError: 16,
                            modelMatrix: modelMatrix
                        });
                        break;
                        
                    case 'gltf':
                        modelInstance = await Cesium.Model.fromGltfAsync({
                            id: modelId,
                            url: modelUrl,
                            show: false,
                            modelMatrix: modelMatrix,
                        });
                        break;
                        
                    default:
                        console.warn(`未知模型类型: ${model.modelType}，跳过加载`);
                        URL.revokeObjectURL(modelUrl);
                        return;
                }
                
                // 添加到场景
                const primitive = this.viewer.scene.primitives.add(modelInstance);
                console.log('primitives', this.viewer.scene.primitives);
                
                primitive.show = model.visible !== false;
                this.subsidiaryModels.push(primitive);
                console.log(`模型加载成功: ${model.url}`);

                // 清理内存URL
                URL.revokeObjectURL(modelUrl);

            } catch (error) {
                console.error(`模型加载失败: ${model.url}`, {
                    name: model.name,
                    type: model.modelType,
                    error: error.message
                });
            }
        },
        async loadBuilding () {
            const res = await this.$cache.getBuildingConfig()
            console.log('loadBuilding', res)
            this.buildingConfig = res
            let keepLoadBuilding = this.buildingConfig.keepLoading
            for (let i = 0; i < keepLoadBuilding.length; i++) {
                let index = keepLoadBuilding[i]
                if (this.buildingConfig.hasOwnProperty(index)) {
                    if (this.buildingConfig[index]['modelType'] == undefined || this.buildingConfig[index]['modelType'] == '3Dtiles') {
                        // 变电站地形压平
                        if(this.buildingConfig[index]['underdraughtPosition'] && Array.isArray(this.buildingConfig[index]['underdraughtPosition']) && this.buildingConfig[index]['underdraughtPosition'].length >= 6) {
                          addFlattenTerrain(viewer, this.buildingConfig[index], this.buildingConfig[index]['underdraughtPosition'], this.buildingConfig[index]['underdraughtHeight'])
                        }
                        
                        // URL构建逻辑
                        let url;
                        if (this.buildingConfig[index]['url'].startsWith('http')) {
                            // 如果是完整的URL，直接使用
                            url = this.buildingConfig[index]['url'];
                        } else if (this.buildingConfig[index]['url'].startsWith('/building')) {
                            // 如果是以/building开头的路径，保持原样
                            url = this.buildingConfig[index]['url'];
                            url = `${MODEL_STATIC_URL}/${this.$cache.getProjectCode()}${this.buildingConfig[index]['url']}`;
                        } else {
                            // 其他情况下，添加项目前缀
                            url = `${MODEL_STATIC_URL}/${this.$cache.getProjectCode()}${this.buildingConfig[index]['url']}`;
                        }
                        console.log(`尝试加载3D模型: ${url}`);
                        
                        // 使用fromUrl替代直接构造函数
                        const tilesetPromise = Cesium.Cesium3DTileset.fromUrl(url, {
                            maximumScreenSpaceError: 16,
                            // modelMatrix: m //形状矩阵
                        });
                        
                        tilesetPromise.then(tileset => {
                            viewer.scene.primitives.add(tileset);
                            console.log(`${index} 建筑模型加载成功`, tileset)
                            if (this.buildingConfig[index].fixPosition) {
                                // 获取tileset的中心点坐标
                                var boundingSphere = tileset.boundingSphere
                                var center = boundingSphere.center

                                // 将中心点坐标转换为WGS84坐标系下的经纬度
                                var cartographic = Cesium.Cartographic.fromCartesian(center)
                                var longitude = Cesium.Math.toDegrees(cartographic.longitude)
                                var latitude = Cesium.Math.toDegrees(cartographic.latitude)
                                var height = cartographic.height

                                // 将经纬度调整为目标的经纬度
                                var targetLongitude = this.buildingConfig[index].position[0]
                                var targetLatitude = this.buildingConfig[index].position[1]
                                var targetHeight = this.buildingConfig[index].position[2]

                                // 计算tileset的平移量，并将其应用到modelMatrix中
                                var translation = Cesium.Cartesian3.fromDegrees(targetLongitude, targetLatitude, targetHeight)
                                var centerNew = Cesium.Cartesian3.fromDegrees(longitude, latitude, height)
                                var translationVector = Cesium.Cartesian3.subtract(translation, centerNew, new Cesium.Cartesian3())
                                tileset.modelMatrix = Cesium.Matrix4.fromTranslation(translationVector)

                                let m = tileset._root.transform
                                // 旋转
                                const mx = Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(this.buildingConfig[index].rotation[0]));
                                const my = Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(this.buildingConfig[index].rotation[1]));
                                const mz = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(this.buildingConfig[index].rotation[2]));
                                const rotateX = Cesium.Matrix4.fromRotationTranslation(mx);
                                const rotateY = Cesium.Matrix4.fromRotationTranslation(my);
                                const rotateZ = Cesium.Matrix4.fromRotationTranslation(mz);
                                Cesium.Matrix4.multiply(m, rotateX, m);
                                Cesium.Matrix4.multiply(m, rotateY, m);
                                Cesium.Matrix4.multiply(m, rotateZ, m);
                                // tileset._root.transform = m;

                                // 缩放
                                const _scale = Cesium.Matrix4.fromUniformScale(this.buildingConfig[index].scale);
                                Cesium.Matrix4.multiply(m, _scale, m);
                                tileset._root.transform = m;
                            }

                            console.log(`${index} 建筑模型加载成功`, tileset)
                            if (this.buildingConfig[index]['foundation']) {
                                this.viewer.entities.add({
                                    polygon: {
                                        hierarchy: new Cesium.PolygonHierarchy(
                                            Cesium.Cartesian3.fromDegreesArray(this.buildingConfig[index]['foundation']['positions'])
                                        ),
                                        height: 0.0,
                                        extrudedHeight: this.buildingConfig[index]['foundation']['extrudedHeight'],
                                        outline: false,
                                        outlineColor: Cesium.Color.WHITE,
                                        outlineWidth: 4,
                                        material: Cesium.Color.fromCssColorString(this.buildingConfig[index]['foundation']['color']),
                                        // material: Cesium.Color.fromRandom({ alpha: 1.0 }),                  
                                    }
                                })
                            }
                        }).catch(error => {
                            console.error(`加载3D模型失败: ${url}`, error);
                            
                            // 即使模型加载失败，也创建变电站的标记，防止整个功能失效
                            if (this.buildingConfig[index].fonts) {
                                this.buildingConfig[index].fonts.forEach(label => {
                                    let label_option = {
                                        id: `${label.font}`,
                                        text: `${label.font} (模型加载失败)`,
                                        position: label.center,
                                        font: `${label.size}`,
                                        bgColor: `rgba(220,20,60,0.8)`,
                                        fillColor: `rgba(255,255,255,1)`
                                    }
                                    let _divLabel = new DivLabel()
                                    _divLabel.addDynamicLabel_cesium(this.viewer, label_option)
                                });
                            }
                        });
                    } else if (this.buildingConfig[index]['modelType'] == 'gltf') {
                        let modelInfo = {
                            id: `building@${this.buildingConfig[index]['name']}`,
                            name: `${this.buildingConfig[index]['name']}`,
                            url: `${MODEL_STATIC_URL}/${this.$cache.getProjectCode()}${this.buildingConfig[index]['url']}`,
                            position: this.buildingConfig[index]['position'],
                            angle: 0,
                            scale: 1,
                        }
                        let entity = await addModel_glb(this.viewer, modelInfo)
                        console.log('建筑模型加载成功', modelInfo, entity)
                        // zoomTo(this.viewer, entity, { heading: 0, pitch: -0.15, range: 2000 })
                    }
                    for (var j = 0; j < this.buildingConfig[index]['fonts'].length; j++) {
                        var label = this.buildingConfig[index]['fonts'][j]
                        // this.addZndLabel(label['font'], label['center'], label['size'])
                        let label_option = {
                            id: `${label['font']}`,
                            text: `${label['font']}`,
                            position: label['center'],
                            font: `${label['size']}`,
                            bgColor: `rgba(64,158,255,0.9)`,
                            fillColor: `rgba(255,255,255,1)`
                        }
                        let _divLabel = new DivLabel()
                        _divLabel.addDynamicLabel_cesium(this.viewer, label_option)
                    }
                }

            }
        },
        // 加载机械化施工模型
        async loadConstructionModel(typeOrFlag) {
          try {
            // 首先移除之前可能已经加载的模型
            if (this.constructionModelExample) {
              this.constructionModelExample.removeAllModels();
            }

            const enable = typeof typeOrFlag === 'boolean' ? typeOrFlag : (typeof typeOrFlag === 'string' ? true : false);
            const modelType = typeof typeOrFlag === 'string' ? typeOrFlag : '全部';

            if (enable) {
              if (this.cablewayLineCollection == null) {
                this.cablewayLineCollection = createLineCollection(this.viewer);
              }
              await this.loadCablewayLine(true);
            } else {
              console.log('关闭机械化施工模型显示');
              if (this.constructionModelExample) {
                this.constructionModelExample.removeAllModels();
              }
              await this.loadCablewayLine(false);
              return;
            }

            console.log('加载机械化施工模型，类型:', modelType);


            const constructionModelFiles = {
              '索道运输': 'static/jsdata/TEST/cableway.json',
              '架线施工': 'static/jsdata/TEST/MehanizedConstructionOfWireStringing.json',
              '铁塔组立': 'static/jsdata/TEST/erectionIronTower.json',
              '基础及接地施工': 'static/jsdata/TEST/foundationGrounding.json',
              // '材料运输': 'static/jsdata/TEST/materialTransport.json'
            };

            // 模型加载流程
            if (!this.constructionModelExample) {
              // 动态加载模块并初始化实例
              const module = await import('@/js/construction/example');
              const ConstructionModelExample = module.ConstructionModelExample;
              this.constructionModelExample = new ConstructionModelExample(this.viewer);

              // 添加URL重写方法
              this.constructionModelExample.getModelUrl = function(originalUrl) {
                if (originalUrl.startsWith('/model/')) {
                  return `/modelStatic/TEST/model/${originalUrl.slice(7)}`;
                }
                return originalUrl;
              };

              // 加载指定类型模型
              await this.loadConstructionModelByType(modelType, constructionModelFiles);


            } else {
              // 直接加载模型
              await this.loadConstructionModelByType(modelType, constructionModelFiles);
            }
          } catch (error) {
            console.error('加载机械化施工模型时发生错误:', error);
          }
        },
        // 根据类型加载对应的机械化施工模型
        async loadConstructionModelByType(type, constructionModelFiles) {
          try {
            if (type === '全部') {

              console.log('加载所有类型的机械化施工模型');
              await this.constructionModelExample.loadAllModels();
            } else if (constructionModelFiles[type]) {

              console.log(`加载 ${type} 类型的机械化施工模型`);
              await this.constructionModelExample.loadSpecificModels([constructionModelFiles[type]]);
            } else {
              console.warn(`未找到 "${type}" 类型的机械化施工模型配置`);
            }


            this.constructionModelExample.reportModelStatus();
          } catch (error) {
            console.error(`加载 ${type} 机械化施工模型失败:`, error);
          }
        },
        async loadCablewayLine(enable) {
          try {
              if (!enable) {
                  if (this.cablewayLineCollection && !this.cablewayLineCollection.isDestroyed()) {
                      destroyLineCollection(this.viewer, this.cablewayLineCollection);
                      this.cablewayLineCollection.show = false;
                  }
                  this.cablewayLineCollection = null;
                  return;
              }

              const cablewayResponse = 'cableway.json';
              const cablewayData = await this.$cache.loadJsdata(cablewayResponse);

              const mountsResponse = 'cablewayMounts.json';
              const mountsData = await this.$cache.loadJsdata(mountsResponse);
              
              if (!cablewayResponse || !mountsResponse) {
                  throw new Error('加载索道配置文件失败');
              }

              // 获取线路信息（分组结构）
              const lineInfo = await this.map3d_v2.getConstructionLineInfo(cablewayData, mountsData);
              
              if (!lineInfo || !lineInfo.groups || !lineInfo.mountGroups) {
                  throw new Error('获取线路信息失败');
              }

              // 创建线路集合
              if (!this.cablewayLineCollection) {
                  this.cablewayLineCollection = createLineCollection(this.viewer);
              }

              const COLORS = ['#ff0000', '#00ff00', '#0000ff', '#ffff00']; // 红、绿、蓝、黄

              // 遍历每个segment组
              Object.entries(lineInfo.groups).forEach(([segment, positions]) => {
                  const segmentNum = parseInt(segment);
                  
                  // 绘制本段内的挂点连接线
                  const mountGroup = lineInfo.mountGroups[segmentNum];
                  for (let i = 0; i < mountGroup.length - 1; i++) {
                      const currentMounts = mountGroup[i];
                      const nextMounts = mountGroup[i+1];
                      
                      for (let j = 0; j < 4; j++) {
                          if (
                              currentMounts[j]?.position &&
                              nextMounts[j]?.position
                          ) {
                              const origin = [...currentMounts[j].position];
                              const target = [...nextMounts[j].position];
                              const color = COLORS[j % COLORS.length];
                              const width = 1.5;
                              straight_line(this.cablewayLineCollection, origin, target, color, width);
                          }
                      }
                  }
              });
              
              console.log('按segment分组加载索道线路成功');
          } catch (error) {
              console.error('加载施工索道线路失败:', error);
          }
      },
        async loadProtectedArea () {
            console.log('loadProtectedArea')
            const geojsonFile = 'protectedArea02.geojson'
            return new Promise((resolve, reject) => {
                this.$cache.loadJsdata(geojsonFile).then((data) => {
                    let protectedAreaData = protectedAreaDataPreprocessing(data)
                    protectedAreaData.show = false
                    console.log('protectedAreaData', protectedAreaData)
                    let geojsonService = new GeojsonService(viewer)
                    geojsonService.loadGeoJson(protectedAreaData).then(res => {
                        console.log('protectedAreaData ok res', res)
                        resolve(res)
                    })
                })
            })
        },
        // 加载保护区名称
        async loadProtectedAreaLabel (flag) {
          const protectedAreaLabel = 'protectedAreaLabel.json'
          if(flag == true && this.protectedAreaEntities.length == 0) {
            return new Promise((resolve, reject) => {
              this.$cache.loadJsdata(protectedAreaLabel).then(async (labels)=>{
                labels.forEach((lab,index)=>{
                  var position = Cesium.Cartesian3.fromDegrees(lab.coordinates[0], lab.coordinates[1], lab.coordinates[0] + 300);

                  // 创建标签
                  var label_entity = viewer.entities.add({
                      id:`protectedArea_lable_${index}`,
                      name: 'protectedArea_lable',
                      position: position,
                      // 定位图标
                      billboard: {
                          image: 'static/img/position.svg',
                          width: 32, // 图标宽度
                          height: 32, // 图标高度
                          scaleByDistance : new Cesium.NearFarScalar(0, 2.5, 15000, 0.0),
                          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 图标底部对齐
                          horizontalOrigin: Cesium.HorizontalOrigin.CENTER // 图标水平居中
                      },
                      label: {
                          text: lab.name,
                          font: '20px sans-serif',
                          fillColor: Cesium.Color.AQUAMARINE,
                          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                          outlineWidth: 2,
                          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                          pixelOffset: new Cesium.Cartesian2(0, -43),
                          scaleByDistance : new Cesium.NearFarScalar(0, 2.5, 15000, 0.0),
                          // disableDepthTestDistance: Number.POSITIVE_INFINITY,
                          disableDepthTestDistance: 0 // 启用深度测试，标签在被物体挡住时会消失
                      },
                      // 垂直线
                      polyline: {
                          positions: [position, Cesium.Cartesian3.fromDegrees(lab.coordinates[0], lab.coordinates[1], 0)],
                          width: 2,
                          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 15000.0),
                          material: Cesium.Color.AQUAMARINE
                      }
                  });
                  this.protectedAreaEntities.push(label_entity)
                })
              })
            })
          }
          if(flag == true && this.protectedAreaEntities.length > 0) {
            // 显示保护区数据
            this.protectedAreaEntities.forEach(entity => {
              entity.show = true
            })
          }
          if (flag == false) {
            // 隐藏保护区数据
            this.protectedAreaEntities.forEach(entity => {
              entity.show = false
            })
          }
          
        },
        // 加载索道线路
        // async loadCablewayLine(flag) {
        //     try {
        //         // 当 flag 为 false 时，优先销毁现有线路
        //         if (!flag) {
        //             // 使用新方法销毁集合
        //             if (this.cablewayLineCollection && !this.cablewayLineCollection.isDestroyed()) {
        //               console.log('cablewayLineCollection', this.cablewayLineCollection);
        //               destroyLineCollection(this.viewer, this.cablewayLineCollection);
        //               this.cablewayLineCollection.show = false
        //             }
        //             this.cablewayLineCollection = null;
        //             return;
        //         }

        //         const cablewayJsonData = 'cablewayLine.json';
        //         const data = await this.$cache.loadJsdata(cablewayJsonData);
                
        //         if (!data || !data.cableGroups || !Array.isArray(data.cableGroups)) {
        //             console.error('索道线路数据格式不正确');
        //             return;
        //         }
                
        //         // 遍历每组缆线
        //         for (const group of data.cableGroups) {
        //             console.log(`正在加载索道线路组: ${group.groupName}`);
                    
        //             if (!group.lines || !Array.isArray(group.lines)) {
        //                 console.warn(`索道线路组 ${group.groupName} 数据格式不正确，跳过处理`);
        //                 continue;
        //             }

        //             // 遍历组内的每条线路
        //             for (const line of group.lines) {
        //                 if (!line.points || line.points.length < 2) {
        //                     console.warn(`索道线路 ${line.name} 坐标点数量不足，跳过处理`);
        //                     continue;
        //                 }

        //                 try {
        //                     const points = line.points;
        //                     const lineParams = line.lineParameters || {};
                            
        //                     // 获取起点和终点坐标
        //                     const origin = [points[0].position.longitude, points[0].position.latitude, points[0].position.height];
        //                     const target = [points[1].position.longitude, points[1].position.latitude, points[1].position.height];
                            
        //                     // 获取线路参数
        //                     const color = lineParams.style?.color || '#0000ff';
        //                     const width = lineParams.style?.width || 1.5;
                        
        //                     // 调用straight_line方法绘制线路
        //                     straight_line(this.cablewayLineCollection, origin, target, color, width);
        //                     console.log(`索道线路 ${line.name} 加载成功`);
        //                 } catch (lineError) {
        //                     console.error(`处理索道线路 ${line.name} 时发生错误:`, lineError);
        //                 }
        //             }
        //         }
        //         console.log('所有索道线路加载完成');
        //     } catch (error) {
        //         console.error('加载索道线路失败:', error);
        //     }
        // },
        // 加载机械化施工标签
        async loadConstructionLabel (flag) {
          const constructionLabel = 'constructionLabel.json';
          // 缓存Canvas对象，key为label.name，value为Canvas对象
          const canvasCache = new Map(); 

          if(flag == true && this.constructionLabelEntities.length == 0) {
            return new Promise((resolve, reject) => {
              this.viewer.entities.suspendEvents(); // 暂停事件
              this.$cache.loadJsdata(constructionLabel).then(async (labels)=>{
                // 确保labels是数组
                if (!labels || !Array.isArray(labels)) {
                  console.error('Invalid labels data:', labels);
                  resolve([]);
                  return;
                }
                
                labels.forEach((label,index)=>{
                  // 创建一个 Canvas 元素
                  var canvas = document.createElement('canvas');
                  canvas.width = 300;
                  canvas.height = 80;
                  var ctx = canvas.getContext('2d');

                  // 绘制半透明的具有科技感的边框和背景
                  function drawBorderAndBackground() {
                      // 绘制背景
                      ctx.fillStyle = 'rgba(28, 63, 92, 0.7)';
                      ctx.fillRect(10, 10, canvas.width - 20, canvas.height - 20);

                      // 绘制边框
                      ctx.beginPath();
                      ctx.lineWidth = 3;
                      ctx.strokeStyle = 'rgba(0, 255, 255, 0.8)';
                      ctx.roundRect(5, 5, canvas.width - 10, canvas.height - 10, 15);
                      ctx.stroke();

                      // 绘制内部发光效果
                      var gradient = ctx.createLinearGradient(10, 10, canvas.width - 10, canvas.height - 10);
                      gradient.addColorStop(0, 'rgba(0, 255, 255, 0.2)');
                      gradient.addColorStop(0.5, 'rgba(0, 255, 255, 0.4)');
                      gradient.addColorStop(1, 'rgba(0, 255, 255, 0.2)');
                      ctx.strokeStyle = gradient;
                      ctx.lineWidth = 2;
                      ctx.strokeRect(10, 10, canvas.width - 20, canvas.height - 20);
                  }

                  // 绘制自动换行的文字，并确保文字在边框范围内
                  function drawText(text) {
                      ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                      ctx.font = '13px Arial';
                      ctx.textAlign = 'start';
                      ctx.textBaseline = 'top';
                      var maxWidth = canvas.width; // 考虑左右边距
                      var lineHeight = 18;
                      var y = 18;
                      var words = text.split(' ');
                      var line = '';
                      for (var n = 0; n < words.length; n++) {
                          var testLine = line + words[n] + ' ';
                          var metrics = ctx.measureText(testLine);
                          var testWidth = metrics.width;
                          if (testWidth > maxWidth && n > 0) {
                              ctx.fillText(line, 15, y);
                              line = words[n] + ' ';
                              y += lineHeight;
                          } else {
                              line = testLine;
                          }
                      }
                      ctx.fillText(line, 15, y);
                  }

                  let cachedCanvas = canvasCache.get(label.name);
                  if (cachedCanvas) {
                      canvas = cachedCanvas;
                  } else {
                      // 调用绘制函数
                      drawBorderAndBackground();
                      drawText(label.name);
                      canvasCache.set(label.name, canvas); // 缓存绘制结果
                  }


                  // 坐标
                  var position = Cesium.Cartesian3.fromDegrees(label.coordinates[0], label.coordinates[1], label.coordinates[0] + 130);
                  var groundPositionCartesian = Cesium.Cartesian3.fromDegrees(label.coordinates[0], label.coordinates[1], 0)

                  // 创建标签
                  var label_entity = viewer.entities.add({
                      id:`construction_label${index}`,
                      name: 'construction_label',
                      position: position,
                      // 定位图标
                      billboard: {
                          image: canvas,
                          scaleByDistance : new Cesium.NearFarScalar(0, 2.0, 5000, 0.0),
                          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 图标底部对齐
                          horizontalOrigin: Cesium.HorizontalOrigin.CENTER, // 图标水平居中
                          disableDepthTestDistance: 0, // 启用深度测试，标签在被物体挡住时会消失
                          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 5000.0)
                      },
                      // 垂直线
                      polyline: {
                        positions: [Cesium.Cartesian3.fromDegrees(label.coordinates[0], label.coordinates[1], label.coordinates[0] + 130), groundPositionCartesian],
                        width: 2,
                        material: Cesium.Color.AQUA,
                        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.0, 5000.0)
                      }
                  });
                  this.constructionLabelEntities.push(label_entity)
                });
                this.viewer.entities.resumeEvents(); // 恢复事件
                resolve(this.constructionLabelEntities);
              }).catch(error => {
                console.error('Failed to load construction labels:', error);
                // 建议：此处可以添加用户提示，告知网络问题
                console.warn('加载施工标签失败，请检查您的网络连接并重试。');
                this.viewer.entities.resumeEvents(); // 确保在出错时也恢复事件
                resolve([]); // 仍然 resolve 空数组，避免阻塞后续逻辑
              });
            });
          }
          
          if(flag == true && this.constructionLabelEntities.length > 0) {
            // 显示机械化施工数据
            this.constructionLabelEntities.forEach(entity => {
              entity.show = true
            })
          }
          
          if (flag == false) {
            // 隐藏机械化施工数据
            this.constructionLabelEntities.forEach(entity => {
              entity.show = false
            })
            }
        },

        // 加载缓冲区
        async loadLineBuffer(flag) {
          let cur_projectId = getInitProjectId()
          console.log('1project', cur_projectId);
          
          SCENE_TOOL.loadLineBuffer(this.viewer, cur_projectId, flag, 40)
        },
        // 加载绝缘子片
        async loadInsulator(flag) {
          const innovateObject = this.viewer.entities.getById(this.innovateData[0].modelId)
          SCENE_TOOL.showSilhouette(innovateObject,flag,'red')
          if(flag) {
            flyTo(this.viewer, this.innovateData[0].innovateView);
          } else {
            this.flyToHome(1)
          }
        },
        // 切换施工/设计模式
        draw3DLine() {
          // 打开遮挡效果,避免悬空
          this.viewer.scene.globe.depthTestAgainstTerrain = true
          this.removeModel();
          const store_lineProj = useLineProjStore();
          this.map3d = new Map3DTool(this.$cache.getProjectCode(),this.towerModels, store_lineProj.insulators, store_lineProj.towerProfile || [], true, this.viewer, false, this.planRecordMap)
          store_lineProj.linesInfo.forEach(line => {
            this.map3d.draw3DLine(line,
              store_lineProj.lineDetail.find(item => item.lineId == line.lineId).towers
            )
          })
          // 加载额外连接的地线
          this.$cache.loadJsdata("groundLine.json").then(res => {
            for (let i = 0; i < res.length; i++) {
              this.map3d.addGroundLine(res[i])
            }
          })
          this.loadUnderGroundLine() // 加载地下电缆数据
          // 加载变电站模型和连接线 等需要保持加载的建筑模型
          this.building_keep_loading()

        },
        async draw3DLineV2() {
          console.log('测试新版本线路加载')
          const store_lineProj = useLineProjStore()
        //   await this.loadProtectedArea() // 加载保护区数据
          if (this.map3d_v2 == undefined) {
            let options = await store_lineProj.initDataV2(70)
            return new Promise((resolve, reject) => {
              console.log('options:', options)
                // 根据经纬度坐标 计算高程
                options.lineDetail = options.lineDetail.filter(item => {
                  if(item.longitude==undefined||item.latitude==undefined){
                    console.log('杆塔经纬度为空，请检查杆塔数据', item)
                    return false
                  }
                  return true
                })
                let positions = []
                for (let i = 0; i < options.lineDetail.length; i++) {
                  positions.push([options.lineDetail[i].longitude, options.lineDetail[i].latitude])
                }
                console.log('positions:', positions)
                getPositionHeight(this.viewer, positions).then(async positions_new => {
                  for (let i = 0; i < options.lineDetail.length; i++) {
                    options.lineDetail[i].height = options.lineDetail[i].altitude + positions_new[i].height
                  }
                  console.log('compute Height ok', options.lineDetail);
                  this.map3d_v2 = new Map3DTool_v2(this.viewer, this.$cache.getProjectCode(), options, this.planRecordMap)
                  this.map3d_v2.setDesign(true)
                  this.map3d_v2.autoDrawLine()
                  this.loadUnderGroundLine()// 加载地下电缆数据
                  resolve(true)
                })
              // })
            })
          } else {
            console.log('loadV2');
            
            this.map3d_v2.setDesign(true)
            this.map3d_v2.autoDrawLine()
            this.loadUnderGroundLine()// 加载地下电缆数据
            return Promise.resolve(true)
          }
        },
        draw3DLineNoDesign(flag) {
          // 为正常显示工序情况，关闭遮挡效果
          // this.viewer.scene.globe.depthTestAgainstTerrain = false
          this.removeModel();
          const store_lineProj = useLineProjStore();
          this.map3d = new Map3DTool(this.$cache.getProjectCode(),this.towerModels, store_lineProj.insulators, store_lineProj.towerProfile || [], false, this.viewer, flag, this.planRecordMap)
          store_lineProj.linesInfo.forEach(line => {
            this.map3d.draw3DLine(line, store_lineProj.lineDetail.find(item => item.lineId == line.lineId).towers)
          })
          // 加载额外连接的地线
          this.$cache.loadJsdata("groundLine.json").then(res => {
            for (let i = 0; i < res.length; i++) {
              this.map3d.addGroundLine(res[i])
            }
          })
          this.loadUnderGroundLine() // 加载地下电缆数据
        },
        async draw3DLineNoDesignV2(constructionMsg) {
          // 为正常显示工序情况，关闭遮挡效果
          // this.viewer.scene.globe.depthTestAgainstTerrain = false
        //   await this.loadProtectedArea() // 加载保护区数据
          const store_lineProj = useLineProjStore();
          let projectData = store_lineProj.lineDataV20
          console.log('lineDetail:', projectData)
          console.log('constructionMsg', constructionMsg);
          projectData.lineDetail.forEach(item => {
            if (constructionMsg[item.towerNumber]) {
              item.procedureStatus = constructionMsg[item.towerNumber].procedureStatus
            } else {
              item.procedureStatus = 5
            }
          })
          
          // 移除所有模型
          this.map3d_v2.setDesign(false)
          this.map3d_v2.removeAllModels()
          this.map3d_v2.updateProjectData(projectData, constructionMsg)
          //this.map3d_v2 = new Map3DTool_v2(viewer, this.$cache.getProjectCode(), projectData, constructionMsg)
          this.map3d_v2.autoDrawLine()
          this.loadUnderGroundLine()// 加载地下电缆数据
        },
        loadPowerLineInLeafletMap () {
          this.$refs.leafletMap.removeLines() // 移除原有线路
          if (this.$cache.getProjectCode() == 'ZDN') {
                // 如果是珠东南的项目 则需要另外方法获取线路位置信息 暂时不处理
                return
          }
          const store_lineProj = useLineProjStore()
          let lineInfo = store_lineProj.lineDataV20.linesInfo[0] // 在leafletMap中只绘制其中一条主线路
          let insulatorAllocation = store_lineProj.lineDataV20.insulatorAllocation.filter((item) => { return item.lineId == lineInfo.id })
          let sortedInsulatorAllocation = insulatorAllocation.sort((a, b) => a.positionId - b.positionId) // 将线路按positionId排序 确保顺序正确
          let positions = []
          let towerNumber = []
          let _tempTower = {}
          for (let i = 0; i < sortedInsulatorAllocation.length; i++) {
            if (!_tempTower[sortedInsulatorAllocation[i].towerNumber]) {
              let towerInfo = store_lineProj.lineDataV20.lineDetail.find((item) => { return item.towerNumber == sortedInsulatorAllocation[i].towerNumber })
              if (towerInfo.latitude != 0 && towerInfo.longitude !== 0) {
                positions.push([towerInfo.latitude, towerInfo.longitude])
                towerNumber.push(towerInfo.towerNumber)
              }
              _tempTower[sortedInsulatorAllocation[i].towerNumber] = sortedInsulatorAllocation[i]
            }
          }
          this.$refs.leafletMap.loadPowerLine(lineInfo.id, positions)
          this.$refs.leafletMap.loadPointIcons(lineInfo.id, positions, towerNumber)
          console.log('projectStructure4', towerNumber, _tempTower, store_lineProj.lineDataV20)
        },
        /**
         * 触发显示特定类型的机械化施工模型
         * @param {string} type 机械化施工类型，如'索道运输'、'架线施工'等
         */
        showConstructionModelByType(type) {
          console.log(`显示${type}类型的机械化施工模型`);
          this.flagConstructionLabel = { type: type, enable: true };
        },

        /**
         * 切换所有机械化施工模型的显示状态
         */
        toggleAllConstructionModels() {
          this.flagConstructionLabel = {
            type: '全部',
            enable: !this.flagConstructionLabel.enable
          };
        },

        /**
         * 显示特定机械化施工按钮的弹出菜单
         */
        showConstructionModelMenu() {
          const menuElement = document.getElementById('construction-model-menu');
          if (menuElement) {
            menuElement.style.display = menuElement.style.display === 'none' ? 'block' : 'none';
          }
        },
    },
    beforeDestroy() {
      // 清理机械化施工模型资源
      if (this.constructionModelExample) {
        this.constructionModelExample.removeAllModels();
        this.constructionModelExample = null;
        console.log('Cleaned up constructionModelExample');
      }

      // 清理机械化施工标签实体
      if (this.constructionLabelEntities && this.constructionLabelEntities.length > 0) {
        this.constructionLabelEntities.forEach(entity => {
          if (viewer && viewer.entities && !viewer.entities.isDestroyed()) {
            viewer.entities.remove(entity);
          }
        });
        this.constructionLabelEntities = [];
        console.log('Cleaned up constructionLabelEntities');
      }

      // 清理索道线路集合
      if (this.cablewayLineCollection && !this.cablewayLineCollection.isDestroyed()) {
        destroyLineCollection(this.viewer, this.cablewayLineCollection);
        this.cablewayLineCollection = null;
        console.log('Cleaned up cablewayLineCollection');
      }

      // 清理交叉跨越线路集合
      if (this.crossLineCollection && !this.crossLineCollection.isDestroyed()) {
        destroyLineCollection(this.viewer, this.crossLineCollection);
        this.crossLineCollection = null;
        console.log('Cleaned up crossLineCollection');
      }

      // 清理倾斜摄影相关资源
      if (this.tilesetD3dms && this.tilesetD3dms.length > 0) {
        const primitives = this.viewer.scene.primitives;
        this.tilesetD3dms.forEach(tileset => {
          if (tileset && !tileset.isDestroyed() && primitives.contains(tileset)) {
            primitives.remove(tileset);
          }
        });
        this.tilesetD3dms = [];
        console.log('Cleaned up tilesetD3dms (oblique photography models)');
      }
      if (this.obliquePhotographyLabelList && this.obliquePhotographyLabelList.length > 0) {
        this.obliquePhotographyLabelList.forEach(label => {
          if (label instanceof Cesium.Entity && viewer && viewer.entities && !viewer.entities.isDestroyed()) {
            viewer.entities.remove(label);
          } else if (label instanceof Cesium.LabelCollection && viewer && viewer.scene && viewer.scene.primitives && !viewer.scene.primitives.isDestroyed()) {
            viewer.scene.primitives.remove(label);
          }
        });
        this.obliquePhotographyLabelList = [];
        console.log('Cleaned up obliquePhotographyLabelList');
      }
      if (this.subsidiaryModels && this.subsidiaryModels.length > 0) {
        const primitives = this.viewer.scene.primitives;
        this.subsidiaryModels.forEach(primitive => {
          if (primitive && !primitive.isDestroyed() && primitives.contains(primitive)) {
            primitives.remove(primitive);
          }
        });
        this.subsidiaryModels = [];
        console.log('Cleaned up subsidiaryModels for oblique photography');
      }

      // 清理保护区标签实体
      if (this.protectedAreaEntities && this.protectedAreaEntities.length > 0) {
        this.protectedAreaEntities.forEach(entity => {
          if (viewer && viewer.entities && !viewer.entities.isDestroyed()) {
            viewer.entities.remove(entity);
          }
        });
        this.protectedAreaEntities = [];
        console.log('Cleaned up protectedAreaEntities');
      }

      // 清理 map3d 和 map3d_v2 实例中的资源
      if (this.map3d && typeof this.map3d.destroy === 'function') {
        this.map3d.destroy();
        this.map3d = null;
        console.log('Cleaned up map3d instance');
      }
      if (this.map3d_v2 && typeof this.map3d_v2.destroy === 'function') {
        this.map3d_v2.destroy(); // 假设 map3d_v2 也有一个 destroy 方法
        this.map3d_v2 = null;
        console.log('Cleaned up map3d_v2 instance');
      }

      // 移除事件监听器
      if (viewer && viewer.camera && viewer.camera.changed) {
        viewer.camera.changed.removeEventListener(this.cameraChangedHandler);
        console.log('Removed camera.changed event listener');
      }
      if (viewer && viewer.scene && viewer.scene.postRender) {
        viewer.scene.postRender.removeEventListener(this.cameraChangedHandler);
        console.log('Removed scene.postRender event listener');
      }
      // 清理 window 上的 acceptMessage
      if (window.acceptMessage) {
        window.acceptMessage = null; // 或更安全的做法是移除事件监听器，如果它是通过 addEventListener 添加的
        console.log('Cleaned up window.acceptMessage');
      }
      // 如果 acceptMessage 是通过 addEventListener 添加的，需要像这样移除：
      // removeEventListener('message', this.handleParentMessage); // 假设 handleParentMessage 是实际的监听函数

      // 其他可能的清理，例如清除 $cache 中的特定项目
      if (this.$cache && typeof this.$cache.clearProjectSpecificCache === 'function') { // 假设有这样的方法
        this.$cache.clearProjectSpecificCache();
        console.log('Cleared project-specific cache');
      }

      console.log('PowerLineView component beforeDestroy cleanup complete.');
    },
}
</script>

<style scoped>
#cesiumDiv {
    /*width: calc(100% - 330px * 2);*/
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
}

.leafletMapDiv {
    position: fixed;
    height: 39%;
    width: 22%;
    left: 7px;
    bottom: 5px;
    /* background-color: gray; */
    z-index: 100;
}
</style>
