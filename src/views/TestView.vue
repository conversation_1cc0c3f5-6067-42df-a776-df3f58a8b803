<template>
  <div>
    <h1>Cesium测试页面</h1>
    <div id="cesiumContainer" style="width: 100%; height: 500px;"></div>
    <div style="margin-top: 20px;">
      <p>状态: {{ status }}</p>
      <button @click="testPolyline">测试Polyline</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import * as Cesium from 'cesium';

const status = ref('初始化中...');

onMounted(async () => {
  try {
    // 创建Cesium viewer
    const viewer = new Cesium.Viewer('cesiumContainer', {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      vrButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false
    });

    // 设置相机位置到深圳
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(114.057868, 22.543099, 1000)
    });

    status.value = 'Cesium初始化成功';

    // 测试基本功能
    window.testViewer = viewer;

  } catch (error) {
    console.error('Cesium初始化失败:', error);
    status.value = `初始化失败: ${error.message}`;
  }
});

const testPolyline = () => {
  try {
    const viewer = window.testViewer;
    if (!viewer) {
      status.value = 'Viewer未初始化';
      return;
    }

    // 创建一个简单的polyline
    const polyline = viewer.entities.add({
      name: '测试线路',
      polyline: {
        positions: Cesium.Cartesian3.fromDegreesArray([
          114.057868, 22.543099,
          114.067868, 22.553099,
          114.077868, 22.563099
        ]),
        width: 5,
        material: Cesium.Color.YELLOW,
        clampToGround: true
      }
    });

    status.value = 'Polyline创建成功';
    
    // 飞行到polyline
    viewer.flyTo(polyline);

  } catch (error) {
    console.error('Polyline创建失败:', error);
    status.value = `Polyline创建失败: ${error.message}`;
  }
};
</script>

<style scoped>
#cesiumContainer {
  border: 1px solid #ccc;
}
</style>
