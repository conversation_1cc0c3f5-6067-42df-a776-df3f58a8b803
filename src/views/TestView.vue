<template>
  <div>
    <h1>电力线路3D展示测试</h1>
    <div id="cesiumContainer" style="width: 100%; height: 600px;"></div>
    <div style="margin-top: 20px; padding: 20px; background: #f5f5f5;">
      <p><strong>状态:</strong> {{ status }}</p>
      <div style="margin-top: 10px;">
        <button @click="testBasicPolyline" style="margin-right: 10px;">测试基本Polyline</button>
        <button @click="testPowerLine" style="margin-right: 10px;">测试电力线路</button>
        <button @click="clearAll" style="margin-right: 10px;">清除所有</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import SimpleCesiumViewer from '@/js/cesium/simple-viewer';
import SimplePowerLineRenderer, { type PowerLineProject } from '@/js/powerline/simple-powerline';

const status = ref('初始化中...');
let cesiumViewer: SimpleCesiumViewer | null = null;
let powerLineRenderer: SimplePowerLineRenderer | null = null;

// 演示电力线路数据
const demoProject: PowerLineProject = {
  projectId: 'demo-001',
  projectName: '深圳电力线路演示项目',
  lines: [
    {
      lineId: 'line-001',
      lineName: '深圳220kV线路A',
      voltage: 220,
      towers: [
        {
          id: '1',
          towerNumber: 'A1',
          longitude: 114.057868,
          latitude: 22.543099,
          height: 50,
          altitude: 45
        },
        {
          id: '2',
          towerNumber: 'A2',
          longitude: 114.067868,
          latitude: 22.553099,
          height: 55,
          altitude: 50
        },
        {
          id: '3',
          towerNumber: 'A3',
          longitude: 114.077868,
          latitude: 22.563099,
          height: 60,
          altitude: 55
        }
      ]
    }
  ],
  centerPosition: {
    longitude: 114.067868,
    latitude: 22.553099,
    height: 1500
  }
};

onMounted(async () => {
  try {
    // 等待Cesium加载
    await waitForCesium();

    // 初始化SimpleCesiumViewer
    cesiumViewer = new SimpleCesiumViewer({
      containerId: 'cesiumContainer',
      homePosition: {
        longitude: 114.067868,
        latitude: 22.553099,
        height: 1500
      }
    });

    // 初始化电力线路渲染器
    powerLineRenderer = new SimplePowerLineRenderer(cesiumViewer.viewer);

    status.value = 'Cesium初始化成功，可以开始测试';

  } catch (error) {
    console.error('初始化失败:', error);
    status.value = `初始化失败: ${error.message}`;
  }
});

onUnmounted(() => {
  if (cesiumViewer) {
    cesiumViewer.destroy();
  }
});

// 等待Cesium加载
const waitForCesium = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (window.Cesium) {
      resolve();
      return;
    }

    const checkCesium = () => {
      if (window.Cesium) {
        resolve();
      } else {
        setTimeout(checkCesium, 100);
      }
    };

    checkCesium();

    // 10秒超时
    setTimeout(() => {
      reject(new Error('Cesium加载超时'));
    }, 10000);
  });
};

const testBasicPolyline = () => {
  try {
    if (!cesiumViewer) {
      status.value = 'Cesium未初始化';
      return;
    }

    // 清除之前的测试
    cesiumViewer.clearAll();

    // 添加简单的polyline
    const positions = [
      [114.057868, 22.543099, 100],
      [114.067868, 22.553099, 110],
      [114.077868, 22.563099, 120]
    ];

    cesiumViewer.addPolyline(positions, {
      name: '测试线路',
      width: 5,
      color: window.Cesium.Color.YELLOW
    });

    // 添加杆塔标记
    positions.forEach((pos, index) => {
      cesiumViewer.addTowerMarker(pos[0], pos[1], pos[2], {
        name: `T${index + 1}`,
        showLabel: true
      });
    });

    status.value = '基本Polyline测试成功';

  } catch (error) {
    console.error('基本Polyline测试失败:', error);
    status.value = `基本Polyline测试失败: ${error.message}`;
  }
};

const testPowerLine = () => {
  try {
    if (!powerLineRenderer) {
      status.value = '电力线路渲染器未初始化';
      return;
    }

    // 渲染演示电力线路项目
    powerLineRenderer.renderProject(demoProject);
    status.value = '电力线路3D展示测试成功';

  } catch (error) {
    console.error('电力线路测试失败:', error);
    status.value = `电力线路测试失败: ${error.message}`;
  }
};

const clearAll = () => {
  try {
    if (cesiumViewer) {
      cesiumViewer.clearAll();
    }
    if (powerLineRenderer) {
      powerLineRenderer.clearAll();
    }
    status.value = '已清除所有内容';
  } catch (error) {
    console.error('清除失败:', error);
    status.value = `清除失败: ${error.message}`;
  }
};
</script>

<style scoped>
#cesiumContainer {
  border: 1px solid #ccc;
  border-radius: 4px;
}

button {
  padding: 8px 16px;
  background: #007cba;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #005a87;
}
</style>
