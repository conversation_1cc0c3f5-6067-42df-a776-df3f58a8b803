<template>
  <div id="container" class="container"></div>
</template>

<script setup>
import {
  ref,
  onMounted,
  onUnmounted
} from "vue";
import * as THREE from "three";
import { VRButton } from "three/examples/jsm/webxr/VRButton.js";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
import { RoomEnvironment } from "three/examples/jsm/environments/RoomEnvironment.js";
import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer.js';
import TWEEN from '@tweenjs/tween.js';

let camera, scene, renderer, labelRenderer;
let controls;
let vrButton;
let environment;
let spheres = [];

onMounted(() => {
  init();
});

onUnmounted(() => {
    window.removeEventListener('resize', onWindowResize);
    if (vrButton && vrButton.parentElement) {
        vrButton.parentElement.removeChild(vrButton);
    }
    if (renderer) {
        renderer.dispose();
        const container = document.getElementById("container");
        if (container && renderer.domElement.parentElement) {
            container.removeChild(renderer.domElement);
        }
    }
});


function init() {
  const container = document.getElementById("container");
  if (!container) {
      console.error("Container element not found");
      return;
  }

  // Renderer
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(container.clientWidth, container.clientHeight);
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.xr.enabled = true;
  container.appendChild(renderer.domElement);

  // Scene
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xbbbbbb);

  // Environment
  const pmremGenerator = new THREE.PMREMGenerator(renderer);
  scene.environment = pmremGenerator.fromScene(new RoomEnvironment(renderer)).texture;

  // Camera
  camera = new THREE.PerspectiveCamera(
    50,
    container.clientWidth / container.clientHeight,
    0.1,
    100
  );
  camera.position.set(0, 1.6, 3);
  scene.add(camera);

  // Controls
  controls = new OrbitControls(camera, renderer.domElement);
  controls.target.set(0, 1.6, 0);
  controls.update();

  // VR Button
  vrButton = VRButton.createButton(renderer);
  document.body.appendChild(vrButton);

  // Lights
  const light = new THREE.HemisphereLight( 0xffffff, 0xbbbbff, 3 );
  light.position.set( 0.5, 1, 0.25 );
  scene.add( light );

  // Geometry
  const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
  const material = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
  const cube = new THREE.Mesh(geometry, material);
  cube.position.set(0, 1.6, -1);
  scene.add(cube);
  spheres.push(cube);

  // Label
  const labelDiv = document.createElement('div');
  labelDiv.className = 'label';
  labelDiv.textContent = 'Green Cube';
  const label = new CSS2DObject(labelDiv);
  label.position.set(0, 0.3, 0);
  cube.add(label);

  // Tween animation
  new TWEEN.Tween(cube.rotation)
    .to({ y: Math.PI * 2 }, 2000)
    .repeat(Infinity)
    .start();

  // Label Renderer
  labelRenderer = new CSS2DRenderer();
  labelRenderer.setSize(container.clientWidth, container.clientHeight);
  labelRenderer.domElement.style.position = 'absolute';
  labelRenderer.domElement.style.top = '0px';
  container.appendChild(labelRenderer.domElement);

  window.addEventListener("resize", onWindowResize);
  
  animate();
}

function onWindowResize() {
  const container = document.getElementById("container");
  if (!container) return;
  camera.aspect = container.clientWidth / container.clientHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(container.clientWidth, container.clientHeight);
  labelRenderer.setSize(container.clientWidth, container.clientHeight);
}

function animate() {
  renderer.setAnimationLoop(render);
}

function render() {
  TWEEN.update();
  renderer.render(scene, camera);
  labelRenderer.render(scene, camera);
}
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
}

.label {
  color: #fff;
  font-size: 16px;
  /* background-color: rgba(0, 0, 0, 0.5); */
  padding: 5px;
  border-radius: 5px;
}

.label-container {
  position: absolute;
  pointer-events: none;
  /* 确保标签容器不会捕获鼠标事件 */
  transform: translate(-50%, -50%);
  /* 经典居中 */
}
</style>
