<template>
  <canvas id="threeCanvas"></canvas>
  <div class="threeDetail">
    <a style="color: aqua;">{{threeDeatail}}</a>
  </div>
  
  <div class="sysMsgDiv">
    <div class="navbar">
      <div style="position: relative;" v-for="(image, index) in images" :key="index">
        <img class="imgClass" :src="image.viewUrl" @click="changeImage(index)" :class="{active: currentImageName === image.name}">
        <p :class="{active: currentImageName === image.name}"><span>{{image.name}}</span></p>
      </div>
    </div>
  </div>
  <div id="loading-mark" v-if="loading">
    <div class="loading-box">
      <div class="loading">
        <img src="../utils/loading.svg" />
        <div class="progress-txt">
          加载中...
        </div>
        <div class="loading-txt">
          加载时间较长请耐心等待.....
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { createThreeScene, createBox, addPlane, axesOn } from '@/js/three/basicFunction.js'
import VRSphereTool from '@/js/three/VRSphere.js'
import {ElMessage} from "element-plus";
import * as THREE from 'three';
import {convert3To2, convertVector3To2, conver2ToVector3, addLable, remodeCSS3DRenderer} from '@/js/three/tools.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { CAMERA_POSITION } from '@/config/global.js';
import router from '@/router';
import Cache from "@/js/cache";

export default {
    name: 'threejs3d',
    data () {
      return {
        projectCode:"",
        progress: 0,
        loading: true,
        loadCallBack: (flag) => {
          // this.progress = Number((progressNum / 1024 / 1024).toFixed(2));
          this.loading = flag
        },
        currentImageName: '',
        threeDeatail: '',
        towerMap: null,
        addLabel:true,
        controls :{},
        css3Renderers:[],
        propertyLabelTop: 0, // 设置初始值
        propertyLabelLeft: 0,
        showPropertyLabel: false,
        selectedObject: null,
        images: [
        ],
        selectedImage: null,
        mouse: new THREE.Vector2(),
        raycaster: new THREE.Raycaster(),
        labelDatas:null
      }
    },
    mounted () {
      ElMessage.success('VR场景加载')
      console.log('vr page mounted')

      const searchParams = new URLSearchParams(window.location.search);
      this.projectCode =searchParams.get("projectCode");
      if(!this.projectCode){
        ElMessage({
            message: '缺失项目编码信息',
            grouping: true,
            type: 'warning',
          })
          return
      }else{
        console.log('projectCode : ', this.projectCode)
      }
      let $cache = new Cache([{projectCode:this.projectCode}],0)
 
      $cache.getVrdata().then(vrdata=>{
        this.images = vrdata.images
        this.towerMap = vrdata.towerMap
        this.labelDatas = vrdata.labelDatas
        console.log('vrdata : ', vrdata)
        this.initVr()
      }).catch(e=>{
        console.log(e)
      })
    },
    watch: {
      // selectedImage(newImage) {
      //   console.log('select img : ', newImage);
      // },
    },
    methods: {
      initVr(){
        let index = 0;
        const searchParams = new URLSearchParams(window.location.search);
        const tower = searchParams.get('tower');
        
        // 根据tower参数选择对应的VR图
        if (tower && this.towerMap[tower]) {
          this.currentImageName = this.towerMap[tower].name;
          // 在images数组中查找对应name的索引
          index = this.images.findIndex(img => img.name === this.towerMap[tower].name);
          if (index === -1) {
            index = 0; // 如果没找到对应的图片，使用默认图片
          }
        } else if (tower) {
          ElMessage({
            message: '未找到对应的杆塔VR图！',
            grouping: true,
            type: 'warning',
          });
          index = 0;
        }
        
        // 设置当前图片名称（如果没有tower参数或找不到对应图片，使用第一张图）
        this.currentImageName = this.images[index].name;
      let threeCanvas = document.getElementById("threeCanvas");
      this.myThree = createThreeScene(threeCanvas);
      // this.animate()
      // // this.createPlane(); // 添加一个地板参照
      // // axesOn(this.myThree); // 打开坐标轴
      // // this.createBox(); // 创建一个立方体
      // createVRSphere(this.myThree, img_url); // 创建一个全息投影环境
      // axesOn(this.myThree); // 打开坐标轴

      this.selectedImage = this.images[index];
      this.VRSphereTool = new VRSphereTool(this.myThree);
      this.VRSphereTool.createVRSphere(this.images[index].url, 6000, this.loadCallBack); // 创建一个全息投影环境
      document.addEventListener('mousedown', this.VRSphereTool.onMouseDown, false);

      let labelElement = document.createElement('div');

      // this.addMouseEventListeners();
      this.controls = new OrbitControls(this.myThree.camera, this.myThree.renderer.domElement);
      // this.controls.enableZoom = false; //禁止缩放
      this.controls.enablePan = false; //禁止平移
      // 上下旋转范围
      this.controls.minPolarAngle = Math.PI / 4;
      this.controls.maxPolarAngle = Math.PI/2.7;
      this.controls.minDistance = 2800; // 最小缩放距离
      this.controls.maxDistance = 6000; // 最大缩放距离

      

      this.animate()
      /*
      console.log(this.labelDatas[0].labels)*/
      this.threeDeatail = this.labelDatas[this.currentImageName].desc
      this.labelDatas[this.currentImageName].labels.forEach(l=>{
        // console.log('DDDDD')
        var html = `<div style="background-color:rgba(13, 33, 52,0.7); border-radius: 7px; border: 2px solid rgba(36, 216, 217, 0.5);"><p style="color: aqua;font-size:large;padding: 5px;font-family: 'Segoe UI', Arial, sans-serif;font-weight: bold;">${l.label}</p></div>`
        this.css3Renderers.push(addLable(html,
            new THREE.Vector3(l.x,l.y,l.z)
            ,this.myThree.camera,this.myThree.renderer))
      })
      },
      changeImage(index) {
        this.loading = true
        // 清空描述信息
        this.threeDeatail = '';
        // 先销毁所有现有标签
        if (this.css3Renderers && this.css3Renderers.length > 0) {
          this.css3Renderers.forEach(j => {
            if (j && j.css3Renderer) {
              remodeCSS3DRenderer(j.css3Renderer);
              // 移除DOM元素
              if (j.css3Renderer.domElement && j.css3Renderer.domElement.parentNode) {
                j.css3Renderer.domElement.parentNode.removeChild(j.css3Renderer.domElement);
              }
            }
          });
          this.css3Renderers = [];
        }

        this.VRSphereTool.createVRSphere(this.images[index].url, 5000, this.loadCallBack);
        this.selectedImage = this.images[index];
        this.currentImageName = this.images[index].name;
        this.threeDeatail = this.labelDatas[this.currentImageName].desc;

        // 添加新标签
        this.labelDatas[this.currentImageName].labels.forEach(l => {
          var html = `<div style="background-color:rgba(13, 33, 52,0.7); border-radius: 7px; border: 2px solid rgba(36, 216, 217, 0.5);"><p style="color: aqua;font-size:large;padding: 5px;font-family: 'Segoe UI', Arial, sans-serif;font-weight: bold;">${l.label}</p></div>`;
          this.css3Renderers.push(addLable(html,
            new THREE.Vector3(l.x,l.y,l.z),
            this.myThree.camera,
            this.myThree.renderer
          ));
        });
      },
      _onCameraChange(){
        console.log('DDDDD')
      },
      animate() {
        this.myThree.renderer.render(this.myThree.scene, this.myThree.camera)
        this.controls.update();

        this.css3Renderers.forEach(r=>{

          r.css3Renderer.render(this.myThree.scene,this.myThree.camera)
          const point = convertVector3To2(new THREE.Vector3(r.position.x, r.position.y, r.position.z),this.myThree.camera,this.myThree.renderer)

          r.css3Renderer.domElement.style.top = `${point.y}px`;
          r.css3Renderer.domElement.style.left = `${point.x}px`;
          if(point.y<0 || point.x<0 || point.y>window.innerHeight || point.x>window.innerWidth){
            // r.css3Renderer.visible  = false;
            r.css3Renderer.domElement.style.display  = 'none';
          }else{
            r.css3Renderer.domElement.style.display  = 'block';
          }
        })
        requestAnimationFrame(this.animate)
      },
      // 创建一个最基本的box添加到场景
      createBox () {
          // 确定box的基本参数
          let boxConf = {
              name: "box1",
              width: 100,
              height: 300,
              depth: 100,
              color: "#ffff00",
              lineColor: "#ffff00",
              opacity: 0.3,
              position: {
                  x: 0,
                  y: 0,
                  z: 0,
              },
              shadow: false,
          };
          let box = createBox(boxConf);
          this.myThree.scene.add(box); //需要注意这里的scene为小写
      },
      createPlane () {
          const planeConf = {
              "type": 1,
              "width": 4000,
              "height": 4000,
              "color": "#f2f2f2",
              "img": "",
              "opacity": 0.6,
              "name": "GroundPlane",
              "enableAxes": true
          }
          addPlane(this.myThree, planeConf); // 创建一个地平面
      },
      addMouseEventListeners() {
        window.addEventListener('mousemove', this.onMouseMove);
        window.addEventListener('click', this.onClick);
      },
      removeMouseEventListeners() {
        window.removeEventListener('mousemove', this.onMouseMove);
        window.removeEventListener('click', this.onClick);
      },
      onMouseMove(event) {
        event.preventDefault();

        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        this.raycaster.setFromCamera(this.mouse, this.myThree.camera);
      },

      displayPropertyLabel(object) {
        this.selectedObject = object;
        this.showPropertyLabel = true;
      },

      hidePropertyLabel() {
        this.selectedObject = null;
        this.showPropertyLabel = false;
      },

      onClick(event) {

        event.preventDefault();
        var v2=conver2ToVector3(event,this.myThree.camera,this.myThree.renderer,this.myThree.scene)
        console.log('onClick')
        //convert3To2(0.10758535572212474,799.8555873188085,1599.6249940867326,this.myThree.camera)
        //var html = `<p style="color:red;font-size:xx-large">dddddFFFFF</p>`
        //this.css3Renderers.push(addLable(html,vetor3,this.myThree.camera))
      }
    }
}
</script>

<style scoped lang="less">
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden; /* 确保根元素不出现滚动条 */
}

#threeCanvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 0;
  outline: none; /* 移除点击时的轮廓线 */
}

@keyframes neon-glow {
  0% { box-shadow: 0 0 5px #0ff, 0 0 10px #0ff, 0 0 15px #0ff; }
  50% { box-shadow: 0 0 10px #0ff, 0 0 20px #0ff, 0 0 30px #0ff; }
  100% { box-shadow: 0 0 5px #0ff, 0 0 10px #0ff, 0 0 15px #0ff; }
}

@keyframes border-flow {
  0% { border-color: #0ff; }
  50% { border-color: #08f; }
  100% { border-color: #0ff; }
}

#propertyLabel {
  position: absolute;
  background: rgba(0, 20, 40, 0.8);
  padding: 15px;
  color: #0ff;
  font-size: 14px;
  border: 1px solid #0ff;
  border-radius: 5px;
  backdrop-filter: blur(5px);
  animation: neon-glow 2s infinite;
}

.propertyContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.propertyContent p {
  margin: 0;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0,255,255,0.2);
}

.sysMsgDiv {
  position: fixed;
  top: 20px;
  left: 20px;
  padding: 10px;
}

.threeDetail {
  position: fixed; /* 改为fixed定位 */
  top: 20px;
  right: 20px;
  width: 200px;
  background: rgba(0,20,40,0.9);
  color: #0ff;
  font-size: 18px;
  padding: 15px;
  border-radius: 10px;
  border: 1px solid #0ff;
  animation: border-flow 3s infinite;
  backdrop-filter: blur(5px);
  box-shadow: 0 0 20px rgba(0,255,255,0.2);
}

.navbar {
  position: relative;
  max-height: 80vh;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  padding: 10px;
  border: 3px solid #00a7ff;
  border-radius: 12px;
  box-shadow: 0 0 15px rgba(0, 167, 255, 0.3),
              inset 0 0 20px rgba(0, 167, 255, 0.1);
  backdrop-filter: blur(8px);
  animation: border-glow 2s infinite;
  scrollbar-width: thin;
  scrollbar-color: rgb(30, 169, 250) rgba(0, 167, 255, 0.1);
  transform: translate3d(0,0,0);
  will-change: scroll-position;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;

  &::-webkit-scrollbar {
    width: 10px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 167, 255, 0.1);
    border-radius: 5px;
    margin: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #00a7ff;
    border-radius: 5px;
    border: 2px solid transparent;
    background-clip: padding-box;
    box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
    transition: all 0.2s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #0ff;
    border-width: 1px;
  }
}

.imgClass {
  width: 150px;
  height: 100px;
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  object-fit: cover;
  
  &:hover {
    transform: scale(1.05);
    border-color: #0ff;
    box-shadow: 0 0 15px rgba(0,255,255,0.5);
  }
}

.navbar p {
  position: absolute;
  background: rgba(0,20,40,0.9);
  color: #0ff;
  bottom: 0;
  right: 20px;
  padding: 8px 12px;
  font-family: "微软雅黑";
  font-size: 12px;
  border-radius: 5px;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.navbar p.active {
  background: rgba(0,255,255,0.2);
  color: #fff;
}

.navbar img {
  margin: 0 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &.active {
    opacity: 0.7;
    transform: scale(0.95);
  }
}

#loading-mark {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 2000;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;

  .loading-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0,10,20,0.85);
    backdrop-filter: blur(10px);

    .loading {
      width: 320px;
      padding: 30px;
      text-align: center;
      background: rgba(0,20,40,0.9);
      border-radius: 15px;
      border: 1px solid #0ff;
      animation: neon-glow 2s infinite;
    }

    .progress-txt {
      font-size: 20px;
      color: #0ff;
      margin: 15px 0;
      text-shadow: 0 0 10px rgba(0,255,255,0.5);
      
      b {
        color: #0ff;
      }
    }

    .loading-txt {
      text-align: center;
      font-size: 14px;
      color: #08f;
      margin-top: 10px;
    }
  }
}
</style>
