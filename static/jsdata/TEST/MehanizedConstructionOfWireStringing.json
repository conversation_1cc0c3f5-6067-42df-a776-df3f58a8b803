{"pnts_building_url": "", "constructionModelLoading": ["WireStringingMultifunctionVehicle1", "WireStringingCrane1", "WireStringingHelicopter1", "WireStringingDrone1", "WireStringingMultifunctionVehicle2", "WireStringingCrane2", "WireStringingHelicopter2", "WireStringingDrone2", "WireStringingMultifunctionVehicle3", "WireStringingCrane3", "WireStringingHelicopter3", "WireStringingDrone3"], "building_url": [], "WireStringingMultifunctionVehicle1": {"url": "/model/架线机械化施工/智能化集控式牵张设备/智能化集控式牵张设备.glb", "name": "架线多功能车辆", "view": [114.44506111619086, 22.575245851518808, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.44506111619086, 22.575245851518808]}, "WireStringingCrane1": {"url": "/model/架线机械化施工/绞磨自动收绳装置/绞磨自动收绳装置.glb", "name": "架线吊车", "view": [114.44494351624304, 22.575172650195736, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.44494351624304, 22.575172650195736]}, "WireStringingHelicopter1": {"url": "/model/铁塔组立机械化施工/直升机/直升机.glb", "name": "架线直升机", "view": [114.44499283916397, 22.575038171220587, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.44499283916397, 22.575038171220587, 50]}, "WireStringingDrone1": {"url": "/model/铁塔组立机械化施工/无人机/无人机.glb", "name": "架线无人机", "view": [114.44502374177976, 22.5751367922868, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.44502374177976, 22.5751367922868, 30]}, "WireStringingMultifunctionVehicle2": {"url": "/model/架线机械化施工/智能化集控式牵张设备/智能化集控式牵张设备.glb", "name": "架线多功能车辆", "view": [114.4547839177525, 22.598572913324702, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.4547839177525, 22.598572913324702]}, "WireStringingCrane2": {"url": "/model/架线机械化施工/绞磨自动收绳装置/绞磨自动收绳装置.glb", "name": "架线吊车", "view": [114.45470128675385, 22.598693473520115, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.45470128675385, 22.598693473520115]}, "WireStringingHelicopter2": {"url": "/model/铁塔组立机械化施工/直升机/直升机.glb", "name": "架线直升机", "view": [114.45473147380324, 22.59878454279862, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.45473147380324, 22.59878454279862, 50]}, "WireStringingDrone2": {"url": "/model/铁塔组立机械化施工/无人机/无人机.glb", "name": "架线无人机", "view": [114.45477594183242, 22.59868467372395, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.45477594183242, 22.59868467372395, 30]}, "WireStringingMultifunctionVehicle3": {"url": "/model/架线机械化施工/智能化集控式牵张设备/智能化集控式牵张设备.glb", "name": "架线多功能车辆", "view": [114.43725954353297, 22.633621486272766, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.43725954353297, 22.633621486272766]}, "WireStringingCrane3": {"url": "/model/架线机械化施工/绞磨自动收绳装置/绞磨自动收绳装置.glb", "name": "架线吊车", "view": [114.43723795402026, 22.63371022962654, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.43723795402026, 22.63371022962654]}, "WireStringingHelicopter3": {"url": "/model/铁塔组立机械化施工/直升机/直升机.glb", "name": "架线直升机", "view": [114.43725111246924, 22.633915252797856, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.43725111246924, 22.633915252797856, 50]}, "WireStringingDrone3": {"url": "/model/铁塔组立机械化施工/无人机/无人机.glb", "name": "架线无人机", "view": [114.4372931574026, 22.63382030333304, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.4372931574026, 22.63382030333304, 30]}}