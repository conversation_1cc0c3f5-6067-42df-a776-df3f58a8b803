{"pnts_building_url": "", "constructionModelLoading": ["multifunctionalVehicle1", "Crane1", "Helicopter1", "Drone1", "multifunctionalVehicle2", "Crane2", "Helicopter2", "Drone2", "multifunctionalVehicle3", "Crane3", "Helicopter3", "Drone3", "multifunctionalVehicle4", "Crane4", "Helicopter4", "Drone4", "multifunctionalVehicle5", "Crane5", "Helicopter5", "Drone5", "multifunctionalVehicle6", "Crane6", "Helicopter6", "Drone6", "multifunctionalVehicle7", "Crane7", "Helicopter7", "Drone7"], "building_url": [], "multifunctionalVehicle1": {"url": "/model/铁塔组立机械化施工/多功能作业车/多功能作业车.glb", "name": "多功能作业车", "view": [114.44518453912991, 22.575617891269204, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.44518453912991, 22.575617891269204]}, "Crane1": {"url": "/model/铁塔组立机械化施工/起重机/起重机.glb", "name": "起重机", "view": [114.44508979823944, 22.575491035115217, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [10.25, -0.025, 0.25], "position": [114.44503824767222, 22.575724770306643]}, "Helicopter1": {"url": "/model/铁塔组立机械化施工/直升机/直升机.glb", "name": "直升机", "view": [114.4451611396437, 22.575534002470388, 100], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.4451611396437, 22.575534002470388, 100]}, "Drone1": {"url": "/model/铁塔组立机械化施工/无人机/无人机.glb", "name": "无人机", "view": [114.44516099670292, 22.575658229079803, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.5, "rotation": [0.25, -2.025, 0.25], "position": [114.44516099670292, 22.575658229079803]}, "multifunctionalVehicle2": {"url": "/model/铁塔组立机械化施工/多功能作业车/多功能作业车.glb", "name": "多功能作业车", "view": [114.45490195164903, 22.59852003046337, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.45490195164903, 22.59852003046337]}, "Crane2": {"url": "/model/铁塔组立机械化施工/起重机/起重机.glb", "name": "起重机", "view": [114.45494785826372, 22.598630920367373, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.45494785826372, 22.598630920367373]}, "Helicopter2": {"url": "/model/铁塔组立机械化施工/直升机/直升机.glb", "name": "直升机", "view": [114.45498587489341, 22.598563480702886, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.45498587489341, 22.598563480702886, 100]}, "Drone2": {"url": "/model/铁塔组立机械化施工/无人机/无人机.glb", "name": "无人机", "view": [114.45493064325389, 22.59843793067896, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.5, "rotation": [0.25, -2.025, 0.25], "position": [114.45493064325389, 22.59843793067896]}, "multifunctionalVehicle3": {"url": "/model/铁塔组立机械化施工/多功能作业车/多功能作业车.glb", "name": "多功能作业车", "view": [114.43803602642802, 22.63354074365572, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.43803602642802, 22.63354074365572]}, "Crane3": {"url": "/model/铁塔组立机械化施工/起重机/起重机.glb", "name": "起重机", "view": [114.43800326385407, 22.633748739917028, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.43800326385407, 22.633748739917028]}, "Helicopter3": {"url": "/model/铁塔组立机械化施工/直升机/直升机.glb", "name": "直升机", "view": [114.43802800597857, 22.633921522541748, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.43802800597857, 22.633921522541748, 100]}, "Drone3": {"url": "/model/铁塔组立机械化施工/无人机/无人机.glb", "name": "无人机", "view": [114.43818076925675, 22.633743411255583, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.5, "rotation": [0.25, -2.025, 0.25], "position": [114.43818076925675, 22.633743411255583]}, "multifunctionalVehicle4": {"url": "/model/铁塔组立机械化施工/多功能作业车/多功能作业车.glb", "name": "多功能作业车", "view": [114.44233305215742, 22.645186484043503, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.44233305215742, 22.645186484043503]}, "Crane4": {"url": "/model/铁塔组立机械化施工/起重机/起重机.glb", "name": "起重机", "view": [114.43193807845186, 22.645244074461143, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.43193807845186, 22.645244074461143]}, "Helicopter4": {"url": "/model/铁塔组立机械化施工/直升机/直升机.glb", "name": "直升机", "view": [114.43173940555195, 22.645315332624552, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.43173940555195, 22.645315332624552, 100]}, "Drone4": {"url": "/model/铁塔组立机械化施工/无人机/无人机.glb", "name": "无人机", "view": [114.43191994396764, 22.645360178301306, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.5, "rotation": [0.25, -2.025, 0.25], "position": [114.43191994396764, 22.645360178301306]}, "multifunctionalVehicle5": {"url": "/model/铁塔组立机械化施工/多功能作业车/多功能作业车.glb", "name": "多功能作业车", "view": [114.41014059589214, 22.650663382707716, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.41014059589214, 22.650663382707716]}, "Crane5": {"url": "/model/铁塔组立机械化施工/起重机/起重机.glb", "name": "起重机", "view": [114.40992301146531, 22.650704871120748, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.40992301146531, 22.650704871120748]}, "Helicopter5": {"url": "/model/铁塔组立机械化施工/直升机/直升机.glb", "name": "直升机", "view": [114.41013273426817, 22.65078177379356, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.41013273426817, 22.65078177379356, 100]}, "Drone5": {"url": "/model/铁塔组立机械化施工/无人机/无人机.glb", "name": "无人机", "view": [114.40995090909762, 22.6508150376892, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.5, "rotation": [0.25, -2.025, 0.25], "position": [114.40995090909762, 22.6508150376892]}, "multifunctionalVehicle6": {"url": "/model/铁塔组立机械化施工/多功能作业车/多功能作业车.glb", "name": "多功能作业车", "view": [114.39320168359966, 22.65361298042061, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.39320168359966, 22.65361298042061]}, "Crane6": {"url": "/model/铁塔组立机械化施工/起重机/起重机.glb", "name": "起重机", "view": [114.39301435135111, 22.65356969109935, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.39301435135111, 22.65356969109935]}, "Helicopter6": {"url": "/model/铁塔组立机械化施工/直升机/直升机.glb", "name": "直升机", "view": [114.39320010118831, 22.653743379308057, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.39320010118831, 22.653743379308057, 100]}, "Drone6": {"url": "/model/铁塔组立机械化施工/无人机/无人机.glb", "name": "无人机", "view": [114.39298730796907, 22.65371272910321, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.5, "rotation": [0.25, -2.025, 0.25], "position": [114.39298730796907, 22.65371272910321]}, "multifunctionalVehicle7": {"url": "/model/铁塔组立机械化施工/多功能作业车/多功能作业车.glb", "name": "多功能作业车", "view": [114.35566493626102, 22.657703625732914, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.35566493626102, 22.657703625732914]}, "Crane7": {"url": "/model/铁塔组立机械化施工/起重机/起重机.glb", "name": "起重机", "view": [114.35644990851088, 22.65771344141461, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.8, "rotation": [20.25, -0.025, 0.25], "position": [114.35644990851088, 22.65771344141461]}, "Helicopter7": {"url": "/model/铁塔组立机械化施工/直升机/直升机.glb", "name": "直升机", "view": [114.35669230343525, 22.657877802948654, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 1, "rotation": [0.25, -2.025, 0.25], "position": [114.35669230343525, 22.657877802948654, 100]}, "Drone7": {"url": "/model/铁塔组立机械化施工/无人机/无人机.glb", "name": "无人机", "view": [114.35650638131321, 22.657878260031676, 200], "heading": 0, "pitch": 0, "modelType": "gltf", "fixPosition": true, "scale": 0.5, "rotation": [0.25, -2.025, 0.25], "position": [114.35650638131321, 22.657878260031676]}}