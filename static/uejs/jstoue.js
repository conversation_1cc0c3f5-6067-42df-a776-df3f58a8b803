(function() {
  // 仅在特定WebView环境中执行
  if (navigator.userAgent.indexOf("ZdnWebView") === -1) {
      return;
  }

  // 初始化ue对象
  if (typeof ue !== 'object') {
      ue = {};
  }
  // 初始化ue.interface对象
  if (typeof ue.interface !== 'object') {
      ue.interface = {};
  }
  // 定义ue.call方法（异步通信核心逻辑）
  if (typeof ue.call !== 'function') {
      ue.call = (function () {
          /**
           * 生成符合UUID v4标准的唯一ID（用于回调标识）
           * @returns {string} UUIDv4字符串
           */
          function generateCallbackId() {
              return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                  const r = crypto.getRandomValues(new Uint8Array(1))[0] % 16;
                  const v = c === 'x' ? r : (r & 0x3 | 0x8); // UUID版本4规范
                  return v.toString(16);
              });
          }

          /**
           * 注册回调并设置超时清理
           * @param {Function} callback 回调函数
           * @param {number} timeout 超时时间(秒)
           * @returns {string} 回调ID
           */
          function registerCallback(callback, timeout) {
              if (typeof callback !== 'function') return '';
              const funcid = generateCallbackId();
              ue.interface[funcid] = callback;
              // 设置超时清除（最低2秒）
              setTimeout(() => {
                  delete ue.interface[funcid];
              }, 1000 * Math.max(2, parseInt(timeout) || 0));
              return funcid;
          }

          /**
           * 异步通信方法
           * @param {string} key 接口标识
           * @param {Object|string|null} json 请求参数
           * @param {Function} [cback] 回调函数
           * @param {number} [timeout] 超时时间
           */
          return function (key, json, cback, timeout) {
              // 参数重载处理：支持简写形式 call(key, callback)
              if (typeof json === 'function') {
                  timeout = cback;
                  cback = json;
                  json = null;
              }

              // 检查接收器是否就绪
              if (typeof ue.$receive?.asyn !== 'function') {
                  console.error('[ue.$receive.asyn] 通信通道未就绪，丢弃消息:', 
                      'key:', key, 'data:', json);
                  return;
              }

              // 注册回调并获取ID
              const callbackId = registerCallback(cback, timeout);

              // 发送消息（自动序列化非字符串数据）
              const payload = (typeof json !== 'string' && json !== null) 
                  ? JSON.stringify(json) 
                  : json;
              ue.$receive.asyn(key, payload, callbackId);
          };
      })();
  }

  // 提供ue4别名（兼容旧版本）
  if (typeof ue4 !== 'function') {
      ue4 = ue.call;
  }
})();
