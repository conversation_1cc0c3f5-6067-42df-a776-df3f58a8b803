import { fileURLToPath, URL } from 'node:url'
import path from 'node:path'

import { defineConfig,loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import cesium from 'vite-plugin-cesium'
import { viteStaticCopy } from 'vite-plugin-static-copy'



// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) =>{
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV,VITE_CONTEXT } = env
   return {

  plugins: [
     vue(),
     cesium({
       cesiumBuildPath: 'public/Cesium',
       cesiumBuildRootPath: 'Cesium',
       rebuildCesium: false, // 不重建，使用现有定制版本
       devMinifyCesium: false // 开发模式不压缩
     }),
     viteStaticCopy({
      targets: [
        {
          src: 'static/jessibuca',
          dest: 'jessibuca'
        }
        // Cesium资源由vite-plugin-cesium自动处理，不需要手动复制
      ]
    })
    ],
  base: VITE_CONTEXT,


  server: {
    port: 3080,
    proxy: {
      "/api": {
        target: env.VITE_API_TARGET,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
      '/modelStatic': {
        target: env.VITE_MODELSTATIC_TARGET,
        changeOrigin: true,
      },
      '/server-modelStatic': {
        target: env.VITE_SERVER_MODELSTATIC_TARGET,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/server-modelStatic/, '/modelStatic'),
      },
      '/building': {
        target: env.VITE_SERVER_MODELSTATIC_TARGET,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/building/, '/modelStatic/TEST/building'),
      },
      '/model': {
        target: env.VITE_SERVER_MODELSTATIC_TARGET,
        changeOrigin: true,
        secure: false,
        logLevel: 'debug',
        rewrite: (path) => path.replace(/^\/model/, '/modelStatic/TEST/model'),
        configure: (proxy, options) => {
          // 调试日志
          proxy.on('proxyReq', (proxyReq, req, res, options) => {
            if (req?.url) {
              console.log('Model代理请求:', req.method, req.url);
              const targetUrl = String(options.target || '') + 
                                req.url.replace(/^\/model/, '/modelStatic/TEST/model');
              console.log('Model目标URL:', targetUrl);
            }
          });
        }
      },

      '/server-zdnbase': {
        target: env.VITE_SERVER_ZDNBASE_TARGET,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/server-zdnbase/, '/zdnbase-dm'),
      },
      '/zdnbase-dm': {
        target: env.VITE_BACKEN_TARGET,
        changeOrigin: true,
      },
    '/gsdmap': {
        target: env.VITE_GSDMAP_TARGET,
        changeOrigin: true,
      },
      '/localMapUrl': {
        target: env.VITE_LOCALMAP_URL_TARGET,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/localMapUrl/, ""),
      },
      '/geoserver': {
        target: env.VITE_GEOSERVER_TARGET || 'http://127.0.0.1:5030',
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          // 错误处理
          proxy.on('error', (err, req, res) => {
            console.error('GeoServer代理错误:', err);
            
            if (!res.headersSent) {
              res.writeHead(502, {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              });
              
              const errorMessage = {
                error: true,
                message: 'GeoServer请求错误',
                url: req?.url,
                cause: err.message
              };
              
              res.end(JSON.stringify(errorMessage));
            }
          });
          
          // 请求处理及超时设置
          proxy.on('proxyReq', (proxyReq, req, res, options) => {
            if (req?.url) {
              console.log('GeoServer代理请求:', req.method, req.url);
              
              // 额外的调试信息
              if (req.url.includes('GetTile')) {
                console.log('WMTS瓦片请求:', req.url);
                const match = req.url.match(/TILEMATRIX=([^&]+)/);
                if (match) {
                  console.log('请求瓦片级别:', match[1]);
                }
              }
              
              // 对瓦片请求设置更长的超时时间
              if (req.url.includes('GetTile') || req.url.includes('GetMap')) {
                proxyReq.setTimeout(60000); // 60秒超时
              } else {
                proxyReq.setTimeout(30000); // 30秒超时
              }
            }
          });
          
          // 响应处理及CORS配置
          proxy.on('proxyRes', (proxyRes, req, res) => {
            const statusCode = proxyRes.statusCode;
            if (req?.url) {
              console.log('GeoServer代理响应:', statusCode, req.url);
              
              // 对错误响应记录更详细信息
              if (statusCode && statusCode >= 400) {
                console.error(`GeoServer错误响应: ${statusCode} ${req.url || ''}`);
                if (req.url.includes('GetTile')) {
                  console.error('瓦片请求失败:', req.url);
                }
              }
            }
            
            // 设置CORS头
            proxyRes.headers['access-control-allow-origin'] = '*';
            proxyRes.headers['access-control-allow-methods'] = 'GET, POST, OPTIONS';
            proxyRes.headers['access-control-allow-headers'] = 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
            
            // 删除潜在的问题头
            delete proxyRes.headers['x-frame-options'];
            delete proxyRes.headers['content-security-policy'];
          });
        },
      }
    },

  },

  define: {
    // 定义Cesium全局变量
    'CESIUM_BASE_URL': JSON.stringify(VITE_CONTEXT + 'Cesium/')
  },

  // 配置public目录
  publicDir: 'public',

  optimizeDeps: {
    include: ['lerc', 'cesium > mersenne-twister', 'urijs', 'grapheme-splitter', 'bitmap-sdf'],
    // exclude: ['cesium'], // 让vite-plugin-cesium处理Cesium
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '#': fileURLToPath(new URL('./public', import.meta.url)),

      'bitmap-sdf': fileURLToPath(new URL('./node_modules/bitmap-sdf/index.js', import.meta.url)),
      'lerc': path.resolve(__dirname, 'src/js/common/lerc-wrapper.js'),
    }
  },

  build: {
    target: 'esnext', // 确保目标支持最新的JS特性
    assetsInlineLimit: 0, // 禁用资源内联
    commonjsOptions: {
        transformMixedEsModules: true
    },
    rollupOptions: {
        external: ['cesium'],
        output: {
          globals: {
            cesium: 'Cesium'
          }
        },
        treeshake: false
      }
  }
}
})
